{"version": 3, "file": "collection.js", "sourceRoot": "", "sources": ["../../src/model/collection.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,iBAAiB,CAAA;AAKpD,MAAM,OAAO,UAAW,SAAQ,QAA8B;IAM5D,YAAY,KAAoB,EAAE,UAA8B,EAAE;QAChE,KAAK,EAAE,CAAA;QANF,WAAM,GAAG,CAAC,CAAA;QAOf,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,QAAQ,CAAA;QAChD,IAAI,CAAC,KAAK,EAAE,CAAA;QACZ,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAA;SACpC;IACH,CAAC;IAED,MAAM;QACJ,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAA;IAChD,CAAC;IAQD,GAAG,CACD,KAAoB,EACpB,KAAsC,EACtC,OAA+B;QAE/B,IAAI,UAAkB,CAAA;QACtB,IAAI,YAAmC,CAAA;QAEvC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,UAAU,GAAG,KAAK,CAAA;YAClB,YAAY,mBAAK,KAAK,EAAE,KAAK,IAAK,OAAO,CAAE,CAAA;SAC5C;aAAM;YACL,UAAU,GAAG,IAAI,CAAC,MAAM,CAAA;YACxB,YAAY,mBAAK,KAAK,EAAE,KAAK,IAAK,KAAK,CAAE,CAAA;SAC1C;QAED,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE;YAC5B,UAAU,GAAG,IAAI,CAAC,MAAM,CAAA;SACzB;QACD,IAAI,UAAU,GAAG,CAAC,EAAE;YAClB,UAAU,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;SAC9B;QAED,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;QACvD,MAAM,QAAQ,GACZ,IAAI,CAAC,UAAU;YACf,OAAO,KAAK,KAAK,QAAQ;YACzB,YAAY,CAAC,IAAI,KAAK,KAAK,CAAA;QAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAA;QAExC,IAAI,IAAI,GAAG,KAAK,CAAA;QAChB,MAAM,KAAK,GAAW,EAAE,CAAA;QACxB,MAAM,MAAM,GAAW,EAAE,CAAA;QAEzB,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;YAC/B,IAAI,QAAQ,EAAE;gBACZ,IAAI,YAAY,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE;oBACrD,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,CAAC,CAAA,CAAC,QAAQ;oBAClD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;oBACrB,IAAI,QAAQ,IAAI,CAAC,IAAI,EAAE;wBACrB,IAAI,QAAQ,IAAI,IAAI,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;4BACtD,IAAI,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAA;yBAC7B;6BAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;4BACvC,IAAI,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;yBACrC;6BAAM;4BACL,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAA;yBACxD;qBACF;iBACF;aACF;iBAAM;gBACL,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAChB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;aACrB;QACH,CAAC,CAAC,CAAA;QAEF,IAAI,KAAK,CAAC,MAAM,EAAE;YAChB,IAAI,QAAQ,EAAE;gBACZ,IAAI,GAAG,IAAI,CAAA;aACZ;YACD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,CAAA;YAC1C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAA;SAChC;QAED,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAA;SAC5B;QAED,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YACxB,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;gBACxB,MAAM,IAAI,GAAG;oBACX,IAAI;oBACJ,KAAK,EAAE,UAAU,GAAG,CAAC;oBACrB,OAAO,EAAE,YAAY;iBACtB,CAAA;gBACD,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;gBAC3B,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;oBACxB,IAAI,CAAC,MAAM,CAAC,OAAO,oBAAO,IAAI,EAAG,CAAA;iBAClC;YACH,CAAC,CAAC,CAAA;YAEF,IAAI,IAAI,EAAE;gBACR,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;aACvB;YAED,IAAI,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;gBACjC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;oBACtB,KAAK;oBACL,MAAM;oBACN,OAAO,EAAE,EAAE;oBACX,OAAO,EAAE,YAAY;iBACtB,CAAC,CAAA;aACH;SACF;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAID,MAAM,CAAC,KAAoB,EAAE,UAAoC,EAAE;QACjE,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;QAClD,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;QAC9C,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACzC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;gBACtB,OAAO;gBACP,OAAO;gBACP,KAAK,EAAE,EAAE;gBACT,MAAM,EAAE,EAAE;aACX,CAAC,CAAA;SACH;QACD,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IACpD,CAAC;IAES,WAAW,CAAC,KAAa,EAAE,OAAiC;QACpE,MAAM,OAAO,GAAG,EAAE,CAAA;QAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YACxC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;YAC/B,IAAI,IAAI,IAAI,IAAI,EAAE;gBAChB,SAAQ;aACT;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YACtC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;YAC3B,IAAI,CAAC,MAAM,IAAI,CAAC,CAAA;YAChB,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YACxB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAClB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;YAEtB,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACnB,IAAI,CAAC,MAAM,EAAE,CAAA;aACd;YAED,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACnB,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAA;gBAEjD,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;oBACnB,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAA;iBACjD;aACF;SACF;QAED,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,KAAK,CAAC,KAAoB,EAAE,UAAiC,EAAE;QAC7D,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;QACnC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;QAClD,IAAI,CAAC,KAAK,EAAE,CAAA;QACZ,IAAI,CAAC,GAAG,CAAC,KAAK,kBAAI,MAAM,EAAE,IAAI,IAAK,OAAO,EAAG,CAAA;QAC7C,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACnB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;YAClC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;gBACtB,OAAO;gBACP,QAAQ;gBACR,OAAO;aACR,CAAC,CAAA;YAEF,MAAM,KAAK,GAAW,EAAE,CAAA;YACxB,MAAM,OAAO,GAAW,EAAE,CAAA;YAE1B,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBACpB,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAA;gBACjD,IAAI,CAAC,KAAK,EAAE;oBACV,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;iBACd;YACH,CAAC,CAAC,CAAA;YAEF,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBACrB,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAA;gBAChD,IAAI,CAAC,KAAK,EAAE;oBACV,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;iBAChB;YACH,CAAC,CAAC,CAAA;YAEF,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAA;SACjE;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED,IAAI,CAAC,IAAU,EAAE,OAA+B;QAC9C,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IAC7C,CAAC;IAED,GAAG,CAAC,OAA+B;QACjC,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAE,CAAA;QACtC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IACnC,CAAC;IAED,OAAO,CAAC,IAAU,EAAE,OAA+B;QACjD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,CAAA;IACnC,CAAC;IAED,KAAK,CAAC,OAA+B;QACnC,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAE,CAAA;QACxB,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IACnC,CAAC;IAED,GAAG,CAAC,IAAoC;QACtC,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,OAAO,IAAI,CAAA;SACZ;QAED,MAAM,EAAE,GACN,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAA;QACvE,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAA;IAC7B,CAAC;IAED,GAAG,CAAC,IAAmB;QACrB,OAAO,IAAI,CAAC,GAAG,CAAC,IAAW,CAAC,IAAI,IAAI,CAAA;IACtC,CAAC;IAED,EAAE,CAAC,KAAa;QACd,IAAI,KAAK,GAAG,CAAC,EAAE;YACb,KAAK,IAAI,IAAI,CAAC,MAAM,CAAA,CAAC,sBAAsB;SAC5C;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,IAAI,CAAA;IAClC,CAAC;IAED,KAAK;QACH,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;IACnB,CAAC;IAED,IAAI;QACF,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;IACpB,CAAC;IAED,OAAO,CAAC,IAAU;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;IACjC,CAAC;IAED,OAAO;QACL,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;IAC3B,CAAC;IAED,IAAI,CAAC,UAAiC,EAAE;QACtC,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE;YAC3B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;YACzD,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACnB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;aACvB;SACF;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED,KAAK;QACH,MAAM,WAAW,GAAG,IAAI,CAAC,WAAkB,CAAA;QAC3C,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE;YACzC,UAAU,EAAE,IAAI,CAAC,UAAU;SAC5B,CAAe,CAAA;IAClB,CAAC;IAES,SAAS,CAAC,IAAU;QAC5B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAA;QACxB,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;IAC1C,CAAC;IAES,WAAW,CAAC,IAAU;QAC9B,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;QACzC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;IAC1B,CAAC;IAES,eAAe,CACvB,IAAO,EACP,IAAuB;QAEvB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,EAAE,IAAI,CAAC,CAAA;QAClC,IAAI,IAAI,EAAE;YACR,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;gBACjB,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,kCAAO,IAAI,KAAE,IAAI,EAAE,IAAI,IAAG,CAAA;aACtD;iBAAM,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;gBACxB,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,kCAAO,IAAI,KAAE,IAAI,EAAE,IAAI,IAAG,CAAA;aACtD;SACF;IACH,CAAC;IAES,KAAK;QACb,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;QACf,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;QACf,IAAI,CAAC,GAAG,GAAG,EAAE,CAAA;IACf,CAAC;IAGD,OAAO;QACL,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;IAChB,CAAC;CACF;AAHC;IADC,UAAU,CAAC,OAAO,EAAE;yCAGpB"}