{"version": 3, "file": "text.js", "sourceRoot": "", "sources": ["../../../src/registry/attr/text.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,SAAS,EAET,SAAS,EACT,GAAG,EACH,WAAW,GAEZ,MAAM,iBAAiB,CAAA;AAGxB,MAAM,CAAC,MAAM,IAAI,GAAoB;IACnC,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;QACrB,OAAO,KAAK,CAAC,QAAQ,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;IAC3E,CAAC;IACD,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE;QAC7B,MAAM,SAAS,GAAG,SAAS,CAAA;QAC3B,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;QACvC,MAAM,IAAI,GAAG,CAAI,GAAQ,EAAE,EAAE;YAC3B,IAAI;gBACF,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAM,CAAA;aAC5B;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,GAAG,CAAA;aACX;QACH,CAAC,CAAA;QACD,MAAM,OAAO,GAAoB;YAC/B,CAAC,EAAE,KAAK,CAAC,CAAoB;YAC7B,GAAG,EAAE,KAAK,CAAC,GAAa;YACxB,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAEd;YACrB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC;YACpD,kBAAkB,EAAE,CAAC,KAAK,CAAC,sBAAsB,CAAC;gBAChD,KAAK,CAAC,kBAAkB,CAAyC;YACnE,YAAY,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,KAAK,MAAM;YACvE,UAAU,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,UAAU,CAAW;SACjE,CAAA;QAED,MAAM,QAAQ,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAW,CAAA;QACjE,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAA;QAEhD,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAA;SACzC;QAED,4DAA4D;QAC5D,4BAA4B;QAC5B,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,QAAQ,EAAE;YACvC,2BAA2B;YAC3B,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAe,CAAA;YACxC,IAAI,QAAQ,IAAI,IAAI,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;gBACpD,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAA;gBAClC,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;oBAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;oBACvC,IAAI,QAAQ,YAAY,cAAc,EAAE;wBACtC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;wBACtB,OAAO,CAAC,QAAQ,mBACd,YAAY,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAC5B,QAAQ,CACZ,CAAA;qBACF;iBACF;aACF;YAED,GAAG,CAAC,IAAI,CAAC,IAAkB,EAAE,GAAG,IAAI,EAAE,EAAE,OAAO,CAAC,CAAA;YAChD,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAA;SACpC;IACH,CAAC;CACF,CAAA;AAED,MAAM,CAAC,MAAM,QAAQ,GAAoB;IACvC,OAAO,EAAE,SAAS,CAAC,aAAa;IAChC,GAAG,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE;QACrC,MAAM,IAAI,GAAG,GAAiB,CAAA;QAE9B,iBAAiB;QACjB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,CAAA;QAC7B,IAAI,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;YACjC,OAAO,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,CAAA;SACzC;aAAM,IAAI,KAAK,IAAI,CAAC,EAAE;YACrB,OAAO,CAAC,KAAK,IAAI,KAAe,CAAA;SACjC;aAAM;YACL,OAAO,CAAC,KAAK,GAAG,KAAe,CAAA;SAChC;QAED,kBAAkB;QAClB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,CAAA;QAC/B,IAAI,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;YAClC,OAAO,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA;SAC3C;aAAM,IAAI,MAAM,IAAI,CAAC,EAAE;YACtB,OAAO,CAAC,MAAM,IAAI,MAAgB,CAAA;SACnC;aAAM;YACL,OAAO,CAAC,MAAM,GAAG,MAAgB,CAAA;SAClC;QAED,gBAAgB;QAChB,IAAI,WAAW,CAAA;QACf,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAA;QACnB,IAAI,GAAG,IAAI,IAAI,EAAE;YACf,2CAA2C;YAC3C,GAAG,GAAG,KAAK,CAAC,IAAI,KAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAA,CAAA;SACtC;QAED,IAAI,GAAG,IAAI,IAAI,EAAE;YACf,WAAW,GAAG,GAAG,CAAC,SAAS,CACzB,GAAG,GAAG,EAAE,EACR,OAAO,EACP;gBACE,aAAa,EAAE,KAAK,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,UAAU;gBACvD,WAAW,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,QAAQ;gBACjD,aAAa,EAAE,KAAK,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,UAAU;gBACvD,UAAU,EAAE,KAAK,CAAC,UAAU;aAC7B,EACD;gBACE,oCAAoC;gBACpC,QAAQ,EAAE,IAAI,CAAC,QAAkB;gBACjC,iCAAiC;gBACjC,wCAAwC;aACzC,CACF,CAAA;SACF;aAAM;YACL,WAAW,GAAG,EAAE,CAAA;SACjB;QAED,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE;YAC5C,IAAI;YACJ,IAAI;YACJ,KAAK;YACL,OAAO;YACP,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC,CAAA;IACJ,CAAC;CACF,CAAA;AAED,MAAM,WAAW,GAAyB,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;IAC3D,OAAO,KAAK,CAAC,IAAI,KAAK,SAAS,CAAA;AACjC,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,UAAU,GAAoB;IACzC,OAAO,EAAE,WAAW;CACrB,CAAA;AAED,MAAM,CAAC,MAAM,kBAAkB,GAAoB;IACjD,OAAO,EAAE,WAAW;CACrB,CAAA;AAED,MAAM,CAAC,MAAM,QAAQ,GAAoB;IACvC,OAAO,EAAE,WAAW;CACrB,CAAA;AAED,MAAM,CAAC,MAAM,WAAW,GAAoB;IAC1C,OAAO,EAAE,WAAW;CACrB,CAAA;AAED,MAAM,CAAC,MAAM,GAAG,GAAoB;IAClC,OAAO,EAAE,WAAW;CACrB,CAAA;AAED,MAAM,CAAC,MAAM,YAAY,GAAoB;IAC3C,OAAO,EAAE,WAAW;CACrB,CAAA"}