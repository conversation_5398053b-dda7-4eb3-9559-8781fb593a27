webpackJsonp([26],{

/***/ 2002:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.default = {
    props: {
        row: Object
    },
    data: function data() {
        return {
            logInfo: [{
                title: "级别",
                width: 80,
                key: "name"
            }, {
                title: "时间",

                key: "date"
            }, {
                title: "来源",

                key: "address"
            }, {
                title: "描述",
                width: 80,
                key: "age"
            }],
            logInfoData: [{
                name: '11111',
                age: 18,
                address: '2222222',
                date: '2016-10-03'
            }, {
                name: '11111',
                age: 18,
                address: '2222222',
                date: '2016-10-03'
            }, {
                name: '11111',
                age: 18,
                address: '2222222',
                date: '2016-10-03'
            }]
        };
    }
};

/***/ }),

/***/ 2138:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_tableExpand_vue__ = __webpack_require__(2002);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_tableExpand_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_tableExpand_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_tableExpand_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_tableExpand_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_c128c672_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_tableExpand_vue__ = __webpack_require__(2141);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_c128c672_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_tableExpand_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_c128c672_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_tableExpand_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(2139)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = null
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_tableExpand_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_c128c672_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_tableExpand_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/report/tableExpand.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-c128c672", Component.options)
  } else {
    hotAPI.reload("data-v-c128c672", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ }),

/***/ 2139:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(2140);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("84ca2d3c", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-c128c672\",\"scoped\":false,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./tableExpand.vue", function() {
     var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-c128c672\",\"scoped\":false,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./tableExpand.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 2140:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.rateTitle{border-bottom:1px solid #ccc;padding:0 0 8px 5px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:start;-ms-flex-pack:start;justify-content:flex-start;-webkit-box-align:center;-ms-flex-align:center;align-items:center;margin-bottom:15px\n}\n.rateTitle span{display:block;width:4px;height:15px;background:#ff6902;margin-right:10px\n}\n.basicInfo{height:110px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:start;-ms-flex-pack:start;justify-content:flex-start;line-height:2rem;font-size:0.875rem\n}\n.infoList{margin-right:40px\n}\n.logInfo{height:160px\n}", ""]);

// exports


/***/ }),

/***/ 2141:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", [_c("Row", { staticClass: "expand-row" }, [_c("Col", { attrs: { span: "24" } }, [_c("h3", { staticClass: "rateTitle" }, [_c("span"), _vm._v("基本信息")])]), _vm._v(" "), _c("Col", { attrs: { span: "24" } }, [_c("div", { staticClass: "basicInfo" }, [_c("div", { staticClass: "infoList" }, [_c("p", [_c("strong", { staticClass: "expandKey" }, [_vm._v("test222")]), _vm._v(" "), _c("span", { staticClass: "expanValue" }, [_vm._v("TEST222")])]), _vm._v(" "), _c("p", [_c("strong", { staticClass: "expandKey" }, [_vm._v("test222")]), _vm._v(" "), _c("span", { staticClass: "expanValue" }, [_vm._v("TEST222")])]), _vm._v(" "), _c("p", [_c("strong", { staticClass: "expandKey" }, [_vm._v("test222")]), _vm._v(" "), _c("span", { staticClass: "expanValue" }, [_vm._v("TEST222")])])]), _vm._v(" "), _c("div", { staticClass: "infoList" }, [_c("p", [_c("strong", { staticClass: "expandKey" }, [_vm._v("test222")]), _vm._v(" "), _c("span", { staticClass: "expanValue" }, [_vm._v("TEST222")])]), _vm._v(" "), _c("p", [_c("strong", { staticClass: "expandKey" }, [_vm._v("test222")]), _vm._v(" "), _c("span", { staticClass: "expanValue" }, [_vm._v("TEST222")])]), _vm._v(" "), _c("p", [_c("strong", { staticClass: "expandKey" }, [_vm._v("test222")]), _vm._v(" "), _c("span", { staticClass: "expanValue" }, [_vm._v("TEST222")])])]), _vm._v(" "), _c("div", { staticClass: "infoList" }, [_c("p", [_c("strong", { staticClass: "expandKey" }, [_vm._v("test222")]), _vm._v(" "), _c("span", { staticClass: "expanValue" }, [_vm._v("TEST222")])]), _vm._v(" "), _c("p", [_c("strong", { staticClass: "expandKey" }, [_vm._v("test222")]), _vm._v(" "), _c("span", { staticClass: "expanValue" }, [_vm._v("TEST222")])]), _vm._v(" "), _c("p", [_c("strong", { staticClass: "expandKey" }, [_vm._v("test222")]), _vm._v(" "), _c("span", { staticClass: "expanValue" }, [_vm._v("TEST222")])])]), _vm._v(" "), _c("div", { staticClass: "infoList" }, [_c("p", [_c("strong", { staticClass: "expandKey" }, [_vm._v("test222")]), _vm._v(" "), _c("span", { staticClass: "expanValue" }, [_vm._v("TEST222")])]), _vm._v(" "), _c("p", [_c("strong", { staticClass: "expandKey" }, [_vm._v("test222")]), _vm._v(" "), _c("span", { staticClass: "expanValue" }, [_vm._v("TEST222")])]), _vm._v(" "), _c("p", [_c("strong", { staticClass: "expandKey" }, [_vm._v("test222")]), _vm._v(" "), _c("span", { staticClass: "expanValue" }, [_vm._v("TEST222")])])]), _vm._v(" "), _c("div", { staticClass: "infoList" }, [_c("p", [_c("strong", { staticClass: "expandKey" }, [_vm._v("test222")]), _vm._v(" "), _c("span", { staticClass: "expanValue" }, [_vm._v("TEST222")])]), _vm._v(" "), _c("p", [_c("strong", { staticClass: "expandKey" }, [_vm._v("test222")]), _vm._v(" "), _c("span", { staticClass: "expanValue" }, [_vm._v("TEST222")])]), _vm._v(" "), _c("p", [_c("strong", { staticClass: "expandKey" }, [_vm._v("test222")]), _vm._v(" "), _c("span", { staticClass: "expanValue" }, [_vm._v("TEST222")])])]), _vm._v(" "), _c("div", { staticClass: "infoList" }, [_c("p", [_c("strong", { staticClass: "expandKey" }, [_vm._v("test222")]), _vm._v(" "), _c("span", { staticClass: "expanValue" }, [_vm._v("TEST222")])]), _vm._v(" "), _c("p", [_c("strong", { staticClass: "expandKey" }, [_vm._v("test222")]), _vm._v(" "), _c("span", { staticClass: "expanValue" }, [_vm._v("TEST222")])]), _vm._v(" "), _c("p", [_c("strong", { staticClass: "expandKey" }, [_vm._v("test222")]), _vm._v(" "), _c("span", { staticClass: "expanValue" }, [_vm._v("TEST222")])])])])])], 1), _vm._v(" "), _c("Row", { staticClass: "expand-row" }, [_c("Col", { attrs: { span: "24" } }, [_c("h3", { staticClass: "rateTitle" }, [_c("span"), _vm._v("任务日志")])]), _vm._v(" "), _c("Col", { attrs: { span: "24" } }, [_c("div", { staticClass: "logInfo" }, [_c("Table", {
    attrs: { data: _vm.logInfoData, columns: _vm.logInfo }
  })], 1)])], 1)], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-c128c672", esExports);
  }
}

/***/ }),

/***/ 2834:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _util = __webpack_require__(16);

var _util2 = _interopRequireDefault(_util);

var _axios = __webpack_require__(211);

var _axios2 = _interopRequireDefault(_axios);

var _tableExpand = __webpack_require__(2138);

var _tableExpand2 = _interopRequireDefault(_tableExpand);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
	components: {
		expandRow: _tableExpand2.default
	},
	data: function data() {
		return {
			noImgUrl: __webpack_require__(317),
			tableHeight: 0,
			divArr: [],
			pgpool: 10,
			cityList: [],
			tooltipdata: '',
			tooltipdefint: '选择开始和结束时间',
			bgid: '',
			showBox: true,
			tabsData: '运行记录报表',
			numNowList: [],
			exportValue: '6',
			exportportValue: '8',
			pageSize: 10,
			modal2: false,
			modal3: false,
			mediumReport: [{
				title: '名称',
				key: 'name'
			}, {
				title: 'Barcode',
				key: 'barcode'
			}, {
				title: '已使用容量',
				key: 'bytes'
			}, {
				title: '介质池',
				key: 'pool'
			}, {
				title: '介质状态',
				key: 'status'
			}, {
				title: '在线状态',
				key: 'online'
			}, {
				title: '镜像数量',
				key: 'images'
			}, {
				title: '最后写入时间',
				key: 'lastwrtime'
			}, {
				title: '最后回收时间',
				key: 'RecycleTime'
			}],
			policyMaxPange: 1,
			poolMaxPange: 1,
			curentPage: 1,
			indexPage: 1,
			runPage: 100,
			medium: [{
				name: '',
				barcode: '',
				bytes: '',
				pool: '',
				status: '',
				online: '',
				images: '',
				lastwrtime: '',
				RecycleTime: ''
			}],
			status: [],
			clientSelect: [],
			typeSelect: [],
			serverSelect: [],
			statusSelect: [{
				code: 1,
				name: '成功'
			}, {
				code: 2,
				name: '失败'
			}, {
				code: 3,
				name: '取消'
			}],
			poolSelect: [],
			poolStatusSelect: [{
				code: '-1',
				name: '全部'
			}, {
				code: '0',
				name: '正常'
			}, {
				code: '2',
				name: '出错'
			}, {
				code: '1',
				name: '已满'
			}],
			deviceQuery: {
				mediaserver: '',
				type: '',
				status: '在线'
			},
			deviceQuery1: {
				mediaserver: '',
				type: '',
				status: ''
			},
			poolQuery: {
				pool: '',
				status: '正常'
			},
			poolQuery1: {
				pool: '',
				status: '-1'
			}
		};
	},
	created: function created() {
		this.$store.dispatch('getPrivilege', this.$store.state.power.module.report);
		this.generate();

		_util2.default.restfullCall('/rest-ful/v3.0/report/volume?pageno=1&nums=10', null, 'get', this.callbackMedium);
	},
	mounted: function mounted() {
		window.addEventListener('resize', this.getTableHeight);
		this.getTableHeight();
		_util2.default.restfullCall('/rest-ful/v3.0/volpools', null, 'get', this.poolData);

		_util2.default.restfullCall('/rest-ful/v3.0/report/volume?pageno=1&nums=10', null, 'get', this.callbackMedium);
		_util2.default.restfullCall('/rest-ful/v3.0/clients?nums=0', null, 'get', this.cliData);
		_util2.default.restfullCall('/rest-ful/v3.0/resourcetype', null, 'get', this.typeData);
		_util2.default.restfullCall('/rest-ful/v3.0/mediaservers', null, 'get', this.serverData);

		_util2.default.restfullCall('/rest-ful/v3.0/mediaservers', null, 'get', this.serverDatas);
		_util2.default.restfullCall('/rest-ful/v3.0/devicetype', null, 'get', this.deviceData);
		_util2.default.restfullCall('/rest-ful/v3.0/clients?nums=0', null, 'get', this.cliData);
		_util2.default.restfullCall('rest-ful/v3.0/devices?type=0', null, 'get', this.divData);
	},

	computed: {
		navIcon: function navIcon() {
			return this.$store.state.index.siderNameIcon;
		},
		getPrivilege: function getPrivilege() {
			return this.$store.state.index.privilegeData;
		},
		getPower: function getPower() {
			return this.$store.state.power.name;
		},
		devicesList: function devicesList() {
			return this.$store.state.index.devicesList;
		}
	},
	watch: {
		getPrivilege: function getPrivilege(data) {
			this.numNowList = data;
			if (this.numNowList.indexOf(this.getPower.seeMediaReprot) != -1) {
				this.tabsData = '介质报表';
			}
			if (this.numNowList.indexOf(this.getPower.seeDeviceReprot) != -1) {
				this.tabsData = '设备报表';
			}
			if (this.numNowList.indexOf(this.getPower.seeRunReprot) != -1) {
				this.tabsData = '运行记录报表';
			}
		},

		'poolQuery1.status': {
			handler: function handler(val) {
				this.poolQuery1.status = val;
				this.generatePool();
			},

			deep: true
		}
	},
	methods: {
		tishi: function tishi(currentRow, index) {
			if (currentRow.id == this.bgid) {
				return 'trbgshow';
			}
			return 'trbgshow_a';
		},
		onRowClick: function onRowClick(row) {
			this.bgid = row.id;
		},
		getTableHeight: function getTableHeight() {
			this.tableHeight = window.innerHeight - 280;
		},
		divData: function divData(res) {
			var _this = this;

			res.data.forEach(function (item, i) {
				_this.divArr.push({ id: item.id, name: item.name });
			});
		},
		medhandleSizeChange: function medhandleSizeChange(val) {
			this.pgpool = val;

			var url = '/rest-ful/v3.0/report/volume?pageno=' + this.curentPage + '&nums=' + val + '&pool=' + this.poolQuery1.pool + '&status=' + this.poolQuery1.status;
			_util2.default.restfullCall(url, null, 'get', this.callbackMedium);
		},

		showList: function showList(show) {
			this.showBox = show;
		},
		showListT: function showListT(show) {
			this.showBox = show;
		},
		showListJ: function showListJ(show) {
			this.showBox = show;
		},
		changePagev: function changePagev(index) {
			this.curentPage = index;
			var url = '/rest-ful/v3.0/report/volume?pageno=' + index + '&nums=' + this.pgpool + '&pool=' + this.poolQuery1.pool + '&status=' + this.poolQuery1.status;
			_util2.default.restfullCall(url, null, 'get', this.callbackMedium);
		},
		exportExcelReport: function exportExcelReport() {
			var _this2 = this;

			var me = this;
			var tableData = this.device;
			var title = me.deviceReport;
			var tHeader = [];
			var tHeaderId = [];
			for (var i = 0; i < title.length; i++) {
				if (title[i].key != null && title[i].key != 'action') {
					tHeader.push(title[i].title);
					tHeaderId.push(title[i].key);
				}
			}
			var tableinfo = [];
			for (var n = 0; n < tableData.length; n++) {
				var obj = {};
				for (var j = 0; j < tHeaderId.length; j++) {
					var id = tHeaderId[j];
					var value = tableData[n][tHeaderId[j]];
					obj[id] = value;
				}
				tableinfo.push(obj);
			}
			var formatJson = function formatJson(filterVal, jsonData) {
				return jsonData.map(function (v) {
					return filterVal.map(function (j) {
						return v[j];
					});
				});
			};

			new Promise(function(resolve) { resolve(); }).then((function () {
				var _require = __webpack_require__(539),
				    export_json_to_excel = _require.export_json_to_excel;

				var excelTile = '设备报表' + _this2.formatDateTime(new Date());
				var tHeaderTitle = tHeader;
				var filterVal = tHeaderId;
				var list = tableinfo;
				var data = formatJson(filterVal, list);
				export_json_to_excel(tHeaderTitle, data, excelTile);
			}).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
		},
		exportExcel: function exportExcel() {
			var _this3 = this;

			var me = this;
			var tableData = this.run;
			var title = me.runReport;
			var tHeader = [];
			var tHeaderId = [];
			for (var i = 0; i < title.length; i++) {
				if (title[i].key != null && title[i].key != 'action') {
					tHeader.push(title[i].title);
					tHeaderId.push(title[i].key);
				}
			}
			var tableinfo = [];
			for (var n = 0; n < tableData.length; n++) {
				var obj = {};
				for (var j = 0; j < tHeaderId.length; j++) {
					var id = tHeaderId[j];
					var value = tableData[n][tHeaderId[j]];
					obj[id] = value;
				}
				tableinfo.push(obj);
			}
			var formatJson = function formatJson(filterVal, jsonData) {
				return jsonData.map(function (v) {
					return filterVal.map(function (j) {
						return v[j];
					});
				});
			};

			new Promise(function(resolve) { resolve(); }).then((function () {
				var _require2 = __webpack_require__(539),
				    export_json_to_excel = _require2.export_json_to_excel;

				var excelTile = '运行记录报表' + _this3.formatDateTime(new Date());
				var tHeaderTitle = tHeader;
				var filterVal = tHeaderId;
				var list = tableinfo;
				var data = formatJson(filterVal, list);
				export_json_to_excel(tHeaderTitle, data, excelTile);
			}).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
		},
		formatDateTime: function formatDateTime(date) {
			var y = date.getFullYear();
			var m = date.getMonth() + 1;
			m = m < 10 ? '0' + m : m;
			var d = date.getDate();
			d = d < 10 ? '0' + d : d;
			var h = date.getHours();
			h = h < 10 ? '0' + h : h;
			var minute = date.getMinutes();
			minute = minute < 10 ? '0' + minute : minute;
			var second = date.getSeconds();
			second = second < 10 ? '0' + second : second;
			return y + '-' + m + '-' + d + ' ' + h + '.' + minute + '.' + second;
		},
		exportData: function exportData(value) {
			this.modal2 = true;
		},
		exportClanOk: function exportClanOk() {
			if (this.exportValue == '6') {
				this.exportCsv();
			}
			if (this.exportValue == '7') {
				this.exportExcel();
			}
			if (this.exportValue == '9') {
				this.exportWps();
			}
			this.modal2 = false;
		},
		exportClanOkReport: function exportClanOkReport() {
			if (this.exportportValue == '6') {
				this.exportCsv();
			}
			if (this.exportportValue == '8') {
				this.exportExcelReport();
			}
			this.modal3 = false;
		},
		exportClance: function exportClance() {
			this.modal2 = false;
		},
		exportClanceReport: function exportClanceReport() {
			this.modal3 = false;
		},
		exportCsv: function exportCsv() {
			this.$refs.exp.exportCsv({
				filename: '运行记录报表' + this.formatDateTime(new Date())
			});
		},
		exportWps: function exportWps() {
			var _this4 = this;

			var me = this;
			var tableData = this.run;
			var title = me.runReport;
			var tHeader = [];
			var tHeaderId = [];
			for (var i = 0; i < title.length; i++) {
				if (title[i].key != null && title[i].key != 'action') {
					tHeader.push(title[i].title);
					tHeaderId.push(title[i].key);
				}
			}
			var tableinfo = [];
			for (var n = 0; n < tableData.length; n++) {
				var obj = {};
				for (var j = 0; j < tHeaderId.length; j++) {
					var id = tHeaderId[j];
					var value = tableData[n][tHeaderId[j]];
					obj[id] = value;
				}
				tableinfo.push(obj);
			}
			var formatJson = function formatJson(filterVal, jsonData) {
				return jsonData.map(function (v) {
					return filterVal.map(function (j) {
						return v[j];
					});
				});
			};

			new Promise(function(resolve) { resolve(); }).then((function () {
				var _require3 = __webpack_require__(539),
				    export_json_to_excel = _require3.export_json_to_excel;

				var excelTile = '运行记录报表' + _this4.formatDateTime(new Date());
				var tHeaderTitle = tHeader;
				var filterVal = tHeaderId;
				var list = tableinfo;
				var data = formatJson(filterVal, list);

				try {
					var oXL = new ActiveXObject('Ket.Application');
					if (oXL == null) {
						alert(oXL);
					}

					var url = './VRTSTask.et';

					var oWB = oXL.Workbooks.Open('D:\\VRTSTask.et');

					var oSheet = oWB.ActiveSheet;
					var ExcelSheet = oWB.Worksheets(1);

					var sucess = 0;
					var faile = 0;
					var countbf = 0;
					var counthf = 0;
					for (var i = 0; i < data.length; i++) {
						for (var j = 0; j < data[i].length; j++) {
							oSheet.Cells.Item(i + 17, j + 1).value2 = data[i][j];
							if (j == 1) {
								if (data[i][j] == '备份') {
									countbf = countbf + 1;
								} else {
									counthf = counthf + 1;
								}
							}
							if (j == 11) {
								if (data[i][j] == '成功') {
									sucess = sucess + 1;
								} else {
									faile = faile + 1;
								}
							}
						}
					}
					oSheet.Cells.Item(3, 3).Value2 = sucess;
					oSheet.Cells.Item(4, 3).Value2 = faile;
					oSheet.Cells.Item(3, 6).Value2 = countbf;
					oSheet.Cells.Item(4, 6).Value2 = counthf;
					var stids = 'D:\\' + excelTile + '.et';

					ExcelSheet.SaveAs(stids);
				} catch (e) {
					alert('导出WPS失败，确定是否安装了WPS');

					oXL = null;
					oWB = null;
					oSheet = null;
				}
			}).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
		},
		nowShow: function nowShow(num) {
			if (this.numNowList.indexOf(num) != -1) {
				return true;
			} else {
				return false;
			}
		},

		callbackMedium: function callbackMedium(mediumObj) {
			this.poolMaxPange = mediumObj.data.data.TotalNums;
			var array = new Array();
			for (var i = 0; i < mediumObj.data.data.Volumes.length; i++) {
				array.push({
					name: mediumObj.data.data.Volumes[i].name,
					barcode: mediumObj.data.data.Volumes[i].barcode,
					lastwrtime: mediumObj.data.data.Volumes[i].lastwrtime,
					bytes: mediumObj.data.data.Volumes[i].bytes,
					status: mediumObj.data.data.Volumes[i].status,
					online: mediumObj.data.data.Volumes[i].online,
					pool: mediumObj.data.data.Volumes[i].pool,
					images: mediumObj.data.data.Volumes[i].images,
					RecycleTime: mediumObj.data.data.Volumes[i].RecycleTime,
					id: i + 1
				});
			}
			this.medium = array;
		},
		generatePool: function generatePool() {
			var url = '/rest-ful/v3.0/report/volume?pageno=' + 1 + '&nums=' + this.pgpool + '&pool=' + this.poolQuery1.pool + '&status=' + this.poolQuery1.status;
			_util2.default.restfullCall(url, null, 'get', this.callbackPoolQuery1);
		},

		callbackPoolQuery: function callbackPoolQuery(obj) {
			this.poolMaxPange = obj.data.data.TotalNums;
			var array = new Array();
			for (var i = 0; i < obj.data.data.Volumes.length; i++) {
				array.push({
					name: obj.data.data.Volumes[i].name,
					barcode: obj.data.data.Volumes[i].barcode,
					lastwrtime: obj.data.data.Volumes[i].lastwrtime,
					bytes: obj.data.data.Volumes[i].bytes,
					status: obj.data.data.Volumes[i].status,
					online: obj.data.data.Volumes[i].online,
					pool: obj.data.data.Volumes[i].pool,
					images: obj.data.data.Volumes[i].images,
					RecycleTime: obj.data.data.Volumes[i].RecycleTime,
					id: i + 1
				});
			}
			this.medium = array;
		},
		callbackPoolQuery1: function callbackPoolQuery1(obj) {
			this.poolMaxPange = obj.data.data.TotalNums;
			var array = new Array();
			for (var i = 0; i < obj.data.data.Volumes.length; i++) {
				array.push({
					name: obj.data.data.Volumes[i].name,
					barcode: obj.data.data.Volumes[i].barcode,
					lastwrtime: obj.data.data.Volumes[i].lastwrtime,
					bytes: obj.data.data.Volumes[i].bytes,
					status: obj.data.data.Volumes[i].status,
					online: obj.data.data.Volumes[i].online,
					pool: obj.data.data.Volumes[i].pool,
					images: obj.data.data.Volumes[i].images,
					RecycleTime: obj.data.data.Volumes[i].RecycleTime,
					id: i + 1
				});
			}
			this.medium = array;
			this.curentPage = 1;
		},

		openClient: function openClient(openCli) {
			if (openCli == true) _util2.default.restfullCall('/rest-ful/v3.0/clients?nums=0', null, 'get', this.cliData);
		},

		cliData: function cliData(obj) {
			var array = new Array();
			for (var i = 0; i < obj.data.data.vrts_client_data.length; i++) {
				array.push({
					id: obj.data.data.vrts_client_data[i].id,
					machine: obj.data.data.vrts_client_data[i].machine
				});
			}

			this.clientSelect = array;
		},

		openType: function openType(openTyp) {
			if (openTyp == true) _util2.default.restfullCall('/rest-ful/v3.0/resourcetype', null, 'get', this.typeData);
		},

		typeData: function typeData(obj) {
			var array = new Array();
			for (var i = 0; i < obj.data.data.length; i++) {
				array.push({
					type: obj.data.data[i].type,
					name: obj.data.data[i].name
				});
			}

			this.typeSelect = array;
		},

		openServer: function openServer(openSer11) {
			if (openSer11 == true) _util2.default.restfullCall('/rest-ful/v3.0/mediaservers', null, 'get', this.serverData);
		},

		serverData: function serverData(obj) {
			var array = new Array();
			for (var i = 0; i < obj.data.data.length; i++) {
				array.push({
					id: obj.data.data[i].id,
					name: obj.data.data[i].name
				});
			}

			this.serverSelect = array;
		},

		startTime: function startTime(start) {
			this.query.starttime = start;
		},

		endTime: function endTime(end) {
			this.query.endtime = end;
		},

		openStatus: function openStatus(open) {
			if (open == true) this.statusSelect = [{
				code: 1,
				name: '成功'
			}, {
				code: -1,
				name: '失败'
			}];
		},

		openDevice: function openDevice(open) {
			if (open == true) _util2.default.restfullCall('/rest-ful/v3.0/devicetype', null, 'get', this.deviceData);
		},

		deviceData: function deviceData(obj) {
			var array = new Array();
			for (var i = 0; i < obj.data.data.length; i++) {
				array.push({
					type: obj.data.data[i].type,
					name: obj.data.data[i].name
				});
			}

			this.deviceTypeSelect = array;
		},

		openPool: function openPool(open) {
			if (open == true) _util2.default.restfullCall('/rest-ful/v3.0/volpools', null, 'get', this.poolData);
		},

		poolData: function poolData(obj) {
			var array = new Array();
			for (var i = 0; i < obj.data.data.length; i++) {
				array.push({
					id: obj.data.data[i].id,
					name: obj.data.data[i].name
				});
			}

			this.poolQuery.pool = array[0].id;
			this.poolSelect = array;
		},

		changesPool: function changesPool(id) {
			this.poolQuery1.pool = id;
			this.generatePool();
		},

		openPoolStatus: function openPoolStatus(open) {
			if (open == true) this.poolStatusSelect = [{
				code: '-1',
				name: '全部'
			}, {
				code: '0',
				name: '正常'
			}, {
				code: '2',
				name: '错误'
			}, {
				code: '1',
				name: '已满'
			}];
		},

		changesPoolStatus: function changesPoolStatus(datas) {
			this.poolQuery1.status = datas;

			this.generatePool();
		},
		rowRun: function rowRun(row, index) {
			if (row.result === '失败') {
				return 'error';
			}
		},
		rowDevice: function rowDevice(row, index) {
			if (row.result === '离线') {
				return 'error';
			}
		}
	}
};

/***/ }),

/***/ 3804:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3805);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("9ef67650", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-065f35da\",\"scoped\":false,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./mediumReport.vue", function() {
     var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-065f35da\",\"scoped\":false,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./mediumReport.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3805:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.restore1{border:1px dashed #fb784d;padding:20px 20px 0;border-radius:5px;height:130px;margin-bottom:20px;min-width:1000px;background-color:#f3f3f3\n}", ""]);

// exports


/***/ }),

/***/ 3806:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3807);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("296ca45f", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-065f35da\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/less-loader/dist/cjs.js!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=1!./mediumReport.vue", function() {
     var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-065f35da\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/less-loader/dist/cjs.js!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=1!./mediumReport.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3807:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.run-top[data-v-065f35da]{margin-top:10px;margin-bottom:10px;padding:10px;height:auto;background:#f9f9f9;margin-bottom:0;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between\n}\n.run-top>h3[data-v-065f35da]{display:block;width:100px;padding:5px;text-align:center;left:20px;background:#fff\n}\n.run-row[data-v-065f35da],.run-top>h3[data-v-065f35da]{position:relative;top:-15px\n}\n.run-row[data-v-065f35da]{padding:0\n}\n.ivu-table .error td[data-v-065f35da]{background-color:#c95032\n}\n.pageBox[data-v-065f35da]{position:relative;width:100%;text-align:center;margin-top:7px\n}\n.pageBox .button[data-v-065f35da]{position:absolute;left:10px\n}\n.pageBox .buttonEx[data-v-065f35da]{position:absolute;left:130px\n}\n.sys-page[data-v-065f35da]{bottom:2px;margin-bottom:10px\n}\n.frame[data-v-065f35da]{padding:20px 10px 10px;border:1px solid #ededed;border-radius:5px;margin-bottom:20px;margin-top:20px;position:relative\n}\n.frame .titles[data-v-065f35da]{position:absolute;left:30px;top:-11px;background-color:#fff;padding:0 6px;font-size:0.875rem\n}\n.frame .blanks[data-v-065f35da]{margin-bottom:10px\n}\n[data-v-065f35da] .is-active .el-radio-button__inner{background:#fe6902;border-color:#fe6902;-webkit-box-shadow:-1px 0 0 0 #fe6902;box-shadow:-1px 0 0 0 #fe6902\n}\n[data-v-065f35da] .is-active .el-radio-button__inner:hover{color:#fff\n}\n.top-box[data-v-065f35da]{-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;padding:10px;padding:.625rem;margin-bottom:10px;border-bottom:1px solid #e1e2e8\n}\n.top-box[data-v-065f35da],.top-right-box[data-v-065f35da]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center\n}\n.top-right-box[data-v-065f35da]{-webkit-box-pack:start;-ms-flex-pack:start;justify-content:flex-start\n}\n.mar-box[data-v-065f35da]{margin:15px\n}\n.search-input[data-v-065f35da]{margin-left:5px\n}\n[data-v-065f35da] .el-form-item{margin-bottom:0\n}", ""]);

// exports


/***/ }),

/***/ 3808:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", [_c("Row", [_c("Col", { staticClass: "breadstyle", attrs: { span: "24" } }, [_c("Breadcrumb", [_c("BreadcrumbItem", {
    staticClass: "breadlist",
    staticStyle: { display: "flex", "align-items": "center" }
  }, [_c("img", {
    staticStyle: {
      width: "1.2rem",
      height: "1.2rem",
      "margin-right": "0.3rem"
    },
    attrs: { src: __webpack_require__(318) }
  }), _vm._v(" "), _c("span", [_vm._v("报表分析 / 运行介质")])])], 1), _vm._v(" "), _c("div")], 1)], 1), _vm._v(" "), _c("el-card", {
    staticClass: "marBox",
    staticStyle: { margin: "15px", height: "calc(100vh - 145px)" }
  }, [_vm.showBox ? _c("div", [_c("div", {
    staticClass: "row",
    staticStyle: {
      height: "calc(100vh - 215px)",
      overflow: "auto"
    }
  }, [_c("div", { staticClass: "top-box" }, [_c("div", {
    staticClass: "leftbox",
    staticStyle: {
      display: "flex",
      "justify-content": "flex-start"
    }
  }, [_c("p", { staticStyle: { "margin-right": "15px" } }, [_c("span", [_vm._v("介质池")]), _vm._v(" "), _c("el-select", {
    attrs: {
      clearable: "",
      filterable: "",
      size: "small"
    },
    on: { change: _vm.changesPool },
    model: {
      value: _vm.poolQuery1.pool,
      callback: function callback($$v) {
        _vm.$set(_vm.poolQuery1, "pool", $$v);
      },
      expression: "poolQuery1.pool"
    }
  }, _vm._l(_vm.poolSelect, function (item) {
    return _c("el-option", {
      key: item.id,
      attrs: {
        label: item.name,
        value: item.id
      }
    }, [_vm._v("\n\t\t\t\t\t\t\t\t\t\t" + _vm._s(item.name) + "\n\t\t\t\t\t\t\t\t\t")]);
  }), 1)], 1)]), _vm._v(" "), _c("div", { staticClass: "top-right-box" }, [_c("el-radio-group", {
    attrs: { size: "small" },
    model: {
      value: _vm.poolQuery1.status,
      callback: function callback($$v) {
        _vm.$set(_vm.poolQuery1, "status", $$v);
      },
      expression: "poolQuery1.status"
    }
  }, _vm._l(_vm.poolStatusSelect, function (item) {
    return _c("el-radio-button", { key: item.code, attrs: { label: item.code } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1)]), _vm._v(" "), _c("Table", {
    attrs: {
      data: _vm.medium,
      columns: _vm.mediumReport,
      "row-class-name": _vm.tishi,
      height: _vm.tableHeight,
      "no-data-text": "<div class='no-img-url-text'><img class='noImgUrl' src=" + _vm.noImgUrl + " /><div class='text'>暂无数据</div><div>"
    },
    on: { "on-row-click": _vm.onRowClick }
  })], 1), _vm._v(" "), _c("div", { staticClass: "fy-page" }, [_c("el-pagination", {
    attrs: {
      background: "",
      "current-page": _vm.curentPage,
      "page-sizes": [10, 20, 30, 40],
      "page-size": 10,
      layout: "total, sizes, prev, pager, next, jumper",
      total: _vm.poolMaxPange
    },
    on: {
      "size-change": _vm.medhandleSizeChange,
      "current-change": _vm.changePagev
    }
  })], 1)]) : _vm._e()])], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-065f35da", esExports);
  }
}

/***/ }),

/***/ 591:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_mediumReport_vue__ = __webpack_require__(2834);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_mediumReport_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_mediumReport_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_mediumReport_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_mediumReport_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_065f35da_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_mediumReport_vue__ = __webpack_require__(3808);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_065f35da_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_mediumReport_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_065f35da_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_mediumReport_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3804)
  __webpack_require__(3806)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = "data-v-065f35da"
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_mediumReport_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_065f35da_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_mediumReport_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/report/mediumReport.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-065f35da", Component.options)
  } else {
    hotAPI.reload("data-v-065f35da", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ })

});