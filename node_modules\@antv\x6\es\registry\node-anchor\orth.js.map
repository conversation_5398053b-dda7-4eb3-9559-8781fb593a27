{"version": 3, "file": "orth.js", "sourceRoot": "", "sources": ["../../../src/registry/node-anchor/orth.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAA;AACzC,OAAO,EAAkB,OAAO,EAAE,MAAM,QAAQ,CAAA;AAOhD,MAAM,UAAU,GACd,UAAU,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO;IACvC,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;IACnD,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA;IAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAA;IACjC,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAA;IAEzC,IAAI,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;IAC7B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;QAC7B,OAAO,GAAG,CAAC,CAAA;KACZ;IAED,IACE,OAAO,CAAC,CAAC,GAAG,OAAO,IAAI,QAAQ,CAAC,CAAC;QACjC,QAAQ,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,GAAG,OAAO,EACrC;QACA,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAA;QAChC,MAAM,CAAC,CAAC;YACN,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,GAAG;gBAC1B,CAAC,CAAC,CAAC;gBACH,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA;QAC7C,MAAM,CAAC,CAAC,IAAI,EAAE,CAAA;KACf;SAAM,IACL,OAAO,CAAC,CAAC,GAAG,OAAO,IAAI,QAAQ,CAAC,CAAC;QACjC,QAAQ,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,GAAG,OAAO,EACrC;QACA,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAA;QAChC,MAAM,CAAC,CAAC;YACN,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA;QACvE,MAAM,CAAC,CAAC,IAAI,EAAE,CAAA;KACf;IAED,OAAO,MAAM,CAAA;AACf,CAAC,CAAA;AAEH;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,IAAI,GAAG,OAAO,CAGzB,UAAU,CAAC,CAAA"}