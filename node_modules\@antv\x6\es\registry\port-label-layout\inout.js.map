{"version": 3, "file": "inout.js", "sourceRoot": "", "sources": ["../../../src/registry/port-label-layout/inout.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAA;AAMjC,MAAM,CAAC,MAAM,OAAO,GAA0C,CAC5D,YAAY,EACZ,QAAQ,EACR,IAAI,EACJ,EAAE,CAAC,aAAa,CAAC,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;AAEvD,MAAM,CAAC,MAAM,eAAe,GAA0C,CACpE,YAAY,EACZ,QAAQ,EACR,IAAI,EACJ,EAAE,CAAC,aAAa,CAAC,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AAEtD,MAAM,CAAC,MAAM,MAAM,GAA0C,CAC3D,YAAY,EACZ,QAAQ,EACR,IAAI,EACJ,EAAE,CAAC,YAAY,CAAC,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;AAEtD,MAAM,CAAC,MAAM,cAAc,GAA0C,CACnE,YAAY,EACZ,QAAQ,EACR,IAAI,EACJ,EAAE,CAAC,YAAY,CAAC,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AAErD,SAAS,aAAa,CACpB,YAAmB,EACnB,QAAmB,EACnB,UAAmB,EACnB,IAAe;IAEf,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAA;IACrD,MAAM,KAAK,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;IACtD,MAAM,UAAU,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAA;IAE1C,IAAI,CAAC,CAAA;IACL,IAAI,EAAE,CAAA;IACN,IAAI,EAAE,CAAA;IACN,IAAI,UAAU,CAAA;IACd,IAAI,WAAW,GAAG,CAAC,CAAA;IAEnB,IAAI,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE;QAClD,CAAC,GAAG,MAAM,CAAA;QACV,EAAE,GAAG,MAAM,CAAA;QACX,EAAE,GAAG,CAAC,CAAA;QACN,UAAU,GAAG,OAAO,CAAA;KACrB;SAAM,IAAI,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE;QAChC,CAAC,GAAG,GAAG,CAAA;QACP,EAAE,GAAG,CAAC,CAAA;QACN,EAAE,GAAG,CAAC,MAAM,CAAA;QACZ,IAAI,UAAU,EAAE;YACd,WAAW,GAAG,CAAC,EAAE,CAAA;YACjB,UAAU,GAAG,OAAO,CAAA;SACrB;aAAM;YACL,UAAU,GAAG,QAAQ,CAAA;SACtB;KACF;SAAM,IAAI,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE;QAChC,CAAC,GAAG,MAAM,CAAA;QACV,EAAE,GAAG,CAAC,MAAM,CAAA;QACZ,EAAE,GAAG,CAAC,CAAA;QACN,UAAU,GAAG,KAAK,CAAA;KACnB;SAAM;QACL,CAAC,GAAG,MAAM,CAAA;QACV,EAAE,GAAG,CAAC,CAAA;QACN,EAAE,GAAG,MAAM,CAAA;QACX,IAAI,UAAU,EAAE;YACd,WAAW,GAAG,EAAE,CAAA;YAChB,UAAU,GAAG,OAAO,CAAA;SACrB;aAAM;YACL,UAAU,GAAG,QAAQ,CAAA;SACtB;KACF;IAED,OAAO,QAAQ,CACb;QACE,QAAQ,EAAE;YACR,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACjB,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;SAClB;QACD,KAAK,EAAE,WAAW;QAClB,KAAK,EAAE;YACL,GAAG,EAAE;gBACH,CAAC;gBACD,aAAa,EAAE,UAAU;aAC1B;SACF;KACF,EACD,IAAI,CACL,CAAA;AACH,CAAC;AAED,SAAS,YAAY,CACnB,YAAmB,EACnB,QAAmB,EACnB,UAAmB,EACnB,IAAe;IAEf,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAA;IACrD,MAAM,KAAK,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;IACtD,MAAM,UAAU,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAA;IAE1C,IAAI,CAAC,CAAA;IACL,IAAI,EAAE,CAAA;IACN,IAAI,EAAE,CAAA;IACN,IAAI,UAAU,CAAA;IACd,IAAI,WAAW,GAAG,CAAC,CAAA;IAEnB,IAAI,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE;QAClD,CAAC,GAAG,MAAM,CAAA;QACV,EAAE,GAAG,CAAC,MAAM,CAAA;QACZ,EAAE,GAAG,CAAC,CAAA;QACN,UAAU,GAAG,KAAK,CAAA;KACnB;SAAM,IAAI,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE;QAChC,CAAC,GAAG,MAAM,CAAA;QACV,EAAE,GAAG,CAAC,CAAA;QACN,EAAE,GAAG,MAAM,CAAA;QACX,IAAI,UAAU,EAAE;YACd,WAAW,GAAG,EAAE,CAAA;YAChB,UAAU,GAAG,OAAO,CAAA;SACrB;aAAM;YACL,UAAU,GAAG,QAAQ,CAAA;SACtB;KACF;SAAM,IAAI,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE;QAChC,CAAC,GAAG,MAAM,CAAA;QACV,EAAE,GAAG,MAAM,CAAA;QACX,EAAE,GAAG,CAAC,CAAA;QACN,UAAU,GAAG,OAAO,CAAA;KACrB;SAAM;QACL,CAAC,GAAG,KAAK,CAAA;QACT,EAAE,GAAG,CAAC,CAAA;QACN,EAAE,GAAG,CAAC,MAAM,CAAA;QACZ,IAAI,UAAU,EAAE;YACd,WAAW,GAAG,CAAC,EAAE,CAAA;YACjB,UAAU,GAAG,OAAO,CAAA;SACrB;aAAM;YACL,UAAU,GAAG,QAAQ,CAAA;SACtB;KACF;IAED,OAAO,QAAQ,CACb;QACE,QAAQ,EAAE;YACR,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACjB,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;SAClB;QACD,KAAK,EAAE,WAAW;QAClB,KAAK,EAAE;YACL,GAAG,EAAE;gBACH,CAAC;gBACD,aAAa,EAAE,UAAU;aAC1B;SACF;KACF,EACD,IAAI,CACL,CAAA;AACH,CAAC;AAED,SAAS,aAAa,CAAC,QAAmB;IACxC,MAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAA;IAEnC,MAAM,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAA;IAC9C,MAAM,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,CAAA;IACjD,MAAM,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC,CAAA;IAClD,MAAM,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAA;IAE/C,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAA;AACzB,CAAC"}