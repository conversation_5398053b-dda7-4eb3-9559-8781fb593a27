{"version": 3, "file": "ref.js", "sourceRoot": "", "sources": ["../../../src/registry/attr/ref.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAa,MAAM,mBAAmB,CAAA;AACpE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,EAAE,MAAM,iBAAiB,CAAA;AAG7D,MAAM,CAAC,MAAM,GAAG,GAAoB;AAClC,wDAAwD;AACxD,kEAAkE;CACnE,CAAA;AAED,yEAAyE;AACzE,6FAA6F;AAC7F,+DAA+D;AAE/D,MAAM,CAAC,MAAM,IAAI,GAAoB;IACnC,QAAQ,EAAE,eAAe,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC;CAClD,CAAA;AAED,MAAM,CAAC,MAAM,IAAI,GAAoB;IACnC,QAAQ,EAAE,eAAe,CAAC,GAAG,EAAE,QAAQ,EAAE,QAAQ,CAAC;CACnD,CAAA;AAED,gGAAgG;AAChG,uCAAuC;AAEvC,MAAM,CAAC,MAAM,KAAK,GAAoB;IACpC,QAAQ,EAAE,eAAe,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC;CAClD,CAAA;AAED,MAAM,CAAC,MAAM,KAAK,GAAoB;IACpC,QAAQ,EAAE,eAAe,CAAC,GAAG,EAAE,QAAQ,EAAE,QAAQ,CAAC;CACnD,CAAA;AAED,oFAAoF;AACpF,6BAA6B;AAC7B,mFAAmF;AACnF,4FAA4F;AAC5F,MAAM,CAAC,MAAM,QAAQ,GAAoB;IACvC,GAAG,EAAE,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC;CAClC,CAAA;AAED,MAAM,CAAC,MAAM,SAAS,GAAoB;IACxC,GAAG,EAAE,UAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC;CACpC,CAAA;AAED,MAAM,CAAC,MAAM,KAAK,GAAoB;IACpC,GAAG,EAAE,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC;CAC/B,CAAA;AAED,MAAM,CAAC,MAAM,KAAK,GAAoB;IACpC,GAAG,EAAE,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC;CAChC,CAAA;AAED,MAAM,CAAC,MAAM,aAAa,GAAoB;IAC5C,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAoB,EAAE;QACnC,MAAM,OAAO,GAAG,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;QAC7C,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;QAC/C,OAAO,UAAU,KAAK,EAAE,OAAO;YAC7B,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;YAC/B,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAA;YAC9D,OAAO,WAAW,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;QACnD,CAAC,CAAA;IACH,CAAC,CAAC,CAAC,GAAG,CAAC;CACR,CAAA;AAED,MAAM,CAAC,MAAM,iBAAiB,GAAoB;IAChD,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE;QAClB,IAAI,KAAK,GAAG,UAAU,CAAC,GAAa,CAAC,CAAA;QACrC,MAAM,UAAU,GAAG,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;QAC9C,IAAI,UAAU,EAAE;YACd,KAAK,IAAI,GAAG,CAAA;SACb;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAC9B,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAChE,CAAA;QAED,IAAI,MAAM,CAAA;QACV,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC1B,IAAI,UAAU,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,EAAE;gBAC5C,MAAM,GAAG,KAAK,GAAG,cAAc,CAAA;aAChC;iBAAM;gBACL,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,cAAc,EAAE,CAAC,CAAC,CAAA;aAC7C;SACF;QAED,OAAO,EAAE,CAAC,EAAE,MAAM,EAAsB,CAAA;IAC1C,CAAC;CACF,CAAA;AAED,MAAM,CAAC,MAAM,KAAK,GAAoB;IACpC,GAAG,EAAE,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC;CAC/B,CAAA;AAED,MAAM,CAAC,MAAM,KAAK,GAAoB;IACpC,GAAG,EAAE,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC;CAChC,CAAA;AAED,MAAM,CAAC,MAAM,eAAe,GAAoB;IAC9C,GAAG,EAAE,QAAQ,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;CACrC,CAAA;AAED,MAAM,CAAC,MAAM,cAAc,GAAoB;IAC7C,GAAG,EAAE,QAAQ,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;CACtC,CAAA;AAED,MAAM,CAAC,MAAM,oBAAoB,GAAoB;IACnD,GAAG,EAAE,aAAa,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;CAC1C,CAAA;AAED,MAAM,CAAC,MAAM,mBAAmB,GAAoB;IAClD,GAAG,EAAE,aAAa,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;CAC3C,CAAA;AAED,UAAU;AACV,UAAU;AACV,MAAM,CAAC,MAAM,IAAI,GAAG,aAAa,CAAA;AACjC,MAAM,CAAC,MAAM,IAAI,GAAG,eAAe,CAAA;AACnC,MAAM,CAAC,MAAM,SAAS,GAAG,oBAAoB,CAAA;AAC7C,2DAA2D;AAC3D,uBAAuB;AACvB,MAAM,CAAC,MAAM,KAAK,GAAG,IAAI,CAAA;AACzB,MAAM,CAAC,MAAM,KAAK,GAAG,IAAI,CAAA;AACzB,MAAM,CAAC,MAAM,SAAS,GAAG,QAAQ,CAAA;AACjC,MAAM,CAAC,MAAM,UAAU,GAAG,SAAS,CAAA;AAEnC,QAAQ;AACR,QAAQ;AAER,SAAS,eAAe,CACtB,IAAe,EACf,SAA6B,EAC7B,MAA2B;IAE3B,OAAO,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;QAC1B,IAAI,GAAG,IAAI,IAAI,EAAE;YACf,OAAO,IAAI,CAAA;SACZ;QAED,IAAI,KAAK,GAAG,UAAU,CAAC,GAAa,CAAC,CAAA;QACrC,MAAM,UAAU,GAAG,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;QAC9C,IAAI,UAAU,EAAE;YACd,KAAK,IAAI,GAAG,CAAA;SACb;QAED,IAAI,KAAK,CAAA;QACT,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC1B,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,CAAA;YACjC,IAAI,UAAU,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;gBAC1C,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAA;aACrD;iBAAM;gBACL,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,CAAA;aAChC;SACF;QAED,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAA;QACzB,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,CAAA;QACxB,OAAO,KAAK,CAAA;IACd,CAAC,CAAA;AACH,CAAC;AAED,SAAS,UAAU,CACjB,QAAgB,EAChB,SAA6B;IAE7B,OAAO,UAAU,GAAG,EAAE,EAAE,OAAO,EAAE;QAC/B,IAAI,KAAK,GAAG,UAAU,CAAC,GAAa,CAAC,CAAA;QACrC,MAAM,UAAU,GAAG,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;QAC9C,IAAI,UAAU,EAAE;YACd,KAAK,IAAI,GAAG,CAAA;SACb;QAED,MAAM,KAAK,GAAqB,EAAE,CAAA;QAElC,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC1B,MAAM,SAAS,GACb,UAAU,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;gBACtC,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC;gBAC5B,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAA;YAC7C,KAAK,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAA;SAC5B;QAED,OAAO,KAAK,CAAA;IACd,CAAC,CAAA;AACH,CAAC;AAED,SAAS,YAAY,CACnB,gBAAuD,EACvD,OAAiC;IAEjC,MAAM,SAAS,GAAG,UAAU,CAAA;IAC5B,MAAM,WAAW,GAAG,OAAO,IAAI,OAAO,CAAC,WAAW,CAAA;IAElD,OAAO,UAAU,KAAK,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;QACvC,IAAI,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;QACrC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK,EAAE;YACnC,wCAAwC;YACxC,MAAM,WAAW,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAA;YAC3C,KAAK,GAAG;gBACN,KAAK;gBACL,KAAK,EAAE,WAAW;gBAClB,SAAS,EAAE,WAAW,CAAC,IAAI,EAAE;aAC9B,CAAA;YACD,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAA;SACjC;QAED,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,EAAe,CAAA;QACtD,MAAM,WAAW,GAAG,SAAS,CAAC,SAAS,EAAE,CAAA;QACzC,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,EAAE,CAAA;QAErC,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAA;QACzB,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAA;QAEzB,MAAM,QAAQ,GAAG,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAA;QAC/D,gEAAgE;QAChE,MAAM,EAAE,GAAG,SAAS,CAAC,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAA;QACzE,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAA;QAE3E,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,WAAW,CAAC,CAAA;QAChC,IAAI,WAAW,EAAE;YACf,KAAK,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;SAChD;QAED,OAAO,KAAK,CAAA;IACd,CAAC,CAAA;AACH,CAAC;AAED,6BAA6B;AAC7B,SAAS,QAAQ,CAAC,OAAiC;IACjD,SAAS,eAAe,CAAC,KAAa;QACpC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;IAC1B,CAAC;IAED,MAAM,KAAK,GAAG,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC,CAAA;IAEpD,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;QACrB,MAAM,IAAI,GAAG,KAAK,CAAO,KAAK,EAAE,IAAI,CAAC,CAAA;QACrC,OAAO;YACL,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE;SACpB,CAAA;IACH,CAAC,CAAA;AACH,CAAC;AAED,sDAAsD;AACtD,SAAS,aAAa,CAAC,OAAiC;IACtD,MAAM,KAAK,GAAG,YAAY,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,QAAQ,CAAC,MAAa,CAAC,EAAE,OAAO,CAAC,CAAA;IAC5E,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;QACrB,MAAM,QAAQ,GAAG,KAAK,CAAW,KAAK,EAAE,IAAI,CAAC,CAAA;QAC7C,OAAO;YACL,MAAM,EAAE,QAAQ,CAAC,SAAS,EAAE;SAC7B,CAAA;IACH,CAAC,CAAA;AACH,CAAC"}