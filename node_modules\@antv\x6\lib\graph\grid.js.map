{"version": 3, "file": "grid.js", "sourceRoot": "", "sources": ["../../src/graph/grid.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAA6C;AAC7C,sDAAuC;AACvC,iCAA6B;AAE7B,MAAa,WAAY,SAAQ,WAAI;IAInC,IAAc,IAAI;QAChB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA;IACvB,CAAC;IAED,IAAc,IAAI;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA;IAC1B,CAAC;IAES,IAAI;QACZ,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACtB,CAAC;IAES,cAAc;QACtB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QACzC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IAC/C,CAAC;IAES,aAAa;QACrB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAC1C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IAChD,CAAC;IAES,UAAU,CAAC,OAAgB;QACnC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,OAAO,EAAE;YACjC,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;YAC3B,IAAI,CAAC,MAAM,EAAE,CAAA;SACd;IACH,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA;IACvB,CAAC;IAED,WAAW,CAAC,IAAY;QACtB,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;QAClC,IAAI,CAAC,MAAM,EAAE,CAAA;IACf,CAAC;IAED,IAAI;QACF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;QACrB,IAAI,CAAC,MAAM,EAAE,CAAA;IACf,CAAC;IAED,IAAI;QACF,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;QACtB,IAAI,CAAC,MAAM,EAAE,CAAA;IACf,CAAC;IAED,KAAK;QACH,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,EAAE,CAAA;IACtC,CAAC;IAED,IAAI,CAAC,OAAqC;QACxC,IAAI,CAAC,KAAK,EAAE,CAAA;QACZ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;QACpB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;QACjC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;QACzC,IAAI,CAAC,MAAM,EAAE,CAAA;IACf,CAAC;IAED,MAAM,CACJ,UAEuC,EAAE;QAEzC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA;QAC/B,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACvC,OAAO,IAAI,CAAC,KAAK,EAAE,CAAA;SACpB;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAA;QAC/B,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;QAC/B,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAA;QAE1D,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;YACxC,MAAM,EAAE,GAAG,WAAW,KAAK,EAAE,CAAA;YAC7B,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAA;YACrB,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAA;YAErB,MAAM,EAAE,MAAM,EAAE,MAAM,KAAgB,QAAQ,EAAnB,MAAM,UAAK,QAAQ,EAAxC,oBAA6B,CAAW,CAAA;YAC9C,MAAM,OAAO,iDACR,MAAM,GACN,KAAK,CAAC,KAAK,CAAC,KACf,EAAE;gBACF,EAAE,EACF,EAAE,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,EACd,EAAE,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,EACd,KAAK,EAAE,QAAQ,GAAG,EAAE,EACpB,MAAM,EAAE,QAAQ,GAAG,EAAE,GACtB,CAAA;YAED,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;gBACjB,IAAI,CAAC,GAAG,CACN,EAAE,EACF,kBAAM,CAAC,MAAM,CACX,SAAS,EACT,EAAE,EAAE,EAAE,YAAY,EAAE,gBAAgB,EAAE,EACtC,kBAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAC7B,CAAC,IAAI,CACP,CAAA;aACF;YAED,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YAEhC,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;gBAChC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAY,EAAE,OAAO,CAAC,CAAA;aACtD;YAED,IAAI,CAAC,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,KAAK,CAAA;YAClC,IAAI,CAAC,GAAG,CAAC,EAAE;gBACT,CAAC,IAAI,OAAO,CAAC,KAAK,CAAA;aACnB;YAED,IAAI,CAAC,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,MAAM,CAAA;YACnC,IAAI,CAAC,GAAG,CAAC,EAAE;gBACT,CAAC,IAAI,OAAO,CAAC,MAAM,CAAA;aACpB;YAED,eAAG,CAAC,IAAI,CAAC,WAAW,EAAE;gBACpB,CAAC;gBACD,CAAC;gBACD,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,MAAM,MAAM,GAAG,IAAI,aAAa,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC/D,MAAM,GAAG,GAAG,iCAAiC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAA;QAC5D,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,CAAA;IACvC,CAAC;IAES,WAAW;QACnB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAA;SACpC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAA;IACtB,CAAC;IAES,WAAW,CACnB,OAAqC;QAErC,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,EAAE,CAAA;SACV;QAED,MAAM,IAAI,GAAI,OAAoC,CAAC,IAAI,CAAA;QACvD,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,OAAO;gDAEA,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,GACzB,OAAO,CAAC,IAAI;aAElB,CAAA;SACF;QAED,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QAC9C,IAAI,KAAK,EAAE;YACT,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,EAAE,CAAA;YAC7B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACxB,IAAI,GAAG,CAAC,IAAI,CAAC,CAAA;aACd;YAED,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;gBACzB,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,iCAAM,IAAI,GAAK,IAAI,CAAC,KAAK,CAAC,EAAG,CAAC;gBAC3D,CAAC,CAAC,iCAAM,KAAK,GAAK,IAAI,CAAC,CAAC,CAAC,EAAG,CAAA;SAC/B;QAED,OAAO,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;IAChD,CAAC;IAGD,OAAO;QACL,IAAI,CAAC,aAAa,EAAE,CAAA;QACpB,IAAI,CAAC,KAAK,EAAE,CAAA;IACd,CAAC;CACF;AAJC;IADC,WAAI,CAAC,OAAO,EAAE;0CAId;AApLH,kCAqLC"}