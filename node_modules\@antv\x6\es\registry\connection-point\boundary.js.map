{"version": 3, "file": "boundary.js", "sourceRoot": "", "sources": ["../../../src/registry/connection-point/boundary.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,iBAAiB,CAAA;AAChD,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAW,MAAM,mBAAmB,CAAA;AACrE,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,MAAM,QAAQ,CAAA;AAE9D,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAA;AAejC;;;GAGG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAgD,UACnE,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,OAAO;IAEP,IAAI,IAAI,CAAA;IACR,IAAI,YAAY,CAAA;IAChB,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAA;IACvB,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;IAEjC,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;QAChC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;KAC9B;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;QAClC,IAAI,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;KAC7C;SAAM;QACL,IAAI,GAAG,aAAa,CAAC,MAAM,CAAC,CAAA;KAC7B;IAED,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE;QACnC,IAAI,IAAI,KAAK,MAAM,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE;YACxD,OAAO,MAAM,CAAA;SACd;QACD,IAAI,GAAG,MAAM,CAAA;KACd;IAED,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;IAC/C,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA;IAClD,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAA;IACtD,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAA;IAChD,MAAM,YAAY,GAAG,eAAe;SACjC,QAAQ,CAAC,YAAY,CAAC;SACtB,QAAQ,CAAC,YAAY,CAAC,CAAA;IACzB,MAAM,WAAW,GAAG,YAAY,CAAC,OAAO,EAAE,CAAA;IAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,WAAW,CAAC,CAAA;IACvD,MAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;IACxC,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAkB,CAAA;IAEzD,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE;QAC/B,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,EAAE;YAC1B,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,IAAI,EAAE,CAAA;SACnC;QACD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;QAChC,IAAI,SAAS,IAAI,IAAI,IAAI,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;YAC1D,OAAO,MAAM,CAAA;SACd;KACF;IAED,IAAI,OAAO,CAAC,WAAW,KAAK,IAAI,EAAE;QAChC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;KACzB;IAED,yCAAyC;IACzC,IAAI,WAAW,CAAA;IACf,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE;QAC3B,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,CAAC,CAAA;QACxC,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,EAAE;YACpC,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC,sBAAsB,CAAC;gBAC3D,SAAS;aACV,CAAC,CAAA;SACH;QACD,WAAW,GAAG;YACZ,SAAS;YACT,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;SAC9C,CAAA;QAED,YAAY,GAAG,SAAS,CAAC,SAAS,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;KAC5D;SAAM;QACL,YAAY,GAAG,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;KAC/C;IAED,IAAI,YAAY,EAAE;QAChB,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YAC/B,YAAY,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;SAC9C;KACF;SAAM,IAAI,OAAO,CAAC,MAAM,KAAK,IAAI,EAAE;QAClC,kDAAkD;QAClD,IAAI,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE;YACrC,YAAY,GAAG,UAAU,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAA;SAC3D;aAAM,IAAI,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;YACxC,YAAY,GAAG,UAAU,CAAC,mCAAmC,CAAC,QAAQ,CAAC,CAAA;SACxE;aAAM;YACL,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAA;SAC9D;KACF;IAED,MAAM,EAAE,GAAG,YAAY;QACrB,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,YAAY,CAAC;QACjD,CAAC,CAAC,MAAM,CAAA;IACV,IAAI,QAAQ,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAA;IAClC,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;QAC7B,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;YAChC,QAAQ,qBAAQ,QAAQ,CAAE,CAAA;YAC1B,IAAI,QAAQ,CAAC,CAAC,IAAI,IAAI,EAAE;gBACtB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAA;aACf;YACD,QAAQ,CAAC,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;SACvC;aAAM;YACL,QAAQ,IAAI,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;SACrC;KACF;IAED,OAAO,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;AACzC,CAAC,CAAA"}