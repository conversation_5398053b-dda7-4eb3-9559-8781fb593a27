{"version": 3, "file": "bbox.js", "sourceRoot": "", "sources": ["../../../src/registry/node-anchor/bbox.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAA;AAc3C,MAAM,CAAC,MAAM,MAAM,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAA;AAChD,MAAM,CAAC,MAAM,GAAG,GAAG,gBAAgB,CAAC,WAAW,CAAC,CAAA;AAChD,MAAM,CAAC,MAAM,MAAM,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAA;AACtD,MAAM,CAAC,MAAM,IAAI,GAAG,gBAAgB,CAAC,YAAY,CAAC,CAAA;AAClD,MAAM,CAAC,MAAM,KAAK,GAAG,gBAAgB,CAAC,aAAa,CAAC,CAAA;AACpD,MAAM,CAAC,MAAM,OAAO,GAAG,gBAAgB,CAAC,SAAS,CAAC,CAAA;AAClD,MAAM,CAAC,MAAM,QAAQ,GAAG,gBAAgB,CAAC,UAAU,CAAC,CAAA;AACpD,MAAM,CAAC,MAAM,UAAU,GAAG,gBAAgB,CAAC,YAAY,CAAC,CAAA;AACxD,MAAM,CAAC,MAAM,WAAW,GAAG,gBAAgB,CAAC,aAAa,CAAC,CAAA;AAE1D,SAAS,gBAAgB,CACvB,MASiB;IAEjB,OAAO,UAAU,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,UAA+B,EAAE;QACnE,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM;YACzB,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC;YACxC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAA;QAE3B,MAAM,CAAC,CAAC,IAAI,SAAS,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;QACjE,MAAM,CAAC,CAAC,IAAI,SAAS,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;QAElE,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,OAAO,OAAO,CAAC,MAAM;YACnB,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,SAAS,EAAE,CAAC;YAC7D,CAAC,CAAC,MAAM,CAAA;IACZ,CAAC,CAAA;AACH,CAAC"}