
 export default class  SoftKey3W
{
    static  Socket_UK;
    static url;
    static LastError
    constructor() {

        var isIE11 = navigator.userAgent.indexOf('Trident') > -1 && navigator.userAgent.indexOf("rv:11.0") > -1;
        var isEDGE= navigator.userAgent.indexOf("Edge") > -1;
        var u = document.URL;
        if (u.substring(0, 5) == "https") {
            if(isIE11 || isEDGE)
    	    {
    		 if(isIE11)this.url = "wss://127.0.0.1:4006/xxx"; else this.url = "ws://127.0.0.1:4006/xxx";
    	    }
            else
            {
                    this.url = "ws://localhost:4006/xxx";
                }
            } else {
                this.url = "ws://127.0.0.1:4006/xxx";
            }

        if (typeof MozWebSocket != "undefined") {
            this.Socket_UK = new MozWebSocket(this.url,"usbkey-protocol");
        } else {
            this.Socket_UK = new WebSocket(this.url,"usbkey-protocol");
        }
        this.Socket_UK.onerror = (event) => {
            alert('未能连接服务程序，请确定服务程序是否安装。');
        };
        SoftKey3W.LastError=0
    }

   _FindPort(UK,start)
    {
        var msg =
        {
            FunName: "FindPort",
            start: start
        };
        UK.send(JSON.stringify(msg));
   }

    _FindPort_2(UK,start, in_data , verf_data)
    {
         var msg =
        {
            FunName: "FindPort_2",
            start: start,
            in_data: in_data,
            verf_data:verf_data
        };
        UK.send(JSON.stringify(msg));
    }

    _FindPort_3(UK,start,in_data,verf_data)
    {
        var msg =
        {
            FunName: "FindPort_3",
            start: start,
            in_data: in_data,
            verf_data:verf_data
        };
        UK.send(JSON.stringify(msg));
    }

    _GetVersion(UK,Path)
    {
        var msg =
        {
            FunName: "GetVersion",
            Path: Path
        };
        UK.send(JSON.stringify(msg));
    }

    _GetVersionEx(UK,Path)
    {
        var msg =
        {
            FunName: "GetVersionEx",
            Path: Path
        };
        UK.send(JSON.stringify(msg));
    }

    _GetID_1(UK,Path)
    {
        var msg =
        {
            FunName: "GetID_1",
            Path: Path
        };
        UK.send(JSON.stringify(msg));
    }

    _GetID_2(UK,Path)
    {
        var msg =
        {
            FunName: "GetID_2",
            Path: Path
        };
        UK.send(JSON.stringify(msg));
    }


    _sRead(UK,Path)
    {
        var msg =
        {
            FunName: "sRead",
            Path: Path
        };
        UK.send(JSON.stringify(msg));
    }

    _sWrite(UK,InData, Path)
    {
         var msg =
        {
            FunName: "sWrite",
            InData: InData,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _sWrite_2(UK,InData, Path)
    {
        var msg =
        {
            FunName: "sWrite_2",
            InData: InData,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _sWrite_2Ex(UK,InData,Path)
    {
        var msg =
        {
            FunName: "sWrite_2Ex",
            InData: InData,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _sWriteEx(UK,InData,Path)
    {
        var msg =
        {
            FunName: "sWriteEx",
            InData: InData,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _sWriteEx_New(UK,InData,Path)
    {
        var msg =
        {
            FunName: "sWriteEx_New",
            InData: InData,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _sWrite_2Ex_New(UK,InData,Path)
    {
        var msg =
        {
            FunName: "sWrite_2Ex_New",
            InData: InData,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }


     _YRead(Address,HKey,LKey,Path)
    {
        var msg =
        {
            FunName: "YRead",
            Address:Address,
            HKey:HKey,
            LKey:LKey,
            Path:Path
        };
        Socket_UK.send(JSON.stringify(msg));
    }

    _YWrite(InData,Address,HKey,LKey,Path)
    {
        var msg =
        {
            FunName: "YWrite",
            InData:InData,
            Address:Address,
            HKey:HKey,
            LKey:LKey,
            Path:Path
        };
        Socket_UK.send(JSON.stringify(msg));
    }


    static _SetBuf(UK,InData,pos)
    {
        var msg =
        {
            FunName: "SetBuf",
            InData: InData,
            pos:pos
        };
        UK.send(JSON.stringify(msg));
    }

    static _GetBuf(UK,pos)
    {
        var msg =
        {
            FunName: "GetBuf",
            pos: pos
        };
        UK.send(JSON.stringify(msg));
    }

    static _YReadEx(UK,Address,len, HKey,LKey,Path)
    {
        var msg =
        {
            FunName: "YReadEx",
            Address:Address,
            len:len,
            HKey:HKey,
            LKey:LKey,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    static _YWriteEx(UK,Address,len,HKey,LKey,Path)
    {
        var msg =
        {
            FunName: "YWriteEx",
            Address:Address,
            len:len,
            HKey:HKey,
            LKey:LKey,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _YReadString(UK,Address,len,HKey,LKey,Path)
    {
        var msg =
        {
            FunName: "YReadString",
            Address:Address,
            len:len,
            HKey:HKey,
            LKey:LKey,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _YWriteString(UK,InString,Address,HKey,LKey,Path)
    {
        var msg =
        {
            FunName: "YWriteString",
            InString:InString,
            Address:Address,
            HKey:HKey,
            LKey:LKey,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _SetWritePassword(UK,W_Hkey,W_Lkey,new_Hkey,new_Lkey,Path)
    {
        var msg =
        {
            FunName: "SetWritePassword",
            W_Hkey:W_Hkey,
            W_Lkey:W_Lkey,
            new_Hkey:new_Hkey,
            new_Lkey:new_Lkey,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _SetReadPassword(UK,W_Hkey,W_Lkey,new_Hkey,new_Lkey,Path)
    {
        var msg =
        {
            FunName: "SetReadPassword",
            W_Hkey:W_Hkey,
            W_Lkey:W_Lkey,
            new_Hkey:new_Hkey,
            new_Lkey:new_Lkey,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }


    _DecString(UK,InString,Key)
    {
        var msg =
        {
            FunName: "DecString",
            InString:InString,
            Key:Key
        };
        UK.send(JSON.stringify(msg));
    }

    _EncString(UK,InString,Path)
    {
        var msg =
        {
            FunName: "EncString",
            InString:InString,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _EncString_New(UK,InString,Path)
    {
        var msg =
        {
            FunName: "EncString_New",
            InString:InString,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _Cal(UK,Path)
    {
        var msg =
        {
            FunName: "Cal",
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _Cal_New(UK,Path)
    {
        var msg =
        {
            FunName: "Cal_New",
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _GetProduceDate(UK,Path)
    {
        var msg =
        {
            FunName: "GetProduceDate",
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _SetID(UK,Seed,Path)
    {
        var msg =
        {
            FunName: "SetID",
            Seed:Seed,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _SetCal_2(UK,Key,Path)
    {
        var msg =
        {
            FunName: "SetCal_2",
            Key:Key,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _SetCal_New(UK,Key,Path)
    {
        var msg =
        {
            FunName: "SetCal_New",
            Key:Key,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    static _SetEncBuf(UK,InData,pos)
    {
        var msg =
        {
            FunName: "SetEncBuf",
            InData:InData,
            pos: pos
        };
        UK.send(JSON.stringify(msg));
    }

    static _GetEncBuf(UK,pos)
    {
        var msg =
        {
            FunName: "GetEncBuf",
            pos: pos
        };
        UK.send(JSON.stringify(msg));
    }


    _ReSet(UK,Path)
    {
        var msg =
        {
            FunName: "ReSet",
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _MacAddr(UK)
    {
        var msg =
        {
            FunName: "MacAddr"
        };
        UK.send(JSON.stringify(msg));
    }


    _GetChipID(UK,Path)
    {
        var msg =
        {
            FunName: "GetChipID",
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    static _StarGenKeyPair(UK,Path)
    {
        var msg =
        {
            FunName: "StarGenKeyPair",
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    static _GenPubKeyY(UK)
    {
        var msg =
        {
            FunName: "GenPubKeyY"
        };
        UK.send(JSON.stringify(msg));
    }

    static _GenPubKeyX(UK)
    {
        var msg =
        {
            FunName: "GenPubKeyX"
        };
        UK.send(JSON.stringify(msg));
    }

    static  _GenPriKey(UK)
    {
        var msg =
        {
            FunName: "GenPriKey"
        };
        UK.send(JSON.stringify(msg));
    }

    _GetPubKeyY(UK,Path)
    {
        var msg =
        {
            FunName: "GetPubKeyY",
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _GetPubKeyX(UK,Path)
    {
        var msg =
        {
            FunName: "GetPubKeyX",
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _GetSm2UserName(UK,Path)
    {
        var msg =
        {
            FunName: "GetSm2UserName",
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _Set_SM2_KeyPair(UK,PriKey,PubKeyX,PubKeyY,sm2UserName,Path )
    {
        var msg =
        {
            FunName: "Set_SM2_KeyPair",
            PriKey:PriKey,
            PubKeyX:PubKeyX,
            PubKeyY:PubKeyY,
            sm2UserName:sm2UserName,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _YtSign(UK,SignMsg,Pin,Path)
    {
        var msg =
        {
            FunName: "YtSign",
            SignMsg:SignMsg,
            Pin:Pin,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _YtSign_2(UK,SignMsg,Pin,Path)
    {
        var msg =
        {
            FunName: "YtSign_2",
            SignMsg:SignMsg,
            Pin:Pin,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _YtVerfiy(UK,id,SignMsg,PubKeyX, PubKeyY,VerfiySign,Path)
    {
        var msg =
        {
            FunName: "YtVerfiy",
            id:id,
            SignMsg:SignMsg,
            PubKeyX:PubKeyX,
            PubKeyY:PubKeyY,
            VerfiySign:VerfiySign,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _SM2_DecString(UK,InString,Pin,Path)
    {
        var msg =
        {
            FunName: "SM2_DecString",
            InString:InString,
            Pin:Pin,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _SM2_EncString(UK,InString,Path)
    {
        var msg =
        {
            FunName: "SM2_EncString",
            InString:InString,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

    _YtSetPin(UK,OldPin,NewPin,Path)
    {
        var msg =
        {
            FunName: "YtSetPin",
            OldPin:OldPin,
            NewPin:NewPin,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

	_FindU(UK,start)
	{
        var msg =
        {
            FunName: "FindU",
            start: start
        };
        UK.send(JSON.stringify(msg));
    }

	_FindU_2(UK,start,in_data,verf_data)
	{
        var msg =
        {
            FunName: "FindU_2",
            start: start,
            in_data: in_data,
            verf_data:verf_data
        };
        UK.send(JSON.stringify(msg));
    }

	_FindU_3(UK,start,in_data,verf_data)
	{
        var msg =
        {
            FunName: "FindU_3",
            start: start,
            in_data: in_data,
            verf_data:verf_data
        };
        UK.send(JSON.stringify(msg));
    }

	_IsUReadOnly(UK,Path)
	{
        var msg =
        {
            FunName: "IsUReadOnly",
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

	_SetUReadOnly(UK,Path)
	{
        var msg =
        {
            FunName: "SetUReadOnly",
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

	_SetHidOnly(UK,IsHidOnly,Path)
	{
        var msg =
        {
            FunName: "SetHidOnly",
            IsHidOnly:IsHidOnly,
            Path:Path
        };
        UK.send(JSON.stringify(msg));
    }

   static ResetOrder (UK)
    {
		 var msg =
        {
            FunName: "ResetOrder"
        };
         UK.send(JSON.stringify(msg));
	 }

	ContinueOrder   (UK)
    {
		 var msg =
        {
            FunName: "ContinueOrder"
        };
         UK.send(JSON.stringify(msg));
     }


     _ComputerName (UK)
    {
        var msg =
        {
            FunName: "ComputerName"
        };
        UK.send(JSON.stringify(msg));
    };



	 SendCmdAndWait  (IsReturnErr,fun,param1,param2,param3,param4,param5,param6,param7,param8,param9,param10)
{
 var UK;

   return new Promise((resolve, reject) => {

    if (typeof MozWebSocket != "undefined") {
	   UK = new MozWebSocket(this.url,"usbkey-protocol");
	} else {
		UK = new WebSocket(this.url,"usbkey-protocol");
	}

	 try
	 {
	      UK.onopen = function() {
            SoftKey3W.ResetOrder(UK);//
	    }

	    UK.onmessage =function got_packet(Msg)
	    {
	        var UK_Data = JSON.parse(Msg.data);
	        var return_value;
	        if(UK_Data.type!="Process")return ;//
	        switch(UK_Data.order)
	        {
	            case 0:
	                {
	                  fun(UK,param1,param2,param3,param4,param5,param6,param7,param8,param9,param10);
	                }
	                break; //!!!!!
	            case 1:
	                {
	                    SoftKey3W.LastError=UK_Data.LastError;
	                    return_value=UK_Data.return_value;
	                    if( SoftKey3W.LastError!=0){
	                       if(IsReturnErr)return_value=SoftKey3W.LastError;
	                     }
	                     //
						 UK.close();

						 resolve(return_value);
	                }
	                break;
            }
	    }
	    UK.onclose = function(){

	    }
        UK.onerror = (event) => {
            alert('未能连接服务程序，请确定服务程序是否安装。');
        };
	  }
	 catch(e)
	  {
				alert(e.name + ": " + e.message);
				 resolve(false);
	  }
	})
}

    GetLastError  ()
    {
        return SoftKey3W.LastError;
    }

    FindPort  (start)
    {
        return this.SendCmdAndWait(false,this._FindPort,start);
    }

    FindPort_2 ( start,in_data,  verf_data )
    {
       return this.SendCmdAndWait(false,this._FindPort_2,start,in_data,  verf_data);
    };

    GetVersionEx (KeyPath)
    {
         return this.SendCmdAndWait(false,this._GetVersionEx,KeyPath);
    };

    GetVersion (KeyPath)
    {
        return this.SendCmdAndWait(false,this._GetVersion,KeyPath);
    };



    GetID_1 (KeyPath)
    {
        return this.SendCmdAndWait(false,this._GetID_1,KeyPath);
    }

    GetID_2 (KeyPath)
    {
       return this.SendCmdAndWait(false,this._GetID_2,KeyPath);

    }

    GetChipID (KeyPath)
    {
        return this.SendCmdAndWait(false,this._GetChipID,KeyPath);

    };

    SetWritePassword ( W_HKey,  W_LKey,  new_HKey,  new_LKey,KeyPath)
    {
        return this.SendCmdAndWait(true,this._SetWritePassword, W_HKey,  W_LKey,  new_HKey,  new_LKey,KeyPath);
    }

    SetReadPassword ( W_HKey,  W_LKey,  new_HKey,  new_LKey,KeyPath)
    {
        return this.SendCmdAndWait(true,this._SetReadPassword,W_HKey,  W_LKey,  new_HKey,  new_LKey,KeyPath);
    }


    SetCal_2 ( Key,KeyPath)
    {
        return this.SendCmdAndWait(true,this._SetCal_2,Key,KeyPath);
    }

    SetCal_New (Key,KeyPath)
    {
        return this.SendCmdAndWait(true,this._SetCal_New,Key,KeyPath);
    }


    EncString ( InString,KeyPath)
    {
        return this.SendCmdAndWait(false,this._EncString,InString,KeyPath);
    }

    EncString_New ( InString,KeyPath)
    {
        return this.SendCmdAndWait(false,this._EncString_New, InString,KeyPath);
    }



    Cal (Inbuf,KeyPath)
    {
        return SubCal(_Cal,Inbuf,KeyPath);
    }

    Cal_New (Inbuf,KeyPath)
    {
       return SubCal(_Cal_New,Inbuf,KeyPath)
    }



    sWriteEx ( in_data ,KeyPath)
    {
        return this.SendCmdAndWait(false,this._sWriteEx,in_data ,KeyPath);
    }

    sWrite_2Ex ( in_data ,KeyPath)
    {
        return this.SendCmdAndWait(false,this._sWrite_2Ex,in_data ,KeyPath);
    }

    sWriteEx_New ( in_data ,KeyPath)
    {
        return this.SendCmdAndWait(false,this._sWriteEx_New,in_data ,KeyPath);
    }

    sWrite_2Ex_New ( in_data ,KeyPath)
    {
        return this.SendCmdAndWait(false,this._sWrite_2Ex_New,in_data ,KeyPath);
    }

    sWrite ( in_data ,KeyPath)
    {
        return this.SendCmdAndWait(true,this._sWrite,in_data ,KeyPath);
    }

    sWrite_2 ( in_data ,KeyPath)
    {
        return this.SendCmdAndWait(true,this._sWrite_2,in_data ,KeyPath);
    }

    sRead (KeyPath)
    {
        return this.SendCmdAndWait(false,this._sRead,KeyPath);
    }


     YWriteEx  (indata,   address,  nlen,  HKey,  LKey,KeyPath)
    {
        var UK;

       return new Promise((resolve, reject) => {

        if (typeof MozWebSocket != "undefined") {
	       UK = new MozWebSocket(this.url,"usbkey-protocol");
	    } else {
		    UK = new WebSocket(this.url,"usbkey-protocol");
	    }

	     try
	     {
	        var i=0,order;
	          UK.onopen = function() {
                SoftKey3W.ResetOrder(UK);//

	        }

	        UK.onmessage =function got_packet(Msg)
	        {
	            var UK_Data = JSON.parse(Msg.data);
	            var return_value;
	            if(UK_Data.type!="Process")return ;//
	            if(UK_Data.order<nlen)
	            {
                    SoftKey3W._SetBuf(UK,indata[UK_Data.order],UK_Data.order);
	              return;
	            }
	           else{
	              order=UK_Data.order-nlen;
	           }

	            switch(order)
	            {
	                case 0:
	                    {
	                       SoftKey3W.LastError=UK_Data.LastError;
	                      if( SoftKey3W.LastError!=0){ UK.close();resolve(UK_Data.return_value);return ;  }
	                      SoftKey3W._YWriteEx(UK,address,  nlen,  HKey,  LKey,KeyPath);
	                    }
	                    break; //!!!!!
	                case 1:
	                    {
	                        SoftKey3W.LastError=UK_Data.LastError;
	                        return_value=UK_Data.return_value;
	                        if( SoftKey3W.LastError!=0){
	                           return_value=SoftKey3W.LastError;
	                         }

						     UK.close();

						     resolve(return_value);
	                    }
	                    break;
                }
	        }
	        UK.onclose = function(){

	        }
            UK.onerror = (event) => {
                alert('未能连接服务程序，请确定服务程序是否安装。');
            };
	      }
	     catch(e)
	      {
				    alert(e.name + ": " + e.message);
				     resolve(false);
	      }
	    })
}


    YWriteString (InString,Address , HKey,  LKey,KeyPath)
    {
        return this.SendCmdAndWait(true,this._YWriteString,InString,Address , HKey,  LKey,KeyPath);
    }

    YReadEx (address,  nlen,  HKey,  LKey,KeyPath )
    {
        var outb = new Uint8Array(nlen);
          var UK;
       return new Promise((resolve, reject) => {

        if (typeof MozWebSocket != "undefined") {
	       UK = new MozWebSocket(this.url,"usbkey-protocol");
	    } else {
		    UK = new WebSocket(this.url,"usbkey-protocol");
	    }

	     try
	     {
	        var i,order;
	          UK.onopen = function() {
                SoftKey3W.ResetOrder(UK);//

	        }

	        UK.onmessage =function got_packet(Msg)
	        {
	            var UK_Data = JSON.parse(Msg.data);
	            var return_value;
	            if(UK_Data.type!="Process")return ;//
	           if(UK_Data.order<2)
	           {

	                switch(UK_Data.order)
	                {
	                    case 0:
	                        {
                                SoftKey3W._YReadEx(UK,address,  nlen,  HKey,  LKey,KeyPath);
	                        }
	                        break; //!!!!!
	                    case 1:
	                        {
	                            SoftKey3W.LastError=UK_Data.LastError;
	                            if( SoftKey3W.LastError!=0){ UK.close();resolve(outb);return ;  }

						          i=0;
						          SoftKey3W._GetBuf(UK,i);//
	                        }
	                        break;
                    }
                  }
                  else{
                    SoftKey3W.LastError=UK_Data.LastError;
	                  if( SoftKey3W.LastError!=0){ UK.close();resolve(outb);return ;  }
                      outb[i]=UK_Data.return_value;
                      i++;
                      if( UK_Data.LastError!=0 || i>=nlen)
                      {
                         UK.close();resolve(outb);return ;
                      }
                      SoftKey3W._GetBuf(UK,i);//

                  }
	        }
	        UK.onclose= function (){

	        }
            UK.onerror = (event) => {
                alert('未能连接服务程序，请确定服务程序是否安装。');
            };
	      }
	     catch(e)
	      {
				    alert(e.name + ": " + e.message);
				     resolve(false);
	      }
	    })
		return outb;
    }

    YReadString ( Address,  nlen, HKey,  LKey,KeyPath)
    {
        return this.SendCmdAndWait(false,this._YReadString,Address,  nlen, HKey,  LKey,KeyPath);
    }


    YRead (Address,HKey,LKey,Path)
    {
        return this.SendCmdAndWait(false,this._YRead,Address,HKey,LKey,Path);
    };

    YWrite (InData,Address,HKey,LKey,Path)
    {
         return this.SendCmdAndWait(true,this._YWrite,InData,Address,HKey,LKey,Path)
    };


    ReSet ( KeyPath )
    {
        return this.SendCmdAndWait(true,this._ReSet,KeyPath);
    }


    SetCal ( HKey,  LKey,  new_HKey, new_LKey,  KeyPath)
    {
        return this.SendCmdAndWait(true,this._SetCal,HKey,  LKey,  new_HKey, new_LKey,KeyPath);
    }



    SetID ( Seed, KeyPath)
    {
        return this.SendCmdAndWait(true,this._SetID, Seed,KeyPath);
    }

    GetProduceDate ( KeyPath)
    {
        return this.SendCmdAndWait(false,this._GetProduceDate,KeyPath);
    }

    SetHidOnly ( IsHidOnly, KeyPath)
    {
        return this.SendCmdAndWait(true,this._SetHidOnly, IsHidOnly,KeyPath);
    }


    SetUReadOnly (KeyPath)
    {
        return this.SendCmdAndWait(true,this._SetUReadOnly,KeyPath);
    }


     StarGenKeyPair  (KeyPath)
    {
     var KeyPairInfo={
            GenPriKey:"",
            GenPubKeyX:"",
            GenPubKeyY:"",
        }
     var UK;

       return new Promise((resolve, reject) => {

        if (typeof MozWebSocket != "undefined") {
	       UK = new MozWebSocket(this.url,"usbkey-protocol");
	    } else {
		    UK = new WebSocket(this.url,"usbkey-protocol");
	    }
	     try
	     {
	          UK.onopen = function() {
                SoftKey3W.ResetOrder(UK);//
	        }

	        UK.onmessage =function got_packet(Msg)
	        {
	            var UK_Data = JSON.parse(Msg.data);
	            var return_value;
	            if(UK_Data.type!="Process")return ;//
	            switch(UK_Data.order)
	            {
	                case 0:
	                    {
                            SoftKey3W._StarGenKeyPair(UK,KeyPath);
	                    }
	                    break; //
	                case 1:
	                    {
	                        SoftKey3W.LastError=UK_Data.LastError;
	                        if( SoftKey3W.LastError!=0){UK.close();  resolve(KeyPairInfo);return ;}
                            SoftKey3W._GenPriKey(UK);
	                }
	                break;
                    case 2:
	                    {
	                         SoftKey3W.LastError=UK_Data.LastError;
	                        if( SoftKey3W.LastError!=0){UK.close();  resolve(KeyPairInfo);return ;}
                            KeyPairInfo.GenPriKey=UK_Data.return_value;
	                        SoftKey3W._GenPubKeyX(UK);
	                    }
	                    break;
                    case 3:
	                    {
	                         SoftKey3W.LastError=UK_Data.LastError;
	                        if( SoftKey3W.LastError!=0){UK.close();  resolve(KeyPairInfo);return ;}
	                        KeyPairInfo.GenPubKeyX=UK_Data.return_value;
	                        SoftKey3W._GenPubKeyY(UK);
	                    }
	                    break;
	                  case 4:
	                    {
	                         SoftKey3W.LastError=UK_Data.LastError;
	                        if( SoftKey3W.LastError!=0){UK.close();  resolve(KeyPairInfo);return ;}
	                        KeyPairInfo.GenPubKeyY=UK_Data.return_value;

	                        UK.close();  resolve(KeyPairInfo);return ;
	                    }
	                    break;
                    }
	        }
	        UK.onclose = function(){

	        }
            UK.onerror = (event) => {
                alert('未能连接服务程序，请确定服务程序是否安装。');
            };
	      }
	     catch(e)
	      {
				    alert(e.name + ": " + e.message);
				     resolve(false);
	      }
	    })
    }



    Set_SM2_KeyPair (PriKey, PubKeyX, PubKeyY, SM2_UserName, KeyPath)
    {
        return this.SendCmdAndWait(true,this._Set_SM2_KeyPair,PriKey, PubKeyX, PubKeyY, SM2_UserName,KeyPath);
    }

    Get_SM2_PubKey (KeyPath)
    {
        return this.SendCmdAndWait(false,this._Get_SM2_PubKey,KeyPath);
    }

    GetPubKeyX (KeyPath)
    {
        return this.SendCmdAndWait(false,this._GetPubKeyX,KeyPath);
    }

    GetPubKeyY (KeyPath)
    {
        return this.SendCmdAndWait(false,this._GetPubKeyY,KeyPath);
    }

    GetSm2UserName (KeyPath)
    {
        return this.SendCmdAndWait(false,this._GetSm2UserName,KeyPath);
    }

    SM2_EncBuf ( InBuf, inlen, KeyPath)
    {
        return this.SendCmdAndWait(true,this._SM2_EncBuf, InBuf, inlen,KeyPath);
    }

    SM2_DecBuf ( InBuf, inlen, pin, KeyPath)
    {
        return this.SendCmdAndWait(true,this._SM2_DecBuf,InBuf, inlen, pin, KeyPath);
    }

    SM2_EncString (InString,  KeyPath)
    {
        return this.SendCmdAndWait(false,this._SM2_EncString,InString, KeyPath);
    }

    SM2_DecString (InString,  pin, KeyPath)
    {
        return this.SendCmdAndWait(false,this._SM2_DecString,InString,  pin,KeyPath);
    }

    YtSetPin (old_pin, new_pin, KeyPath)
    {
        return this.SendCmdAndWait(true,this._YtSetPin,old_pin, new_pin, KeyPath);
    }


    YtSign (msg,  pin,  KeyPath)
    {
        return this.SendCmdAndWait(false,this._YtSign,msg,  pin,  KeyPath);
    }

    YtSign_2 (msg,  pin,  KeyPath)
    {
        return this.SendCmdAndWait(false,this._YtSign_2,msg,  pin,  KeyPath);
    }

    MacAddr ()
    {
        return this.SendCmdAndWait(false,this._MacAddr);
    }

    ComputerName ()
    {
       return this.SendCmdAndWait(false,this._ComputerName);
    };

       SubCal  (Fun,Inbuf,KeyPath)
    {
        var UK;var outb = new Uint8Array(8);
       return new Promise((resolve, reject) => {

        if (typeof MozWebSocket != "undefined") {
	       UK = new MozWebSocket(this.url,"usbkey-protocol");
	    } else {
		    UK = new WebSocket(this.url,"usbkey-protocol");
	    }

	     try
	     {
	        var i=0,order;var bIsEnc=true;
	          UK.onopen = function() {
                SoftKey3W.ResetOrder(UK);//

	        }

	        UK.onmessage =function got_packet(Msg)
	        {
	            var UK_Data = JSON.parse(Msg.data);
	            var return_value;
	            if(UK_Data.type!="Process")return ;//
	            if(bIsEnc)
	            {
	                if(UK_Data.order<8)
	                {
                        SoftKey3W._SetEncBuf(UK,Inbuf[UK_Data.order],UK_Data.order);
	                  return;
	                }
	               else{
	                  order=UK_Data.order-8;
	               }

	                switch(order)
	                {
	                    case 0:
	                        {
	                           SoftKey3W.LastError=UK_Data.LastError;
	                           if( SoftKey3W.LastError!=0){UK.close(); resolve(outb);return ;}
	                           Fun(UK, KeyPath);
	                        }
	                        break; //
	                    case 1:
	                        {
	                            SoftKey3W.LastError=UK_Data.LastError;
	                            return_value=UK_Data.return_value;
	                            if( SoftKey3W.LastError!=0){UK.close(); resolve(outb);return ;}
	                             bIsEnc=false;i=0;
	                             SoftKey3W.ResetOrder(UK);

	                        }
	                        break;
                    }
                }
                else{
                      SoftKey3W.LastError=UK_Data.LastError;
                     if( SoftKey3W.LastError!=0){
                       UK.close();
			           resolve(outb);return ;
                     }
                    if(UK_Data.order<8)
	                {
	                  if(UK_Data.order>0)
	                  {
	                    outb[i-1]=UK_Data.return_value;
	                  }
	                  SoftKey3W._GetEncBuf(UK,i);//
	                   i++;
	                }
	                else{
	                 outb[i-1]=UK_Data.return_value;
	                 UK.close();
					  resolve(outb);return ;
	                }
                }
	        }
	        UK.onclose= function (){

	        }
            UK.onerror = (event) => {
                alert('未能连接服务程序，请确定服务程序是否安装。');
            };
	      }
	     catch(e)
	      {
				    alert(e.name + ": " + e.message);
				     resolve(false);
	      }
	    })
}


}

