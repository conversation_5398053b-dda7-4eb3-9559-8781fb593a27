webpackJsonp([36],{

/***/ 2831:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _util = __webpack_require__(16);

var _util2 = _interopRequireDefault(_util);

var _ModalBox = __webpack_require__(540);

var _ModalBox2 = _interopRequireDefault(_ModalBox);

var _index = __webpack_require__(210);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
	components: {
		ModalBox: _ModalBox2.default
	},
	data: function data() {
		var _this = this;

		return {
			noImgUrl: __webpack_require__(317),
			showModalBox: false,
			modelProps: {},
			bgid: '',
			addrzbox: false,
			editrzbox: false,
			tableHeight: 0,
			userList: [],
			deviceList: [],
			poolList: [],
			siteId: null,
			siteRowId: null,
			modalDelete: false,
			modalDeleteGrard: false,

			DataGuard: {
				id: null,
				name: '',
				user: '',
				password: '',
				address: '',
				remark: '',
				port: ''
			},
			DataGuardrule: {
				name: [{ required: true, message: '请输入站点名称', trigger: 'blur' }],
				user: [{ required: true, message: '请输入登录用户', trigger: 'blur' }],
				password: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],

				port: [{ required: true, validator: this.validatePort, trigger: 'blur' }],
				address: [{ required: true, message: '请输入访问地址', trigger: 'blur' }]
			},

			dataGuardList: [],
			DataGuardId: null,
			editPassWord: '',

			DataGuardCol: [{
				title: '站点ID',
				key: 'id'
			}, {
				title: '站点名称',
				key: 'name'
			}, {
				title: '登录用户',
				key: 'user'
			}, {
				title: '访问地址',
				key: 'address'
			}, {
				title: '索引服务端口号',
				key: 'port'
			}, {
				title: '备注',
				key: 'remark'
			}, {
				title: '操作',
				key: 'operation',
				render: function render(h, params) {
					return h('div', {
						style: {
							display: 'flex',
							justifyContent: 'center',
							alignItems: 'center'
						}
					}, [_this.hasPrivilege(_this.getPower.VRTS_FUNC_MODIFY_DISASTER_STATION) ? h('div', {
						style: {
							fontSize: '22px',

							marginRight: '5px',
							width: '30px',
							height: '30px',
							borderRadius: '3px',
							textAlign: 'center'
						}
					}, [h('span', {
						'class': 'iconfont',
						style: {
							fontSize: '20px',
							color: '#3377ff'
						},
						domProps: {
							innerHTML: '&#xe63d;'
						},
						on: {
							click: function click() {
								_this.DataGuardId = params.row.id;

								_this.editrzbox = true;

								_this.DataGuard.name = params.row.name;
								_this.DataGuard.user = params.row.user;

								_this.DataGuard.password = _this.$getInitPassword();
								_this.editPassWord = params.row.password;
								_this.DataGuard.address = params.row.address;
								_this.DataGuard.remark = params.row.remark;
								_this.DataGuard.port = params.row.port;
							}
						},
						directives: [{
							name: 'tooltip',
							value: '修改'
						}]
					})]) : '', _this.hasPrivilege(_this.getPower.VRTS_FUNC_DELETE_DISASTER_STATION) ? h('div', {
						style: {
							fontSize: '18px',

							marginRight: '5px',
							width: '30px',
							height: '30px',
							borderRadius: '3px',
							textAlign: 'center'
						}
					}, [h('span', {
						'class': 'iconfont',
						style: {
							fontSize: '22px',
							color: '#f56c6c'
						},
						domProps: {
							innerHTML: '&#xe625;'
						},
						on: {
							click: function click() {
								_this.DataGuardId = params.row.id;
								_this.showModalBox = true;
								_this.modelProps = {
									title: '删除站点',
									width: '540',
									messageValue: '确认要删除该站点',
									type: _index.MODEL_TYPE_LIST.warning.type,
									color: _index.MODEL_TYPE_LIST.warning.color,
									row: params.row
								};
							}
						},
						directives: [{
							name: 'tooltip',
							value: '删除'
						}]
					})]) : '']);
				}
			}]
		};
	},
	created: function created() {
		this.$store.dispatch('getPrivilege', this.$store.state.power.module.remCon);

		this.getUserList();
		this.getDeviceList();
		this.getPoolList();

		this.getDataGuard();
	},
	mounted: function mounted() {
		window.addEventListener('resize', this.getTableHeight);
		this.getTableHeight();
	},

	computed: {
		navIcon: function navIcon() {
			return this.$store.state.index.siderNameIcon;
		},
		getPower: function getPower() {
			return this.$store.state.power.module;
		}
	},
	methods: {
		tishi: function tishi(currentRow, index) {
			if (currentRow.id == this.bgid) {
				return 'trbgshow';
			}
			return 'trbgshow_a';
		},
		addbox: function addbox() {
			this.addrzbox = true;
			this.DataGuard.name = '';
			this.DataGuard.user = '';
			this.DataGuard.password = '';
			this.DataGuard.address = '';
			this.DataGuard.remark = '';
			this.DataGuard.port = '9002';
		},
		closeMode: function closeMode() {
			this.addrzbox = false;
			this.editrzbox = false;
		},
		getTableHeight: function getTableHeight() {
			this.tableHeight = window.innerHeight - 375;
		},
		getUserList: function getUserList() {
			_util2.default.restfullCall('rest-ful/v3.0/users', null, 'get', this.userData);
		},
		userData: function userData(res) {
			var array = [];
			for (var i = 0; i < res.data.data.length; i++) {
				array.push({
					name: res.data.data[i].name,
					id: Number(res.data.data[i].id)
				});
			}
			this.userList = array;
		},
		getDeviceList: function getDeviceList() {
			_util2.default.restfullCall('/rest-ful/v3.0/devices?type=0', null, 'get', this.deviceData);
		},
		deviceData: function deviceData(res) {
			var array = [];
			for (var i = 0; i < res.data.data.length; i++) {
				array.push({
					name: res.data.data[i].name,
					id: res.data.data[i].id
				});
			}
			this.deviceList = array;
		},
		getPoolList: function getPoolList() {
			_util2.default.restfullCall('/rest-ful/v3.0/volpools', null, 'get', this.poolData);
		},
		poolData: function poolData(res) {
			var array = [];
			for (var i = 0; i < res.data.data.length; i++) {
				array.push({
					name: res.data.data[i].name,
					id: res.data.data[i].id
				});
			}
			this.poolList = array;
		},
		getDataGuard: function getDataGuard() {
			_util2.default.restfullCall('/rest-ful/v3.0/remote/stations', null, 'get', this.callbackDataGuard);
		},
		callbackDataGuard: function callbackDataGuard(res) {
			this.dataGuardList = res.data.data;
		},
		addDataGuard: function addDataGuard() {
			if (this.DataGuard.address.indexOf('http://') !== -1 || this.DataGuard.address.indexOf('https://') !== -1) {
				var dataGuardOjb = {
					name: this.DataGuard.name,
					user: this.DataGuard.user,
					password: this.DataGuard.password,
					address: this.DataGuard.address,
					remark: this.DataGuard.remark,
					port: Number(this.DataGuard.port)
				};

				if (this.DataGuard.name != '' && this.DataGuard.user != '' && this.DataGuard.password != '' && this.DataGuard.address != '' && this.DataGuard.port != '') {
					_util2.default.restfullCall('/rest-ful/v3.0/remote/station', dataGuardOjb, 'POST', this.callbackMessage);

					this.DataGuard.name = '';
					this.DataGuard.user = '';
					this.DataGuard.password = '';
					this.DataGuard.address = '';
					this.DataGuard.remark = '';
					this.DataGuard.port = '';
				} else {
					this.$Message.warning('请填写必填信息');
				}
			} else {
				this.$Message.warning("访问地址需要包含 'http://' 或 'https://'");
				return;
			}
		},
		callbackMessage: function callbackMessage(res) {
			if (res.data.code == 0) {
				this.$Message.success(res.data.message);
				this.addrzbox = false;
				this.getDataGuard();
			} else {
				this.$Message.warning(res.data.message);
			}
		},
		getRowDataGuard: function getRowDataGuard(row) {
			this.siteRowId = row.id;
			this.DataGuard.id = row.id, this.bgid = row.id, this.DataGuard.name = row.name, this.DataGuard.user = row.user, this.DataGuard.password = this.$getInitPassword(), this.DataGuard.address = row.address, this.DataGuard.remark = row.remark, this.DataGuard.port = row.port;
		},
		submitDataGuard: function submitDataGuard() {
			if (this.DataGuard.address.indexOf('http://') !== -1 || this.DataGuard.address.indexOf('https://') !== -1) {
				var getPassword = this.$getPasswordValue(this.DataGuard.password, this.editPassWord);
				var modifyDataGuard = {
					id: Number(this.DataGuard.id),
					name: this.DataGuard.name,
					user: this.DataGuard.user,
					password: getPassword,
					address: this.DataGuard.address,
					remark: this.DataGuard.remark,
					port: Number(this.DataGuard.port)
				};
				if (this.DataGuard.name != '' && this.DataGuard.user != '' && this.DataGuard.password != '' && this.DataGuard.address != '') {
					_util2.default.restfullCall('/rest-ful/v3.0/remote/station', modifyDataGuard, 'PUT', this.editDataGuard);
				} else {
					this.$Message.warning('请填写必填信息');
				}
			} else {
				this.$Message.warning("访问地址需要包含 'http://' 或 'https://'");
				return;
			}
		},
		editDataGuard: function editDataGuard(res) {
			if (res.data.code == 0) {
				this.$Message.success(res.data.message);
				this.getDataGuard();

				this.DataGuard.name = '';
				this.DataGuard.user = '';
				this.DataGuard.password = '';
				this.DataGuard.address = '';
				this.DataGuard.remark = '';
				this.DataGuard.port = '';
				this.editrzbox = false;
			} else {
				this.$Message.warning(res.data.message);
			}
		},
		deleteDataGuard: function deleteDataGuard() {
			_util2.default.restfullCall('/rest-ful/v3.0/remote/station/' + this.DataGuardId, null, 'delete', this.delDataGuard);
		},
		delDataGuard: function delDataGuard(res) {
			if (res.data.code == 0) {
				this.$Message.success(res.data.message);
				this.modalDeleteGrard = false;
				this.getDataGuard();
			} else {
				this.$Message.warning(res.data.message);
			}
		},
		cancelDataGuard: function cancelDataGuard() {
			this.modalDeleteGrard = false;
		},
		postStatus: function postStatus(type, params) {
			this.siteId = params.row.id;
			if (type === true) {
				this.deleteDataGuard();
			}
		},
		validatePort: function validatePort(rule, value, callback) {
			console.log('value====', value);
			if (this.$isNull(value)) {
				callback(new Error('请输入索引服务端口号'));
			} else {
				callback();
			}
		}
	}
};

/***/ }),

/***/ 3789:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3790);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("5d908c71", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-7b1decaa\",\"scoped\":false,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./dataGuardMag.vue", function() {
     var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-7b1decaa\",\"scoped\":false,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./dataGuardMag.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3790:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.restore1{border:1px dashed #fb784d;padding:20px 20px 0;border-radius:5px;height:130px;margin-bottom:20px;min-width:1000px;background-color:#f3f3f3\n}\n.trbgshow{position:relative\n}\n.trbgshow,.trbgshow_a{cursor:pointer\n}\n.trbgshow td{background:transparent\n}\n.ivu-table-stripe .ivu-table-body tr.trbgshow td,.ivu-table-stripe .ivu-table-fixed-body tr.ivu-table-row-hover td,tr.ivu-table-row-hover td{background-color:transparent\n}", ""]);

// exports


/***/ }),

/***/ 3791:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3792);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("d36729ea", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-7b1decaa\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=1!./dataGuardMag.vue", function() {
     var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-7b1decaa\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=1!./dataGuardMag.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3792:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.marBox[data-v-7b1decaa]{margin:15px;overflow-y:auto\n}\n.restore1[data-v-7b1decaa]{border:1px dashed #fb784d;padding:20px 20px 0;border-radius:5px;height:130px;margin-bottom:20px;min-width:1000px;background-color:#f3f3f3\n}", ""]);

// exports


/***/ }),

/***/ 3793:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", [_c("Row", [_c("Col", { staticClass: "breadstyle", attrs: { span: "24" } }, [_c("Breadcrumb", [_c("BreadcrumbItem", {
    staticClass: "breadlist",
    staticStyle: { display: "flex", "align-items": "center" }
  }, [_c("img", {
    staticStyle: {
      width: "1.2rem",
      height: "1.2rem",
      "margin-right": "0.3rem"
    },
    attrs: { src: __webpack_require__(318) }
  }), _vm._v(" "), _c("span", [_vm._v("站点容灾 / 容灾管理")])])], 1), _vm._v(" "), _c("div", [this.hasPrivilege(_vm.getPower.VRTS_FUNC_ADD_DISASTER_STATION) ? _c("Button", {
    staticStyle: { "margin-right": "15px" },
    attrs: { type: "primary", icon: "md-add-circle" },
    on: { click: _vm.addbox }
  }, [_vm._v("\n\t\t\t\t\t\t添加容灾管理\n\t\t\t\t\t")]) : _vm._e()], 1)], 1)], 1), _vm._v(" "), _c("el-card", {
    staticClass: "marBox",
    staticStyle: { height: "calc(100vh - 145px)" }
  }, [_c("div", { staticStyle: { width: "100%" } }, [_c("Table", {
    staticClass: "rowTable auto-column-size-table",
    attrs: {
      border: "",
      columns: _vm.DataGuardCol,
      data: _vm.dataGuardList,
      "row-class-name": _vm.tishi,
      height: _vm.tableHeight,
      "no-data-text": "<div class='no-img-url-text'><img class='noImgUrl' src=" + _vm.noImgUrl + " /><div class='text'>暂无数据</div><div>"
    },
    on: { "on-row-click": _vm.getRowDataGuard }
  })], 1)]), _vm._v(" "), _c("Modal", {
    attrs: { closable: false, "footer-hide": true, width: "540" },
    model: {
      value: _vm.addrzbox,
      callback: function callback($$v) {
        _vm.addrzbox = $$v;
      },
      expression: "addrzbox"
    }
  }, [_c("div", { staticClass: "addPolicyModeStyle" }, [_c("h3", [_vm._v("添加容灾管理")]), _vm._v(" "), _c("span", { staticClass: "iconfont", on: { click: _vm.closeMode } }, [_vm._v("")])]), _vm._v(" "), _c("div", { staticClass: "modeContent" }, [_c("Form", {
    ref: "DataGuard",
    attrs: {
      model: _vm.DataGuard,
      rules: _vm.DataGuardrule,
      "label-width": 110
    }
  }, [_c("FormItem", { attrs: { label: "站点名称", prop: "name" } }, [_c("Input", {
    attrs: { placeholder: "站点名称" },
    model: {
      value: _vm.DataGuard.name,
      callback: function callback($$v) {
        _vm.$set(_vm.DataGuard, "name", $$v);
      },
      expression: "DataGuard.name"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "索引服务端口号", prop: "port" } }, [_c("Input", {
    attrs: { placeholder: "索引服务端口号" },
    model: {
      value: _vm.DataGuard.port,
      callback: function callback($$v) {
        _vm.$set(_vm.DataGuard, "port", $$v);
      },
      expression: "DataGuard.port"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "登录用户", prop: "user" } }, [_c("Input", {
    attrs: { placeholder: "登录用户" },
    model: {
      value: _vm.DataGuard.user,
      callback: function callback($$v) {
        _vm.$set(_vm.DataGuard, "user", $$v);
      },
      expression: "DataGuard.user"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "登录密码", prop: "password" } }, [_c("Input", {
    attrs: { type: "password", placeholder: "登录密码" },
    model: {
      value: _vm.DataGuard.password,
      callback: function callback($$v) {
        _vm.$set(_vm.DataGuard, "password", $$v);
      },
      expression: "DataGuard.password"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "访问地址", prop: "address" } }, [_c("Input", {
    attrs: { placeholder: "访问地址" },
    model: {
      value: _vm.DataGuard.address,
      callback: function callback($$v) {
        _vm.$set(_vm.DataGuard, "address", $$v);
      },
      expression: "DataGuard.address"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "备注" } }, [_c("Input", {
    attrs: { placeholder: "备注", type: "textarea" },
    model: {
      value: _vm.DataGuard.remark,
      callback: function callback($$v) {
        _vm.$set(_vm.DataGuard, "remark", $$v);
      },
      expression: "DataGuard.remark"
    }
  })], 1)], 1)], 1), _vm._v(" "), _c("div", { staticClass: "modefooter", staticStyle: { width: "100%" } }, [_c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { size: "large", icon: "md-close-circle" },
    on: { click: _vm.closeMode }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      size: "large",
      icon: "md-checkmark-circle"
    },
    on: { click: _vm.addDataGuard }
  }, [_vm._v("确定")])], 1)]), _vm._v(" "), _c("Modal", {
    attrs: { closable: false, "footer-hide": true, width: "540" },
    model: {
      value: _vm.editrzbox,
      callback: function callback($$v) {
        _vm.editrzbox = $$v;
      },
      expression: "editrzbox"
    }
  }, [_c("div", { staticClass: "addPolicyModeStyle" }, [_c("h3", [_vm._v("修改容灾管理")]), _vm._v(" "), _c("span", { staticClass: "iconfont", on: { click: _vm.closeMode } }, [_vm._v("")])]), _vm._v(" "), _c("div", { staticClass: "modeContent" }, [_c("Form", {
    ref: "DataGuard",
    attrs: {
      model: _vm.DataGuard,
      rules: _vm.DataGuardrule,
      "label-width": 110
    }
  }, [_c("FormItem", { attrs: { label: "站点名称", prop: "name" } }, [_c("Input", {
    attrs: { placeholder: "站点名称" },
    model: {
      value: _vm.DataGuard.name,
      callback: function callback($$v) {
        _vm.$set(_vm.DataGuard, "name", $$v);
      },
      expression: "DataGuard.name"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "索引服务端口号", prop: "port" } }, [_c("Input", {
    attrs: { placeholder: "索引服务端口号" },
    model: {
      value: _vm.DataGuard.port,
      callback: function callback($$v) {
        _vm.$set(_vm.DataGuard, "port", $$v);
      },
      expression: "DataGuard.port"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "登录用户", prop: "user" } }, [_c("Input", {
    attrs: { placeholder: "登录用户" },
    model: {
      value: _vm.DataGuard.user,
      callback: function callback($$v) {
        _vm.$set(_vm.DataGuard, "user", $$v);
      },
      expression: "DataGuard.user"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "登录密码", prop: "password" } }, [_c("Input", {
    attrs: { type: "password", placeholder: "登录密码" },
    model: {
      value: _vm.DataGuard.password,
      callback: function callback($$v) {
        _vm.$set(_vm.DataGuard, "password", $$v);
      },
      expression: "DataGuard.password"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "访问地址", prop: "address" } }, [_c("Input", {
    attrs: { placeholder: "访问地址" },
    model: {
      value: _vm.DataGuard.address,
      callback: function callback($$v) {
        _vm.$set(_vm.DataGuard, "address", $$v);
      },
      expression: "DataGuard.address"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "备注" } }, [_c("Input", {
    attrs: { placeholder: "备注" },
    model: {
      value: _vm.DataGuard.remark,
      callback: function callback($$v) {
        _vm.$set(_vm.DataGuard, "remark", $$v);
      },
      expression: "DataGuard.remark"
    }
  })], 1)], 1)], 1), _vm._v(" "), _c("div", { staticClass: "modefooter", staticStyle: { width: "100%" } }, [_c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { size: "large", icon: "md-close-circle" },
    on: { click: _vm.closeMode }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      size: "large",
      icon: "md-checkmark-circle"
    },
    on: { click: _vm.submitDataGuard }
  }, [_vm._v("确定")])], 1)]), _vm._v(" "), _vm.showModalBox ? _c("ModalBox", {
    attrs: { modelProps: _vm.modelProps },
    on: { postStatus: _vm.postStatus },
    model: {
      value: _vm.showModalBox,
      callback: function callback($$v) {
        _vm.showModalBox = $$v;
      },
      expression: "showModalBox"
    }
  }) : _vm._e()], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-7b1decaa", esExports);
  }
}

/***/ }),

/***/ 588:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_dataGuardMag_vue__ = __webpack_require__(2831);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_dataGuardMag_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_dataGuardMag_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_dataGuardMag_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_dataGuardMag_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_7b1decaa_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_dataGuardMag_vue__ = __webpack_require__(3793);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_7b1decaa_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_dataGuardMag_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_7b1decaa_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_dataGuardMag_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3789)
  __webpack_require__(3791)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = "data-v-7b1decaa"
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_dataGuardMag_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_7b1decaa_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_dataGuardMag_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/remoteControl/dataGuardMag.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-7b1decaa", Component.options)
  } else {
    hotAPI.reload("data-v-7b1decaa", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ })

});