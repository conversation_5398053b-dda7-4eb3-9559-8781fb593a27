# 安装准备
## 环境检查

-	检查相关系统安全软件，是否有做过排除，对THE VRTS 3.0有关服务与进程进行排除。
-	检查网络内是否相关网管软件做网络传输速率控制,如有请对备份服务器和客户端解除速率控制。
-	连接介质服务器的存储必须是在线而且调试已完成状态。
-	磁带库驱动已经装上,可以通过厂商测试软件进行正常连接测试。
-	磁盘阵列正确连接，已经划分了正确的分区和准备状态。
-	其他存储网络&存储设备等都已经正确配置完成。
## 系统要求
-	主服务器必须安装在centos7以及Windows 2008以后的操作系统版本。
-	硬件处理器为Intel Pentium、Xeon、AMD 或兼容的处理器。
-	硬件内存要求为16G RAM或更多，以获得更佳性能。
-	显示器的屏幕分辨率至少调节到1024x768或以上,颜色质量至少调节到中(16位)或以上。
-	磁盘所需剩余空间至少为100G。

## 	数据库安装

### 卸载mariadb

-	前往mysql官网下载所需的版本

-	下载完成后就上传的CentOS系统上

-　Mysql5.7的rpm包下载地址为
        
    https://dev.mysql.com/get/Downloads/MySQL-5.7/mysql-5.7.26-1.el7.x86_64.rpm-bundle.tar

-	通过mkdir mysql 命令创建MySQL目录，把安装包解压到该目录下；

-	并通过yum安装需要的依赖包

``` javascript
[root@youxi2 ~]# mkdir Mysql　　//创建一个专门的Mysql目录
[root@youxi2 ~]# tar xf mysql-5.7.16-1.el7.x86_64.rpm-bundle.tar -C Mysql/　　//将解压的文件放到Mysql目录下
[root@youxi2 ~]# yum -y install make gcc-c++ cmake bison-devel ncurses-devel libaio libaio-devel net-tools　//安装依赖包

```
- 由于CentOS7开始自带的数据库是mariadb，所以需要卸载系统中的mariadb组件，才能安装mysql的组件

``` javascript
[root@youxi2 ~]# rpm -qa | grep mariadb
mariadb-libs-5.5.60-1.el7_5.x86_64
[root@youxi2 ~]# yum -y remove mariadb-libs

```
### 安装MySQL数据库
- 现在开始安装mysql，由于依赖关系，所以顺序是固定的。

    ![安装MySQL数据库](/dist/img/2-1-3-2-01.png)

- 启动MySQL数据库，并设置成开机自启模式，并查看启动状态

    ![安装MySQL数据库](/dist/img/2-1-3-2-02.png)

## 	修改默认MySQL默认密码

- 进入MySQL数据库，通过以下命令更改数据密码；

``` javascript
“alter user ‘root’@’localhost’ identified by ‘Admin123!’”
```

- 通过 flush privileges 命令，更新配置；

    ![修改默认MySQL默认密码](/dist/img/2-1-3-3.png)

###	配置MySQL数据库，把sql_mode改成非only_full_group_by

- 进去MySQL数据库，通过“select @@sql_mode”命令，查看当前sql_mode模式；

    ![配置MySQL数据库](/dist/img/2-1-3-4-01.png)

- 进入MySQL配置文件/etc/my.cnf 配置sql_mode模式，改为非 only_full_group_by模式    

    ![配置MySQL数据库](/dist/img/2-1-3-4-02.png)

    ![配置MySQL数据库](/dist/img/2-1-3-4-03.png)

-	使用“systemctl restart mysqld”命令重启MySQL数据库；

    ![配置MySQL数据库](/dist/img/2-1-3-4-04.png)

- 进入MySQL数据库通过“select @@sql_mode”命令，确认当前sql_mode模式是否是非only_full_group_by模式；

确认sql_mode模式：
    ![配置MySQL数据库](/dist/img/2-1-3-4-05.png)
