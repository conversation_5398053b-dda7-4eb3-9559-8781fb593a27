{"version": 3, "file": "anchor.js", "sourceRoot": "", "sources": ["../../../src/registry/connection-point/anchor.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAA;AAS/B,SAAS,SAAS,CAAC,IAAU,EAAE,IAAW,EAAE,MAAM,GAAG,CAAC;IACpD,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;IAC3B,IAAI,CAAC,CAAA;IACL,IAAI,CAAC,CAAA;IACL,IAAI,SAAS,CAAA;IACb,IAAI,UAAqB,CAAA;IAEzB,QAAQ,IAAI,EAAE;QACZ,KAAK,MAAM;YACT,UAAU,GAAG,GAAG,CAAA;YAChB,CAAC,GAAG,GAAG,CAAA;YACP,CAAC,GAAG,KAAK,CAAA;YACT,SAAS,GAAG,CAAC,CAAC,CAAA;YACd,MAAK;QACP,KAAK,OAAO;YACV,UAAU,GAAG,GAAG,CAAA;YAChB,CAAC,GAAG,KAAK,CAAA;YACT,CAAC,GAAG,GAAG,CAAA;YACP,SAAS,GAAG,CAAC,CAAA;YACb,MAAK;QACP,KAAK,KAAK;YACR,UAAU,GAAG,GAAG,CAAA;YAChB,CAAC,GAAG,GAAG,CAAA;YACP,CAAC,GAAG,KAAK,CAAA;YACT,SAAS,GAAG,CAAC,CAAC,CAAA;YACd,MAAK;QACP,KAAK,QAAQ;YACX,UAAU,GAAG,GAAG,CAAA;YAChB,CAAC,GAAG,KAAK,CAAA;YACT,CAAC,GAAG,GAAG,CAAA;YACP,SAAS,GAAG,CAAC,CAAA;YACb,MAAK;QACP;YACE,OAAM;KACT;IAED,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,EAAE;QACvC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAA;KAC9B;SAAM;QACL,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAA;KAC9B;IAED,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC3B,CAAC,CAAC,UAAU,CAAC,IAAI,SAAS,GAAG,MAAM,CAAA;QACnC,CAAC,CAAC,UAAU,CAAC,IAAI,SAAS,GAAG,MAAM,CAAA;KACpC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,MAAM,GAA8C,UAC/D,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,OAAO;IAEP,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,OAAO,CAAA;IACtC,IAAI,KAAK,EAAE;QACT,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,WAAW,CAAC,CAAA;KACpC;IACD,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAA;AACrD,CAAC,CAAA"}