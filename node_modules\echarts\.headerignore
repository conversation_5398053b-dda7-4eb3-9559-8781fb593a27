# Only support regexp, testing against each relative file path
# based on the echart base directory. And the pattern should
# match the relative path completely.

node_modules
.*\.git
.*\.github
.*\.editorconfig
.*\.gitignore
.*\.jshintrc
.*\.jshintrc-dist
.*\.npmignore
.*\.ratignore
.*\.headerignore
.*\.DS_Store
.*\.idea
.*rat\.iml
__MAC_OS
.*README.md
.*MANIFEST\.txt
DISCLAIMER
NOTICE
KEYS
LICENSE
LICENSE-.+
licenses
map/js
map/json
benchmark/dep/*
test/ut/lib
test/data$
test/lib/esl\.js
test/lib/perlin\.js
test/lib/countup\.js
.*jquery\.min\.js
.*rollup\.browser\.js
.*configure
.+\.json
.+\.map
.+\.gexf
.+\.jar
.+\.bin
.+\.csv
.+\.png
.+\.PNG
.+\.jpg
.+\.JPG
.+\.jpeg
.+\.JPEG
.+\.gif
.+\.GIF
.+\.class
