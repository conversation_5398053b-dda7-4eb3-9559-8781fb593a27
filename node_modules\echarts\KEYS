This file contains the PGP keys of various developers.
Please don't use them for email unless you have to. Their main
purpose is code signing.

Examples of importing this file in your keystore:
 gpg --import KEYS.txt
 (need pgp and other examples here)

Examples of adding your key to this file:
 pgp -kxa <your name> and append it to this file.
 (pgpk -ll <your name> && pgpk -xa <your name>) >> this file.
 (gpg --list-sigs <your name>
     && gpg --armor --export <your name>) >> this file.

---------------------------------------
pub   rsa4096 2018-04-23 [SC]
      9B06D9B4FA37C4DD52725742747985D7E3CEB635
uid           [ultimate] <PERSON> (CODE SIGNING KEY) <<EMAIL>>
sig 3        747985D7E3CEB635 2018-04-23  <PERSON> (CODE SIGNING KEY) <<EMAIL>>
sub   rsa4096 2018-04-23 [E]
sig          747985D7E3CEB635 2018-04-23  <PERSON> (CODE SIGNING KEY) <<EMAIL>>

-----BEGIN PGP PUBLIC KEY BLOCK-----
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=Uv8d
-----END PGP PUBLIC KEY BLOCK-----

pub   rsa4096 2019-01-24 [SC]
      1683FBD23F6DD36C0E52223507D78F777D2C0C27
uid           [ultimate] Ovilia (CODE SIGNING KEY) <<EMAIL>>
sig 3        07D78F777D2C0C27 2019-01-24  Ovilia (CODE SIGNING KEY) <<EMAIL>>
sub   rsa4096 2019-01-24 [E]
sig          07D78F777D2C0C27 2019-01-24  Ovilia (CODE SIGNING KEY) <<EMAIL>>

-----BEGIN PGP PUBLIC KEY BLOCK-----
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=wft0
-----END PGP PUBLIC KEY BLOCK-----
