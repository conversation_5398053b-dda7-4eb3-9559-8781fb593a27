{"version": 3, "file": "view.js", "sourceRoot": "", "sources": ["../../src/view/view.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,GAAG,EAAY,QAAQ,EAAE,MAAM,iBAAiB,CAAA;AAEzD,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAA;AAIlC,MAAM,OAAgB,IAAgC,SAAQ,QAAW;IAKvE,IAAW,QAAQ;QACjB,OAAO,CAAC,CAAA;IACV,CAAC;IAED,0CAA0C;IAC1C,IAAc,gBAAgB;QAC5B,OAAO,IAAI,CAAA;IACb,CAAC;IAED;QACE,KAAK,EAAE,CAAA;QACP,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAA;QAC7B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;IAC7B,CAAC;IAED,6DAA6D;IAC7D,aAAa,CAAC,IAAY,EAAE,OAAY;QACtC,OAAO,CAAC,CAAA;IACV,CAAC;IAED,KAAK,CAAC,OAAgB,IAAI,CAAC,SAAS;QAClC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QACf,OAAO,IAAI,CAAA;IACb,CAAC;IAED,OAAO,CAAC,OAAgB,IAAI,CAAC,SAAS;QACpC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QAChB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,MAAM,CAAC,OAAgB,IAAI,CAAC,SAAS;QACnC,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAA;YACnC,IAAI,CAAC,QAAQ,EAAE,CAAA;YACf,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAC3B,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACzB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;aACnB;SACF;aAAM;YACL,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;SACnB;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAES,QAAQ,KAAI,CAAC;IAEvB,QAAQ,CAAC,SAA4B,EAAE,OAAgB,IAAI,CAAC,SAAS;QACnE,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC;YAC7C,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC;YACrB,CAAC,CAAC,SAAS,CAAA;IACf,CAAC;IAED,QAAQ,CAAC,SAA4B,EAAE,OAAgB,IAAI,CAAC,SAAS;QACnE,GAAG,CAAC,QAAQ,CACV,IAAI,EACJ,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAC3D,CAAA;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,WAAW,CAAC,SAA4B,EAAE,OAAgB,IAAI,CAAC,SAAS;QACtE,GAAG,CAAC,WAAW,CACb,IAAI,EACJ,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAC3D,CAAA;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,QAAQ,CACN,KAAsC,EACtC,OAAgB,IAAI,CAAC,SAAS;QAE9B,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;QACpB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,QAAQ,CAAC,KAA+B,EAAE,OAAgB,IAAI,CAAC,SAAS;QACtE,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;YACjC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;SACtB;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;;OAOG;IACH,QAAQ,CAAC,QAAgB,EAAE,OAAgB,IAAI,CAAC,SAAS;QACvD,IAAI,OAAO,GAAG,IAAI,CAAA;QAClB,OAAO,OAAO,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE;YACxC,MAAM,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAA;YAC5C,IAAI,KAAK,IAAI,IAAI,EAAE;gBACjB,OAAO,KAAK,CAAA;aACb;YAED,IAAI,OAAO,KAAK,IAAI,CAAC,SAAS,EAAE;gBAC9B,OAAO,IAAI,CAAA;aACZ;YAED,OAAO,GAAG,OAAO,CAAC,UAAqB,CAAA;SACxC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED,IAAI,CACF,QAAiB,EACjB,WAAoB,IAAI,CAAC,SAAS,EAClC,YAA8B,IAAI,CAAC,SAAS;QAE5C,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,KAAK,CAAA;IACvD,CAAC;IAED,OAAO,CACL,QAAiB,EACjB,WAAoB,IAAI,CAAC,SAAS,EAClC,YAA8B,IAAI,CAAC,SAAS;QAE5C,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAA;QACtD,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IAC3C,CAAC;IAED,UAAU,CAAC,QAAgB,EAAE,OAAgB,IAAI,CAAC,SAAS;QACzD,IAAI,IAAI,GAAG,IAAI,CAAA;QACf,OAAO,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;YAChC,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAA;YACvC,IAAI,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,KAAK,OAAO,EAAE;gBAC/D,OAAO,IAAI,CAAA;aACZ;YACD,IAAI,GAAG,IAAI,CAAC,UAAqB,CAAA;SAClC;QAED,+DAA+D;QAC/D,6DAA6D;QAC7D,+DAA+D;QAC/D,kEAAkE;QAClE,0CAA0C;QAC1C,OAAO,IAAI,CAAA;IACb,CAAC;IAED,WAAW,CAAC,IAAa,EAAE,YAAqB;QAC9C,IAAI,QAAQ,CAAA;QAEZ,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS,EAAE;YAC3B,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;gBACpC,QAAQ,GAAG,KAAK,YAAY,EAAE,CAAA;aAC/B;YACD,OAAO,QAAQ,CAAA;SAChB;QAED,IAAI,IAAI,EAAE;YACR,MAAM,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAC/B,QAAQ,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,cAAc,GAAG,GAAG,CAAA;YAC5D,IAAI,YAAY,EAAE;gBAChB,QAAQ,IAAI,MAAM,YAAY,EAAE,CAAA;aACjC;YAED,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAqB,EAAE,QAAQ,CAAC,CAAA;SAClE;QAED,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,eAAe,CAAC,SAAiB;QAC/B,OAAO,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;IACjC,CAAC;IAED,cAAc,CAAC,MAAmB,EAAE,MAAgB;QAClD,IAAI,MAAM,IAAI,IAAI,EAAE;YAClB,OAAO,IAAI,CAAA;SACZ;QAED,IAAI,CAAC,MAAM,EAAE;YACX,IAAI,CAAC,gBAAgB,EAAE,CAAA;SACxB;QAED,MAAM,QAAQ,GAAG,gBAAgB,CAAA;QACjC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YAClC,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;YACjC,IAAI,KAAK,IAAI,IAAI,EAAE;gBACjB,OAAM;aACP;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAA;YAChD,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;gBAChC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAA;aAC/C;QACH,CAAC,CAAC,CAAA;QAEF,OAAO,IAAI,CAAA;IACb,CAAC;IAED,gBAAgB;QACd,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAA;QACvD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,sBAAsB,CAAC,MAAmB,EAAE,IAAe;QACzD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;QAC9C,OAAO,IAAI,CAAA;IACb,CAAC;IAED,wBAAwB;QACtB,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAA;QACnC,OAAO,IAAI,CAAA;IACb,CAAC;IAES,aAAa,CACrB,SAAiB,EACjB,QAA0C,EAC1C,QAAa;QAEb,GAAG,CAAC,KAAK,CAAC,EAAE,CACV,IAAI,CAAC,SAAS,EACd,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,EACpC,QAAQ,EACR,QAAQ,CACT,CAAA;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IASS,eAAe,CACvB,SAAiB,EACjB,QAAuB,EACvB,QAAc;QAEd,MAAM,IAAI,GAAG,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACjD,IAAI,QAAQ,IAAI,IAAI,EAAE;YACpB,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;SACpC;aAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;YACvC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAA;SACxD;aAAM;YACL,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAA;SAC9C;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAES,iBAAiB,CACzB,IAAwB,EACxB,MAAmB,EACnB,IAAe;QAEf,IAAI,MAAM,IAAI,IAAI,EAAE;YAClB,OAAO,IAAI,CAAA;SACZ;QAED,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACnC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;YACxC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAA;YACtD,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;gBAChC,GAAG,CAAC,KAAK,CAAC,EAAE,CACV,IAAe,EACf,SAAS,GAAG,EAAE,EACd,IAAI,EACJ,MAAa,CACd,CAAA;aACF;QACH,CAAC,CAAC,CAAA;QAEF,OAAO,IAAI,CAAA;IACb,CAAC;IAES,oBAAoB,CAAC,IAAwB;QACrD,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAe,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAA;SACzD;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAES,iBAAiB;QACzB,OAAO,IAAI,MAAM,CAAC,SAAS,UAAU,IAAI,CAAC,GAAG,EAAE,CAAA;IACjD,CAAC;IAED,2BAA2B;IACjB,eAAe,CAAC,OAA0B;QAClD,2BAA2B;QAC3B,IAAI,MAA4B,CAAA;QAChC,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC/B,MAAM,EAAE,GAAI,IAAY,CAAC,OAAO,CAAC,CAAA;YACjC,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;gBAC5B,MAAM,GAAG,CAAC,GAAG,IAAS,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAA;aAClD;SACF;aAAM;YACL,MAAM,GAAG,CAAC,GAAG,IAAS,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAA;SACvD;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAED,cAAc,CAAC,CAAkB,EAAE,UAAmC,EAAE;QACtE,kEAAkE;QAClE,2CAA2C;QAC3C,oDAAoD;QACpD,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,EAAE,GAAG,CAAC,CAAA;QACpD,IAAI,OAAO,CAAC,SAAS,IAAI,IAAI,KAAK,WAAW,IAAI,IAAI,KAAK,UAAU,EAAE;YACpE,OAAO,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;SACnD;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAED,eAAe,CAAC,CAAkB;QAChC,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC,CAAA;QAClD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,oBAAoB,CAAC,CAAkB;QACrC,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,kBAAkB,KAAK,IAAI,CAAA;IACzD,CAAC;IAED,YAAY,CAAqB,CAAkB;QACjD,OAAO,IAAI,CAAC,SAAS,CAAI,CAAC,CAAC,CAAA;IAC7B,CAAC;IAED,YAAY,CAAqB,CAAkB,EAAE,IAAO;QAC1D,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;IAChC,CAAC;IAES,SAAS,CAAqB,CAAkB,EAAE,IAAQ;QAClE,IAAI,CAAC,IAAI,IAAI,EAAE;YACb,MAAM,IAAI,SAAS,CAAC,uBAAuB,CAAC,CAAA;SAC7C;QAED,IAAI,WAAW,GAAG,CAAC,CAAC,IAAI,CAAA;QACxB,MAAM,GAAG,GAAG,KAAK,IAAI,CAAC,GAAG,IAAI,CAAA;QAE7B,MAAM;QACN,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,IAAI,WAAW,IAAI,IAAI,EAAE;gBACvB,OAAO,EAAO,CAAA;aACf;YACD,OAAO,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,CAAA;SAC9B;QAED,MAAM;QACN,IAAI,WAAW,IAAI,IAAI,EAAE;YACvB,WAAW,GAAG,CAAC,CAAC,IAAI,GAAG,EAAE,CAAA;SAC1B;QAED,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE;YAC5B,WAAW,CAAC,GAAG,CAAC,qBAAQ,IAAI,CAAE,CAAA;SAC/B;aAAM;YACL,WAAW,CAAC,GAAG,CAAC,mCAAQ,WAAW,CAAC,GAAG,CAAC,GAAK,IAAI,CAAE,CAAA;SACpD;QAED,OAAO,WAAW,CAAC,GAAG,CAAC,CAAA;IACzB,CAAC;IAED,cAAc,CAA4B,GAAM;QAC9C,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAA;IACjC,CAAC;IAGD,OAAO;QACL,IAAI,CAAC,MAAM,EAAE,CAAA;IACf,CAAC;CACF;AAHC;IADC,IAAI,CAAC,OAAO,EAAE;mCAGd;AAOH,WAAiB,IAAI;IACnB,SAAgB,aAAa,CAAC,OAAgB,EAAE,YAAsB;QACpE,OAAO,YAAY;YACjB,CAAC,CAAC,GAAG,CAAC,gBAAgB,CAAC,OAAO,IAAI,GAAG,CAAC;YACtC,CAAC,CAAE,GAAG,CAAC,eAAe,CAAC,OAAO,IAAI,KAAK,CAAiB,CAAA;IAC5D,CAAC;IAJe,kBAAa,gBAI5B,CAAA;IAED,SAAgB,IAAI,CAClB,QAAmC,EACnC,QAAiB,EACjB,SAA2B;QAE3B,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,GAAG,EAAE;YACjC,OAAO,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAA;SAC7B;QAED,IAAI,SAAS,EAAE;YACb,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAA;YACjC,IAAI,KAAK,EAAE;gBACT,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAA;aACzD;SACF;QAED,IAAI,MAAM,CAAC,cAAc,EAAE;YACzB,MAAM,aAAa,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC;gBAC1C,CAAC,CAAC,UAAU,QAAQ,EAAE;gBACtB,CAAC,CAAC,QAAQ,CAAA;YACZ,OAAO;gBACL,aAAa,EAAE,IAAI;gBACnB,oDAAoD;gBACpD,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAC/B,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,CACzC;aACF,CAAA;SACF;QAED,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,CAAA;IACtB,CAAC;IA9Be,SAAI,OA8BnB,CAAA;IAED,SAAgB,cAAc,CAA4B,GAAM;QAC9D,IAAI,eAAe,GAAG,GAAG,CAAA;QACzB,MAAM,aAAa,GAAG,GAAG,CAAC,aAA2B,CAAA;QACrD,MAAM,QAAQ,GACZ,aAAa;YACb,aAAa,CAAC,cAAc;YAC5B,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,CAAA;QAEjC,IAAI,QAAQ,EAAE;YACZ,gDAAgD;YAChD,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE;gBACrB,IAAI,QAAQ,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;oBAC/B,QAAQ,CAAC,GAAG,CAAC,GAAI,GAAW,CAAC,GAAG,CAAC,CAAA;iBAClC;aACF;YACD,eAAe,GAAG,QAAQ,CAAA;SAC3B;QAED,OAAO,eAAe,CAAA;IACxB,CAAC;IAnBe,mBAAc,iBAmB7B,CAAA;AACH,CAAC,EA3DgB,IAAI,KAAJ,IAAI,QA2DpB;AAED,WAAiB,IAAI;IACN,UAAK,GAA4B,EAAE,CAAA;IAEhD,SAAgB,OAAO,CAAC,GAAW;QACjC,OAAO,KAAA,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,CAAA;IAC3B,CAAC;IAFe,YAAO,UAEtB,CAAA;AACH,CAAC,EANgB,IAAI,KAAJ,IAAI,QAMpB;AAED,IAAU,OAAO,CAOhB;AAPD,WAAU,OAAO;IACf,IAAI,OAAO,GAAG,CAAC,CAAA;IACf,SAAgB,QAAQ;QACtB,MAAM,EAAE,GAAG,IAAI,OAAO,EAAE,CAAA;QACxB,OAAO,IAAI,CAAC,CAAA;QACZ,OAAO,EAAE,CAAA;IACX,CAAC;IAJe,gBAAQ,WAIvB,CAAA;AACH,CAAC,EAPS,OAAO,KAAP,OAAO,QAOhB"}