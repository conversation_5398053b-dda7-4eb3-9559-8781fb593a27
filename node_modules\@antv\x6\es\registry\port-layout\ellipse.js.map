{"version": 3, "file": "ellipse.js", "sourceRoot": "", "sources": ["../../../src/registry/port-layout/ellipse.ts"], "names": [], "mappings": "AAAA,OAAO,EAAa,OAAO,EAAE,MAAM,mBAAmB,CAAA;AAEtD,OAAO,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAA;AAYjC,MAAM,CAAC,MAAM,OAAO,GAAuC,CACzD,iBAAiB,EACjB,QAAQ,EACR,iBAAiB,EACjB,EAAE;IACF,MAAM,UAAU,GAAG,iBAAiB,CAAC,KAAK,IAAI,CAAC,CAAA;IAC/C,MAAM,SAAS,GAAG,iBAAiB,CAAC,IAAI,IAAI,EAAE,CAAA;IAE9C,OAAO,aAAa,CAClB,iBAAiB,EACjB,QAAQ,EACR,UAAU,EACV,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,SAAS,CACxD,CAAA;AACH,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,aAAa,GAAuC,CAC/D,iBAAiB,EACjB,QAAQ,EACR,iBAAiB,EACjB,EAAE;IACF,MAAM,UAAU,GAAG,iBAAiB,CAAC,KAAK,IAAI,CAAC,CAAA;IAC/C,MAAM,SAAS,GAAG,iBAAiB,CAAC,IAAI,IAAI,GAAG,GAAG,iBAAiB,CAAC,MAAM,CAAA;IAE1E,OAAO,aAAa,CAAC,iBAAiB,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,KAAK,EAAE,EAAE;QACtE,OAAO,KAAK,GAAG,SAAS,CAAA;IAC1B,CAAC,CAAC,CAAA;AACJ,CAAC,CAAA;AAED,SAAS,aAAa,CACpB,iBAAgC,EAChC,QAAmB,EACnB,UAAkB,EAClB,MAAgD;IAEhD,MAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAA;IACnC,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAA;IACrC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAA;IAC9C,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;IAC1C,MAAM,KAAK,GAAG,iBAAiB,CAAC,MAAM,CAAA;IAEtC,OAAO,iBAAiB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;QAC3C,MAAM,KAAK,GAAG,UAAU,GAAG,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC/C,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,MAAM,CAAC,CAAA;QAEtE,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAElE,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,EAAE;YACtB,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAA;SACxC;QAED,IAAI,IAAI,CAAC,EAAE,EAAE;YACX,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;SACxB;QAED,OAAO,QAAQ,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;IACzC,CAAC,CAAC,CAAA;AACJ,CAAC"}