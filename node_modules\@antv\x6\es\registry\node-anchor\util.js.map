{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../src/registry/node-anchor/util.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAA;AAC3C,OAAO,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAA;AAOzC,2BAA2B;AAC3B,MAAM,UAAU,OAAO,CAAwB,EAAK;IAClD,OAAO,UAEL,IAAc,EACd,MAAkB,EAClB,GAAQ,EACR,OAAuB;QAEvB,IAAI,GAAG,YAAY,OAAO,EAAE;YAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAA;YAC9C,IAAI,QAAQ,CAAA;YACZ,IAAI,OAAO,EAAE;gBACX,IAAI,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;oBAC9B,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAA;oBAClE,QAAQ,GAAG,cAAc,CAAC,OAAmB,EAAE,QAAQ,CAAC,CAAA;iBACzD;qBAAM;oBACL,QAAQ,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,CAAA;iBACrD;aACF;iBAAM;gBACL,QAAQ,GAAG,IAAI,KAAK,EAAE,CAAA;aACvB;YACD,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;SACtD;QACD,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA,CAAC,sBAAsB;IACzD,CAAa,CAAA;AACf,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,QAAkB,EAAE,KAAsB;IACvE,MAAM,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA;IAClD,MAAM,GAAG,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;IACjE,IAAI,YAAY,EAAE;QAChB,OAAO,QAAQ,CAAC,eAAe,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA;KAC3C;IACD,OAAO,QAAQ,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAA;AACvC,CAAC"}