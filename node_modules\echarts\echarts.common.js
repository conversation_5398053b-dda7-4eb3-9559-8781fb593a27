/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/

export * from './src/echarts';
export * from './src/export';

import './src/component/dataset';

import './src/chart/line';
import './src/chart/bar';
import './src/chart/pie';
import './src/chart/scatter';
import './src/component/graphic';
import './src/component/tooltip';
import './src/component/axisPointer';
import './src/component/legendScroll';

import './src/component/grid';
import './src/component/title';

import './src/component/markPoint';
import './src/component/markLine';
import './src/component/markArea';
import './src/component/dataZoom';
import './src/component/toolbox';

import 'zrender/src/vml/vml';
import 'zrender/src/svg/svg';