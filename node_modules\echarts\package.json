{"name": "echarts", "version": "4.9.0", "description": "A powerful charting and visualization library for browser", "license": "Apache-2.0", "keywords": ["visualization", "canvas"], "homepage": "http://echarts.apache.org", "repository": {"type": "git", "url": "https://github.com/apache/incubator-echarts.git"}, "scripts": {"release": "node build/build.js --release", "build": "node build/build.js", "watch": "node build/build.js -w", "help": "node build/build.js --help", "prepublish": "node build/build.js --prepublish", "test:visual": "node test/runTest/server.js", "test:visual:report": "node test/runTest/genReport.js", "test": "node build/build.js --prepublish && jest --config test/ut/jest.config.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "lint": "./node_modules/.bin/eslint src extension-src", "lint:dist": "echo 'It might take a while. Please wait ...' && ./node_modules/.bin/jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"zrender": "4.3.2"}, "devDependencies": {"@babel/core": "7.3.4", "@babel/helper-module-transforms": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/types": "7.0.0-beta.31", "assert": "1.4.1", "canvas": "^2.6.0", "commander": "2.11.0", "coordtransform": "2.0.2", "escodegen": "1.8.0", "eslint": "6.3.0", "esprima": "2.7.2", "estraverse": "4.1.1", "fs-extra": "0.26.7", "glob": "7.0.0", "husky": "^4.2.5", "jest": "^24.9.0", "jest-canvas-mock": "^2.2.0", "jsdom": "^15.2.1", "jshint": "2.10.2", "open": "6.4.0", "pixelmatch": "5.0.2", "pngjs": "3.4.0", "rollup": "0.50.0", "rollup-plugin-commonjs": "8.4.1", "rollup-plugin-node-resolve": "3.0.0", "rollup-plugin-uglify": "2.0.1", "seedrandom": "3.0.3", "semver": "6.3.0", "serve-handler": "6.1.1", "slugify": "1.3.4", "socket.io": "2.2.0"}}