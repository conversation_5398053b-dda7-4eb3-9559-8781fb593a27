# Linux系统客户端的配置

## 	THE VRTS客户端模块安装

-	执行install.sh脚本文件；
-	选择“1”安装Client客户端；
-	添加master server IP地址；

    ![Linux系统客户端的配置](/dist/img/3-1-1.png)

## THE VRTS代理模块安装

### MySQL代理模块安装

-	执行install.sh脚本文件；
-	下一步，选择“2”安装客户端代理；
-	下一步，选择“1”安装MySQL代理；
    ![Linux系统客户端的配置](/dist/img/3-1-2-1.png)
 
### Oracle代理模块安装
-	执行install.sh脚本文件；
-	下一步，选择“2”安装客户端代理；
-	下一步，选择“2”安装Oracle代理；
-	下一步，选择“2”安装Oracle11G数据库代理；
-	下一步，添加Oracle数据库安装目录；
    ![Linux系统客户端的配置](/dist/img/3-1-2-2.png)

 ###  DB2代理模块安装
-	查看DB2安装目录；
-	执行install.sh脚本文件；
-	下一步，选择“2”安装客户端代理；
-	下一步，选择“1”安装DB2代理；
-	下一步，选择“2”安装DB2 V10数据库代理；
-	下一步，添加DB2数据库安装目录；
 
    ![Linux系统客户端的配置](/dist/img/3-1-2-3.png)

## THE VRTS客户端服务启动
-	进入客户端服务器启动脚本所在目录；
-	执行./thevrtsclient start，启动THE VRTS 客户端服务；
 
    ![Linux系统客户端的配置](/dist/img/3-1-3.png)
 
## 	THE VRTS备份软件客户端的代理配置

###	MySQL代理配置
-	点击左侧客户端，进入客户端界面；
-	在客户端界面可以看到要配置的客户端信息；
-	点击该客户端右侧的操作；

    ![Linux系统客户端的配置](/dist/img/3-1-4-1-01.png)
 
-	点击该客户端右侧的操作，进入客户端配置界面；
-	可以看到客户端的基本信息，包括客户端的机器名称、操作系统类型、软件版本、CPU、内存、状态；

    ![Linux系统客户端的配置](/dist/img/3-1-4-1-02.png)

 
-	在客户端配置点击右侧的MYSQL数据库，进入MySQL数据库配置界面；
-	在MySQL数据库配置界面，填写服务地址、用户名、密码、端口，点击测试连接；

    ![Linux系统客户端的配置](/dist/img/3-1-4-1-03.png)

 
-	如果测试连接通过，会显示测试连接成功；
-	测试显示成功后，点击确定；

    ![Linux系统客户端的配置](/dist/img/3-1-4-1-04.png)

 
-	点击添加实例，该客户端的MySQL代理配置完成；
-	最后点击确定配置MySQL代理完成。
 
    ![Linux系统客户端的配置](/dist/img/3-1-4-1-05.png)

###	Oracle代理配置
-	点击左侧客户端，进入客户端界面；
-	在客户端界面可以看到要配置的客户端信息；
-	点击该客户端右侧的操作；
 
    ![Linux系统客户端的配置](/dist/img/3-1-4-2-01.png)

-	点击该客户端右侧的操作，进入客户端配置界面；
-	可以看到客户端的基本信息，包括客户端的机器名称、操作系统类型、软件版本、CPU、内存、状态；
 
    ![Linux系统客户端的配置](/dist/img/3-1-4-2-02.png)

-	在客户端配置点击右侧的ORACLE数据库，进入ORACLE数据库配置界面；
-	在ORACLE数据库配置界面，填写服务地址、用户名、密码、端口，点击测试连接；
 
    ![Linux系统客户端的配置](/dist/img/3-1-4-2-03.png)

-	如果测试连接通过，会显示测试连接成功；
 
    ![Linux系统客户端的配置](/dist/img/3-1-4-2-04.png)

-	测试显示成功后，点击确定；
-	再点击添加实例，该客户端的ORACLE代理配置完成；
 
    ![Linux系统客户端的配置](/dist/img/3-1-4-2-05.png)

### DB2代理配置

DB2数据库在客户端中不用配置，只需在创建DB2备份策略时，添加DB2系统用户即可。详情请参考--[创建DB2数据库备份策略](/creatpolicy#创建db2数据库备份策略)。
