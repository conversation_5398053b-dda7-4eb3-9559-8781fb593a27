# 产品模块功能介绍

THE VRTS数据备份与恢复系统是在自主开发一整套数据通讯传输机制基础上，实现对各种通用计算机操作系统如IBM AIX、SUN Solaris、HP_UNIX、SCO Unix/Unixware、Linux、IRIX和Windows等操作系统上的数据进行传输,它能够对异构操作系统数据集中备份和智能恢复管理。

THE VRTS数据备份与恢复系统包括的各个功能模块可独立运行在不同的计算机上,各个模块功能描述如下:

## 界面管理
提供图形化的界面供用户管理和角色管理、任务监控、介质服务、介质管理、策略管理、恢复管理、报表管理、日志管理、客户端、系统设置等。

## 备份服务器
主要用于备份（恢复）作业的调度和执行；向管理界面发警报生成日志等。
## 存储服务器
主要实现备份介质的读取和写入，接收备份数据,并写入磁带、硬盘、光介质等；从磁带、硬盘、光介质读取数据，发送恢复数据。
## 备份客户端服务器
安装于被备份的设备上，执行备份服务器程序对远程服务器和工作站的查询，使备份服务器能访问工作站的目录和驱动器，具体执行服务器和工作站的文件系统备份和恢复任务，执行备份数据压缩和恢复数据解压缩功能，接收数据库备份指令，并发送到相应数据库代理和其它代理程序通信并传递备份和恢复的数据。
## 应用代理
与备份客户端代理安装在同一台计算机上，主要功能包括：通过数据源的备份接口获取需要备份的数据；通过数据源的备份接口恢复数据，与备份客户端代理通信并接收备份和恢复指令和数据流。
## 异地备份
THE VRTS数据备份与恢复系统通过独有的备份网关（转存天华星航远程存储服务）技术,可以方便地把局域网内禁止连接外网的计算机的数据，直接往异地备份。

