webpackJsonp([10],{

/***/ 1538:
/***/ (function(module, exports, __webpack_require__) {

module.exports = { "default": __webpack_require__(1596), __esModule: true };

/***/ }),

/***/ 1596:
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(1597);
module.exports = __webpack_require__(31).Object.assign;


/***/ }),

/***/ 1597:
/***/ (function(module, exports, __webpack_require__) {

// ******** Object.assign(target, source)
var $export = __webpack_require__(43);

$export($export.S + $export.F, 'Object', { assign: __webpack_require__(1598) });


/***/ }),

/***/ 1598:
/***/ (function(module, exports, __webpack_require__) {

"use strict";

// ******** Object.assign(target, source, ...)
var DESCRIPTORS = __webpack_require__(58);
var getKeys = __webpack_require__(212);
var gOPS = __webpack_require__(320);
var pIE = __webpack_require__(213);
var toObject = __webpack_require__(133);
var IObject = __webpack_require__(319);
var $assign = Object.assign;

// should work with symbols and should have deterministic property order (V8 bug)
module.exports = !$assign || __webpack_require__(114)(function () {
  var A = {};
  var B = {};
  // eslint-disable-next-line no-undef
  var S = Symbol();
  var K = 'abcdefghijklmnopqrst';
  A[S] = 7;
  K.split('').forEach(function (k) { B[k] = k; });
  return $assign({}, A)[S] != 7 || Object.keys($assign({}, B)).join('') != K;
}) ? function assign(target, source) { // eslint-disable-line no-unused-vars
  var T = toObject(target);
  var aLen = arguments.length;
  var index = 1;
  var getSymbols = gOPS.f;
  var isEnum = pIE.f;
  while (aLen > index) {
    var S = IObject(arguments[index++]);
    var keys = getSymbols ? getKeys(S).concat(getSymbols(S)) : getKeys(S);
    var length = keys.length;
    var j = 0;
    var key;
    while (length > j) {
      key = keys[j++];
      if (!DESCRIPTORS || isEnum.call(S, key)) T[key] = S[key];
    }
  } return T;
} : $assign;


/***/ }),

/***/ 1617:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


exports.__esModule = true;

var _assign = __webpack_require__(1538);

var _assign2 = _interopRequireDefault(_assign);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

exports.default = _assign2.default || function (target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i];

    for (var key in source) {
      if (Object.prototype.hasOwnProperty.call(source, key)) {
        target[key] = source[key];
      }
    }
  }

  return target;
};

/***/ }),

/***/ 1640:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});
exports.onMount = exports.getCasCcPool = exports.getUisCcPool = exports.getUisvswitch = exports.getVmwareMode = exports.getVmwareStore = exports.getVmwareNetwork = exports.getVmwareHost = exports.getDataCenter = exports.getVswitch = exports.getCasPool = exports.getCasHost = exports.getHostPool = exports.getUisPool = exports.getHost = exports.getAddress = exports.getProtocol = exports.getExample = exports.getClientList = undefined;

var _request = __webpack_require__(316);

var _request2 = _interopRequireDefault(_request);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

var getClientList = exports.getClientList = function getClientList(type) {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/clients?nums=0' + '&restype=' + type
	});
};
var getExample = exports.getExample = function getExample(client, type) {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/client/agent/instances?cid=' + client + '&type=' + type
	});
};
var getProtocol = exports.getProtocol = function getProtocol() {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/mount/protocol'
	});
};
var getAddress = exports.getAddress = function getAddress(client, type) {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/client/agent/instances?cid=' + client + '&type=' + type
	});
};
var getHost = exports.getHost = function getHost(host, user, password) {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/h3c/uis/hosts?host=' + host + '&user=' + user + '&password=' + password
	});
};
var getUisPool = exports.getUisPool = function getUisPool(host, user, password, hpid) {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/h3c/uis/clusters?host=' + host + '&user=' + user + '&password=' + password + '&hpId=' + hpid
	});
};
var getHostPool = exports.getHostPool = function getHostPool(host, instanceId) {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/h3c/cas/hostpools?host=' + host + '&instance=' + instanceId
	});
};
var getCasHost = exports.getCasHost = function getCasHost(host, instanceId, hpId, ClusterId) {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/h3c/cas/hosts?host=' + host + '&instance=' + instanceId + '&hpId=' + hpId + '&ClusterId=' + ClusterId
	});
};

var getCasPool = exports.getCasPool = function getCasPool(host, instance, hostId) {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/h3c/cas/clusters?host=' + host + '&instance=' + instance + '&hpId=' + hostId
	});
};

var getVswitch = exports.getVswitch = function getVswitch(host, instanceId, hostId) {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/h3c/cas/host/vswitch?host=' + host + '&instance=' + instanceId + '&hostId=' + hostId
	});
};

var getDataCenter = exports.getDataCenter = function getDataCenter(instance) {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/vmware/datacenter?instance=' + instance
	});
};

var getVmwareHost = exports.getVmwareHost = function getVmwareHost(instance, dc) {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/vmware/hosts?instance=' + instance + '&dc=' + dc
	});
};

var getVmwareNetwork = exports.getVmwareNetwork = function getVmwareNetwork(instance, dc, hosts) {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/vmware/network?instance=' + instance + '&dc=' + dc + '&host=' + hosts
	});
};

var getVmwareStore = exports.getVmwareStore = function getVmwareStore(instance, dc, hosts) {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/vmware/datastore?instance=' + instance + '&dc=' + dc + '&host=' + hosts
	});
};
var getVmwareMode = exports.getVmwareMode = function getVmwareMode() {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/vmware/diskmode'
	});
};

var getUisvswitch = exports.getUisvswitch = function getUisvswitch(host, user, password, hostId) {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/h3c/uis/vswitch?host=' + host + '&user=' + user + '&password=' + password + '&hostId=' + hostId
	});
};

var getUisCcPool = exports.getUisCcPool = function getUisCcPool(host, user, password, hostId) {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/h3c/uis/host/splist?host=' + host + '&user=' + user + '&password=' + password + '&hostId=' + hostId
	});
};

var getCasCcPool = exports.getCasCcPool = function getCasCcPool(host, instanceId, hostId) {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/h3c/cas/host/splist?host=' + host + '&instance=' + instanceId + '&hostId=' + hostId
	});
};

var onMount = exports.onMount = function onMount(data) {
	return (0, _request2.default)({
		method: 'POST',
		url: '/rest-ful/v3.0/zvolume/mount',
		data: data
	});
};

/***/ }),

/***/ 1714:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getSystemLicense = exports.upCatalog = exports.getConlogSetInfo = exports.getDevice = exports.onceInspecct = exports.getInspecctRes = exports.getPutSet = exports.savePutSet = exports.getInspectioList = exports.sendEmail = undefined;

var _request = __webpack_require__(316);

var _request2 = _interopRequireDefault(_request);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

var sendEmail = exports.sendEmail = function sendEmail(data) {
    return (0, _request2.default)({
        method: 'GET',
        url: '/rest-ful/v3.0/system/email/send'
    });
};

var getInspectioList = exports.getInspectioList = function getInspectioList(curentPage, Nums) {
    return (0, _request2.default)({
        method: 'GET',
        url: '/rest-ful/v3.0/report/inspect?pageno=' + curentPage + '&nums=' + Nums
    });
};
var savePutSet = exports.savePutSet = function savePutSet(data) {
    return (0, _request2.default)({
        method: 'put',
        url: '/rest-ful/v3.0/system/param',
        data: data
    });
};

var getPutSet = exports.getPutSet = function getPutSet() {
    return (0, _request2.default)({
        method: 'get',
        url: '/rest-ful/v3.0/system/param'
    });
};

var getInspecctRes = exports.getInspecctRes = function getInspecctRes(id) {
    return (0, _request2.default)({
        method: 'get',
        url: '/rest-ful/v3.0/report/inspect/detail/' + id
    });
};

var onceInspecct = exports.onceInspecct = function onceInspecct(val) {
    return (0, _request2.default)({
        method: 'get',
        url: '/rest-ful/v3.0/system/inspect/manual?start_time=' + val
    });
};

var getDevice = exports.getDevice = function getDevice() {
    return (0, _request2.default)({
        method: 'get',
        url: 'rest-ful/v3.0/devices?type=0'
    });
};

var getConlogSetInfo = exports.getConlogSetInfo = function getConlogSetInfo() {
    return (0, _request2.default)({
        method: 'get',
        url: 'rest-ful/v3.0/catalog/config'
    });
};

var upCatalog = exports.upCatalog = function upCatalog(data) {
    return (0, _request2.default)({
        method: 'post',
        url: '/rest-ful/v3.0/catalog/config',
        data: data
    });
};

var getSystemLicense = exports.getSystemLicense = function getSystemLicense() {
    return (0, _request2.default)({
        method: 'get',
        url: '/rest-ful/v3.0/system/license'
    });
};

/***/ }),

/***/ 2147:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});
exports.getPlaceonfileData = exports.getVolpools = exports.getZvolumeTableData = exports.getZvolumeRowData = exports.getZvolumeTree = exports.unmountFun = exports.copyFun = exports.getDeviceList = exports.getNoFileTree = exports.getFileTree = undefined;

var _request = __webpack_require__(316);

var _request2 = _interopRequireDefault(_request);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

var getFileTree = exports.getFileTree = function getFileTree(data) {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/zvolume/browse/?device=' + data.dev_id + '&path=' + data.dev_name + '/' + data.parent + '@' + data.names + '/' + data.path
	});
};
var getNoFileTree = exports.getNoFileTree = function getNoFileTree(data) {
	return (0, _request2.default)({
		method: 'GET',
		url: 'rest-ful/v3.0/zvolume/browse/' + data.id + '?volume=' + data.volume_id + '&path=' + data.path
	});
};

var getDeviceList = exports.getDeviceList = function getDeviceList() {
	return (0, _request2.default)({
		method: 'GET',
		url: 'rest-ful/v3.0/devices?cdm=1'
	});
};
var copyFun = exports.copyFun = function copyFun(data) {
	return (0, _request2.default)({
		method: 'POST',
		url: 'rest-ful/v3.0/zvolume/copy',
		data: data
	});
};
var unmountFun = exports.unmountFun = function unmountFun(data) {
	return (0, _request2.default)({
		method: 'POST',
		url: 'rest-ful/v3.0/zvolume/unmount',
		data: data
	});
};

var getZvolumeTree = exports.getZvolumeTree = function getZvolumeTree() {
	return (0, _request2.default)({
		method: 'GET',
		url: 'rest-ful/v3.0/zvolume/buildzvolumetree'
	});
};
var getZvolumeRowData = exports.getZvolumeRowData = function getZvolumeRowData(rowData) {
	return (0, _request2.default)({
		method: 'GET',
		url: 'rest-ful/v3.0/zvolume/volumelist/' + rowData.policy
	});
};

var getZvolumeTableData = exports.getZvolumeTableData = function getZvolumeTableData(rowData) {
	return (0, _request2.default)({
		method: 'GET',
		url: 'rest-ful/v3.0/zvolume/snapshot/list/' + rowData.volume
	});
};

var getVolpools = exports.getVolpools = function getVolpools(data) {
	return (0, _request2.default)({
		method: 'GET',
		url: 'rest-ful/v3.0/volpools'
	});
};

var getPlaceonfileData = exports.getPlaceonfileData = function getPlaceonfileData(data) {
	return (0, _request2.default)({
		method: 'POST',
		url: 'rest-ful/v3.0/zvolume/archived',
		data: data
	});
};

/***/ }),

/***/ 2615:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_vmOpt_vue__ = __webpack_require__(2816);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_vmOpt_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_vmOpt_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_vmOpt_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_vmOpt_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_26d08418_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_vmOpt_vue__ = __webpack_require__(3732);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_26d08418_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_vmOpt_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_26d08418_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_vmOpt_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3730)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = null
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_vmOpt_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_26d08418_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_vmOpt_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/dupMag/components/vmOpt.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-26d08418", Component.options)
  } else {
    hotAPI.reload("data-v-26d08418", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ }),

/***/ 2616:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_uisOpt_vue__ = __webpack_require__(2817);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_uisOpt_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_uisOpt_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_uisOpt_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_uisOpt_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_27e7d58c_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_uisOpt_vue__ = __webpack_require__(3735);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_27e7d58c_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_uisOpt_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_27e7d58c_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_uisOpt_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3733)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = null
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_uisOpt_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_27e7d58c_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_uisOpt_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/dupMag/components/uisOpt.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-27e7d58c", Component.options)
  } else {
    hotAPI.reload("data-v-27e7d58c", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ }),

/***/ 2617:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_casOpt_vue__ = __webpack_require__(2818);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_casOpt_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_casOpt_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_casOpt_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_casOpt_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_589a6956_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_casOpt_vue__ = __webpack_require__(3738);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_589a6956_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_casOpt_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_589a6956_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_casOpt_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3736)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = null
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_casOpt_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_589a6956_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_casOpt_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/dupMag/components/casOpt.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-589a6956", Component.options)
  } else {
    hotAPI.reload("data-v-589a6956", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ }),

/***/ 2814:
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function($) {

Object.defineProperty(exports, "__esModule", {
	value: true
});

var _extends2 = __webpack_require__(1617);

var _extends3 = _interopRequireDefault(_extends2);

var _regenerator = __webpack_require__(537);

var _regenerator2 = _interopRequireDefault(_regenerator);

var _asyncToGenerator2 = __webpack_require__(538);

var _asyncToGenerator3 = _interopRequireDefault(_asyncToGenerator2);

var _defineProperty2 = __webpack_require__(89);

var _defineProperty3 = _interopRequireDefault(_defineProperty2);

var _methods;

var _util = __webpack_require__(16);

var _util2 = _interopRequireDefault(_util);

var _H3cUis = __webpack_require__(3727);

var _H3cUis2 = _interopRequireDefault(_H3cUis);

var _H3cCas = __webpack_require__(3740);

var _H3cCas2 = _interopRequireDefault(_H3cCas);

var _Vmware = __webpack_require__(3744);

var _Vmware2 = _interopRequireDefault(_Vmware);

var _vTree = __webpack_require__(3748);

var _vTree2 = _interopRequireDefault(_vTree);

var _index = __webpack_require__(210);

var _index2 = __webpack_require__(2147);

var _index3 = __webpack_require__(1714);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
	components: {
		H3cUis: _H3cUis2.default,
		H3cCas: _H3cCas2.default,
		Vmware: _Vmware2.default,
		vTree: _vTree2.default
	},
	data: function data() {
		var _this2 = this,
		    _ref12;

		return _ref12 = {
			deviceArr: [],
			volpoolsArr: [],
			placeonfileDate: {
				arc_pool: '',
				arc_device: '',
				snapname: '',
				dataset: ''
			},
			ruleValidate: {
				arc_pool: [{ required: true, message: '请选择归档介质池', trigger: 'blur' }],
				arc_device: [{ required: true, message: '请选择归档设备', trigger: 'blur' }]
			},
			filterText: '',
			copyResTreeData: [],
			expandshow: false,
			rowfbdata: {},
			newResType: '',
			selectTree: {},
			zvoldata: [],
			noImgUrl: __webpack_require__(317),
			queryclient: [],
			tableRow: {},
			scntimeshow: '',
			restoreLogsVal: '2',
			loading: false,
			ztreeArray: [],
			cur: 0,
			poollist: [],
			uispoollist: [],
			restart: false,
			platformrestype: '',
			platformresname: 'H3C CAS',
			vswitchlist: [],
			yjlist: [{
				id: 1441792,
				name: 'H3C CAS'
			}, {
				id: 1507328,
				name: 'H3C UIS'
			}, {
				id: 327680,
				name: 'VMWARE'
			}],
			bgid: '',
			osType: 1,
			expireidarr: [],
			expiremodel: false,
			storelist: [],
			gzformRule: {
				id: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择实例名',
					trigger: 'change'
				}],
				dc: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择数据中心',
					trigger: 'change'
				}],
				hosts: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择主机',
					trigger: 'change'
				}],
				network: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择网段',
					trigger: 'change'
				}],
				name: [{ required: true, message: '请输入重命名', trigger: 'blur' }],
				path: [{ required: true, message: '请输入路径', trigger: 'blur' }],
				client: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择客户端',
					trigger: 'change'
				}],
				agre: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择协议',
					trigger: 'change'
				}],
				store: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择存储',
					trigger: 'change'
				}],
				diskmode: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择模式',
					trigger: 'change'
				}],
				datapath: [{ required: true, message: '数据文件路径', trigger: 'blur' }],
				cliName: [{ required: true, message: '实例名', trigger: 'blur' }],
				scn: [{ required: true, message: '请输入scn', trigger: 'blur' }]
			},
			tilbt: '挂载',
			str: '',
			setting: {
				check: {
					enable: true
				},
				view: {
					selectedMulti: false
				},
				callback: {
					onClick: this.zTreeOnClick,
					onCheck: this.zTreeOnCheck
				}
			},
			glancePath: '',
			db2id: '',
			db2List: [],
			stecur: 0,
			policytotal: '',
			gztotal: '',
			curentPage: 1,
			pageSize: 10,
			sqltype: '',
			typedis: false,
			statusSelect: [],
			cliSelect: [],
			restypeSelect: []
		}, (0, _defineProperty3.default)(_ref12, 'statusSelect', []), (0, _defineProperty3.default)(_ref12, 'query', {
			cliname: '',
			policyname: '',
			restype: '',
			starttime: '',
			endtime: '',
			ip: ''
		}), (0, _defineProperty3.default)(_ref12, 'tooltipdata', ''), (0, _defineProperty3.default)(_ref12, 'tooltipdefint', '选择开始和结束时间'), (0, _defineProperty3.default)(_ref12, 'tooltipdataxt', ''), (0, _defineProperty3.default)(_ref12, 'tooltipdefintxt', '选择开始和结束时间'), (0, _defineProperty3.default)(_ref12, 'vmwarecol', [{
			type: 'selection',
			width: 60,
			align: 'center'
		}, {
			title: '机器名',
			key: 'name'
		}]), (0, _defineProperty3.default)(_ref12, 'uisCol', [{
			type: 'selection',
			width: 60,
			align: 'center'
		}, {
			title: '机器名',
			key: 'name'
		}]), (0, _defineProperty3.default)(_ref12, 'vamTableList', []), (0, _defineProperty3.default)(_ref12, 'casTableList', []), (0, _defineProperty3.default)(_ref12, 'uisTableList', []), (0, _defineProperty3.default)(_ref12, 'vamsldata', []), (0, _defineProperty3.default)(_ref12, 'sqlsldata', []), (0, _defineProperty3.default)(_ref12, 'dmsldata', []), (0, _defineProperty3.default)(_ref12, 'h3ccassldata', []), (0, _defineProperty3.default)(_ref12, 'h3cuissldata', []), (0, _defineProperty3.default)(_ref12, 'centerlist', []), (0, _defineProperty3.default)(_ref12, 'hostslist', []), (0, _defineProperty3.default)(_ref12, 'networklist', []), (0, _defineProperty3.default)(_ref12, 'vamlist', []), (0, _defineProperty3.default)(_ref12, 'resPost', []), (0, _defineProperty3.default)(_ref12, 'optionArr', []), (0, _defineProperty3.default)(_ref12, 'modelist', []), (0, _defineProperty3.default)(_ref12, 'tableHeight', 0), (0, _defineProperty3.default)(_ref12, 'taskType', '挂载数据库'), (0, _defineProperty3.default)(_ref12, 'oracletasttype', '1'), (0, _defineProperty3.default)(_ref12, 'oracleResTime', ''), (0, _defineProperty3.default)(_ref12, 'mountType', ''), (0, _defineProperty3.default)(_ref12, 'resTypeVal', ''), (0, _defineProperty3.default)(_ref12, 'parentid', ''), (0, _defineProperty3.default)(_ref12, 'gzform', {
			id: '',
			client: '',
			agre: '',
			path: '',
			cliName: '',
			slName: '',
			host: '',
			user: '',
			hosts: '',
			dc: '',
			name: '',
			network: '',
			datapath: '',
			dataStroe: '',
			diskmode: '',
			desc: '',
			scn: '',

			'case': '',
			h3ccasuser: '',
			password: '',
			addr: '',
			pool: '',
			uispool: '',
			hostId: '',
			hostName: '',
			clusterId: '',
			cashost: '',
			hpid: '',
			caspool: '',
			distri: '',
			rename: '',
			vswitchid: '',
			vswitchname: ''
		}), (0, _defineProperty3.default)(_ref12, 'agreArr', []), (0, _defineProperty3.default)(_ref12, 'clientSelect', []), (0, _defineProperty3.default)(_ref12, 'onemodel', false), (0, _defineProperty3.default)(_ref12, 'twomodel', false), (0, _defineProperty3.default)(_ref12, 'dupdata', []), (0, _defineProperty3.default)(_ref12, 'onecol', [{
			title: '策略',
			key: 'policy',
			render: function render(h, _ref) {
				var row = _ref.row;

				if (row.status === '过期') {
					return h('span', {
						style: {
							color: '#ccc'
						}
					}, [h('Tooltip', {
						props: {
							placement: 'top',
							transfer: true
						}
					}, [_this2.onTip(row.policy), h('span', {
						slot: 'content',
						style: {
							whiteSpace: 'normal'
						}
					}, row.policy)])]);
				} else {
					return h('span', {}, [h('Tooltip', {
						props: {
							placement: 'top',
							transfer: true
						}
					}, [_this2.onTip(row.policy), h('span', {
						slot: 'content',
						style: {
							whiteSpace: 'normal'
						}
					}, row.policy)])]);
				}
			}
		}, {
			title: '资源类型',
			key: 'resource_type',
			width: 130,
			render: function render(h, _ref2) {
				var row = _ref2.row;

				return [h('svg', {
					'class': 'icon',
					ariaHidden: 'true',
					style: {
						marginRight: '8px'
					},
					domProps: {
						innerHTML: (0, _index.getNewIcon)(row)
					}
				}), [h('Tooltip', {
					props: {
						placement: 'top',
						transfer: true
					}
				}, [_this2.onTip(row.resource_type), h('span', {
					slot: 'content',
					style: {
						whiteSpace: 'normal'
					}
				}, row.resource_type)])]];
			}
		}, {
			title: '备份类型',
			key: 'backup_type',
			render: function render(h, _ref3) {
				var row = _ref3.row;

				if (row.status === '过期') {
					return h('span', {
						style: {
							color: '#ccc'
						}
					}, row.backup_type);
				} else {
					return h('span', {}, [h('Tooltip', {
						props: {
							placement: 'top',
							transfer: true
						}
					}, [_this2.onTip(row.backup_type), h('span', {
						slot: 'content',
						style: {
							whiteSpace: 'normal'
						}
					}, row.backup_type)])]);
				}
			}
		}, {
			title: '客户端',
			key: 'client',
			render: function render(h, _ref4) {
				var row = _ref4.row;

				if (row.status === '过期') {
					return h('div', {
						style: {
							color: '#ccc'
						}
					}, [h('Tooltip', {
						props: {
							placement: 'top',
							transfer: true
						}
					}, [_this2.onTip(row.client), h('span', {
						slot: 'content',
						style: {
							whiteSpace: 'normal'
						}
					}, row.client)])]);
				} else {
					return h('div', [h('Tooltip', {
						props: {
							placement: 'top',
							transfer: true
						}
					}, [_this2.onTip(row.client), h('span', {
						slot: 'content',
						style: {
							whiteSpace: 'normal'
						}
					}, row.client)])]);
				}
			}
		}, {
			title: 'IP',
			key: 'ip',
			render: function render(h, _ref5) {
				var row = _ref5.row;

				if (row.status === '过期') {
					return h('div', {
						style: {
							color: '#ccc'
						}
					}, [h('Tooltip', {
						props: {
							placement: 'top',
							transfer: true
						}
					}, [_this2.onTip(row.ip), h('span', {
						slot: 'content',
						style: {
							whiteSpace: 'normal'
						}
					}, row.ip)])]);
				} else {
					return h('div', [h('Tooltip', {
						props: {
							placement: 'top',
							transfer: true
						}
					}, [_this2.onTip(row.ip), h('span', {
						slot: 'content',
						style: {
							whiteSpace: 'normal'
						}
					}, row.ip)])]);
				}
			}
		}, {
			title: '创建时间',
			key: 'create_time',
			render: function render(h, _ref6) {
				var row = _ref6.row;

				if (row.status === '过期') {
					return h('div', {
						style: {
							color: '#ccc'
						}
					}, [h('Tooltip', {
						props: {
							placement: 'top',
							transfer: true
						}
					}, [_this2.onTip(row.create_time), h('span', {
						slot: 'content',
						style: {
							whiteSpace: 'normal'
						}
					}, row.create_time)])]);
				} else {
					return h('div', [h('Tooltip', {
						props: {
							placement: 'top',
							transfer: true
						}
					}, [_this2.onTip(row.create_time), h('span', {
						slot: 'content',
						style: {
							whiteSpace: 'normal'
						}
					}, row.create_time)])]);
				}
			}
		}, {
			title: '快照名称',
			key: 'name',
			render: function render(h, _ref7) {
				var row = _ref7.row;

				if (row.status === '过期') {
					return h('div', {
						style: {
							color: '#ccc'
						}
					}, [h('Tooltip', {
						props: {
							placement: 'top',
							transfer: true
						}
					}, [_this2.onTip(row.name), h('span', {
						slot: 'content',
						style: {
							whiteSpace: 'normal'
						}
					}, row.name)])]);
				} else {
					return h('div', [h('Tooltip', {
						props: {
							placement: 'top',
							transfer: true
						}
					}, [_this2.onTip(row.name), h('span', {
						slot: 'content',
						style: {
							whiteSpace: 'normal'
						}
					}, row.name)])]);
				}
			}
		}, {
			title: '卷名称',
			key: 'parent',
			render: function render(h, _ref8) {
				var row = _ref8.row;

				if (row.status === '过期') {
					return h('div', {
						style: {
							color: '#ccc'
						}
					}, [h('Tooltip', {
						props: {
							placement: 'top',
							transfer: true
						}
					}, [_this2.onTip(row.parent), h('span', {
						slot: 'content',
						style: {
							whiteSpace: 'normal'
						}
					}, row.parent)])]);
				} else {
					return h('div', [h('Tooltip', {
						props: {
							placement: 'top',
							transfer: true
						}
					}, [_this2.onTip(row.parent), h('span', {
						slot: 'content',
						style: {
							whiteSpace: 'normal'
						}
					}, row.parent)])]);
				}
			}
		}, {
			title: '过期时间',
			key: 'expired_time',
			render: function render(h, _ref9) {
				var row = _ref9.row;

				if (row.status === '过期') {
					return h('div', {
						style: {
							color: '#ccc'
						}
					}, [h('Tooltip', {
						props: {
							placement: 'top',
							transfer: true
						}
					}, [_this2.onTip(row.expired_time), h('span', {
						slot: 'content',
						style: {
							whiteSpace: 'normal'
						}
					}, row.expired_time)])]);
				} else {
					return h('div', [h('Tooltip', {
						props: {
							placement: 'top',
							transfer: true
						}
					}, [_this2.onTip(row.expired_time), h('span', {
						slot: 'content',
						style: {
							whiteSpace: 'normal'
						}
					}, row.expired_time)])]);
				}
			}
		}, {
			title: '存储池',
			key: 'dev_name',
			render: function render(h, _ref10) {
				var row = _ref10.row;

				if (row.status === '过期') {
					return h('div', {
						style: {
							color: '#ccc'
						}
					}, [h('Tooltip', {
						props: {
							placement: 'top',
							transfer: true
						}
					}, [_this2.onTip(row.dev_name), h('span', {
						slot: 'content',
						style: {
							whiteSpace: 'normal'
						}
					}, row.dev_name)])]);
				} else {
					return h('div', [h('Tooltip', {
						props: {
							placement: 'top',
							transfer: true
						}
					}, [_this2.onTip(row.dev_name), h('span', {
						slot: 'content',
						style: {
							whiteSpace: 'normal'
						}
					}, row.dev_name)])]);
				}
			}
		}, {
			title: '状态',
			key: 'status',
			render: function render(h, _ref11) {
				var row = _ref11.row;

				if (row.status === '正常') {
					return h('span', {
						style: {
							color: 'green'
						}
					}, '正常');
				} else if (row.status === '过期') {
					return h('span', {
						style: {
							color: '#ccc'
						}
					}, '过期');
				}
			}
		}, {
			title: '操作',
			key: 'operation',
			render: function render(h, params) {
				return h('div', {
					'class': {
						role: true
					},
					style: {
						display: 'flex',
						justifyContent: 'center',
						alignItems: 'center'
					}
				}, [_this2.hasPrivilege(_this2.getPower.VRTS_FUNC_EXPIRED_COPY) ? h('div', {
					style: {
						marginRight: '5px',

						width: '30px',
						height: '30px',
						borderRadius: '3px',
						textAlign: 'center'
					}
				}, [h('span', {
					'class': 'iconfont',
					style: {
						fontSize: '20px',
						color: '#ff9130',
						cursor: 'pointer'
					},
					domProps: {
						innerHTML: '&#xe618;'
					},
					on: {
						click: function click() {
							console.log('params====', params);
							if (params.row.status == '正常') {
								_this2.expiremodel = true;
							}
						}
					},
					directives: [{
						name: 'tooltip',
						value: '过期'
					}]
				})]) : '', h('div', {
					style: {
						marginRight: '5px',
						width: '30px',
						height: '30px',
						borderRadius: '3px',
						textAlign: 'center'
					}
				}, [h('span', {
					'class': 'iconfont',
					style: {
						fontSize: '25px',
						color: '#ff9130',
						cursor: 'pointer'
					},
					domProps: {
						innerHTML: '&#xe6b8;'
					},
					on: {
						click: function click() {
							console.log('params====', params);
							if (params.row.status == '正常') {
								_this2.getDevice();
								_this2.getVolpoolsFun();
								_this2.placeonfileDate.snapname = params.row.name;
								_this2.placeonfileDate.dataset = params.row.parent;
								_this2.placeonfileModel = true;
							}
						}
					},
					directives: [{
						name: 'tooltip',
						value: '归档'
					}]
				})])]);
			}
		}]), (0, _defineProperty3.default)(_ref12, 'placeonfileModel', false), (0, _defineProperty3.default)(_ref12, 'volid', ''), (0, _defineProperty3.default)(_ref12, 'setRowData', {}), (0, _defineProperty3.default)(_ref12, 'example', []), (0, _defineProperty3.default)(_ref12, 'hostlist', []), (0, _defineProperty3.default)(_ref12, 'poollist', []), (0, _defineProperty3.default)(_ref12, 'casHostList', []), (0, _defineProperty3.default)(_ref12, 'casPoolList', []), (0, _defineProperty3.default)(_ref12, 'fictitious', false), (0, _defineProperty3.default)(_ref12, 'distributor', [{
			name: '延迟置零',
			id: 0
		}, {
			name: '置零',
			id: 1
		}, {
			name: '精简',
			id: 2
		}]), _ref12;
	},
	created: function created() {
		_util2.default.restfullCall('/rest-ful/v3.0/mount/protocol', null, 'get', this.agreData);

		_util2.default.restfullCall('rest-ful/v3.0/mountedvolume/list', null, 'get', this.getgzData);

		_util2.default.restfullCall('/rest-ful/v3.0/resourcetype?cdm=1', null, 'get', this.restypeData);

		this.getClient();
	},
	mounted: function mounted() {
		window.addEventListener('resize', this.getTableHeight);
		this.getTableHeight();
		this.getZvolumeTreeFun();
	},

	computed: {
		getPower: function getPower() {
			return this.$store.state.power.module;
		}
	},
	methods: (_methods = {
		getDevice: function getDevice() {
			var _this3 = this;

			(0, _index3.getDevice)().then(function (res) {
				_this3.deviceArr = res.data;
			});
		},
		getVolpoolsFun: function getVolpoolsFun() {
			var _this4 = this;

			(0, _index2.getVolpools)().then(function (res) {
				_this4.volpoolsArr = res.data;
			});
		},
		onPlaceonfile: function onPlaceonfile() {
			var _this5 = this;

			console.log('----', this.$isNull(this.placeonfileDate.arc_device));
			if (this.$isNull(this.placeonfileDate.arc_device)) {
				this.$Message.error('请选择归档设备');
				return;
			}
			if (this.$isNull(this.placeonfileDate.arc_pool)) {
				this.$Message.error('请选择归档介质池');
				return;
			}
			(0, _index2.getPlaceonfileData)(this.placeonfileDate).then(function (res) {
				_this5.placeonfileModel = false;
				if (res.code == 0) {
					_this5.$Message.success(res.message);
					_this5.placeonfileDate.arc_device = '';
					_this5.placeonfileDate.arc_pool = '';
				} else {
					_this5.$Message.error(res.message);
				}
			});
		},
		getClient: function (_getClient) {
			function getClient() {
				return _getClient.apply(this, arguments);
			}

			getClient.toString = function () {
				return _getClient.toString();
			};

			return getClient;
		}(function () {
			var _this6 = this;

			getClient().then(function (res) {
				console.log('客户端', res);
				_this6.clientArr = res.data;
			});
		}),
		getZvolumeTreeFun: function getZvolumeTreeFun() {
			var _this7 = this;

			(0, _index2.getZvolumeTree)().then(function (res) {
				if (!_this7.$isNull(res.data)) {
					res.data.forEach(function (item) {
						if (!_this7.$isNull(item.children)) {
							item.children.forEach(function (child) {
								if (!_this7.$isNull(child.children)) {
									child.children.forEach(function (c) {
										if (c.node_type == 64) {
											c.expand = true;
										}
									});
								}
							});
						}
					});
				}
				_this7.zvoldata = res.data;
				_this7.copyResTreeData = _this7.zvoldata;
			});
		},
		getRowTree: function getRowTree(rowData) {},
		renderContent: function renderContent(h, _ref13) {
			var _this8 = this;

			var root = _ref13.root,
			    node = _ref13.node,
			    data = _ref13.data;

			var textsTitle = data.title;
			if (data.title != null) {
				if (data.title.length > 16) {
					textsTitle = data.title.slice(0, 16) + '...';
				} else {
					textsTitle = data.title;
				}
			}
			var rowConut = node.node;
			return h('span', {
				style: {
					display: 'inline-block',
					width: '100%',
					padding: '5px',

					background: data.isSelectStatus ? '#ffecde' : null,
					cursor: 'pointer'
				}
			}, [h('span', [h('svg', {
				'class': 'icon',
				ariaHidden: 'true',
				style: {
					marginRight: '8px'
				},
				domProps: {
					innerHTML: (0, _index.getNewIcon)(rowConut)
				}
			}), h('span', {
				on: {
					click: function click() {
						_this8.onResTree(rowConut);
						_this8.addSelectBackground(data);
					}
				}
			}, [h('Tooltip', {
				props: {
					placement: 'top',
					transfer: true
				}
			}, [, textsTitle, h('span', {
				slot: 'content',
				style: {
					fontSize: '12px',
					whiteSpace: 'normal',
					cursor: 'pointer'
				},
				domProps: {
					innerHTML: data.title
				}
			})])], textsTitle)])]);
		},
		onResTree: function onResTree(rowData) {
			this.rowfbdata = rowData;

			this.newResType = rowData.res_type;
			var val = [];
			val.push(rowData);
			this.selectTree = val.length && val[0];

			if (rowData.node_type == 64) {
				rowData.expand = !this.expandshow;

				this.getZvolumeRowDataFun(rowData);
			}
			if (rowData.node_type == 32) {
				this.policytotal = 0;
				this.curentPage = 1;
				this.getZvolumeTableDataFun(rowData);
			}
		},
		getZvolumeTableDataFun: function getZvolumeTableDataFun(rowData) {
			var _this9 = this;

			(0, _index2.getZvolumeTableData)(rowData).then(function (res) {
				_this9.loading = true;
				if (res.code == 0) {
					_this9.policytotal = res.data.nums;

					_this9.dupdata = res.data.Records;
					_this9.loading = false;
					_this9.addSelectBackground(_this9.rowfbdata);
				}
			});
		},
		getZvolumeRowDataFun: function getZvolumeRowDataFun(rowData) {
			var _this10 = this;

			(0, _index2.getZvolumeRowData)(rowData).then(function (res) {
				var newData = res.data;
				_this10.updataTree(rowData, newData, _this10.zvoldata);
			});
		},
		updataTree: function () {
			var _ref14 = (0, _asyncToGenerator3.default)(_regenerator2.default.mark(function _callee(rowData, res, nodes) {
				var _this11 = this;

				return _regenerator2.default.wrap(function _callee$(_context) {
					while (1) {
						switch (_context.prev = _context.next) {
							case 0:
								if (!this.$isNull(nodes)) {
									nodes.forEach(function (node, i) {
										if (node.children && node.children.length > 0) {
											node.children.forEach(function (child) {
												_this11.updataTree(rowData, res, child.children);
											});
										} else {
											rowData.children = res;
										}
									});
								} else {
									rowData.children = res;
								}

							case 1:
							case 'end':
								return _context.stop();
						}
					}
				}, _callee, this);
			}));

			function updataTree(_x, _x2, _x3) {
				return _ref14.apply(this, arguments);
			}

			return updataTree;
		}(),
		addSelectBackground: function () {
			var _ref15 = (0, _asyncToGenerator3.default)(_regenerator2.default.mark(function _callee2(data) {
				return _regenerator2.default.wrap(function _callee2$(_context2) {
					while (1) {
						switch (_context2.prev = _context2.next) {
							case 0:
								this.toggleSelectStatus(this.zvoldata, data.title, data.res_type);

							case 1:
							case 'end':
								return _context2.stop();
						}
					}
				}, _callee2, this);
			}));

			function addSelectBackground(_x4) {
				return _ref15.apply(this, arguments);
			}

			return addSelectBackground;
		}(),
		toggleSelectStatus: function toggleSelectStatus(trees, title, resourceType) {
			var _this12 = this;

			trees.forEach(function (tree) {
				var _this = _this12;

				function traverse(node) {
					if (node.title === title && node.res_type === resourceType && node.children === null) {
						_this.$set(node, 'isSelectStatus', true);
					} else {
						_this.$set(node, 'isSelectStatus', false);
					}

					if (node.children && node.children.length > 0) {
						node.children.forEach(function (child) {
							return traverse(child);
						});
					}
				}

				traverse(tree);
			});
		},
		filterTree: function filterTree() {
			this.zvoldata = this.filterTreeNodes(this.copyResTreeData, this.filterText);
		},
		filterTreeNodes: function filterTreeNodes(arr, val) {
			var _this13 = this;

			var newarr = [];
			arr.forEach(function (item) {
				if (item.children && item.children.length > 0) {
					var children = _this13.filterTreeNodes(item.children, val);
					var obj = (0, _extends3.default)({}, item, {
						children: children
					});
					if (children && children.length) {
						newarr.push(obj);
					} else if (item.title.includes(val)) {
						newarr.push((0, _extends3.default)({}, item));
					}
				} else {
					if (item.title.includes(val)) {
						newarr.push(item);
					}
				}
			});
			return newarr;
		}
	}, (0, _defineProperty3.default)(_methods, 'getClient', function getClient() {
		_util2.default.restfullCall('/rest-ful/v3.0/clients?nums=0', null, 'get', this.clientData);
	}), (0, _defineProperty3.default)(_methods, 'clientData', function clientData(obj) {
		var array = [];
		for (var i = 0; i < obj.data.data.vrts_client_data.length; i++) {
			array.push({
				id: obj.data.data.vrts_client_data[i].id,
				machine: obj.data.data.vrts_client_data[i].machine,
				os_type: obj.data.data.vrts_client_data[i].os_type
			});
		}
		this.queryclient = array;
	}), (0, _defineProperty3.default)(_methods, 'onscntime', function onscntime() {
		if (this.scntimeshow == '1') {
			this.oracleResTime = '';
		} else if (this.scntimeshow == '2') {
			this.gzform.scn = '';
		}
	}), (0, _defineProperty3.default)(_methods, 'clearFun', function clearFun() {
		_util2.default.restfullCall('rest-ful/v3.0/zvolume/list?pageno=1&nums=' + this.pageSize + '&client=' + (this.query.cliname || '') + '&restype=' + (this.query.restype || '') + '&policyname=' + (this.query.policyname || '') + '&starttime=' + (this.query.starttime || '') + '&endtime=' + (this.query.endtime || '') + '&ip=' + (this.query.ip || ''), null, 'get', this.getdupData);
	}), (0, _defineProperty3.default)(_methods, 'onTip', function onTip(val) {
		var rowVal = val;
		if (val != null) {
			if (val.length > 18) {
				rowVal = val.slice(0, 18) + '...';
			} else {
				rowVal = val;
			}
		}
		return rowVal;
	}), (0, _defineProperty3.default)(_methods, 'getResPost', function getResPost(val, valpath) {
		this.resPost = [];
		this.resPost = val;
	}), (0, _defineProperty3.default)(_methods, 'closeDraBox', function closeDraBox() {}), (0, _defineProperty3.default)(_methods, 'closeDrawer', function closeDrawer(val) {
		this.onemodel = val;
	}), (0, _defineProperty3.default)(_methods, 'changeIp', function changeIp(res) {
		this.query.ip = res;

		_util2.default.restfullCall('rest-ful/v3.0/zvolume/list?pageno=1&nums=' + this.pageSize + '&client=' + (this.query.cliname || '') + '&restype=' + (this.query.restype || '') + '&policyname=' + (this.query.policyname || '') + '&starttime=' + (this.query.starttime || '') + '&endtime=' + (this.query.endtime || '') + '&ip=' + (this.query.ip || ''), null, 'get', this.getdupData);
	}), (0, _defineProperty3.default)(_methods, 'getvswitch', function getvswitch() {
		_util2.default.restfullCall('/rest-ful/v3.0//h3c/cas/host/vswitch?host=' + this.gzform.case + '&user=' + this.gzform.h3ccasuser + '&password=' + this.gzform.password + '&hpId=' + this.gzform.hpid, null, 'get', this.vswitchData);
	}), (0, _defineProperty3.default)(_methods, 'vswitchData', function vswitchData(val) {
		this.vswitchlist = val.data.data;
	}), (0, _defineProperty3.default)(_methods, 'onvswitch', function onvswitch(val) {
		this.gzform.vswitchid = val.value;
		this.gzform.vswitchname = val.label;
	}), (0, _defineProperty3.default)(_methods, 'changeyjid', function changeyjid(val) {
		this.platformrestype = val.value;
		this.platformresname = val.label;
		if (val.value == 327680) {}
	}), (0, _defineProperty3.default)(_methods, 'onDistriChange', function onDistriChange(res) {}), (0, _defineProperty3.default)(_methods, 'onCasPoolChange', function onCasPoolChange(res) {}), (0, _defineProperty3.default)(_methods, 'casPoolChange', function casPoolChange(open) {
		if (open == true) {
			this.loadingShowp = true;
			_util2.default.restfullCall('/rest-ful/v3.0/h3c/cas/host/splist?host=' + this.gzform.case + '&user=' + this.gzform.h3ccasuser + '&password=' + this.gzform.password + '&hostId=' + this.gzform.hpid + '&ClusterId=' + this.gzform.clusterId + '&hpId=' + this.gzform.hostId, null, 'get', this.casPoolData);
		}
	}), (0, _defineProperty3.default)(_methods, 'casPoolData', function casPoolData(res) {
		var casPoolL = res.data.data;
		var array = [];
		if (casPoolL.length > 0) {
			this.loadingShowp = false;
		}
		casPoolL.forEach(function (item) {
			array.push({
				freeSize: item.freeSize,
				name: item.name
			});
		});
		this.casPoolList = array;
	}), (0, _defineProperty3.default)(_methods, 'poolChange', function poolChange(open) {
		if (open == true) {
			_util2.default.restfullCall('/rest-ful/v3.0/h3c/cas/clusters?host=' + this.gzform.case + '&user=' + this.gzform.h3ccasuser + '&password=' + this.gzform.password + '&hostId=' + this.gzform.hpid, null, 'get', this.poolData);
		}
	}), (0, _defineProperty3.default)(_methods, 'poolData', function poolData(res) {
		var poolL = res.data.data;
		var array = [];
		if (poolL.length > 0) {
			this.loadingShowp = false;
		}
		poolL.forEach(function (item) {
			array.push({
				name: item.name,
				id: item.id
			});
		});
		this.poollist = array;
	}), (0, _defineProperty3.default)(_methods, 'onPoolChange', function onPoolChange(res) {
		this.gzform.clusterId = res.value;
	}), (0, _defineProperty3.default)(_methods, 'onUisPoolChange', function onUisPoolChange(res) {
		this.gzform.uispool;
	}), (0, _defineProperty3.default)(_methods, 'uisPoolChange', function uisPoolChange(open) {
		if (open == true) {
			this.loadingShowp = true;
			_util2.default.restfullCall('/rest-ful/v3.0/h3c/uis/host/splist?host=' + this.gzform.case + '&user=' + this.gzform.h3ccasuser + '&password=' + this.gzform.password + '&hostId=' + this.gzform.hpid, null, 'get', this.uisPoolData);
		}
	}), (0, _defineProperty3.default)(_methods, 'uisPoolData', function uisPoolData(res) {
		var casPoolL = res.data.data;
		var array = [];
		if (casPoolL.length > 0) {
			this.loadingShowp = false;
		}
		casPoolL.forEach(function (item) {
			array.push({
				freeSize: item.freeSize,
				name: item.name
			});
		});
		this.casPoolList = array;
	}), (0, _defineProperty3.default)(_methods, 'onCasHostChange', function onCasHostChange(res) {
		this.gzform.hpid = res.value;
	}), (0, _defineProperty3.default)(_methods, 'casHostChange', function casHostChange(open) {
		if (open == true) {
			this.loadingShowp = true;
			_util2.default.restfullCall('/rest-ful/v3.0/h3c/cas/hosts?host=' + this.gzform.case + '&user=' + this.gzform.h3ccasuser + '&password=' + this.gzform.password + '&hpId=' + this.gzform.hostId + '&ClusterId=' + this.gzform.clusterId, null, 'get', this.casHostData);
		}
	}), (0, _defineProperty3.default)(_methods, 'casHostData', function casHostData(res) {
		var casHostL = res.data.data;
		var array = [];
		if (casHostL.length > 0) {
			this.loadingShowp = false;
		}
		casHostL.forEach(function (item) {
			array.push({
				name: item.name,
				id: item.id
			});
		});

		this.casHostList = array;
	}), (0, _defineProperty3.default)(_methods, 'onCasHostChange', function onCasHostChange(res) {
		this.gzform.hpid = res.value;
	}), (0, _defineProperty3.default)(_methods, 'onUisHostChange', function onUisHostChange(res) {
		this.gzform.hpid = res.value;
	}), (0, _defineProperty3.default)(_methods, 'uisHostChange', function uisHostChange(open) {
		if (open == true) {
			this.loadingShowp = true;
			_util2.default.restfullCall('/rest-ful/v3.0/h3c/uis/hosts?host=' + this.gzform.case + '&user=' + this.gzform.h3ccasuser + '&password=' + this.gzform.password, null, 'get', this.uisHostData);
		}
	}), (0, _defineProperty3.default)(_methods, 'uisHostData', function uisHostData(res) {
		var casHostL = res.data.data;
		var array = [];
		if (casHostL.length > 0) {
			this.loadingShowp = false;
		}
		casHostL.forEach(function (item) {
			array.push({
				name: item.name,
				id: item.id
			});
		});

		this.casHostList = array;
	}), (0, _defineProperty3.default)(_methods, 'onUisHostChange', function onUisHostChange(res) {
		this.gzform.hpid = res.value;
	}), (0, _defineProperty3.default)(_methods, 'onHostChange', function onHostChange(res) {
		this.gzform.hostId = res.value;
		this.gzform.hostName = res.label;
	}), (0, _defineProperty3.default)(_methods, 'hostChange', function hostChange(open) {
		if (open == true) {
			this.loadingShowh = true;

			_util2.default.restfullCall('/rest-ful/v3.0/h3c/cas/hostpools?host=' + this.gzform.case + '&user=' + this.gzform.h3ccasuser + '&password=' + this.gzform.password, null, 'get', this.hostData);
		}
	}), (0, _defineProperty3.default)(_methods, 'hostData', function hostData(res) {
		var hostL = res.data.data;
		var array = [];
		if (hostL.length > 0) {
			this.loadingShowh = false;
		}
		hostL.forEach(function (item) {
			array.push({
				hostpool: item.hostpool,
				id: item.id,
				name: item.name
			});
		});

		this.hostlist = array;
	}), (0, _defineProperty3.default)(_methods, 'onUisChange', function onUisChange(res) {
		this.gzform.uispool = res.value;
	}), (0, _defineProperty3.default)(_methods, 'getUisChange', function getUisChange(open) {
		if (open == true) {
			this.loadingShowp = true;
			_util2.default.restfullCall('/rest-ful/v3.0/h3c/uis/clusters?host=' + this.gzform.case + '&user=' + this.gzform.h3ccasuser + '&password=' + this.gzform.password + '&hpId=' + this.gzform.hpid, null, 'get', this.uispoolData);
		}
	}), (0, _defineProperty3.default)(_methods, 'uispoolData', function uispoolData(res) {
		var poolL = res.data.data;
		var array = [];

		poolL.forEach(function (item) {
			array.push({
				name: item.name,
				id: item.id
			});
		});
		this.uispoollist = array;
	}), (0, _defineProperty3.default)(_methods, 'changesAddress', function changesAddress(datas) {
		this.gzform.case = datas.label;
		for (var i = 0; i < this.example.length; i++) {
			if (datas.value == this.example[i].host) {
				this.gzform.password = this.example[i].password;
				this.gzform.h3ccasuser = this.example[i].user;
			}
		}
	}), (0, _defineProperty3.default)(_methods, 'caseChange', function caseChange(open) {
		if (open == true) _util2.default.restfullCall('/rest-ful/v3.0/client/agent/instances?cid=' + this.gzform.client + '&type=' + 1441792, null, 'get', this.exampleData);
	}), (0, _defineProperty3.default)(_methods, 'uisChange', function uisChange(open) {
		if (open == true) _util2.default.restfullCall('/rest-ful/v3.0/client/agent/instances?cid=' + this.gzform.client + '&type=' + 1507328, null, 'get', this.exampleData);
	}), (0, _defineProperty3.default)(_methods, 'exampleData', function exampleData(obj) {
		var data = [];
		for (var i = 0; i < obj.data.data.length; i++) {
			var object = JSON.parse(obj.data.data[i].conf);
			data.push(object);
		}

		var array = new Array();
		for (var _i = 0; _i < data.length; _i++) {
			array.push({
				host: data[_i].addr.toString(),
				user: data[_i].user,
				password: data[_i].password
			});
		}

		this.example = array;
	}), (0, _defineProperty3.default)(_methods, 'tishi', function tishi(currentRow, index) {
		if (currentRow.id == this.bgid) {
			return 'trbgshow';
		}
		return 'trbgshow_a';
	}), (0, _defineProperty3.default)(_methods, 'onexpire', function onexpire() {
		_util2.default.restfullCall('/rest-ful/v3.0/zvolume/expired', this.expireidarr, 'post', this.expiredData);
	}), (0, _defineProperty3.default)(_methods, 'expiredData', function expiredData(val) {
		this.expiremodel = false;
		if (val.data.code == 0) {
			this.$Message.success('过期成功');
		} else {
			this.$Message.error('过期失败');
		}
		this.getZvolumeTableDataFun(this.rowfbdata);
	}), (0, _defineProperty3.default)(_methods, 'diskModeChange', function diskModeChange(open) {
		if (open == true) _util2.default.restfullCall('/rest-ful/v3.0/vmware/diskmode', null, 'get', this.diskModeData);
	}), (0, _defineProperty3.default)(_methods, 'diskModeData', function diskModeData(res) {
		this.modelist = res.data.data;
	}), (0, _defineProperty3.default)(_methods, 'onDistMode', function onDistMode(res) {
		this.gzform.diskmode = res.value;
	}), (0, _defineProperty3.default)(_methods, 'dataStoreChange', function dataStoreChange(open) {
		if (open == true) _util2.default.restfullCall('/rest-ful/v3.0/vmware/datastore?server=' + this.gzform.host + '&user=' + this.gzform.user + '&password=' + this.gzform.password + '&dc=' + this.gzform.dc.value + '&host=' + this.gzform.hosts, null, 'get', this.dataStoreData);
	}), (0, _defineProperty3.default)(_methods, 'dataStoreData', function dataStoreData(res) {
		this.storelist = res.data.data;
	}), (0, _defineProperty3.default)(_methods, 'onStoreChange', function onStoreChange(res) {
		this.gzform.dataStroe = res.label;
	}), (0, _defineProperty3.default)(_methods, 'build_path_by_tree_node', function build_path_by_tree_node(treeNode) {
		var path = '';
		var cid = 0;
		do {
			var parent = treeNode.getParentNode();
			if (!parent) {
				cid = treeNode.id;
				name = treeNode.name;
				break;
			}

			if (parent.nodetype != 0) {
				path = '/' + treeNode.name + path;
			} else {
				path = treeNode.name + path;
			}
			if (parent.nodetype != 1) {}
			treeNode = parent;
		} while (true);
		if (path.indexOf('//') == 0) {
			path = path.substr(1);
		}
		return {
			client: cid,
			path: path,
			name: name
		};
	}), (0, _defineProperty3.default)(_methods, 'zTreeOnClick', function zTreeOnClick(event, treeId, treeNode) {
		var _this14 = this;

		if (!treeNode.hasOwnProperty('children')) {
			var path = this.build_path_by_tree_node(treeNode);


			var treeObj = $.fn.zTree.getZTreeObj(treeId);

			var nodes = treeObj.getSelectedNodes();

			if (nodes.length > 0) {

				nodes.forEach(function (el, i) {

					if (el.parentTId == null) {
						_this14.str = 'rest-ful/v3.0/zvolume/browse/' + _this14.volid + '?volume=' + _this14.db2id + '&path=' + path.name;
					} else {
						_this14.str = 'rest-ful/v3.0/zvolume/browse/' + _this14.volid + '?volume=' + _this14.db2id + '&path=' + path.name + '/' + treeNode.name;
					}
				});
			}
			_util2.default.restfullCall(this.str, null, 'get', function (obj) {
				if (obj.data.code == 0 && obj.data.data != null) {
					var arrays = new Array();
					var objj = obj.data.data;
					for (var i = 0; i < objj.length; i++) {
						arrays.push({
							name: objj[i].name,
							iconSkin: 'catalog',
							nodetype: 1,
							typetree: objj[i].type
						});
					}
					var ztreeobj = $.fn.zTree.getZTreeObj(treeId);
					ztreeobj.addNodes(treeNode, arrays);
				}
			});
		}
	}), (0, _defineProperty3.default)(_methods, 'zTreeOnCheck', function zTreeOnCheck(event, treeId, treeNode) {
		var path = this.build_path_by_tree_node(treeNode);
		var pathList = path.path;
		if (treeNode.checked == true) {
			this.resPost.push({
				type: Number(treeNode.typetree),
				path: path.name + path.path,
				desc: '',
				exclude: 1
			});
		} else if (treeNode.checked == false) {
			var indexToRemove = -1;

			for (var i = 0; i < this.resPost.length; i++) {
				if (this.resPost[i].path === path.name + path.path) {
					indexToRemove = i;
					break;
				}
			}
			if (indexToRemove > -1) {
				this.resPost.splice(indexToRemove, 1);
			}
		}
	}), (0, _defineProperty3.default)(_methods, 'nextstep', function nextstep() {
		this.stecur += 1;
	}), (0, _defineProperty3.default)(_methods, 'upstep', function upstep() {
		this.stecur -= 1;
	}), (0, _defineProperty3.default)(_methods, 'getclick', function getclick() {}), (0, _defineProperty3.default)(_methods, 'restypeData', function restypeData(obj) {
		var array = new Array();
		for (var i = 0; i < obj.data.data.length; i++) {
			array.push({
				restype: obj.data.data[i].type,
				name: obj.data.data[i].name
			});
		}
		this.restypeSelect = array;
	}), (0, _defineProperty3.default)(_methods, 'changePolname', function changePolname() {
		_util2.default.restfullCall('rest-ful/v3.0/zvolume/list?pageno=1&nums=' + this.pageSize + '&client=' + (this.query.cliname || '') + '&restype=' + (this.query.restype || '') + '&policyname=' + (this.query.policyname || '') + '&starttime=' + (this.query.starttime || '') + '&endtime=' + (this.query.endtime || '') + '&ip=' + (this.query.ip || ''), null, 'get', this.getdupData);
	}), (0, _defineProperty3.default)(_methods, 'changesCli', function changesCli(res) {
		this.query.cliname = res;

		_util2.default.restfullCall('rest-ful/v3.0/zvolume/list?pageno=1&nums=' + this.pageSize + '&client=' + (this.query.cliname || '') + '&restype=' + (this.query.restype || '') + '&policyname=' + (this.query.policyname || '') + '&starttime=' + (this.query.starttime || '') + '&endtime=' + (this.query.endtime || '') + '&ip=' + (this.query.ip || ''), null, 'get', this.getdupData);
	}), (0, _defineProperty3.default)(_methods, 'changesRes', function changesRes(res) {
		_util2.default.restfullCall('rest-ful/v3.0/zvolume/list?pageno=1&nums=' + this.pageSize + '&client=' + (this.query.cliname || '') + '&restype=' + (this.query.restype || '') + '&policyname=' + (this.query.policyname || '') + '&starttime=' + (this.query.starttime || '') + '&endtime=' + (this.query.endtime || '') + '&ip=' + (this.query.ip || ''), null, 'get', this.getdupData);
	}), (0, _defineProperty3.default)(_methods, 'handleSizeChange', function handleSizeChange(val) {
		this.pageSize = val;

		_util2.default.restfullCall('rest-ful/v3.0/zvolume/snapshot/list/' + this.rowfbdata.volume + '?pageno=1&nums=' + val, null, 'get', this.getdupData);
	}), (0, _defineProperty3.default)(_methods, 'changePage', function changePage(index) {

		this.curentPage = index;

		_util2.default.restfullCall('rest-ful/v3.0/zvolume/snapshot/list/' + this.rowfbdata.volume + '?pageno=1&nums=' + this.pageSize, null, 'get', this.getdupData);
	}), (0, _defineProperty3.default)(_methods, 'torowdata', function torowdata(res, i) {
		this.$refs.selection.toggleSelect(i);
	}), (0, _defineProperty3.default)(_methods, 'tovawlist', function tovawlist(res) {
		var _this15 = this;

		var aa = res;
		if (aa.length > 0) {
			aa.forEach(function (item) {
				if (_this15.resPost.length > 0) {
					_this15.resPost.forEach(function (el, i) {
						if (el.path == item.name) {
							_this15.resPost.slice(i, 1);
						}
					});
				} else {
					_this15.resPost.push({ type: Number(item.type), path: item.name, desc: '', exclude: 0 });
				}
			});
		} else {
			this.resPost = [];
		}
	}), (0, _defineProperty3.default)(_methods, 'netWorkChange', function netWorkChange(open) {
		if (open == true) _util2.default.restfullCall('/rest-ful/v3.0/vmware/network?server=' + this.gzform.host + '&user=' + this.gzform.user + '&password=' + this.gzform.password + '&dc=' + this.gzform.dc.value + '&host=' + this.gzform.hosts, null, 'get', this.netWorkData);
	}), (0, _defineProperty3.default)(_methods, 'netWorkData', function netWorkData(res) {
		this.networklist = res.data.data;
	}), (0, _defineProperty3.default)(_methods, 'onNetWork', function onNetWork(res) {
		this.gzform.network = res.label;
	}), (0, _defineProperty3.default)(_methods, 'getDataCenter', function getDataCenter(res) {
		this.centerlist = res.data.data;
	}), (0, _defineProperty3.default)(_methods, 'onCenterChange', function onCenterChange(res) {
		this.gzform.dc = res;
	}), (0, _defineProperty3.default)(_methods, 'hostsChange', function hostsChange(open) {
		if (open == true) _util2.default.restfullCall('/rest-ful/v3.0/vmware/hosts?server=' + this.gzform.host + '&user=' + this.gzform.user + '&password=' + this.gzform.password + '&dc=' + this.gzform.dc.value, null, 'get', this.gethostsData);
	}), (0, _defineProperty3.default)(_methods, 'gethostsData', function gethostsData(res) {
		this.hostslist = res.data.data;
	}), (0, _defineProperty3.default)(_methods, 'vawarehostData', function vawarehostData(val) {}), (0, _defineProperty3.default)(_methods, 'vawarechange', function vawarechange(val) {
		var _this16 = this;

		this.vamsldata.forEach(function (item, i) {
			if (val == item.id) {
				_this16.gzform.user = item.user;
				_this16.gzform.password = item.password;
				_this16.gzform.host = item.host;
				_util2.default.restfullCall('/rest-ful/v3.0/vmware/datacenter?server=' + _this16.gzform.host + '&user=' + _this16.gzform.user + '&password=' + _this16.gzform.password, null, 'get', _this16.getDataCenter);
			}
		});
	}), (0, _defineProperty3.default)(_methods, 'sqlchange', function sqlchange(val) {
		this.gzform.cliName = val;
	}), (0, _defineProperty3.default)(_methods, 'oraclechange', function oraclechange(val) {
		this.gzform.id = val;
		this.gzform.cliName = val;
	}), (0, _defineProperty3.default)(_methods, 'getTime', function getTime(res) {
		var _this17 = this;

		res.forEach(function (item, i) {
			if (i == 0) {
				_this17.query.starttime = item;
			}
			if (i == 1) {
				_this17.query.endtime = item;
			}
		});
		this.tooltipdata = this.query.starttime + '至' + this.query.endtime;

		setTimeout(function () {
			_util2.default.restfullCall('rest-ful/v3.0/zvolume/list?pageno=1&nums=' + _this17.pageSize + '&client=' + (_this17.query.cliname || '') + '&restype=' + (_this17.query.restype || '') + '&policyname=' + (_this17.query.policyname || '') + '&starttime=' + (_this17.query.starttime || '') + '&endtime=' + (_this17.query.endtime || '') + '&ip=' + (_this17.query.ip || ''), null, 'get', _this17.getdupData);
		});
	}), (0, _defineProperty3.default)(_methods, 'getTableHeight', function getTableHeight() {
		this.tableHeight = window.innerHeight - 270;
	}), (0, _defineProperty3.default)(_methods, 'oracleTime', function oracleTime(res) {
		this.oracleResTime = res;
		if (res) {
			this.gzform.scn = '';
		}
	}), (0, _defineProperty3.default)(_methods, 'getscnFun', function getscnFun(res) {
		if (res) {
			this.oracleResTime = '';
		}
	}), (0, _defineProperty3.default)(_methods, 'ontasktype', function ontasktype(res) {
		if (res == '挂载数据库') {
			this.oracletasttype = '1';
		} else if (res == '挂载文件') {
			this.oracletasttype = '2';
		}
	}), (0, _defineProperty3.default)(_methods, 'onmount', function onmount() {
		var postObj = {};
		if (this.resTypeVal == 1441792) {
			var h3ccasArr = [];
			h3ccasArr.push({
				type: 35,
				value: this.gzform.case
			}, {
				type: 93,
				value: this.gzform.addr.toString()
			}, {
				type: 94,
				value: this.gzform.pool.toString()
			}, {
				type: 78,
				value: this.gzform.hostId.toString()
			}, {
				type: 98,
				value: this.gzform.hostName
			}, {
				type: 91,
				value: this.gzform.cashost.toString()
			}, {
				type: 34,
				value: this.gzform.rename
			}, {
				type: 129,
				value: this.gzform.vswitchname
			}, {
				type: 128,
				value: this.gzform.vswitchid.toString()
			});
			this.optionArr = h3ccasArr;
		}
		if (this.resTypeVal == 1507328) {
			var h3cuisArr = [];
			if (this.restart) {
				h3cuisArr.push({
					type: 35,
					value: this.gzform.case
				}, {
					type: 78,
					value: this.gzform.hostId.toString()
				}, {
					type: 98,
					value: this.gzform.hostName
				}, {
					type: 91,
					value: this.gzform.cashost.toString()
				}, {
					type: 94,
					value: this.gzform.uispool.toString()
				}, {
					type: 34,
					value: this.gzform.rename
				}, {
					type: 33
				});
			} else {
				h3cuisArr.push({
					type: 35,
					value: this.gzform.case
				}, {
					type: 78,
					value: this.gzform.hostId.toString()
				}, {
					type: 98,
					value: this.gzform.hostName
				}, {
					type: 91,
					value: this.gzform.cashost.toString()
				}, {
					type: 94,
					value: this.gzform.uispool.toString()
				}, {
					type: 34,
					value: this.gzform.rename
				});
			}

			this.optionArr = h3cuisArr;
		}
		if (this.mountType == '达梦') {
			var dmArr = [];
			if (this.sqltype == '恢复') {
				dmArr.push({
					type: 35,
					value: this.gzform.id.toString()
				}, {
					type: 36,
					value: this.oracleResTime
				}, {
					type: 119,
					value: this.oracletasttype
				}, {
					type: 120,
					value: ''
				});
			} else {
				dmArr.push({
					type: 35,
					value: this.gzform.id.toString()
				}, {
					type: 36,
					value: this.oracleResTime
				}, {
					type: 119,
					value: this.oracletasttype
				});
			}
			this.optionArr = dmArr;
		}
		if (this.mountType == '文件') {
			var fileArr = [];
			fileArr.push({
				type: 119,
				value: this.oracletasttype
			});
			this.optionArr = fileArr;
		}
		if (this.mountType == 'MYSQL') {
			if (this.sqltype == '恢复') {
				if (this.oracletasttype == 2) {
					var mysqlArr = [];
					if (this.tableRow.backup_type == '日志备份') {
						mysqlArr.push({
							type: 22,
							value: this.gzform.cliName == '' ? '' : this.gzform.cliName
						}, {
							type: 36,
							value: this.oracleResTime
						}, {
							type: 119,
							value: this.oracletasttype
						}, {
							type: 120,
							value: ''
						});
					} else {
						mysqlArr.push({
							type: 22,
							value: this.gzform.cliName == '' ? '' : this.gzform.cliName
						}, {
							type: 119,
							value: this.oracletasttype
						}, {
							type: 120,
							value: ''
						});
					}
					this.optionArr = mysqlArr;
				} else {
					if (this.gzform.cliName != '' && this.gzform.agre != '' && this.gzform.path != '') {
						var _mysqlArr = [];
						if (this.tableRow.backup_type == '日志备份') {
							_mysqlArr.push({
								type: 22,
								value: this.gzform.cliName == '' ? '' : this.gzform.cliName
							}, {
								type: 36,
								value: this.oracleResTime
							}, {
								type: 119,
								value: this.oracletasttype
							}, {
								type: 120,
								value: ''
							});
						} else {
							_mysqlArr.push({
								type: 22,
								value: this.gzform.cliName == '' ? '' : this.gzform.cliName
							}, {
								type: 119,
								value: this.oracletasttype
							}, {
								type: 120,
								value: ''
							});
						}

						this.optionArr = _mysqlArr;
					} else {
						this.$Message.warning('请填写必填信息');
						return;
					}
				}
			} else {
				if (this.oracletasttype == 2) {
					var _mysqlArr2 = [];
					if (this.tableRow.backup_type == '日志备份') {
						_mysqlArr2.push({
							type: 22,
							value: this.gzform.cliName == '' ? '' : this.gzform.cliName
						}, {
							type: 36,
							value: this.oracleResTime
						}, {
							type: 119,
							value: this.oracletasttype
						});
					} else {
						_mysqlArr2.push({
							type: 22,
							value: this.gzform.cliName == '' ? '' : this.gzform.cliName
						}, {
							type: 119,
							value: this.oracletasttype
						});
					}

					this.optionArr = _mysqlArr2;
				} else {
					if (this.gzform.cliName != '' && this.gzform.agre != '' && this.gzform.path != '') {
						var _mysqlArr3 = [];
						if (this.tableRow.backup_type == '日志备份') {
							_mysqlArr3.push({
								type: 22,
								value: this.gzform.cliName == '' ? '' : this.gzform.cliName
							}, {
								type: 36,
								value: this.oracleResTime
							}, {
								type: 119,
								value: this.oracletasttype
							});
						} else {
							_mysqlArr3.push({
								type: 22,
								value: this.gzform.cliName == '' ? '' : this.gzform.cliName
							}, {
								type: 119,
								value: this.oracletasttype
							});
						}

						this.optionArr = _mysqlArr3;
					} else {
						this.$Message.warning('请填写必填信息');
						return;
					}
				}
			}
		}
		if (this.mountType == 'KINGBASE') {
			if (this.sqltype == '恢复') {
				if (this.gzform.cliName != '' && this.gzform.agre != '' && this.gzform.path != '') {
					var kingaseArr = [];
					kingaseArr.push({
						type: 22,
						value: this.gzform.cliName == '' ? '' : this.gzform.cliName
					}, {
						type: 120,
						value: ''
					}, {
						type: 119,
						value: this.oracletasttype
					});
					this.optionArr = kingaseArr;
				} else {
					this.$Message.warning('请填写必填信息');
					return;
				}
			} else {
				if (this.gzform.cliName != '' && this.gzform.agre != '' && this.gzform.path != '') {
					var _kingaseArr = [];
					_kingaseArr.push({
						type: 22,
						value: this.gzform.cliName == '' ? '' : this.gzform.cliName
					}, {
						type: 119,
						value: this.oracletasttype
					});
					this.optionArr = _kingaseArr;
				} else {
					this.$Message.warning('请填写必填信息');
					return;
				}
			}
		}
		if (this.mountType == '瀚高数据库') {
			if (this.sqltype == '恢复') {
				if (this.gzform.cliName != '' && this.gzform.agre != '' && this.gzform.path != '') {
					var _kingaseArr2 = [];
					_kingaseArr2.push({
						type: 22,
						value: this.gzform.cliName == '' ? '' : this.gzform.cliName
					}, {
						type: 120,
						value: ''
					}, {
						type: 36,
						value: this.oracleResTime
					}, {
						type: 119,
						value: this.oracletasttype
					});
					this.optionArr = _kingaseArr2;
				} else {
					this.$Message.warning('请填写必填信息');
					return;
				}
			} else {
				if (this.gzform.cliName != '' && this.gzform.agre != '' && this.gzform.path != '') {
					var _kingaseArr3 = [];
					_kingaseArr3.push({
						type: 22,
						value: this.gzform.cliName == '' ? '' : this.gzform.cliName
					}, {
						type: 36,
						value: this.oracleResTime
					}, {
						type: 119,
						value: this.oracletasttype
					});
					this.optionArr = _kingaseArr3;
				} else {
					this.$Message.warning('请填写必填信息');
					return;
				}
			}
		}
		if (this.mountType == 'ORACLE') {
			var oracleArr = [];
			if (this.sqltype == '恢复') {
				if (this.gzform.scn) {
					oracleArr.push({
						type: 22,
						value: this.gzform.cliName == '' ? '' : this.gzform.cliName
					}, {
						type: 119,
						value: this.oracletasttype
					}, {
						type: 26,
						value: this.gzform.scn
					}, {
						type: 120,
						value: ''
					});
				}
				if (this.oracleResTime) {
					oracleArr.push({
						type: 22,
						value: this.gzform.cliName == '' ? '' : this.gzform.cliName
					}, {
						type: 25,
						value: this.oracleResTime
					}, {
						type: 119,
						value: this.oracletasttype
					}, {
						type: 120,
						value: ''
					});
				}
			} else {
				if (this.gzform.scn) {
					oracleArr.push({
						type: 22,
						value: this.gzform.cliName == '' ? '' : this.gzform.cliName
					}, {
						type: 26,
						value: this.gzform.scn
					}, {
						type: 119,
						value: this.oracletasttype
					});
				} else if (this.oracleResTime) {
					oracleArr.push({
						type: 22,
						value: this.gzform.cliName == '' ? '' : this.gzform.cliName
					}, {
						type: 25,
						value: this.oracleResTime
					}, {
						type: 119,
						value: this.oracletasttype
					});
				} else {
					oracleArr.push({
						type: 22,
						value: this.gzform.cliName == '' ? '' : this.gzform.cliName
					}, {
						type: 119,
						value: this.oracletasttype
					});
				}
			}

			this.optionArr = oracleArr;
			postObj.protocol = this.gzform.agre;
		}
		if (this.mountType == 'VMWARE') {
			if (this.gzform.host != '' && this.gzform.dc.value != '' && this.gzform.hosts != '' && this.gzform.name != '') {
				var vmwareArr = [];
				if (this.sqltype == '恢复') {
					vmwareArr.push({
						type: 22,
						value: this.gzform.cliName == '' ? '' : this.gzform.cliName
					}, {
						type: 35,
						value: this.gzform.host
					}, {
						type: 97,
						value: this.gzform.dataStroe
					}, {
						type: 96,
						value: this.gzform.dc.value
					}, {
						type: 98,
						value: this.gzform.hosts
					}, {
						type: 95,
						value: this.gzform.network
					}, {
						type: 92,
						value: this.gzform.diskmode.toString()
					}, {
						type: 120,
						value: ''
					}, {
						type: 34,
						value: this.gzform.name
					});
				} else {
					vmwareArr.push({
						type: 22,
						value: this.gzform.cliName == '' ? '' : this.gzform.cliName
					}, {
						type: 35,
						value: this.gzform.host
					}, {
						type: 96,
						value: this.gzform.dc.value
					}, {
						type: 98,
						value: this.gzform.hosts
					}, {
						type: 95,
						value: this.gzform.network
					}, {
						type: 34,
						value: this.gzform.name
					}, {
						type: 119,
						value: this.oracletasttype
					});
				}

				this.optionArr = vmwareArr;
			} else {
				this.$Message.warning('请填写必填信息');
				return;
			}
		}
		if (this.mountType == 'DB2') {
			var db2Arr = [];
			db2Arr.push({
				type: 119,
				value: this.oracletasttype
			});
			this.optionArr = db2Arr;
			postObj = {
				volid: this.volid,
				client: this.gzform.client,
				protocol: this.gzform.agre,
				mountpath: this.gzform.path,
				options: this.optionArr,
				resources: this.resPost
			};
		}
		if (this.mountType == 'SQLSERVER') {
			if (this.oracletasttype == 2) {
				if (this.gzform.client != '' && this.gzform.agre != '') {
					var sqlArr = [];
					if (this.sqltype == '恢复') {
						sqlArr.push({
							type: 120,
							value: ''
						}, {
							type: 121,
							value: this.gzform.datapath
						}, {
							type: 119,
							value: this.oracletasttype
						});
						this.optionArr = sqlArr;
					} else {
						this.optionArr = sqlArr;
					}

					postObj = {
						volid: this.volid,
						client: this.gzform.client,
						protocol: this.gzform.agre,
						mountpath: this.gzform.path,
						options: this.optionArr,
						resources: this.resPost
					};
				} else {
					this.$Message.warning('请填写必填信息');
					return;
				}
			} else {
				if (this.gzform.id != '' && this.gzform.client != '' && this.gzform.agre != '' && this.gzform.name != '') {
					var _sqlArr = [];
					if (this.sqltype == '恢复') {
						_sqlArr.push({
							type: 35,
							value: this.gzform.id.toString()
						}, {
							type: 34,
							value: this.gzform.name
						}, {
							type: 120,
							value: ''
						}, {
							type: 121,
							value: this.gzform.datapath
						}, {
							type: 119,
							value: this.oracletasttype
						});
						this.optionArr = _sqlArr;
					} else {
						_sqlArr.push({
							type: 35,
							value: this.gzform.id.toString()
						}, {
							type: 34,
							value: this.gzform.name
						});
						this.optionArr = _sqlArr;
					}

					postObj = {
						volid: this.volid,
						client: this.gzform.client,
						protocol: this.gzform.agre,
						mountpath: this.gzform.path,
						options: this.optionArr,
						resources: this.resPost
					};
				} else {
					this.$Message.warning('请填写必填信息');
					return;
				}
			}
		}
		if (this.mountType == 'PostgreSQL') {
			if (this.sqltype == '恢复') {
				if (this.gzform.cliName != '' && this.gzform.agre != '') {
					if (this.resPost.length > 0) {
						var _kingaseArr4 = [];
						_kingaseArr4.push({
							type: 22,
							value: this.gzform.cliName == '' ? '' : this.gzform.cliName
						}, {
							type: 120,
							value: ''
						}, {
							type: 36,
							value: this.oracleResTime
						}, {
							type: 119,
							value: this.oracletasttype
						});
						this.optionArr = _kingaseArr4;
					} else {
						this.$Message.warning('请勾选资源');
						return;
					}
				} else {
					this.$Message.warning('请填写必填信息');
					return;
				}
			} else {
				if (this.gzform.cliName != '' && this.gzform.agre != '') {
					if (this.resPost.length > 0) {
						var _kingaseArr5 = [];
						_kingaseArr5.push({
							type: 22,
							value: this.gzform.cliName == '' ? '' : this.gzform.cliName
						}, {
							type: 36,
							value: this.oracleResTime
						}, {
							type: 119,
							value: this.oracletasttype
						});
						this.optionArr = _kingaseArr5;
					} else {
						this.$Message.warning('请勾选资源');
						return;
					}
				} else {
					this.$Message.warning('请填写必填信息');
					return;
				}
			}
		}
		if (this.mountType == 'HLSQL') {
			if (this.sqltype == '恢复') {
				if (this.gzform.cliName != '' && this.gzform.agre != '') {
					if (this.resPost.length > 0) {
						var _kingaseArr6 = [];
						_kingaseArr6.push({
							type: 22,
							value: this.gzform.cliName == '' ? '' : this.gzform.cliName
						}, {
							type: 120,
							value: ''
						}, {
							type: 36,
							value: this.oracleResTime
						}, {
							type: 119,
							value: this.oracletasttype
						});
						this.optionArr = _kingaseArr6;
					} else {
						this.$Message.warning('请勾选资源');
						return;
					}
				} else {
					this.$Message.warning('请填写必填信息');
					return;
				}
			} else {
				if (this.gzform.cliName != '' && this.gzform.agre != '') {
					if (this.resPost.length > 0) {
						var _kingaseArr7 = [];
						_kingaseArr7.push({
							type: 22,
							value: this.gzform.cliName == '' ? '' : this.gzform.cliName
						}, {
							type: 36,
							value: this.oracleResTime
						}, {
							type: 119,
							value: this.oracletasttype
						});
						this.optionArr = _kingaseArr7;
					} else {
						this.$Message.warning('请勾选资源');
						return;
					}
				} else {
					this.$Message.warning('请填写必填信息');
					return;
				}
			}
		}
		if (this.mountType == 'GBase') {
			if (this.sqltype == '恢复') {
				if (this.oracletasttype == 2) {
					var GBaseArr = [];
					GBaseArr.push({
						type: 22,
						value: this.gzform.cliName == '' ? '' : this.gzform.cliName
					}, {
						type: 119,
						value: this.oracletasttype
					}, {
						type: 120,
						value: ''
					});
					this.optionArr = GBaseArr;
				} else {
					if (this.gzform.cliName != '' && this.gzform.agre != '') {
						var _GBaseArr = [];
						_GBaseArr.push({
							type: 22,
							value: this.gzform.cliName == '' ? '' : this.gzform.cliName
						}, {
							type: 119,
							value: this.oracletasttype
						}, {
							type: 120,
							value: ''
						});
						this.optionArr = _GBaseArr;
					} else {
						this.$Message.warning('请填写必填信息');
						return;
					}
				}
			} else {
				if (this.oracletasttype == 2) {
					var _GBaseArr2 = [];
					_GBaseArr2.push({
						type: 119,
						value: this.oracletasttype
					}, {
						type: 68,
						value: this.restoreLogsVal
					});
					this.optionArr = _GBaseArr2;
				} else {
					if (this.gzform.cliName != '' && this.gzform.agre != '') {
						var _GBaseArr3 = [];
						_GBaseArr3.push({
							type: 22,
							value: this.gzform.cliName == '' ? '' : this.gzform.cliName
						}, {
							type: 119,
							value: this.oracletasttype
						}, {
							type: 68,
							value: this.restoreLogsVal
						});
						this.optionArr = _GBaseArr3;
					} else {
						this.$Message.warning('请填写必填信息');
						return;
					}
				}
			}
		}
		if (this.mountType == 'Informix数据库') {
			if (this.sqltype == '恢复') {
				if (this.oracletasttype == 2) {
					var _GBaseArr4 = [];
					_GBaseArr4.push({
						type: 22,
						value: this.gzform.cliName == '' ? '' : this.gzform.cliName
					}, {
						type: 119,
						value: this.oracletasttype
					}, {
						type: 120,
						value: ''
					});
					this.optionArr = _GBaseArr4;
				} else {
					if (this.gzform.cliName != '' && this.gzform.agre != '') {
						var _GBaseArr5 = [];
						_GBaseArr5.push({
							type: 22,
							value: this.gzform.cliName == '' ? '' : this.gzform.cliName
						}, {
							type: 119,
							value: this.oracletasttype
						}, {
							type: 120,
							value: ''
						});
						this.optionArr = _GBaseArr5;
					} else {
						this.$Message.warning('请填写必填信息');
						return;
					}
				}
			} else {
				if (this.oracletasttype == 2) {
					var _GBaseArr6 = [];
					_GBaseArr6.push({
						type: 119,
						value: this.oracletasttype
					}, {
						type: 68,
						value: this.restoreLogsVal
					});
					this.optionArr = _GBaseArr6;
				} else {
					if (this.gzform.cliName != '' && this.gzform.agre != '') {
						var _GBaseArr7 = [];
						_GBaseArr7.push({
							type: 22,
							value: this.gzform.cliName == '' ? '' : this.gzform.cliName
						}, {
							type: 119,
							value: this.oracletasttype
						}, {
							type: 68,
							value: this.restoreLogsVal
						});
						this.optionArr = _GBaseArr7;
					} else {
						this.$Message.warning('请填写必填信息');
						return;
					}
				}
			}
		}
		if (this.mountType != '文件') {
			postObj = {
				volid: this.volid,
				client: this.gzform.client,
				protocol: this.gzform.agre,
				mountpath: this.gzform.path,
				options: this.optionArr,
				restype: this.platformrestype,
				resources: this.resPost,
				desc: this.gzform.desc
			};
		} else {
			postObj = {
				volid: this.volid,
				client: this.gzform.client,
				protocol: this.gzform.agre,
				mountpath: this.osType == 0 ? '' : this.gzform.path,
				options: this.optionArr,
				restype: this.platformrestype,
				resources: this.resPost,
				desc: this.gzform.desc
			};
		}

		_util2.default.restfullCall('/rest-ful/v3.0/zvolume/mount', postObj, 'POST', this.mountData);
	}), (0, _defineProperty3.default)(_methods, 'mountData', function mountData(val) {
		this.gzform = {};
		this.stecur = 0;
		if (val.data.code == 0) {
			this.$Message.success(val.data.message);
			this.optionArr = [];
			$.fn.zTree.init($('#treeDemo'), this.setting, null);
			this.resPost = [];
			this.sqlsldata = [];
			this.dmsldata = [];
			this.sqltype = '';
			this.gzform.path = '';
			this.gzform.cliName = '';
			this.gzform.agre = '';
			this.scntimeshow = '';
		} else {
			this.$Message.warning(val.data.message);
		}
		this.onemodel = false;
	}), (0, _defineProperty3.default)(_methods, 'getRowData', function getRowData(row) {
		var _this18 = this;

		this.expireidarr = [];
		this.bgid = row.id;
		this.volid = row.id;
		this.db2id = row.volume_id;
		this.expireidarr.push(row.id);


		_util2.default.restfullCall('/rest-ful/v3.0/clients?nums=0&restype=' + row.res_type_val, null, 'get', this.cliData);
		this.setRowData = row;
		this.$store.commit('getResTypeData', row.res_type_val);
		if (row.res_type_val == 1441792) {
			this.platformrestype = row.res_type_val;
			this.stecur = 0;
			this.typedis = false;
			this.$nextTick(function () {
				_this18.gzform.client = row.client_id;
			});
			_util2.default.restfullCall('rest-ful/v3.0/zvolume/browse/' + row.id + '?volume=' + row.volume_id, null, 'get', this.geth3ccasData);
		}
		if (row.res_type_val == 1507328) {
			this.platformrestype = row.res_type_val;
			this.stecur = 0;
			this.typedis = false;
			this.$nextTick(function () {
				_this18.gzform.client = row.client_id;
			});
			_util2.default.restfullCall('rest-ful/v3.0/zvolume/browse/' + row.id + '?volume=' + row.volume_id, null, 'get', this.geth3cUisData);
		}
		if (row.resource_type == 'VMWARE') {
			this.platformrestype = row.res_type_val;
			this.typedis = true;
			this.$nextTick(function () {
				_this18.gzform.client = row.client_id;
			});
			_util2.default.restfullCall('rest-ful/v3.0/zvolume/browse/' + row.id + '?volume=' + row.volume_id, null, 'get', this.getvmwareData);
		}

		if (row.resource_type == 'DB2') {
			this.platformrestype = row.res_type_val;
			this.typedis = false;
			this.$nextTick(function () {
				_this18.gzform.client = row.client_id;
			});
			_util2.default.restfullCall('rest-ful/v3.0/zvolume/browse/' + row.id + '?volume=' + row.volume_id, null, 'get', this.getdb2Data);
		}

		if (row.resource_type == 'SQLSERVER') {
			this.platformrestype = row.res_type_val;
			this.typedis = false;
			this.$nextTick(function () {
				_this18.gzform.client = row.client_id;
			});
			_util2.default.restfullCall('rest-ful/v3.0/zvolume/browse/' + row.id + '?volume=' + row.volume_id, null, 'get', this.getSqlserverData);
			var url = 'rest-ful/v3.0/client/agent/instances?cid=' + row.client_id + '&type=' + '262144';

			_util2.default.restfullCall(url, null, 'get', function (obj) {
				var sqlsldata = [];
				for (var i = 0; i < obj.data.data.length; i++) {
					var object = JSON.parse(obj.data.data[i].conf);
					object.id = obj.data.data[i].id;

					sqlsldata.push(object);
				}
				for (var _i2 = 0; _i2 < sqlsldata.length; _i2++) {
					_this18.sqlsldata.push(sqlsldata[_i2]);
				}
			});
		}
		if (row.resource_type == '达梦') {
			this.platformrestype = row.res_type_val;
			this.typedis = false;
		}
		if (row.resource_type == 'ORACLE') {
			this.platformrestype = row.res_type_val;
			this.typedis = false;
		}
		if (row.resource_type == 'MYSQL') {
			this.platformrestype = row.res_type_val;
			this.tableRow = row;
			this.typedis = false;
		}
		if (row.resource_type == '文件') {
			this.platformrestype = row.res_type_val;
			this.typedis = false;
		}
		if (row.resource_type == 'KINGBASE') {
			this.platformrestype = row.res_type_val;
			this.typedis = false;
		}
		if (row.resource_type == '瀚高数据库') {
			this.platformrestype = row.res_type_val;
			this.typedis = false;
			this.$nextTick(function () {
				_this18.gzform.client = row.client_id;
			});
			_util2.default.restfullCall('rest-ful/v3.0/zvolume/browse/' + row.id + '?volume=' + row.volume_id, null, 'get', this.getHgbaseData);
			setTimeout(function () {
				var url = 'rest-ful/v3.0/client/agent/instances?cid=' + row.client_id + '&type=' + row.res_type_val;
				_util2.default.restfullCall(url, null, 'get', function (obj) {
					var dmsldata = [];
					for (var i = 0; i < obj.data.data.length; i++) {
						var object = JSON.parse(obj.data.data[i].conf);
						object.id = obj.data.data[i].id;
						dmsldata.push(object);
					}
					for (var _i3 = 0; _i3 < dmsldata.length; _i3++) {
						_this18.dmsldata.push(dmsldata[_i3]);
					}
					_this18.gzform.id = _this18.dmsldata[0].id;
					_this18.gzform.cliName = _this18.dmsldata[0].host;
				});
			}, 100);
		}
		if (row.resource_type == 'PostgreSQL') {
			this.gzform.cliName = '';
			this.gzform.agre = '';
			this.platformrestype = row.res_type_val;
			this.typedis = false;
			this.$nextTick(function () {
				_this18.gzform.client = row.client_id;
			});
			_util2.default.restfullCall('rest-ful/v3.0/zvolume/browse/' + row.id + '?volume=' + row.volume_id, null, 'get', this.getPostgreSQLata);
			setTimeout(function () {
				var url = 'rest-ful/v3.0/client/agent/instances?cid=' + row.client_id + '&type=' + row.res_type_val;
				_util2.default.restfullCall(url, null, 'get', function (obj) {
					var dmsldata = [];
					for (var i = 0; i < obj.data.data.length; i++) {
						var object = JSON.parse(obj.data.data[i].conf);
						object.id = obj.data.data[i].id;
						dmsldata.push(object);
					}
					for (var _i4 = 0; _i4 < dmsldata.length; _i4++) {
						_this18.dmsldata.push(dmsldata[_i4]);
					}
					_this18.gzform.id = _this18.dmsldata[0].id;
					_this18.gzform.cliName = _this18.dmsldata[0].host;
				});
			}, 100);
		}
		if (row.resource_type == 'HLSQL') {
			this.gzform.cliName = '';
			this.gzform.agre = '';
			this.platformrestype = row.res_type_val;
			this.typedis = false;
			this.$nextTick(function () {
				_this18.gzform.client = row.client_id;
			});
			_util2.default.restfullCall('rest-ful/v3.0/zvolume/browse/' + row.id + '?volume=' + row.volume_id, null, 'get', this.getHLSQLata);
			setTimeout(function () {
				var url = 'rest-ful/v3.0/client/agent/instances?cid=' + row.client_id + '&type=' + row.res_type_val;
				_util2.default.restfullCall(url, null, 'get', function (obj) {
					var dmsldata = [];
					for (var i = 0; i < obj.data.data.length; i++) {
						var object = JSON.parse(obj.data.data[i].conf);
						object.id = obj.data.data[i].id;
						dmsldata.push(object);
					}
					for (var _i5 = 0; _i5 < dmsldata.length; _i5++) {
						_this18.dmsldata.push(dmsldata[_i5]);
					}
					_this18.gzform.id = _this18.dmsldata[0].id;
					_this18.gzform.cliName = _this18.dmsldata[0].host;
				});
			}, 100);
		}

		if (row.resource_type == 'GBase') {
			this.platformrestype = row.res_type_val;
			this.typedis = false;
			this.$nextTick(function () {
				_this18.gzform.client = row.client_id;
			});
			setTimeout(function () {
				var url = 'rest-ful/v3.0/client/agent/instances?cid=' + row.client_id + '&type=' + row.res_type_val;
				_util2.default.restfullCall(url, null, 'get', function (obj) {
					var dmsldata = [];
					for (var i = 0; i < obj.data.data.length; i++) {
						var object = JSON.parse(obj.data.data[i].conf);
						object.id = obj.data.data[i].id;
						dmsldata.push(object);
					}
					for (var _i6 = 0; _i6 < dmsldata.length; _i6++) {
						_this18.dmsldata.push(dmsldata[_i6]);
					}

					_this18.gzform.cliName = _this18.dmsldata[0].client;
				});
			}, 100);
		}
	}), (0, _defineProperty3.default)(_methods, 'getHgbaseData', function getHgbaseData(res) {
		if (res.data.code == 0) {} else if (res.data.data == null) {}
	}), (0, _defineProperty3.default)(_methods, 'getPostgreSQLata', function getPostgreSQLata(res) {}), (0, _defineProperty3.default)(_methods, 'getHLSQLata', function getHLSQLata(res) {}), (0, _defineProperty3.default)(_methods, 'geth3ccasData', function geth3ccasData(res) {
		var vamData = [];
		if (res.data.code == 0) {
			this.casTableList = res.data.data;
		} else if (res.data.data == null) {
			this.casTableList = [];
		}
	}), (0, _defineProperty3.default)(_methods, 'geth3cUisData', function geth3cUisData(res) {
		var vamData = [];
		if (res.data.code == 0) {
			this.uisTableList = res.data.data;
		} else if (res.data.data == null) {
			this.uisTableList = [];
		}
	}), (0, _defineProperty3.default)(_methods, 'getSqlserverData', function getSqlserverData(res) {
		this.db2List = [];
		if (res.data.code == 0) {
			this.db2List = res.data.data;
		}

		$.fn.zTree.init($('#treeDemo'), this.setting, this.db2List);
	}), (0, _defineProperty3.default)(_methods, 'getdb2Data', function getdb2Data(res) {
		this.db2List = [];
		if (res.data.code == 0) {
			this.db2List = res.data.data;
		}

		$.fn.zTree.init($('#treeDemo'), this.setting, this.db2List);
	}), (0, _defineProperty3.default)(_methods, 'getvmwareData', function getvmwareData(res) {
		var vamData = [];
		if (res.data.code == 0) {
			this.vamTableList = res.data.data;
		} else if (res.data.data == null) {
			this.vamTableList = [];
		}
	}), (0, _defineProperty3.default)(_methods, 'getgzData', function getgzData(res) {
		if (res.data.code == 0) {
			this.gztotal = res.data.data.nums;
			this.mountdata = res.data.data.Records;
		}
	}), (0, _defineProperty3.default)(_methods, 'getdupData', function getdupData(res) {
		this.loading = true;
		console.log(res);
		if (res.data.code == 0) {
			this.policytotal = res.data.data.nums;

			this.dupdata = res.data.data.Records;
			this.loading = false;
			this.$Message.success(res.data.message);
		} else {
			this.loading = false;
			this.$Message.error(res.data.message);
		}
	}), (0, _defineProperty3.default)(_methods, 'getdbData', function getdbData(res) {
		if (res.data.code == 0) {}
	}), (0, _defineProperty3.default)(_methods, 'openAgre', function openAgre(openCli) {
		if (openCli == true) _util2.default.restfullCall('/rest-ful/v3.0/mount/protocol', null, 'get', this.agreData);
	}), (0, _defineProperty3.default)(_methods, 'agreData', function agreData(obj) {
		if (obj.data.code == 0) {
			this.agreArr = obj.data.data;
		}
	}), (0, _defineProperty3.default)(_methods, 'agreOptionId', function agreOptionId(datas) {
		this.gzform.agre = datas;
	}), (0, _defineProperty3.default)(_methods, 'openClient', function openClient(openCli) {
		if (openCli == true) _util2.default.restfullCall('/rest-ful/v3.0/clients?nums=0&restype=' + this.setRowData.res_type_val, null, 'get', this.cliData);
	}), (0, _defineProperty3.default)(_methods, 'cliData', function cliData(obj) {
		var array = new Array();
		for (var i = 0; i < obj.data.data.vrts_client_data.length; i++) {
			array.push({
				id: obj.data.data.vrts_client_data[i].id,
				machine: obj.data.data.vrts_client_data[i].machine,
				os_type: obj.data.data.vrts_client_data[i].os_type
			});
		}
		this.clientSelect = array;

		this.gzform.cliName = this.clientSelect[0].machine;
	}), (0, _defineProperty3.default)(_methods, 'optionId', function optionId(datas) {
		var _this19 = this;

		this.dmsldata = [];
		this.gzform.id = '';
		this.gzform.cliName = '';
		this.gzform.client = datas;

		this.clientSelect.forEach(function (item, i) {
			if (item.id == datas) {
				_this19.osType = item.os_type;
			}
		});

		var url = 'rest-ful/v3.0/client/agent/instances?cid=' + datas + '&type=' + this.setRowData.res_type_val;
		_util2.default.restfullCall(url, null, 'get', function (obj) {
			var dmsldata = [];
			for (var i = 0; i < obj.data.data.length; i++) {
				var object = JSON.parse(obj.data.data[i].conf);
				object.id = obj.data.data[i].id;
				dmsldata.push(object);
			}
			for (var _i7 = 0; _i7 < dmsldata.length; _i7++) {
				_this19.dmsldata.push(dmsldata[_i7]);
			}
			_this19.gzform.id = _this19.dmsldata[0].id;
			_this19.gzform.cliName = _this19.dmsldata[0].host;
		});
	}), (0, _defineProperty3.default)(_methods, 'cancel', function cancel() {
		var _this20 = this;

		this.$nextTick(function () {
			_this20.onemodel = false;
			_this20.twomodel = false;
			_this20.expiremodel = false;
			_this20.placeonfileModel = false;
			$.fn.zTree.init($('#treeDemo'), _this20.setting, null);
			_this20.resPost = [];
			_this20.sqlsldata = [];
			_this20.vamTableList = [];
			_this20.vamsldata = [];
			_this20.dmsldata = [];
			_this20.sqltype = '';
			_this20.gzform.id = '';
			_this20.gzform = {};
			_this20.platformrestype = '';
			_this20.stecur = 0;
			_this20.scntimeshow = '';
			_this20.placeonfileDate.arc_device = '';
			_this20.placeonfileDate.arc_pool = '';
		});
	}), _methods)
};
/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(74)))

/***/ }),

/***/ 2815:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _defineProperty2 = __webpack_require__(89);

var _defineProperty3 = _interopRequireDefault(_defineProperty2);

var _methods;

var _vmOpt = __webpack_require__(2615);

var _vmOpt2 = _interopRequireDefault(_vmOpt);

var _uisOpt = __webpack_require__(2616);

var _uisOpt2 = _interopRequireDefault(_uisOpt);

var _casOpt = __webpack_require__(2617);

var _casOpt2 = _interopRequireDefault(_casOpt);

var _index = __webpack_require__(1640);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
	components: {
		vmOpt: _vmOpt2.default,
		uisOpt: _uisOpt2.default,
		casOpt: _casOpt2.default
	},
	props: ['uisTableList', 'resTypeVal', 'platformrestype', 'volid', 'sqltype'],
	data: function data() {
		return {
			traResStart: false,
			clientId: '',
			typedis: false,
			stecur: 0,
			uisCol: [{
				type: 'selection',
				width: 60,
				align: 'center'
			}, {
				title: '机器名',
				key: 'name'
			}],
			resPost: [],
			platformrestype: '',
			platformresname: '',
			gzform: {
				client: '',
				agre: 'NFS',
				path: '',
				desc: '',
				password: '',
				h3ccasuser: '',
				hpid: '',
				'case': '',
				cashost: '',
				uispool: '',
				rename: '',
				vswitchname: '',
				vswitchid: '',
				ccpool: ''
			},
			gzformRule: {
				id: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择实例名',
					trigger: 'change'
				}],
				dc: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择数据中心',
					trigger: 'change'
				}],
				hosts: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择主机',
					trigger: 'change'
				}],
				network: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择网段',
					trigger: 'change'
				}],
				name: [{ required: true, message: '请输入重命名', trigger: 'blur' }],
				path: [{ required: true, message: '请输入路径', trigger: 'blur' }],
				client: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择客户端',
					trigger: 'change'
				}],
				agre: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择协议',
					trigger: 'change'
				}],
				store: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择存储',
					trigger: 'change'
				}],
				diskmode: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择模式',
					trigger: 'change'
				}],
				datapath: [{ required: true, message: '数据文件路径', trigger: 'blur' }],
				cliName: [{ required: true, message: '实例名', trigger: 'blur' }]
			},
			restart: false,
			fictitious: false,
			clientSelect: [],
			agreArr: [],
			example: [],
			casHostList: [],
			uispoollist: [],
			yjlist: [{
				id: 1441792,
				name: 'H3C CAS'
			}, {
				id: 1507328,
				name: 'H3C UIS'
			}, {
				id: 327680,
				name: 'VMWARE'
			}]
		};
	},
	created: function created() {
		this.openAgre();
	},
	mounted: function mounted() {},

	computed: {
		getPower: function getPower() {
			return this.$store.state.power.module;
		}
	},
	methods: (_methods = {
		initClient: function initClient() {
			var _this = this;

			this.$store.commit('getResTypeData', this.platformrestype);
			(0, _index.getClientList)(this.platformrestype).then(function (obj) {
				var array = [];
				for (var i = 0; i < obj.data.vrts_client_data.length; i++) {
					array.push({
						id: obj.data.vrts_client_data[i].id,
						machine: obj.data.vrts_client_data[i].machine,
						os_type: obj.data.vrts_client_data[i].os_type
					});
				}
				_this.clientSelect = array;
				_this.gzform.client = _this.clientSelect[0].id;
				_this.clientId = _this.clientSelect[0].id;
			});
		},
		openPlat: function openPlat() {
			this.gzform.client = '';
		},
		getVmCase: function getVmCase(val) {
			this.gzform.case = val;
		},
		getVmDc: function getVmDc(val) {
			this.gzform.dc = val;
		},
		getVmHosts: function getVmHosts(val) {
			this.gzform.hosts = val;
		},
		getVmNetWork: function getVmNetWork(val) {
			this.gzform.network = val;
		},
		getVmDataStroe: function getVmDataStroe(val) {
			this.gzform.dataStroe = val;
		},
		getVmDistMode: function getVmDistMode(val) {
			this.gzform.diskmode = val;
		}
	}, (0, _defineProperty3.default)(_methods, 'getVmDistMode', function getVmDistMode(val) {
		this.gzform.diskmode = val;
	}), (0, _defineProperty3.default)(_methods, 'getVmRename', function getVmRename(val) {
		this.gzform.rename = val;
	}), (0, _defineProperty3.default)(_methods, 'getCasCase', function getCasCase(val) {
		this.gzform.case = val;
	}), (0, _defineProperty3.default)(_methods, 'getCasHpid', function getCasHpid(val) {
		this.gzform.hpid = val;
	}), (0, _defineProperty3.default)(_methods, 'getCasHostE', function getCasHostE(val) {
		this.gzform.addr = val.value;
		this.gzform.hostName = val.label;
	}), (0, _defineProperty3.default)(_methods, 'getCasVswitch', function getCasVswitch(el, val) {
		this.gzform.vswitchid = el;
		this.gzform.vswitchname = val;
	}), (0, _defineProperty3.default)(_methods, 'getCasClusterId', function getCasClusterId(val) {
		this.gzform.pool = val;
	}), (0, _defineProperty3.default)(_methods, 'getCasRename', function getCasRename(val) {
		this.gzform.rename = val;
	}), (0, _defineProperty3.default)(_methods, 'getCasCcPool', function getCasCcPool(val) {
		this.gzform.ccpool = val;
	}), (0, _defineProperty3.default)(_methods, 'getUisCase', function getUisCase(val) {
		this.gzform.case = val;
	}), (0, _defineProperty3.default)(_methods, 'getUisHpid', function getUisHpid(val) {
		this.gzform.hpid = val;
	}), (0, _defineProperty3.default)(_methods, 'getUispool', function getUispool(val) {
		this.gzform.uispool = val;
	}), (0, _defineProperty3.default)(_methods, 'getUisvswitch', function getUisvswitch(el, val) {
		this.gzform.vswitchid = el;
		this.gzform.vswitchname = val;
	}), (0, _defineProperty3.default)(_methods, 'getUisRestarte', function getUisRestarte(val) {
		this.restart = val;
	}), (0, _defineProperty3.default)(_methods, 'getUisRename', function getUisRename(val) {
		this.gzform.rename = val;
	}), (0, _defineProperty3.default)(_methods, 'getUisCcPool', function getUisCcPool(val) {
		this.gzform.ccpool = val;
	}), (0, _defineProperty3.default)(_methods, 'upstep', function upstep() {
		this.stecur -= 1;
	}), (0, _defineProperty3.default)(_methods, 'cancel', function cancel() {
		this.$emit('closeDrawer', false);
		this.stecur = 0;
		this.gzform = {};
		this.gzform.agre = 'NFS';
	}), (0, _defineProperty3.default)(_methods, 'vmOptList', function vmOptList() {
		var vmwareArr = [];
		if (this.sqltype == '恢复') {
			if (this.gzform.starVm) {
				vmwareArr.push({
					type: 33,
					value: ''
				}, {
					type: 35,
					value: this.gzform.case
				}, {
					type: 96,
					value: this.gzform.dc
				}, {
					type: 97,
					value: this.gzform.dataStroe
				}, {
					type: 98,
					value: this.gzform.hosts
				}, {
					type: 95,
					value: this.gzform.network
				}, {
					type: 34,
					value: this.gzform.rename
				}, {
					type: 120,
					value: ''
				});
			} else {
				vmwareArr.push({
					type: 35,
					value: this.gzform.case
				}, {
					type: 96,
					value: this.gzform.dc
				}, {
					type: 97,
					value: this.gzform.dataStroe
				}, {
					type: 98,
					value: this.gzform.hosts
				}, {
					type: 95,
					value: this.gzform.network
				}, {
					type: 34,
					value: this.gzform.rename
				}, {
					type: 120,
					value: ''
				});
			}
		} else {
			if (this.gzform.starVm) {
				vmwareArr.push({
					type: 33,
					value: ''
				}, {
					type: 35,
					value: this.gzform.case
				}, {
					type: 96,
					value: this.gzform.dc
				}, {
					type: 98,
					value: this.gzform.hosts
				}, {
					type: 95,
					value: this.gzform.network
				}, {
					type: 34,
					value: this.gzform.rename
				});
			} else {
				vmwareArr.push({
					type: 35,
					value: this.gzform.case
				}, {
					type: 96,
					value: this.gzform.dc
				}, {
					type: 98,
					value: this.gzform.hosts
				}, {
					type: 95,
					value: this.gzform.network
				}, {
					type: 34,
					value: this.gzform.rename
				});
			}
		}
		return vmwareArr;
	}), (0, _defineProperty3.default)(_methods, 'casOptList', function casOptList() {
		var h3ccasArr = [];
		if (this.sqltype == '恢复') {
			h3ccasArr.push({
				type: 35,
				value: this.gzform.case
			}, {
				type: 90,
				value: this.gzform.ccpool
			}, {
				type: 93,
				value: this.gzform.addr.toString()
			}, {
				type: 94,
				value: this.gzform.pool.toString()
			}, {
				type: 98,
				value: this.gzform.hostName
			}, {
				type: 91,
				value: this.gzform.hpid.toString()
			}, {
				type: 34,
				value: this.gzform.rename
			}, {
				type: 129,
				value: this.gzform.vswitchid.toString()
			}, {
				type: 128,
				value: this.gzform.vswitchname
			}, {
				type: 120,
				value: ''
			});
		} else {
			h3ccasArr.push({
				type: 35,
				value: this.gzform.case
			}, {
				type: 90,
				value: this.gzform.ccpool
			}, {
				type: 93,
				value: this.gzform.addr.toString()
			}, {
				type: 94,
				value: this.gzform.pool.toString()
			}, {
				type: 98,
				value: this.gzform.hostName
			}, {
				type: 91,
				value: this.gzform.hpid.toString()
			}, {
				type: 34,
				value: this.gzform.rename
			}, {
				type: 129,
				value: this.gzform.vswitchid.toString()
			}, {
				type: 128,
				value: this.gzform.vswitchname
			});
		}
		return h3ccasArr;
	}), (0, _defineProperty3.default)(_methods, 'uisOptList', function uisOptList() {
		var h3cuisArr = [];
		if (this.sqltype == '恢复') {
			if (this.restart) {
				h3cuisArr.push({
					type: 35,
					value: this.gzform.case
				}, {
					type: 90,
					value: this.gzform.ccpool
				}, {
					type: 91,
					value: this.gzform.hpid.toString()
				}, {
					type: 94,
					value: this.gzform.uispool.toString()
				}, {
					type: 34,
					value: this.gzform.rename
				}, {
					type: 128,
					value: this.gzform.vswitchname
				}, {
					type: 129,
					value: this.gzform.vswitchid.toString()
				}, {
					type: 120,
					value: ''
				}, {
					type: 33
				});
			} else {
				h3cuisArr.push({
					type: 35,
					value: this.gzform.case
				}, {
					type: 90,
					value: this.gzform.ccpool
				}, {
					type: 91,
					value: this.gzform.hpid.toString()
				}, {
					type: 94,
					value: this.gzform.uispool.toString()
				}, {
					type: 34,
					value: this.gzform.rename
				}, {
					type: 128,
					value: this.gzform.vswitchname
				}, {
					type: 129,
					value: this.gzform.vswitchid.toString()
				}, {
					type: 120,
					value: ''
				});
			}
		} else {
			if (this.restart) {
				h3cuisArr.push({
					type: 35,
					value: this.gzform.case
				}, {
					type: 90,
					value: this.gzform.ccpool
				}, {
					type: 91,
					value: this.gzform.hpid.toString()
				}, {
					type: 94,
					value: this.gzform.uispool.toString()
				}, {
					type: 34,
					value: this.gzform.rename
				}, {
					type: 128,
					value: this.gzform.vswitchname
				}, {
					type: 129,
					value: this.gzform.vswitchid.toString()
				}, {
					type: 33
				});
			} else {
				h3cuisArr.push({
					type: 35,
					value: this.gzform.case
				}, {
					type: 90,
					value: this.gzform.ccpool
				}, {
					type: 91,
					value: this.gzform.hpid.toString()
				}, {
					type: 94,
					value: this.gzform.uispool.toString()
				}, {
					type: 34,
					value: this.gzform.rename
				}, {
					type: 128,
					value: this.gzform.vswitchname
				}, {
					type: 129,
					value: this.gzform.vswitchid.toString()
				});
			}
		}
		return h3cuisArr;
	}), (0, _defineProperty3.default)(_methods, 'h3cCasObj', function h3cCasObj() {
		this.optionArr = this.casOptList();
		var postObj = {};
		return postObj = {
			volid: this.volid,
			client: this.gzform.client,
			protocol: this.gzform.agre,
			mountpath: this.gzform.path,
			options: this.optionArr,
			restype: this.platformrestype,
			resources: this.resPost,
			desc: this.gzform.desc
		};
	}), (0, _defineProperty3.default)(_methods, 'h3cUisObj', function h3cUisObj() {
		var postObj = {};

		this.optionArr = this.uisOptList();
		return postObj = {
			volid: this.volid,
			client: this.gzform.client,
			protocol: this.gzform.agre,
			mountpath: this.gzform.path,
			options: this.optionArr,
			restype: this.platformrestype,
			resources: this.resPost,
			desc: this.gzform.desc
		};
	}), (0, _defineProperty3.default)(_methods, 'h3cVmObj', function h3cVmObj() {
		var postObj = {};
		this.optionArr = this.vmOptList();
		return postObj = {
			volid: this.volid,
			client: this.gzform.client,
			protocol: this.gzform.agre,
			mountpath: this.gzform.path,
			options: this.optionArr,
			restype: this.platformrestype,
			resources: this.resPost,
			desc: this.gzform.desc
		};
	}), (0, _defineProperty3.default)(_methods, 'onmount', function onmount() {
		var _this2 = this;

		var postObj = {};

		if (this.platformrestype == 1441792) {
			postObj = this.h3cCasObj();
		}
		if (this.platformrestype == 1507328) {
			postObj = this.h3cUisObj();
		}
		if (this.platformrestype == 327680) {
			postObj = this.h3cVmObj();
		}

		(0, _index.onMount)(postObj).then(function (val) {
			if (val.code == 0) {
				_this2.$Message.success(val.message);
			} else {
				_this2.$Message.warning(val.message);
			}
			_this2.gzform = {};
			_this2.stecur = 0;
			_this2.$emit('closeDrawer', false);
		});
	}), (0, _defineProperty3.default)(_methods, 'nextstep', function nextstep() {
		if (this.resPost.length <= 0) {
			this.$Message.warning('请先勾选资源');
		} else {
			this.stecur += 1;
			if (this.stecur == 1) {
				this.initClient();
			}
		}
	}), (0, _defineProperty3.default)(_methods, 'torowdata', function torowdata(res, i) {
		this.$refs.selection.toggleSelect(i);
	}), (0, _defineProperty3.default)(_methods, 'tovawlist', function tovawlist(res) {
		var _this3 = this;

		var aa = res;
		if (aa.length > 0) {
			aa.forEach(function (item) {
				if (_this3.resPost.length > 0) {
					_this3.resPost.forEach(function (el, i) {
						if (el.path == item.name) {
							_this3.resPost.slice(i, 1);
						}
					});
				} else {
					_this3.resPost.push({ type: Number(item.type), path: item.name, desc: '', exclude: 0 });
				}
			});
		} else {
			this.resPost = [];
		}
	}), (0, _defineProperty3.default)(_methods, 'openClient', function openClient() {
		var _this4 = this;

		(0, _index.getClientList)(this.platformrestype).then(function (obj) {
			var array = [];
			for (var i = 0; i < obj.data.vrts_client_data.length; i++) {
				array.push({
					id: obj.data.vrts_client_data[i].id,
					machine: obj.data.vrts_client_data[i].machine,
					os_type: obj.data.vrts_client_data[i].os_type
				});
			}
			_this4.clientSelect = array;
		});
	}), (0, _defineProperty3.default)(_methods, 'optionId', function optionId(datas) {
		var _this5 = this;

		this.gzform.client = datas;
		this.clientId = datas;
		this.clientSelect.forEach(function (item, i) {
			if (item.id == datas) {
				_this5.osType = item.os_type;
			}
		});
	}), (0, _defineProperty3.default)(_methods, 'openAgre', function openAgre() {
		var _this6 = this;

		(0, _index.getProtocol)().then(function (obj) {
			if (obj.code == 0) {
				_this6.agreArr = obj.data;
				_this6.agreArr.forEach(function (item) {
					if (item == 'NFS') {
						_this6.gzform.agre = item;
					}
				});
			}
		});
	}), (0, _defineProperty3.default)(_methods, 'agreOptionId', function agreOptionId(datas) {
		this.gzform.agre = datas;
	}), (0, _defineProperty3.default)(_methods, 'changeyjid', function changeyjid(val) {
		var _this7 = this;

		this.platformrestype = val.value;

		this.$store.commit('getResTypeData', val.value);
		this.platformresname = val.label;

		(0, _index.getClientList)(this.platformrestype).then(function (obj) {
			var array = [];
			for (var i = 0; i < obj.data.vrts_client_data.length; i++) {
				array.push({
					id: obj.data.vrts_client_data[i].id,
					machine: obj.data.vrts_client_data[i].machine,
					os_type: obj.data.vrts_client_data[i].os_type
				});
			}
			_this7.clientSelect = array;
			_this7.gzform.client = _this7.clientSelect[0].id;
			_this7.clientId = _this7.clientSelect[0].id;
		});
	}), (0, _defineProperty3.default)(_methods, 'uisChange', function uisChange() {
		var _this8 = this;

		(0, _index.getAddress)(this.gzform.client, this.resTypeVal).then(function (obj) {
			var data = [];
			for (var i = 0; i < obj.data.length; i++) {
				var object = JSON.parse(obj.data[i].conf);
				data.push(object);
			}
			var array = new Array();
			for (var _i = 0; _i < data.length; _i++) {
				array.push({
					host: data[_i].addr.toString(),
					user: data[_i].user,
					password: data[_i].password
				});
			}
			_this8.example = array;
		});
	}), (0, _defineProperty3.default)(_methods, 'changesAddress', function changesAddress(datas) {
		this.gzform.case = datas.label;
		for (var i = 0; i < this.example.length; i++) {
			if (datas.value == this.example[i].host) {
				this.gzform.password = this.example[i].password;
				this.gzform.h3ccasuser = this.example[i].user;
			}
		}
	}), (0, _defineProperty3.default)(_methods, 'uisHostChange', function uisHostChange() {
		var _this9 = this;

		(0, _index.getHost)(this.gzform.case, this.gzform.h3ccasuser, this.gzform.password).then(function (res) {
			var casHostL = res.data;
			var array = [];
			if (casHostL.length > 0) {
				_this9.loadingShowp = false;
			}
			casHostL.forEach(function (item) {
				array.push({
					name: item.name,
					id: item.id
				});
			});
			_this9.casHostList = array;
		});
	}), (0, _defineProperty3.default)(_methods, 'onUisHostChange', function onUisHostChange(res) {
		this.gzform.hpid = res.value;
	}), (0, _defineProperty3.default)(_methods, 'getUisChange', function getUisChange() {
		var _this10 = this;

		(0, _index.getUisPool)(this.gzform.case, this.gzform.h3ccasuser, this.gzform.password, this.gzform.hpid).then(function (res) {
			var poolL = res.data;
			var array = [];
			poolL.forEach(function (item) {
				array.push({
					name: item.name,
					id: item.id
				});
			});
			_this10.uispoollist = array;
		});
	}), (0, _defineProperty3.default)(_methods, 'onUisChange', function onUisChange(res) {
		this.gzform.uispool = res.value;
	}), _methods)
};

/***/ }),

/***/ 2816:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _index = __webpack_require__(1640);

exports.default = {
	props: ['clientId', 'sqltype'],
	data: function data() {
		return {
			restart: false,
			fictitious: false,
			gzform: {
				client: '',
				agre: '',
				path: '',
				desc: '',
				password: '',
				h3ccasuser: '',
				hpid: '',
				'case': '',
				cashost: '',
				uispool: '',
				rename: '',
				dc: '',
				hosts: '',
				network: '',
				dataStroe: '',
				diskmode: '',
				starVm: false,
				powerType: ''
			},
			gzformRule: {
				id: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择实例名',
					trigger: 'change'
				}],
				dc: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择数据中心',
					trigger: 'change'
				}],
				hosts: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择主机',
					trigger: 'change'
				}],
				network: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择网段',
					trigger: 'change'
				}],
				name: [{ required: true, message: '请输入重命名', trigger: 'blur' }],
				path: [{ required: true, message: '请输入路径', trigger: 'blur' }],
				client: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择客户端',
					trigger: 'change'
				}],
				agre: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择协议',
					trigger: 'change'
				}],
				store: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择存储',
					trigger: 'change'
				}],
				diskmode: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择模式',
					trigger: 'change'
				}],
				datapath: [{ required: true, message: '数据文件路径', trigger: 'blur' }],
				cliName: [{ required: true, message: '实例名', trigger: 'blur' }]
			},
			clientSelect: [],
			agreArr: [],
			example: [],
			casHostList: [],
			uispoollist: [],
			centerlist: [],
			hostslist: [],
			networklist: [],
			modelist: [],
			storelist: [],
			vamsldata: []
		};
	},

	computed: {
		resTypeData: function resTypeData() {
			return this.$store.state.index.resTypeData;
		}
	},
	mounted: function mounted() {},

	methods: {
		PowerOn: function PowerOn(val) {
			this.gzform.starVm = val;
			this.$emit('getPowerVal', this.gzform.starVm);
			if (val) {
				this.gzform.powerType = 33;
				this.$emit('getPowerType', this.gzform.powerType);
			}
		},
		vmChange: function vmChange() {
			var _this = this;

			(0, _index.getAddress)(this.clientId, this.resTypeData).then(function (obj) {
				var data = [];
				for (var i = 0; i < obj.data.length; i++) {
					var object = JSON.parse(obj.data[i].conf);
					data.push(object);
				}
				var array = new Array();
				for (var _i = 0; _i < data.length; _i++) {
					array.push({
						host: data[_i].host.toString(),
						user: data[_i].user,
						password: data[_i].password
					});
				}
				_this.vamsldata = array;
			});
		},
		vawarechange: function vawarechange(val) {
			var _this2 = this;

			this.gzform.case = val;
			this.$emit('getVmCase', this.gzform.case);
			this.gzform.host = val;
			for (var i = 0; i < this.vamsldata.length; i++) {
				if (val == this.vamsldata[i].host) {
					this.gzform.password = this.vamsldata[i].password;
					this.gzform.user = this.vamsldata[i].user;
				}
			}

			(0, _index.getDataCenter)(val, this.gzform.user, this.gzform.password).then(function (res) {
				_this2.centerlist = res.data;
			});
		},
		onCenterChange: function onCenterChange(res) {
			this.gzform.dc = res.value;
			this.$emit('getVmDc', this.gzform.dc);
		},
		hostsChange: function hostsChange() {
			var _this3 = this;

			(0, _index.getVmwareHost)(this.gzform.host, this.gzform.user, this.gzform.password, this.gzform.dc).then(function (res) {
				_this3.hostslist = res.data;
			});
		},
		getHostchange: function getHostchange(val) {
			this.gzform.hosts = val;
			this.$emit('getVmHosts', this.gzform.hosts);
		},
		netWorkChange: function netWorkChange() {
			var _this4 = this;

			(0, _index.getVmwareNetwork)(this.gzform.host, this.gzform.user, this.gzform.password, this.gzform.dc, this.gzform.hosts).then(function (res) {
				_this4.networklist = res.data;
			});
		},
		onNetWork: function onNetWork(res) {
			this.gzform.network = res.label;
			this.$emit('getVmNetWork', this.gzform.network);
		},
		dataStoreChange: function dataStoreChange() {
			var _this5 = this;

			(0, _index.getVmwareStore)(this.gzform.host, this.gzform.user, this.gzform.password, this.gzform.dc, this.gzform.hosts).then(function (res) {
				_this5.storelist = res.data;
			});
		},
		onStoreChange: function onStoreChange(res) {
			this.gzform.dataStroe = res.value;
			this.$emit('getVmDataStroe', this.gzform.dataStroe);
		},
		diskModeChange: function diskModeChange() {
			var _this6 = this;

			(0, _index.getVmwareMode)().then(function (res) {
				_this6.modelist = res.data;
			});
		},
		onDistMode: function onDistMode(res) {
			this.gzform.diskmode = res.value;
			this.$emit('getVmDistMode', this.gzform.diskmode);
		},
		onVmRename: function onVmRename(val) {
			this.gzform.rename = val;

			this.$emit('getVmRename', this.gzform.rename);
		}
	}
};

/***/ }),

/***/ 2817:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _index = __webpack_require__(1640);

exports.default = {
	props: ['clientId'],
	data: function data() {
		return {
			traResStart: false,
			restart: false,
			fictitious: false,
			gzform: {
				client: '',
				agre: '',
				path: '',
				desc: '',
				password: '',
				h3ccasuser: '',
				hpid: '',
				'case': '',
				cashost: '',
				uispool: '',
				rename: '',
				dc: '',
				hosts: '',
				network: '',
				dataStroe: '',
				diskmode: '',
				vswitchid: '',
				starVm: false,
				powerType: '',
				ccpool: ''
			},
			gzformRule: {
				id: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择实例名',
					trigger: 'change'
				}],
				dc: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择数据中心',
					trigger: 'change'
				}],
				hosts: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择主机',
					trigger: 'change'
				}],
				network: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择网段',
					trigger: 'change'
				}],
				name: [{ required: true, message: '请输入重命名', trigger: 'blur' }],
				path: [{ required: true, message: '请输入路径', trigger: 'blur' }],
				client: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择客户端',
					trigger: 'change'
				}],
				agre: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择协议',
					trigger: 'change'
				}],
				store: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择存储',
					trigger: 'change'
				}],
				diskmode: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择模式',
					trigger: 'change'
				}],
				datapath: [{ required: true, message: '数据文件路径', trigger: 'blur' }],
				cliName: [{ required: true, message: '实例名', trigger: 'blur' }]
			},
			clientSelect: [],
			agreArr: [],
			example: [],
			casHostList: [],
			uispoollist: [],
			uisVswitchlist: [],
			uisCcPoolList: []
		};
	},

	computed: {
		resTypeData: function resTypeData() {
			return this.$store.state.index.resTypeData;
		}
	},
	mounted: function mounted() {},

	methods: {
		onTraRes: function onTraRes(val) {
			this.$emit('getUisTraRes', val);
		},
		PowerOn: function PowerOn(val) {
			this.restart = val;
			this.$emit('getUisPowerVal', this.gzform.starVm);
			if (val) {
				this.gzform.powerType = 33;
				this.$emit('getUisPowerType', this.gzform.powerType);
			}
		},
		uisChange: function uisChange() {
			var _this = this;

			(0, _index.getAddress)(this.clientId, this.resTypeData).then(function (obj) {
				var data = [];
				for (var i = 0; i < obj.data.length; i++) {
					var object = JSON.parse(obj.data[i].conf);
					data.push(object);
				}
				var array = new Array();
				for (var _i = 0; _i < data.length; _i++) {
					array.push({
						host: data[_i].addr.toString(),
						user: data[_i].user,
						password: data[_i].password
					});
				}
				_this.example = array;
			});
		},
		changesAddress: function changesAddress(datas) {
			this.gzform.case = datas.label;
			for (var i = 0; i < this.example.length; i++) {
				if (datas.value == this.example[i].host) {
					this.gzform.password = this.example[i].password;
					this.gzform.h3ccasuser = this.example[i].user;
				}
			}

			this.$emit('getUisCase', this.gzform.case);
		},
		uisHostChange: function uisHostChange() {
			var _this2 = this;

			(0, _index.getHost)(this.gzform.case, this.gzform.h3ccasuser, this.gzform.password).then(function (res) {
				var casHostL = res.data;
				var array = [];
				if (casHostL.length > 0) {
					_this2.loadingShowp = false;
				}
				casHostL.forEach(function (item) {
					array.push({
						name: item.name,
						id: item.id
					});
				});
				_this2.casHostList = array;
			});
		},
		onUisHostChange: function onUisHostChange(res) {
			this.gzform.hpid = res.value;
			this.$emit('getUisHpid', this.gzform.hpid);
		},
		getUisCcPool: function getUisCcPool() {
			var _this3 = this;

			(0, _index.getUisCcPool)(this.gzform.case, this.gzform.h3ccasuser, this.gzform.password, this.gzform.hpid).then(function (res) {
				_this3.uisCcPoolList = res.data;
			});
		},
		onCcPoolChange: function onCcPoolChange(val) {
			this.gzform.ccpool = val.value;
			this.$emit('getUisCcPool', this.gzform.ccpool);
		},
		getUisChange: function getUisChange() {
			var _this4 = this;

			(0, _index.getUisPool)(this.gzform.case, this.gzform.h3ccasuser, this.gzform.password, this.gzform.hpid).then(function (res) {
				var poolL = res.data;
				var array = [];
				poolL.forEach(function (item) {
					array.push({
						name: item.name,
						id: item.id
					});
				});
				_this4.uispoollist = array;
			});
		},
		onUisChange: function onUisChange(res) {
			this.gzform.uispool = res.value;
			this.$emit('getUispool', this.gzform.uispool);
		},
		getUisvswitch: function getUisvswitch() {
			var _this5 = this;

			(0, _index.getUisvswitch)(this.gzform.case, this.gzform.h3ccasuser, this.gzform.password, this.gzform.hpid).then(function (res) {
				_this5.uisVswitchlist = res.data;
			});
		},
		onUisvswitch: function onUisvswitch(val) {
			this.gzform.vswitchid = val.value;
			this.gzform.vswitchname = val.label;
			this.$emit('getUisvswitch', this.gzform.vswitchid, this.gzform.vswitchname);
		},
		onRestarte: function onRestarte(val) {
			this.restart = val;
			this.$emit('getUisRestarte', val);
		},
		onRename: function onRename(val) {
			this.gzform.rename = val;
			this.$emit('getUisRename', this.gzform.rename);
		}
	}
};

/***/ }),

/***/ 2818:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _index = __webpack_require__(1640);

exports.default = {
	props: ['clientId'],
	data: function data() {
		return {
			restart: false,
			fictitious: false,
			gzform: {
				client: '',
				agre: '',
				path: '',
				desc: '',
				password: '',
				h3ccasuser: '',
				hpid: '',
				'case': '',
				cashost: '',
				uispool: '',
				rename: '',
				dc: '',
				hosts: '',
				network: '',
				dataStroe: '',
				diskmode: '',
				starVm: false,
				powerType: '',
				ccpool: ''
			},
			gzformRule: {
				id: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择实例名',
					trigger: 'change'
				}],
				dc: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择数据中心',
					trigger: 'change'
				}],
				hosts: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择主机',
					trigger: 'change'
				}],
				network: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择网段',
					trigger: 'change'
				}],
				name: [{ required: true, message: '请输入重命名', trigger: 'blur' }],
				path: [{ required: true, message: '请输入路径', trigger: 'blur' }],
				client: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择客户端',
					trigger: 'change'
				}],
				agre: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择协议',
					trigger: 'change'
				}],
				store: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择存储',
					trigger: 'change'
				}],
				diskmode: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择模式',
					trigger: 'change'
				}],
				datapath: [{ required: true, message: '数据文件路径', trigger: 'blur' }],
				cliName: [{ required: true, message: '实例名', trigger: 'blur' }]
			},
			clientSelect: [],
			agreArr: [],
			example: [],
			casHostList: [],
			uispoollist: [],
			hostlist: [],
			poollist: [],
			vswitchlist: [],
			casCcPoolList: []
		};
	},

	computed: {
		resTypeData: function resTypeData() {
			return this.$store.state.index.resTypeData;
		}
	},
	mounted: function mounted() {},

	methods: {
		PowerOn: function PowerOn(val) {
			this.gzform.starVm = val;
			this.$emit('getCasPowerVal', this.gzform.starVm);
			if (val) {
				this.gzform.powerType = 33;
				this.$emit('getCasPowerType', this.gzform.powerType);
			}
		},
		caseChange: function caseChange() {
			var _this = this;

			(0, _index.getAddress)(this.clientId, this.resTypeData).then(function (obj) {
				var data = [];
				for (var i = 0; i < obj.data.length; i++) {
					var object = JSON.parse(obj.data[i].conf);
					data.push(object);
				}
				var array = new Array();
				for (var _i = 0; _i < data.length; _i++) {
					array.push({
						host: data[_i].addr.toString(),
						user: data[_i].user,
						password: data[_i].password
					});
				}
				_this.example = array;
			});
		},
		changesAddress: function changesAddress(datas) {
			this.gzform.case = datas.label;
			this.$emit('getCasCase', this.gzform.case);
			for (var i = 0; i < this.example.length; i++) {
				if (datas.value == this.example[i].host) {
					this.gzform.password = this.example[i].password;
					this.gzform.h3ccasuser = this.example[i].user;
				}
			}
		},
		uisHostChange: function uisHostChange() {
			var _this2 = this;

			(0, _index.getCasHost)(this.gzform.case, this.gzform.h3ccasuser, this.gzform.password, this.gzform.hostId, this.gzform.clusterId).then(function (res) {
				var casHostL = res.data;
				var array = [];
				if (casHostL.length > 0) {
					_this2.loadingShowp = false;
				}
				casHostL.forEach(function (item) {
					array.push({
						name: item.name,
						id: item.id
					});
				});
				_this2.casHostList = array;
			});
		},
		onUisHostChange: function onUisHostChange(res) {
			this.gzform.hpid = res.value;
			this.$emit('getCasHpid', this.gzform.hpid);
		},
		hostChange: function hostChange() {
			var _this3 = this;

			(0, _index.getHostPool)(this.gzform.case, this.gzform.h3ccasuser, this.gzform.password).then(function (res) {
				var hostL = res.data;
				var array = [];

				hostL.forEach(function (item) {
					array.push({
						hostpool: item.hostpool,
						id: item.id,
						name: item.name
					});
				});
				_this3.hostlist = array;
			});
		},
		onHostChange: function onHostChange(res) {
			this.gzform.hostId = res.value;
			this.gzform.hostName = res.label;
			this.gzform.addr = res.value;
			this.$emit('getCasHostE', res);
		},
		poolChange: function poolChange() {
			var _this4 = this;

			(0, _index.getCasPool)(this.gzform.case, this.gzform.h3ccasuser, this.gzform.password, this.gzform.hostId).then(function (res) {
				var poolL = res.data;
				var array = [];
				if (poolL.length > 0) {
					_this4.loadingShowp = false;
				}
				poolL.forEach(function (item) {
					array.push({
						name: item.name,
						id: item.id
					});
				});
				_this4.poollist = array;
			});
		},
		onPoolChange: function onPoolChange(res) {
			this.gzform.clusterId = res.value;
			this.$emit('getCasClusterId', this.gzform.clusterId);
		},
		getCasCcPool: function getCasCcPool() {
			var _this5 = this;

			(0, _index.getCasCcPool)(this.gzform.case, this.gzform.h3ccasuser, this.gzform.password, this.gzform.hpid).then(function (res) {
				_this5.casCcPoolList = res.data;
			});
		},
		onCcPoolChange: function onCcPoolChange(val) {
			this.gzform.ccpool = val.value;
			this.$emit('getCasCcPool', this.gzform.ccpool);
		},
		getvswitch: function getvswitch() {
			var _this6 = this;

			(0, _index.getVswitch)(this.gzform.case, this.gzform.h3ccasuser, this.gzform.password, this.gzform.hpid).then(function (res) {
				_this6.vswitchlist = res.data;
			});
		},
		onvswitch: function onvswitch(val) {
			this.gzform.vswitchid = val.value;
			this.gzform.vswitchname = val.label;
			this.$emit('getCasVswitch', this.gzform.vswitchid, this.gzform.vswitchname);
		},
		onCasRename: function onCasRename(val) {
			this.gzform.rename = val;
			this.$emit('getCasRename', this.gzform.rename);
		}
	}
};

/***/ }),

/***/ 2819:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _defineProperty2 = __webpack_require__(89);

var _defineProperty3 = _interopRequireDefault(_defineProperty2);

var _methods;

var _vmOpt = __webpack_require__(2615);

var _vmOpt2 = _interopRequireDefault(_vmOpt);

var _uisOpt = __webpack_require__(2616);

var _uisOpt2 = _interopRequireDefault(_uisOpt);

var _casOpt = __webpack_require__(2617);

var _casOpt2 = _interopRequireDefault(_casOpt);

var _index = __webpack_require__(1640);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
	props: ['casTableList', 'resTypeVal', 'platformrestype', 'volid', 'sqltype'],
	components: {
		vmOpt: _vmOpt2.default,
		uisOpt: _uisOpt2.default,
		casOpt: _casOpt2.default
	},
	data: function data() {
		return {
			clientId: '',
			crossType: '',
			typedis: false,
			stecur: 0,
			casCol: [{
				type: 'selection',
				width: 60,
				align: 'center'
			}, {
				title: '机器名',
				key: 'name'
			}],
			resPost: [],
			platformrestype: '',
			platformresname: '',
			gzform: {
				client: '',
				agre: 'NFS',
				path: '',
				desc: '',
				password: '',
				h3ccasuser: '',
				hpid: '',
				'case': '',
				cashost: '',
				uispool: '',
				rename: '',
				hostId: '',
				hostName: '',
				clusterId: '',
				vswitchid: '',
				vswitchname: '',
				ccpool: ''
			},
			gzformRule: {
				id: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择实例名',
					trigger: 'change'
				}],
				dc: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择数据中心',
					trigger: 'change'
				}],
				hosts: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择主机',
					trigger: 'change'
				}],
				network: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择网段',
					trigger: 'change'
				}],
				name: [{ required: true, message: '请输入重命名', trigger: 'blur' }],
				path: [{ required: true, message: '请输入路径', trigger: 'blur' }],
				client: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择客户端',
					trigger: 'change'
				}],
				agre: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择协议',
					trigger: 'change'
				}],
				store: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择存储',
					trigger: 'change'
				}],
				diskmode: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择模式',
					trigger: 'change'
				}],
				datapath: [{ required: true, message: '数据文件路径', trigger: 'blur' }],
				cliName: [{ required: true, message: '实例名', trigger: 'blur' }]
			},
			restart: false,
			fictitious: false,
			clientSelect: [],
			agreArr: [],
			example: [],
			casHostList: [],
			uispoollist: [],
			hostlist: [],
			poollist: [],
			vswitchlist: [],
			yjlist: [{
				id: 1441792,
				name: 'H3C CAS'
			}, {
				id: 1507328,
				name: 'H3C UIS'
			}, {
				id: 327680,
				name: 'VMWARE'
			}]
		};
	},
	created: function created() {
		this.openAgre();
	},
	mounted: function mounted() {},

	computed: {
		getPower: function getPower() {
			return this.$store.state.power.module;
		}
	},
	watch: {},
	methods: (_methods = {
		initClient: function initClient() {
			var _this = this;

			this.$store.commit('getResTypeData', this.platformrestype);
			(0, _index.getClientList)(this.platformrestype).then(function (obj) {
				var array = [];
				for (var i = 0; i < obj.data.vrts_client_data.length; i++) {
					array.push({
						id: obj.data.vrts_client_data[i].id,
						machine: obj.data.vrts_client_data[i].machine,
						os_type: obj.data.vrts_client_data[i].os_type
					});
				}
				_this.clientSelect = array;
				_this.gzform.client = _this.clientSelect[0].id;
				_this.clientId = _this.clientSelect[0].id;
			});
		},
		openPlat: function openPlat() {
			this.gzform.client = '';
		},
		getVmCase: function getVmCase(val) {
			this.gzform.case = val;
		},
		getVmDc: function getVmDc(val) {
			this.gzform.dc = val;
		},
		getVmHosts: function getVmHosts(val) {
			this.gzform.hosts = val;
		},
		getVmNetWork: function getVmNetWork(val) {
			this.gzform.network = val;
		},
		getVmDataStroe: function getVmDataStroe(val) {
			this.gzform.dataStroe = val;
		},
		getVmDistMode: function getVmDistMode(val) {
			this.gzform.diskmode = val;
		}
	}, (0, _defineProperty3.default)(_methods, 'getVmDistMode', function getVmDistMode(val) {
		this.gzform.diskmode = val;
	}), (0, _defineProperty3.default)(_methods, 'getVmRename', function getVmRename(val) {
		this.gzform.rename = val;
	}), (0, _defineProperty3.default)(_methods, 'getCasCase', function getCasCase(val) {
		this.gzform.case = val;
	}), (0, _defineProperty3.default)(_methods, 'getCasHpid', function getCasHpid(val) {
		this.gzform.hpid = val;
	}), (0, _defineProperty3.default)(_methods, 'getCasHostE', function getCasHostE(val) {
		this.gzform.addr = val.value;
		this.gzform.hostName = val.label;
	}), (0, _defineProperty3.default)(_methods, 'getCasVswitch', function getCasVswitch(el, val) {
		this.gzform.vswitchid = el;
		this.gzform.vswitchname = val;
	}), (0, _defineProperty3.default)(_methods, 'getCasClusterId', function getCasClusterId(val) {
		this.gzform.pool = val;
	}), (0, _defineProperty3.default)(_methods, 'getCasRename', function getCasRename(val) {
		this.gzform.rename = val;
	}), (0, _defineProperty3.default)(_methods, 'getCasCcPool', function getCasCcPool(val) {
		this.gzform.ccpool = val;
	}), (0, _defineProperty3.default)(_methods, 'getUisCase', function getUisCase(val) {
		this.gzform.case = val;
	}), (0, _defineProperty3.default)(_methods, 'getUisHpid', function getUisHpid(val) {
		this.gzform.hpid = val;
	}), (0, _defineProperty3.default)(_methods, 'getUispool', function getUispool(val) {
		this.gzform.uispool = val;
	}), (0, _defineProperty3.default)(_methods, 'getUisvswitch', function getUisvswitch(el, val) {
		this.gzform.vswitchid = el;
		this.gzform.vswitchname = val;
	}), (0, _defineProperty3.default)(_methods, 'getUisRestarte', function getUisRestarte(val) {
		this.restart = val;
	}), (0, _defineProperty3.default)(_methods, 'getUisRename', function getUisRename(val) {
		this.gzform.rename = val;
	}), (0, _defineProperty3.default)(_methods, 'getUisCcPool', function getUisCcPool(val) {
		this.gzform.ccpool = val;
	}), (0, _defineProperty3.default)(_methods, 'upstep', function upstep() {
		this.stecur -= 1;
	}), (0, _defineProperty3.default)(_methods, 'cancel', function cancel() {
		this.$emit('closeDrawer', false);
		this.stecur = 0;
		this.gzform = {};
		this.gzform.agre = 'NFS';
	}), (0, _defineProperty3.default)(_methods, 'vmOptList', function vmOptList() {
		var vmwareArr = [];
		if (this.sqltype == '恢复') {
			if (this.gzform.starVm) {
				vmwareArr.push({
					type: 33,
					value: ''
				}, {
					type: 35,
					value: this.gzform.case
				}, {
					type: 96,
					value: this.gzform.dc
				}, {
					type: 97,
					value: this.gzform.dataStroe
				}, {
					type: 98,
					value: this.gzform.hosts
				}, {
					type: 95,
					value: this.gzform.network
				}, {
					type: 34,
					value: this.gzform.rename
				}, {
					type: 120,
					value: ''
				});
			} else {
				vmwareArr.push({
					type: 35,
					value: this.gzform.case
				}, {
					type: 96,
					value: this.gzform.dc
				}, {
					type: 97,
					value: this.gzform.dataStroe
				}, {
					type: 98,
					value: this.gzform.hosts
				}, {
					type: 95,
					value: this.gzform.network
				}, {
					type: 34,
					value: this.gzform.rename
				}, {
					type: 120,
					value: ''
				});
			}
		} else {
			if (this.gzform.starVm) {
				vmwareArr.push({
					type: 33,
					value: ''
				}, {
					type: 35,
					value: this.gzform.case
				}, {
					type: 96,
					value: this.gzform.dc
				}, {
					type: 98,
					value: this.gzform.hosts
				}, {
					type: 95,
					value: this.gzform.network
				}, {
					type: 34,
					value: this.gzform.rename
				});
			} else {
				vmwareArr.push({
					type: 35,
					value: this.gzform.case
				}, {
					type: 96,
					value: this.gzform.dc
				}, {
					type: 98,
					value: this.gzform.hosts
				}, {
					type: 95,
					value: this.gzform.network
				}, {
					type: 34,
					value: this.gzform.rename
				});
			}
		}
		return vmwareArr;
	}), (0, _defineProperty3.default)(_methods, 'casOptList', function casOptList() {
		var h3ccasArr = [];
		if (this.sqltype == '恢复') {
			h3ccasArr.push({
				type: 35,
				value: this.gzform.case
			}, {
				type: 90,
				value: this.gzform.ccpool
			}, {
				type: 93,
				value: this.gzform.addr.toString()
			}, {
				type: 94,
				value: this.gzform.pool.toString()
			}, {
				type: 98,
				value: this.gzform.hostName
			}, {
				type: 91,
				value: this.gzform.hpid.toString()
			}, {
				type: 34,
				value: this.gzform.rename
			}, {
				type: 129,
				value: this.gzform.vswitchid.toString()
			}, {
				type: 128,
				value: this.gzform.vswitchname
			}, {
				type: 120,
				value: ''
			});
		} else {
			h3ccasArr.push({
				type: 35,
				value: this.gzform.case
			}, {
				type: 90,
				value: this.gzform.ccpool
			}, {
				type: 93,
				value: this.gzform.addr.toString()
			}, {
				type: 94,
				value: this.gzform.pool.toString()
			}, {
				type: 98,
				value: this.gzform.hostName
			}, {
				type: 91,
				value: this.gzform.hpid.toString()
			}, {
				type: 34,
				value: this.gzform.rename
			}, {
				type: 129,
				value: this.gzform.vswitchid.toString()
			}, {
				type: 128,
				value: this.gzform.vswitchname
			});
		}
		return h3ccasArr;
	}), (0, _defineProperty3.default)(_methods, 'uisOptList', function uisOptList() {
		var h3cuisArr = [];
		if (this.sqltype == '恢复') {
			if (this.restart) {
				h3cuisArr.push({
					type: 35,
					value: this.gzform.case
				}, {
					type: 90,
					value: this.gzform.ccpool
				}, {
					type: 91,
					value: this.gzform.hpid.toString()
				}, {
					type: 94,
					value: this.gzform.uispool.toString()
				}, {
					type: 34,
					value: this.gzform.rename
				}, {
					type: 128,
					value: this.gzform.vswitchname
				}, {
					type: 129,
					value: this.gzform.vswitchid.toString()
				}, {
					type: 120,
					value: ''
				}, {
					type: 33
				});
			} else {
				h3cuisArr.push({
					type: 35,
					value: this.gzform.case
				}, {
					type: 90,
					value: this.gzform.ccpool
				}, {
					type: 91,
					value: this.gzform.hpid.toString()
				}, {
					type: 94,
					value: this.gzform.uispool.toString()
				}, {
					type: 34,
					value: this.gzform.rename
				}, {
					type: 128,
					value: this.gzform.vswitchname
				}, {
					type: 129,
					value: this.gzform.vswitchid.toString()
				}, {
					type: 120,
					value: ''
				});
			}
		} else {
			if (this.restart) {
				h3cuisArr.push({
					type: 35,
					value: this.gzform.case
				}, {
					type: 90,
					value: this.gzform.ccpool
				}, {
					type: 91,
					value: this.gzform.hpid.toString()
				}, {
					type: 94,
					value: this.gzform.uispool.toString()
				}, {
					type: 34,
					value: this.gzform.rename
				}, {
					type: 128,
					value: this.gzform.vswitchname
				}, {
					type: 129,
					value: this.gzform.vswitchid.toString()
				}, {
					type: 33
				});
			} else {
				h3cuisArr.push({
					type: 35,
					value: this.gzform.case
				}, {
					type: 90,
					value: this.gzform.ccpool
				}, {
					type: 91,
					value: this.gzform.hpid.toString()
				}, {
					type: 94,
					value: this.gzform.uispool.toString()
				}, {
					type: 34,
					value: this.gzform.rename
				}, {
					type: 128,
					value: this.gzform.vswitchname
				}, {
					type: 129,
					value: this.gzform.vswitchid.toString()
				});
			}
		}
		return h3cuisArr;
	}), (0, _defineProperty3.default)(_methods, 'h3cCasObj', function h3cCasObj() {
		this.optionArr = this.casOptList();
		var postObj = {};
		return postObj = {
			volid: this.volid,
			client: this.gzform.client,
			protocol: this.gzform.agre,
			mountpath: this.gzform.path,
			options: this.optionArr,
			restype: this.platformrestype,
			resources: this.resPost,
			desc: this.gzform.desc
		};
	}), (0, _defineProperty3.default)(_methods, 'h3cUisObj', function h3cUisObj() {
		var postObj = {};

		this.optionArr = this.uisOptList();
		return postObj = {
			volid: this.volid,
			client: this.gzform.client,
			protocol: this.gzform.agre,
			mountpath: this.gzform.path,
			options: this.optionArr,
			restype: this.platformrestype,
			resources: this.resPost,
			desc: this.gzform.desc
		};
	}), (0, _defineProperty3.default)(_methods, 'h3cVmObj', function h3cVmObj() {
		var postObj = {};
		this.optionArr = this.vmOptList();
		return postObj = {
			volid: this.volid,
			client: this.gzform.client,
			protocol: this.gzform.agre,
			mountpath: this.gzform.path,
			options: this.optionArr,
			restype: this.platformrestype,
			resources: this.resPost,
			desc: this.gzform.desc
		};
	}), (0, _defineProperty3.default)(_methods, 'onmount', function onmount() {
		var _this2 = this;

		var postObj = {};

		if (this.platformrestype == 1441792) {
			postObj = this.h3cCasObj();
		}
		if (this.platformrestype == 1507328) {
			postObj = this.h3cUisObj();
		}
		if (this.platformrestype == 327680) {
			postObj = this.h3cVmObj();
		}

		(0, _index.onMount)(postObj).then(function (val) {
			if (val.code == 0) {
				_this2.$Message.success(val.message);
			} else {
				_this2.$Message.warning(val.message);
			}
			_this2.gzform = {};
			_this2.stecur = 0;
			_this2.$emit('closeDrawer', false);
		});
	}), (0, _defineProperty3.default)(_methods, 'nextstep', function nextstep() {
		if (this.resPost.length <= 0) {
			this.$Message.warning('请先勾选资源');
		} else {
			this.stecur += 1;
			if (this.stecur == 1) {
				this.initClient();
			}
		}
	}), (0, _defineProperty3.default)(_methods, 'torowdata', function torowdata(res, i) {
		this.$refs.selection.toggleSelect(i);
	}), (0, _defineProperty3.default)(_methods, 'tovawlist', function tovawlist(res) {
		var _this3 = this;

		var aa = res;
		if (aa.length > 0) {
			aa.forEach(function (item) {
				if (_this3.resPost.length > 0) {
					_this3.resPost.forEach(function (el, i) {
						if (el.path == item.name) {
							_this3.resPost.slice(i, 1);
						}
					});
				} else {
					_this3.resPost.push({ type: Number(item.type), path: item.name, desc: '', exclude: 0 });
				}
			});
		} else {
			this.resPost = [];
		}
	}), (0, _defineProperty3.default)(_methods, 'openClient', function openClient() {
		var _this4 = this;

		(0, _index.getClientList)(this.platformrestype).then(function (obj) {
			var array = [];
			for (var i = 0; i < obj.data.vrts_client_data.length; i++) {
				array.push({
					id: obj.data.vrts_client_data[i].id,
					machine: obj.data.vrts_client_data[i].machine,
					os_type: obj.data.vrts_client_data[i].os_type
				});
			}
			_this4.clientSelect = array;
		});
	}), (0, _defineProperty3.default)(_methods, 'optionId', function optionId(datas) {
		var _this5 = this;

		this.gzform.client = datas;
		this.clientId = datas;
		this.clientSelect.forEach(function (item, i) {
			if (item.id == datas) {
				_this5.osType = item.os_type;
			}
		});
	}), (0, _defineProperty3.default)(_methods, 'openAgre', function openAgre() {
		var _this6 = this;

		(0, _index.getProtocol)().then(function (obj) {
			if (obj.code == 0) {
				_this6.agreArr = obj.data;
				_this6.agreArr.forEach(function (item) {
					if (item == 'NFS') {
						_this6.gzform.agre = item;
					}
				});
			}
		});
	}), (0, _defineProperty3.default)(_methods, 'agreOptionId', function agreOptionId(datas) {
		this.gzform.agre = datas;
	}), (0, _defineProperty3.default)(_methods, 'changeyjid', function changeyjid(val) {
		var _this7 = this;

		this.platformrestype = val.value;

		this.$store.commit('getResTypeData', val.value);
		this.platformresname = val.label;

		(0, _index.getClientList)(this.platformrestype).then(function (obj) {
			var array = [];
			for (var i = 0; i < obj.data.vrts_client_data.length; i++) {
				array.push({
					id: obj.data.vrts_client_data[i].id,
					machine: obj.data.vrts_client_data[i].machine,
					os_type: obj.data.vrts_client_data[i].os_type
				});
			}
			_this7.clientSelect = array;
			_this7.gzform.client = _this7.clientSelect[0].id;
			_this7.clientId = _this7.clientSelect[0].id;
		});
	}), (0, _defineProperty3.default)(_methods, 'caseChange', function caseChange() {
		var _this8 = this;

		(0, _index.getAddress)(this.gzform.client, this.resTypeVal).then(function (obj) {
			var data = [];
			for (var i = 0; i < obj.data.length; i++) {
				var object = JSON.parse(obj.data[i].conf);
				data.push(object);
			}
			var array = new Array();
			for (var _i = 0; _i < data.length; _i++) {
				array.push({
					host: data[_i].addr.toString(),
					user: data[_i].user,
					password: data[_i].password
				});
			}
			_this8.example = array;
		});
	}), (0, _defineProperty3.default)(_methods, 'changesAddress', function changesAddress(datas) {
		this.gzform.case = datas.label;
		for (var i = 0; i < this.example.length; i++) {
			if (datas.value == this.example[i].host) {
				this.gzform.password = this.example[i].password;
				this.gzform.h3ccasuser = this.example[i].user;
			}
		}
	}), (0, _defineProperty3.default)(_methods, 'uisHostChange', function uisHostChange() {
		var _this9 = this;

		(0, _index.getCasHost)(this.gzform.case, this.gzform.h3ccasuser, this.gzform.password, this.gzform.hostId, this.gzform.clusterId).then(function (res) {
			var casHostL = res.data;
			var array = [];
			if (casHostL.length > 0) {
				_this9.loadingShowp = false;
			}
			casHostL.forEach(function (item) {
				array.push({
					name: item.name,
					id: item.id
				});
			});
			_this9.casHostList = array;
		});
	}), (0, _defineProperty3.default)(_methods, 'onUisHostChange', function onUisHostChange(res) {
		this.gzform.hpid = res.value;
	}), (0, _defineProperty3.default)(_methods, 'hostChange', function hostChange() {
		var _this10 = this;

		(0, _index.getHostPool)(this.gzform.case, this.gzform.h3ccasuser, this.gzform.password).then(function (res) {
			var hostL = res.data;
			var array = [];

			hostL.forEach(function (item) {
				array.push({
					hostpool: item.hostpool,
					id: item.id,
					name: item.name
				});
			});
			_this10.hostlist = array;
		});
	}), (0, _defineProperty3.default)(_methods, 'onHostChange', function onHostChange(res) {
		this.gzform.hostId = res.value;
		this.gzform.hostName = res.label;
	}), (0, _defineProperty3.default)(_methods, 'poolChange', function poolChange() {
		var _this11 = this;

		(0, _index.getCasPool)(this.gzform.case, this.gzform.h3ccasuser, this.gzform.password, this.gzform.hostId).then(function (res) {
			var poolL = res.data;
			var array = [];
			if (poolL.length > 0) {
				_this11.loadingShowp = false;
			}
			poolL.forEach(function (item) {
				array.push({
					name: item.name,
					id: item.id
				});
			});
			_this11.poollist = array;
		});
	}), (0, _defineProperty3.default)(_methods, 'onPoolChange', function onPoolChange(res) {
		this.gzform.clusterId = res.value;
	}), (0, _defineProperty3.default)(_methods, 'getvswitch', function getvswitch() {
		var _this12 = this;

		(0, _index.getVswitch)(this.gzform.case, this.gzform.h3ccasuser, this.gzform.password, this.gzform.hpid).then(function (res) {
			_this12.vswitchlist = val.data;
		});
	}), (0, _defineProperty3.default)(_methods, 'onvswitch', function onvswitch() {
		this.gzform.vswitchid = val.value;
		this.gzform.vswitchname = val.label;
	}), _methods)
};

/***/ }),

/***/ 2820:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _defineProperty2 = __webpack_require__(89);

var _defineProperty3 = _interopRequireDefault(_defineProperty2);

var _methods;

var _vmOpt = __webpack_require__(2615);

var _vmOpt2 = _interopRequireDefault(_vmOpt);

var _uisOpt = __webpack_require__(2616);

var _uisOpt2 = _interopRequireDefault(_uisOpt);

var _casOpt = __webpack_require__(2617);

var _casOpt2 = _interopRequireDefault(_casOpt);

var _index = __webpack_require__(1640);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
	components: {
		vmOpt: _vmOpt2.default,
		uisOpt: _uisOpt2.default,
		casOpt: _casOpt2.default
	},
	props: ['vamTableList', 'resTypeVal', 'typedis', 'platformrestype', 'volid', 'sqltype'],
	data: function data() {
		return {
			clientId: '',
			typedis: false,
			stecur: 0,
			vmwarecol: [{
				type: 'selection',
				width: 60,
				align: 'center'
			}, {
				title: '机器名',
				key: 'name'
			}],
			resPost: [],
			platformrestype: '',
			platformresname: '',
			gzform: {
				client: '',
				agre: 'NFS',
				path: '',
				desc: '',
				password: '',
				h3ccasuser: '',
				hpid: '',
				'case': '',
				cashost: '',
				uispool: '',
				rename: '',
				dc: '',
				hosts: '',
				network: '',
				dataStroe: '',
				diskmode: '',
				starVm: false,
				powerType: '',
				vswitchname: '',
				vswitchid: '',
				ccpool: ''
			},
			gzformRule: {
				id: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择实例名',
					trigger: 'change'
				}],
				dc: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择数据中心',
					trigger: 'change'
				}],
				hosts: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择主机',
					trigger: 'change'
				}],
				network: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择网段',
					trigger: 'change'
				}],
				name: [{ required: true, message: '请输入重命名', trigger: 'blur' }],
				path: [{ required: true, message: '请输入路径', trigger: 'blur' }],
				client: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择客户端',
					trigger: 'change'
				}],
				agre: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择协议',
					trigger: 'change'
				}],
				store: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择存储',
					trigger: 'change'
				}],
				diskmode: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择模式',
					trigger: 'change'
				}],
				datapath: [{ required: true, message: '数据文件路径', trigger: 'blur' }],
				cliName: [{ required: true, message: '实例名', trigger: 'blur' }]
			},
			restart: false,
			fictitious: false,
			clientSelect: [],
			agreArr: [],
			example: [],
			casHostList: [],
			uispoollist: [],
			centerlist: [],
			hostslist: [],
			networklist: [],
			storelist: [],
			modelist: [],
			yjlist: [{
				id: 1441792,
				name: 'H3C CAS'
			}, {
				id: 1507328,
				name: 'H3C UIS'
			}, {
				id: 327680,
				name: 'VMWARE'
			}]
		};
	},
	created: function created() {
		this.openAgre();
	},
	mounted: function mounted() {
		this.stecur = 0;
	},

	computed: {
		getPower: function getPower() {
			return this.$store.state.power.module;
		}
	},
	methods: (_methods = {
		initClient: function initClient() {
			var _this = this;

			this.$store.commit('getResTypeData', this.platformrestype);
			(0, _index.getClientList)(this.platformrestype).then(function (obj) {
				var array = [];
				for (var i = 0; i < obj.data.vrts_client_data.length; i++) {
					array.push({
						id: obj.data.vrts_client_data[i].id,
						machine: obj.data.vrts_client_data[i].machine,
						os_type: obj.data.vrts_client_data[i].os_type
					});
				}
				_this.clientSelect = array;
				_this.gzform.client = _this.clientSelect[0].id;
				_this.clientId = _this.clientSelect[0].id;
			});
		},
		openPlat: function openPlat() {
			this.gzform.client = '';
		},
		getPowerType: function getPowerType(val) {
			this.gzform.powerType = val;
		},
		getPowerVal: function getPowerVal(val) {
			this.gzform.starVm = val;
		},
		getVmCase: function getVmCase(val) {
			this.gzform.case = val;
		},
		getVmDc: function getVmDc(val) {
			this.gzform.dc = val;
		},
		getVmHosts: function getVmHosts(val) {
			this.gzform.hosts = val;
		},
		getVmNetWork: function getVmNetWork(val) {
			this.gzform.network = val;
		},
		getVmDataStroe: function getVmDataStroe(val) {
			this.gzform.dataStroe = val;
		},
		getVmDistMode: function getVmDistMode(val) {
			this.gzform.diskmode = val;
		}
	}, (0, _defineProperty3.default)(_methods, 'getVmDistMode', function getVmDistMode(val) {
		this.gzform.diskmode = val;
	}), (0, _defineProperty3.default)(_methods, 'getVmRename', function getVmRename(val) {
		this.gzform.rename = val;
	}), (0, _defineProperty3.default)(_methods, 'getCasCase', function getCasCase(val) {
		this.gzform.case = val;
	}), (0, _defineProperty3.default)(_methods, 'getCasHpid', function getCasHpid(val) {
		this.gzform.hpid = val;
	}), (0, _defineProperty3.default)(_methods, 'getCasHostE', function getCasHostE(val) {
		this.gzform.addr = val.value;
		this.gzform.hostName = val.label;
	}), (0, _defineProperty3.default)(_methods, 'getCasVswitch', function getCasVswitch(el, val) {
		this.gzform.vswitchid = el;
		this.gzform.vswitchname = val;
	}), (0, _defineProperty3.default)(_methods, 'getCasClusterId', function getCasClusterId(val) {
		this.gzform.pool = val;
	}), (0, _defineProperty3.default)(_methods, 'getCasRename', function getCasRename(val) {
		this.gzform.rename = val;
	}), (0, _defineProperty3.default)(_methods, 'getCasCcPool', function getCasCcPool(val) {
		this.gzform.ccpool = val;
	}), (0, _defineProperty3.default)(_methods, 'getUisCase', function getUisCase(val) {
		this.gzform.case = val;
	}), (0, _defineProperty3.default)(_methods, 'getUisHpid', function getUisHpid(val) {
		this.gzform.hpid = val;
	}), (0, _defineProperty3.default)(_methods, 'getUispool', function getUispool(val) {
		this.gzform.uispool = val;
	}), (0, _defineProperty3.default)(_methods, 'getUisvswitch', function getUisvswitch(el, val) {
		this.gzform.vswitchid = el;
		this.gzform.vswitchname = val;
	}), (0, _defineProperty3.default)(_methods, 'getUisRestarte', function getUisRestarte(val) {
		this.restart = val;
	}), (0, _defineProperty3.default)(_methods, 'getUisRename', function getUisRename(val) {
		this.gzform.rename = val;
	}), (0, _defineProperty3.default)(_methods, 'getUisCcPool', function getUisCcPool(val) {
		this.gzform.ccpool = val;
	}), (0, _defineProperty3.default)(_methods, 'upstep', function upstep() {
		this.stecur -= 1;
		if (this.stecur == 0) {
			this.resPost = [];
		}
	}), (0, _defineProperty3.default)(_methods, 'cancel', function cancel() {
		this.$emit('closeDrawer', false);
		this.stecur = 0;
		this.gzform = {};
		this.gzform.agre = 'NFS';
	}), (0, _defineProperty3.default)(_methods, 'vmOptList', function vmOptList() {
		var vmwareArr = [];
		if (this.sqltype == '恢复') {
			if (this.gzform.starVm) {
				vmwareArr.push({
					type: 33,
					value: ''
				}, {
					type: 35,
					value: this.gzform.case
				}, {
					type: 96,
					value: this.gzform.dc
				}, {
					type: 97,
					value: this.gzform.dataStroe
				}, {
					type: 98,
					value: this.gzform.hosts
				}, {
					type: 95,
					value: this.gzform.network
				}, {
					type: 34,
					value: this.gzform.rename
				}, {
					type: 120,
					value: ''
				});
			} else {
				vmwareArr.push({
					type: 35,
					value: this.gzform.case
				}, {
					type: 96,
					value: this.gzform.dc
				}, {
					type: 97,
					value: this.gzform.dataStroe
				}, {
					type: 98,
					value: this.gzform.hosts
				}, {
					type: 95,
					value: this.gzform.network
				}, {
					type: 34,
					value: this.gzform.rename
				}, {
					type: 120,
					value: ''
				});
			}
		} else {
			if (this.gzform.starVm) {
				vmwareArr.push({
					type: 33,
					value: ''
				}, {
					type: 35,
					value: this.gzform.case
				}, {
					type: 96,
					value: this.gzform.dc
				}, {
					type: 98,
					value: this.gzform.hosts
				}, {
					type: 95,
					value: this.gzform.network
				}, {
					type: 34,
					value: this.gzform.rename
				});
			} else {
				vmwareArr.push({
					type: 35,
					value: this.gzform.case
				}, {
					type: 96,
					value: this.gzform.dc
				}, {
					type: 97,
					value: this.gzform.dataStroe
				}, {
					type: 98,
					value: this.gzform.hosts
				}, {
					type: 95,
					value: this.gzform.network
				}, {
					type: 34,
					value: this.gzform.rename
				});
			}
		}
		return vmwareArr;
	}), (0, _defineProperty3.default)(_methods, 'casOptList', function casOptList() {
		var h3ccasArr = [];
		if (this.sqltype == '恢复') {
			h3ccasArr.push({
				type: 35,
				value: this.gzform.case
			}, {
				type: 90,
				value: this.gzform.ccpool
			}, {
				type: 93,
				value: this.gzform.addr.toString()
			}, {
				type: 94,
				value: this.gzform.pool.toString()
			}, {
				type: 98,
				value: this.gzform.hostName
			}, {
				type: 91,
				value: this.gzform.hpid.toString()
			}, {
				type: 34,
				value: this.gzform.rename
			}, {
				type: 129,
				value: this.gzform.vswitchid.toString()
			}, {
				type: 128,
				value: this.gzform.vswitchname
			}, {
				type: 120,
				value: ''
			});
		} else {
			h3ccasArr.push({
				type: 35,
				value: this.gzform.case
			}, {
				type: 90,
				value: this.gzform.ccpool
			}, {
				type: 93,
				value: this.gzform.addr.toString()
			}, {
				type: 94,
				value: this.gzform.pool.toString()
			}, {
				type: 98,
				value: this.gzform.hostName
			}, {
				type: 91,
				value: this.gzform.hpid.toString()
			}, {
				type: 34,
				value: this.gzform.rename
			}, {
				type: 129,
				value: this.gzform.vswitchid.toString()
			}, {
				type: 128,
				value: this.gzform.vswitchname
			});
		}
		return h3ccasArr;
	}), (0, _defineProperty3.default)(_methods, 'uisOptList', function uisOptList() {
		var h3cuisArr = [];
		if (this.sqltype == '恢复') {
			if (this.restart) {
				h3cuisArr.push({
					type: 35,
					value: this.gzform.case
				}, {
					type: 90,
					value: this.gzform.ccpool
				}, {
					type: 91,
					value: this.gzform.hpid.toString()
				}, {
					type: 94,
					value: this.gzform.uispool.toString()
				}, {
					type: 34,
					value: this.gzform.rename
				}, {
					type: 128,
					value: this.gzform.vswitchname
				}, {
					type: 129,
					value: this.gzform.vswitchid.toString()
				}, {
					type: 120,
					value: ''
				}, {
					type: 33
				});
			} else {
				h3cuisArr.push({
					type: 35,
					value: this.gzform.case
				}, {
					type: 90,
					value: this.gzform.ccpool
				}, {
					type: 91,
					value: this.gzform.hpid.toString()
				}, {
					type: 94,
					value: this.gzform.uispool.toString()
				}, {
					type: 34,
					value: this.gzform.rename
				}, {
					type: 128,
					value: this.gzform.vswitchname
				}, {
					type: 129,
					value: this.gzform.vswitchid.toString()
				}, {
					type: 120,
					value: ''
				});
			}
		} else {
			if (this.restart) {
				h3cuisArr.push({
					type: 35,
					value: this.gzform.case
				}, {
					type: 90,
					value: this.gzform.ccpool
				}, {
					type: 91,
					value: this.gzform.hpid.toString()
				}, {
					type: 94,
					value: this.gzform.uispool.toString()
				}, {
					type: 34,
					value: this.gzform.rename
				}, {
					type: 128,
					value: this.gzform.vswitchname
				}, {
					type: 129,
					value: this.gzform.vswitchid.toString()
				}, {
					type: 33
				});
			} else {
				h3cuisArr.push({
					type: 35,
					value: this.gzform.case
				}, {
					type: 90,
					value: this.gzform.ccpool
				}, {
					type: 91,
					value: this.gzform.hpid.toString()
				}, {
					type: 94,
					value: this.gzform.uispool.toString()
				}, {
					type: 34,
					value: this.gzform.rename
				}, {
					type: 128,
					value: this.gzform.vswitchname
				}, {
					type: 129,
					value: this.gzform.vswitchid.toString()
				});
			}
		}
		return h3cuisArr;
	}), (0, _defineProperty3.default)(_methods, 'h3cCasObj', function h3cCasObj() {
		this.optionArr = this.casOptList();
		var postObj = {};
		return postObj = {
			volid: this.volid,
			client: this.gzform.client,
			protocol: this.gzform.agre,
			mountpath: this.gzform.path,
			options: this.optionArr,
			restype: this.platformrestype,
			resources: this.resPost,
			desc: this.gzform.desc
		};
	}), (0, _defineProperty3.default)(_methods, 'h3cUisObj', function h3cUisObj() {
		var postObj = {};

		this.optionArr = this.uisOptList();
		return postObj = {
			volid: this.volid,
			client: this.gzform.client,
			protocol: this.gzform.agre,
			mountpath: this.gzform.path,
			options: this.optionArr,
			restype: this.platformrestype,
			resources: this.resPost,
			desc: this.gzform.desc
		};
	}), (0, _defineProperty3.default)(_methods, 'h3cVmObj', function h3cVmObj() {
		var postObj = {};
		this.optionArr = this.vmOptList();
		return postObj = {
			volid: this.volid,
			client: this.gzform.client,
			protocol: this.gzform.agre,
			mountpath: this.gzform.path,
			options: this.optionArr,
			restype: this.platformrestype,
			resources: this.resPost,
			desc: this.gzform.desc
		};
	}), (0, _defineProperty3.default)(_methods, 'onmount', function onmount() {
		var _this2 = this;

		var postObj = {};

		if (this.platformrestype == 1441792) {
			postObj = this.h3cCasObj();
		}
		if (this.platformrestype == 1507328) {
			postObj = this.h3cUisObj();
		}
		if (this.platformrestype == 327680) {
			postObj = this.h3cVmObj();
		}
		(0, _index.onMount)(postObj).then(function (val) {
			if (val.code == 0) {
				_this2.$Message.success(val.message);
			} else {
				_this2.$Message.warning(val.message);
			}
			_this2.gzform = {};
			_this2.stecur = 0;
			_this2.$emit('closeDrawer', false);
		});
	}), (0, _defineProperty3.default)(_methods, 'nextstep', function nextstep(i) {
		if (this.resPost.length <= 0) {
			this.$Message.warning('请先勾选资源');
		} else {
			this.stecur += 1;
			if (this.stecur == 1) {
				this.initClient();
			}
			if (this.gzform.client == '' && this.stecur == 2) {
				this.$Message.warning('客户端不能为空');
			} else if (this.gzform.path == '' && this.stecur == 2) {
				this.$Message.warning('路径不能为空');
			} else {}
		}
	}), (0, _defineProperty3.default)(_methods, 'torowdata', function torowdata(res, i) {
		this.$refs.selection.toggleSelect(i);
	}), (0, _defineProperty3.default)(_methods, 'tovawlist', function tovawlist(res) {
		var _this3 = this;

		this.resPost = [];

		var aa = res;
		if (aa.length > 0) {
			aa.forEach(function (item) {
				_this3.resPost.push({ type: Number(item.type), path: item.name, desc: '', exclude: 0 });
			});
		} else {
			this.resPost = [];
		}
	}), (0, _defineProperty3.default)(_methods, 'openClient', function openClient() {}), (0, _defineProperty3.default)(_methods, 'optionId', function optionId(datas) {
		var _this4 = this;

		this.gzform.client = datas;
		this.clientId = datas;
		this.clientSelect.forEach(function (item, i) {
			if (item.id == datas) {
				_this4.osType = item.os_type;
			}
		});
	}), (0, _defineProperty3.default)(_methods, 'openAgre', function openAgre() {
		var _this5 = this;

		(0, _index.getProtocol)().then(function (obj) {
			if (obj.code == 0) {
				_this5.agreArr = obj.data;
				_this5.agreArr.forEach(function (item) {
					if (item == 'NFS') {
						_this5.gzform.agre = item;
					}
				});
			}
		});
	}), (0, _defineProperty3.default)(_methods, 'agreOptionId', function agreOptionId(datas) {
		this.gzform.agre = datas;
	}), (0, _defineProperty3.default)(_methods, 'changeyjid', function changeyjid(val) {
		var _this6 = this;

		this.$store.commit('getResTypeData', val.value);
		this.platformrestype = val.value;
		this.platformresname = val.label;

		(0, _index.getClientList)(this.platformrestype).then(function (obj) {
			var array = [];
			for (var i = 0; i < obj.data.vrts_client_data.length; i++) {
				array.push({
					id: obj.data.vrts_client_data[i].id,
					machine: obj.data.vrts_client_data[i].machine,
					os_type: obj.data.vrts_client_data[i].os_type
				});
			}
			_this6.clientSelect = array;
			_this6.gzform.client = _this6.clientSelect[0].id;
			_this6.clientId = _this6.clientSelect[0].id;
		});
	}), (0, _defineProperty3.default)(_methods, 'uisChange', function uisChange() {
		var _this7 = this;

		(0, _index.getAddress)(this.gzform.client, this.resTypeVal).then(function (obj) {
			var data = [];
			for (var i = 0; i < obj.data.length; i++) {
				var object = JSON.parse(obj.data[i].conf);
				data.push(object);
			}
			var array = new Array();
			for (var _i = 0; _i < data.length; _i++) {
				array.push({
					host: data[_i].addr.toString(),
					user: data[_i].user,
					password: data[_i].password
				});
			}
			_this7.example = array;
		});
	}), (0, _defineProperty3.default)(_methods, 'changesAddress', function changesAddress(datas) {
		this.gzform.case = datas.label;
		for (var i = 0; i < this.example.length; i++) {
			if (datas.value == this.example[i].host) {
				this.gzform.password = this.example[i].password;
				this.gzform.h3ccasuser = this.example[i].user;
			}
		}
	}), (0, _defineProperty3.default)(_methods, 'uisHostChange', function uisHostChange() {
		var _this8 = this;

		(0, _index.getHost)(this.gzform.case, this.gzform.h3ccasuser, this.gzform.password).then(function (res) {
			var casHostL = res.data;
			var array = [];
			if (casHostL.length > 0) {
				_this8.loadingShowp = false;
			}
			casHostL.forEach(function (item) {
				array.push({
					name: item.name,
					id: item.id
				});
			});
			_this8.casHostList = array;
		});
	}), (0, _defineProperty3.default)(_methods, 'onUisHostChange', function onUisHostChange(res) {
		this.gzform.hpid = res.value;
	}), (0, _defineProperty3.default)(_methods, 'getUisChange', function getUisChange() {
		var _this9 = this;

		(0, _index.getUisPool)(this.gzform.case, this.gzform.h3ccasuser, this.gzform.password, this.gzform.hpid).then(function (res) {
			var poolL = res.data;
			var array = [];
			poolL.forEach(function (item) {
				array.push({
					name: item.name,
					id: item.id
				});
			});
			_this9.uispoollist = array;
		});
	}), (0, _defineProperty3.default)(_methods, 'onUisChange', function onUisChange(res) {
		this.gzform.uispool = res.value;
	}), (0, _defineProperty3.default)(_methods, 'vawarechange', function vawarechange(val) {
		var _this10 = this;

		this.vamsldata.forEach(function (item, i) {
			if (val == item.id) {
				_this10.gzform.user = item.user;
				_this10.gzform.password = item.password;
				_this10.gzform.host = item.host;
				(0, _index.getDataCenter)(_this10.gzform.host, _this10.gzform.user, _this10.gzform.password).then(function (res) {
					_this10.centerlist = res.data;
				});
			}
		});
	}), (0, _defineProperty3.default)(_methods, 'onCenterChange', function onCenterChange(res) {
		this.gzform.dc = res.value;
	}), (0, _defineProperty3.default)(_methods, 'hostsChange', function hostsChange() {
		var _this11 = this;

		(0, _index.getVmwareHost)(this.gzform.host, this.gzform.user, this.gzform.password, this.gzform.dc).then(function (res) {
			_this11.hostslist = res.data;
		});
	}), (0, _defineProperty3.default)(_methods, 'netWorkChange', function netWorkChange() {
		var _this12 = this;

		(0, _index.getVmwareNetwork)(this.gzform.host, this.gzform.user, this.gzform.password, this.gzform.dc, this.gzform.hosts).then(function (res) {
			_this12.networklist = res.data;
		});
	}), (0, _defineProperty3.default)(_methods, 'onNetWork', function onNetWork(res) {
		this.gzform.network = res.label;
	}), (0, _defineProperty3.default)(_methods, 'dataStoreChange', function dataStoreChange() {
		var _this13 = this;

		(0, _index.getVmwareStore)(this.gzform.host, this.gzform.user, this.gzform.password, this.gzform.dc, this.gzform.hosts).then(function (res) {
			_this13.storelist = res.data;
		});
	}), (0, _defineProperty3.default)(_methods, 'onStoreChange', function onStoreChange(res) {
		this.gzform.dataStroe = res.value;
	}), (0, _defineProperty3.default)(_methods, 'diskModeChange', function diskModeChange() {
		var _this14 = this;

		(0, _index.getVmwareMode)().then(function (res) {
			_this14.modelist = res.data;
		});
	}), (0, _defineProperty3.default)(_methods, 'onDistMode', function onDistMode(res) {
		this.gzform.diskmode = res.value;
	}), _methods)
};

/***/ }),

/***/ 2821:
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function($) {

Object.defineProperty(exports, "__esModule", {
	value: true
});

var _index = __webpack_require__(2147);

exports.default = {
	props: {
		setRowData: {
			type: Object
		}
	},
	data: function data() {
		return {
			resPost: [],
			resources: {
				pathConten: []
			},
			ztreeArray: [],
			setting: {
				check: {
					enable: true
				},
				edit: {
					enable: false,
					editNameSelectAll: false
				},
				callback: {
					onClick: this.zTreeOnClick,
					onCheck: this.zTreeOnCheck
				},
				view: {
					nameIsHTML: true,
					selectedMulti: false
				},
				data: {
					simpleData: {
						enable: true
					}
				}
			}
		};
	},
	mounted: function mounted() {},

	methods: {
		getZtreeArrayData: function getZtreeArrayData() {
			var _this = this;

			(0, _index.getFileTree)(this.setRowData).then(function (res) {
				_this.ztreeArray = [];
				res.data.forEach(function (item, index) {
					var obj = {
						id: _this.setRowData.id,

						iconSkin: 'files',
						name: item.path,
						path: item.path,
						nocheck: true,
						nodetype: 0,
						names: _this.setRowData.name,
						dev_id: _this.setRowData.dev_id,
						dev_name: _this.setRowData.dev_name,
						parent: _this.setRowData.parent,
						fatherName: item.path,

						res_type_val: item.type
					};
					_this.ztreeArray.push(obj);
				});

				$.fn.zTree.init($('#treeDemoTactics'), _this.setting, _this.ztreeArray);
			});
		},
		getNoFileZtreeArrayData: function getNoFileZtreeArrayData() {
			var _this2 = this;

			(0, _index.getNoFileTree)(this.setRowData).then(function (res) {
				_this2.ztreeArray = [];
				res.data.forEach(function (item, index) {
					var obj = {
						id: _this2.setRowData.id,

						iconSkin: 'files',
						name: item.name,
						path: item.name,
						nocheck: false,
						nodetype: 0,
						names: _this2.setRowData.name,
						dev_id: _this2.setRowData.dev_id,
						dev_name: _this2.setRowData.dev_name,
						parent: _this2.setRowData.parent,
						fatherName: item.name,

						res_type_val: Number(item.type),
						volume_id: _this2.setRowData.volume_id
					};
					_this2.ztreeArray.push(obj);
				});

				$.fn.zTree.init($('#treeDemoTactics'), _this2.setting, _this2.ztreeArray);
			});
		},
		zTreeOnClick: function zTreeOnClick(event, treeId, treeNode) {
			var _this3 = this;

			if (treeNode.isClick) {
				return false;
			} else {
				treeNode.isClick = true;
			}
			if ((treeNode.res_type_val & 0xffff0000) != '65536') {
				(0, _index.getNoFileTree)(treeNode).then(function (res) {
					var childrenArr = [];
					res.data.forEach(function (item, index) {
						var obj = {
							id: treeNode.id,

							iconSkin: 'files',
							name: item.name,
							path: item.path,
							nocheck: false,
							nodetype: 0,
							names: _this3.setRowData.name,
							dev_id: _this3.setRowData.dev_id,
							dev_name: _this3.setRowData.dev_name,
							parent: _this3.setRowData.parent,
							fatherName: item.name,

							res_type_val: item.type,
							volume_id: treeNode.volume_id
						};
						childrenArr.push(obj);
					});
					var ztreeobj = $.fn.zTree.getZTreeObj(treeId);
					ztreeobj.addNodes(treeNode, childrenArr);
				});
			} else {
				(0, _index.getFileTree)(treeNode).then(function (res) {
					var childrenArr = [];
					res.data.forEach(function (item) {
						var obj = {
							id: treeNode.id,
							iconSkin: 'files',
							name: item.path,
							path: treeNode.fatherName + '/' + item.path,
							nocheck: false,
							nodetype: 0,
							names: _this3.setRowData.name,
							dev_id: _this3.setRowData.dev_id,
							dev_name: _this3.setRowData.dev_name,
							parent: _this3.setRowData.parent,
							checked: _this3.findCheckout(treeNode),
							fatherName: treeNode.fatherName + '/' + item.Path,

							res_type_val: item.Type
						};
						childrenArr.push(obj);
					});
					var ztreeobj = $.fn.zTree.getZTreeObj(treeId);
					ztreeobj.addNodes(treeNode, childrenArr);
				});
			}
		},
		zTreeOnCheck: function zTreeOnCheck(event, treeId, treeNode) {
			var pdchecked = treeNode.checked;
			var treeObj = $.fn.zTree.getZTreeObj('treeDemoTactics');
			var nodes = treeObj.getCheckedNodes(true);
			this.checkedNodeList = [];

			if (treeNode.checked) {
				this.SelectNode(treeNode, treeId);
			} else {
				this.DisSelectNode(treeNode);
			}
		},

		findCheckout: function findCheckout(state) {
			if (state.checked) {
				return true;
			} else {
				return false;
			}
		},
		SelectNode: function SelectNode(treeNode, treeId) {
			var _this4 = this;

			this.resPost = [];
			var ztreeobj = $.fn.zTree.getZTreeObj(treeId);

			var bNeedInsert = true;
			var tempNode = treeNode;

			if (bNeedInsert == true) {
				var exclude_path = '-' + this.tree_path(treeNode).namePath;
				var bExclude = false;
				this.resources.pathConten.forEach(function (item, i) {
					if (item.name == exclude_path) {
						bExclude = true;
						_this4.resources.pathConten.splice(i, 1);
						return false;
					}
				});
				if (bExclude == true) {
					return;
				}
				this.resources.pathConten.unshift({
					path: this.tree_path(treeNode).path,

					type: Number(treeNode.res_type_val),

					Exclude: 0
				});


				this.resPost = this.resources.pathConten;
				this.$emit('getResPost', this.resPost);
			}
		},
		DisSelectNode: function DisSelectNode(treeNode) {
			var _this5 = this;

			var _treeNode = treeNode;
			var parent = null;

			var partenNow = false;
			this.resPost.forEach(function (item, i) {
				if (treeNode.name == item.path) {
					_this5.resPost.splice(i, 1);
				}
			});

			this.$emit('getResPost', this.resPost);
		},
		DeleteItemFromArrayAll: function DeleteItemFromArrayAll(path, start) {
			for (var index = 0; index < this.resources.pathConten.length;) {
				if (this.resources.pathConten[index].name.substring(start, path.length + 1) == path) {
					this.resources.pathConten.splice(index, 1);
				} else if (this.resources.pathConten[index].name.substring(start) == path) {
					this.resources.pathConten.splice(index, 1);
				} else {
					++index;
				}
			}
		},
		DeleteItemFromBrotherArray: function DeleteItemFromBrotherArray(path, start) {
			for (var index = 0; index < this.resources.pathConten.length;) {
				if (this.resources.pathConten[index].name.substring(start, path.length + 1) == path && this.resources.pathConten[index].name.substring(path.length + 1, path.length + 2) == '/') {
					this.resources.pathConten.unshift({
						name: '+' + path,
						path: path,
						type: treeNode.ResType,
						client: this.tree_path(treeNode).client,
						Exclude: 0
					});
				} else if (this.resources.pathConten[index].name.substring(start) == path) {
					this.resources.pathConten.splice(index, 1);
				} else {
					++index;
				}
			}
		},
		DeleteItemFromArrayOne: function DeleteItemFromArrayOne(path, start) {
			for (var index = 0; index < this.resources.pathConten.length;) {
				if (this.resources.pathConten[index].name.substring(start) == path) {
					this.resources.pathConten.splice(index, 1);
				} else {
					++index;
				}
			}
		},

		tree_path: function tree_path(treeNode) {
			var path = '';
			var cid = 0;
			do {
				var parent = treeNode.getParentNode();
				if (!parent) {
					cid = treeNode.id;
					name = treeNode.name;
					break;
				}

				if (parent.nodetype != 0) {
					path = '/' + treeNode.name + path;
				} else {
					path = treeNode.name + path;
				}
				if (parent.nodetype != 1) {}
				treeNode = parent;
			} while (true);

			if (path.indexOf('//') == 0) {
				path = path.substr(1);
			}
			return {
				client: cid,
				path: path,
				name: name,
				namePath: name + '_' + path
			};
		}
	},
	watch: {
		setRowData: {
			handler: function handler(newVal, oldVal) {
				if (newVal && newVal.res_type_val != 65536 && newVal.dev_id) {
					newVal.path = '';
					newVal.names = newVal.name;
					this.getNoFileZtreeArrayData();
				} else {
					newVal.path = '';
					newVal.names = newVal.name;
					this.getZtreeArrayData();
				}
			},

			deep: true,
			immediate: true
		},
		'resources.pathConten': function resourcesPathConten(newVal, oldVal) {}
	}
};
/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(74)))

/***/ }),

/***/ 3723:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3724);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("d1b5620e", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-65dd5c28\",\"scoped\":false,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./replicaMag.vue", function() {
     var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-65dd5c28\",\"scoped\":false,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./replicaMag.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3724:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.ivu-steps-item.ivu-steps-status-process .ivu-steps-head-inner{border-color:#00b818;background-color:#00b818\n}\n.ivu-steps-item.ivu-steps-status-finish .ivu-steps-head-inner{background-color:#fff;border-color:#00b818\n}\n.ivu-steps-item.ivu-steps-status-finish .ivu-steps-tail>i:after{width:100%;background:#00b818;transition:all .2s ease-in-out;opacity:1\n}", ""]);

// exports


/***/ }),

/***/ 3725:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3726);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("befea234", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-65dd5c28\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/less-loader/dist/cjs.js!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=1!./replicaMag.vue", function() {
     var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-65dd5c28\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/less-loader/dist/cjs.js!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=1!./replicaMag.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3726:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.tree-search-box[data-v-65dd5c28]{width:120%;margin-left:-20px;margin-left:-1.25rem;margin-top:-5px;margin-top:-.3125rem\n}\n.tree-search-box[data-v-65dd5c28] .el-input__inner{border-radius:17px 17px 0 0\n}\n.tree-search-box[data-v-65dd5c28] .el-input-group__append{border-radius:0 10px 0 0\n}\n.tree-search-box[data-v-65dd5c28] .el-input__inner:focus,.tree-search-box[data-v-65dd5c28] .el-input__inner:hover{border-color:#e7e9ee\n}\n.tree-search-box[data-v-65dd5c28] .el-input--small .el-input__inner{height:40px\n}\n.mar-box[data-v-65dd5c28]{margin:15px 15px 20px;overflow-y:auto\n}\n.mar-l[data-v-65dd5c28]{margin-left:0\n}\n.searchBar[data-v-65dd5c28]{margin-right:10px\n}\n.page-box[data-v-65dd5c28]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:end;-ms-flex-pack:end;justify-content:flex-end\n}\n.tree[data-v-65dd5c28]{width:100%;float:left;overflow:auto\n}\n.tree[data-v-65dd5c28],.tree-box[data-v-65dd5c28]{height:250px\n}\n.query-box[data-v-65dd5c28]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-ms-flex-align:center;align-items:center;padding:10px;padding:.625rem;margin-bottom:10px;border-bottom:1px solid #e1e2e8\n}\n.query-box .leftquerybox[data-v-65dd5c28],.query-wrap-box[data-v-65dd5c28]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:start;-ms-flex-pack:start;justify-content:flex-start\n}\n.query-wrap-box[data-v-65dd5c28]{-webkit-box-align:center;-ms-flex-align:center;align-items:center\n}\n.search-input[data-v-65dd5c28]{margin-left:5px\n}\n.page-wrap[data-v-65dd5c28]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:end;-ms-flex-pack:end;justify-content:flex-end;position:absolute;bottom:32px;right:35px\n}\n[data-v-65dd5c28] .ivu-tree-empty{display:none\n}\n[data-v-65dd5c28] .el-card__body{height:100%\n}\n.no-data-img[data-v-65dd5c28]{margin:100% auto auto;text-align:center\n}\n.no-data-img[data-v-65dd5c28],.no-data-img img[data-v-65dd5c28]{width:150px;height:150px\n}\n.no-data-img span[data-v-65dd5c28]{color:#999;font-size:16px;font-size:1rem;font-weight:600;margin-top:10px\n}", ""]);

// exports


/***/ }),

/***/ 3727:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_H3cUis_vue__ = __webpack_require__(2815);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_H3cUis_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_H3cUis_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_H3cUis_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_H3cUis_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_b83bcac2_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_H3cUis_vue__ = __webpack_require__(3739);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_b83bcac2_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_H3cUis_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_b83bcac2_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_H3cUis_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3728)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = null
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_H3cUis_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_b83bcac2_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_H3cUis_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/dupMag/components/H3cUis.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-b83bcac2", Component.options)
  } else {
    hotAPI.reload("data-v-b83bcac2", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ }),

/***/ 3728:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3729);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("6e04a5ee", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-b83bcac2\",\"scoped\":false,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./H3cUis.vue", function() {
     var newContent = require("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-b83bcac2\",\"scoped\":false,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./H3cUis.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3729:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "", ""]);

// exports


/***/ }),

/***/ 3730:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3731);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("90e255b8", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-26d08418\",\"scoped\":false,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./vmOpt.vue", function() {
     var newContent = require("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-26d08418\",\"scoped\":false,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./vmOpt.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3731:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "", ""]);

// exports


/***/ }),

/***/ 3732:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", [_c("Form", {
    ref: "gzform",
    attrs: { "label-width": 90, model: _vm.gzform, rules: _vm.gzformRule }
  }, [_c("FormItem", { attrs: { label: "平台地址:", prop: "id" } }, [_c("Select", {
    attrs: { placeholder: "请选择平台地址" },
    on: {
      "on-change": _vm.vawarechange,
      "on-open-change": _vm.vmChange
    },
    model: {
      value: _vm.gzform.case,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "case", $$v);
      },
      expression: "gzform.case"
    }
  }, _vm._l(_vm.vamsldata, function (item) {
    return _c("Option", { key: item.host, attrs: { value: item.host + "" } }, [_vm._v(_vm._s(item.host))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "数据中心:", prop: "dc" } }, [_c("Select", {
    attrs: { "label-in-value": true },
    on: { "on-change": _vm.onCenterChange },
    model: {
      value: _vm.gzform.dc,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "dc", $$v);
      },
      expression: "gzform.dc"
    }
  }, _vm._l(_vm.centerlist, function (item) {
    return _c("Option", { key: item, attrs: { value: item + "", label: item } }, [_vm._v(_vm._s(item))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "主机", prop: "hosts" } }, [_c("Select", {
    attrs: { placeholder: "请选择主机" },
    on: {
      "on-change": _vm.getHostchange,
      "on-open-change": _vm.hostsChange
    },
    model: {
      value: _vm.gzform.hosts,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "hosts", $$v);
      },
      expression: "gzform.hosts"
    }
  }, _vm._l(_vm.hostslist, function (item) {
    return _c("Option", { key: item, attrs: { value: item + "" } }, [_vm._v(_vm._s(item))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "存储:", prop: "store" } }, [_c("Select", {
    attrs: { "label-in-value": true },
    on: {
      "on-change": _vm.onStoreChange,
      "on-open-change": _vm.dataStoreChange
    },
    model: {
      value: _vm.gzform.dataStroe,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "dataStroe", $$v);
      },
      expression: "gzform.dataStroe"
    }
  }, _vm._l(_vm.storelist, function (item) {
    return _c("Option", { key: item.name, attrs: { value: item.name } }, [_vm._v(_vm._s(item.title))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "网段:", prop: "network" } }, [_c("Select", {
    attrs: { "label-in-value": true },
    on: {
      "on-change": _vm.onNetWork,
      "on-open-change": _vm.netWorkChange
    },
    model: {
      value: _vm.gzform.network,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "network", $$v);
      },
      expression: "gzform.network"
    }
  }, _vm._l(_vm.networklist, function (item) {
    return _c("Option", { key: item, attrs: { value: item, label: item } }, [_vm._v(_vm._s(item))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "磁盘模式:", prop: "diskmode" } }, [_c("Select", {
    attrs: { "label-in-value": true },
    on: {
      "on-change": _vm.onDistMode,
      "on-open-change": _vm.diskModeChange
    },
    model: {
      value: _vm.gzform.diskmode,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "diskmode", $$v);
      },
      expression: "gzform.diskmode"
    }
  }, _vm._l(_vm.modelist, function (item) {
    return _c("Option", {
      key: item,
      attrs: { value: item.Mode, label: item.Name }
    }, [_vm._v(_vm._s(item.Name))]);
  }), 1)], 1), _vm._v(" "), _c("div", {
    staticStyle: {
      display: "flex",
      "justify-content": "flex-start",
      "align-items": "center",
      "margin-top": "-50px"
    }
  }, [_c("Checkbox", {
    on: { "on-change": _vm.PowerOn },
    model: {
      value: _vm.gzform.starVm,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "starVm", $$v);
      },
      expression: "gzform.starVm"
    }
  }, [_vm._v("挂载后启动虚拟机")]), _vm._v(" "), _c("div", {
    staticStyle: {
      display: "flex",
      "justify-content": "flex-start",
      "align-items": "center"
    }
  }, [_c("p", { staticStyle: { width: "12.25rem", color: "#515a6e" } }, [_c("Checkbox", {
    model: {
      value: _vm.fictitious,
      callback: function callback($$v) {
        _vm.fictitious = $$v;
      },
      expression: "fictitious"
    }
  }, [_vm._v("重命名虚拟机")])], 1), _vm._v(" "), _c("Input", {
    attrs: { type: "text", disabled: !_vm.fictitious },
    on: { input: _vm.onVmRename },
    model: {
      value: _vm.gzform.rename,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "rename", $$v);
      },
      expression: "gzform.rename"
    }
  })], 1)], 1)], 1)], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-26d08418", esExports);
  }
}

/***/ }),

/***/ 3733:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3734);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("1ea514c8", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-27e7d58c\",\"scoped\":false,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./uisOpt.vue", function() {
     var newContent = require("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-27e7d58c\",\"scoped\":false,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./uisOpt.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3734:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "", ""]);

// exports


/***/ }),

/***/ 3735:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", [_c("Form", {
    ref: "gzform",
    attrs: { "label-width": 90, model: _vm.gzform, rules: _vm.gzformRule }
  }, [_c("FormItem", { attrs: { label: "平台地址：" } }, [_c("Select", {
    attrs: { placeholder: "请选择实例", "label-in-value": true },
    on: {
      "on-change": _vm.changesAddress,
      "on-open-change": _vm.uisChange
    },
    model: {
      value: _vm.gzform.case,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "case", $$v);
      },
      expression: "gzform.case"
    }
  }, _vm._l(_vm.example, function (item) {
    return _c("Option", { key: item.host, attrs: { value: item.host + "" } }, [_vm._v(_vm._s(item.host))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "主机:", prop: "host" } }, [_c("Select", {
    attrs: { placeholder: "请选择主机", "label-in-value": true },
    on: {
      "on-change": _vm.onUisHostChange,
      "on-open-change": _vm.uisHostChange
    },
    model: {
      value: _vm.gzform.cashost,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "cashost", $$v);
      },
      expression: "gzform.cashost"
    }
  }, _vm._l(_vm.casHostList, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.id } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "存储池：" } }, [_c("Select", {
    attrs: {
      placeholder: "请选择存储池",
      "label-in-value": true
    },
    on: {
      "on-change": _vm.onCcPoolChange,
      "on-open-change": _vm.getUisCcPool
    },
    model: {
      value: _vm.gzform.ccpool,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "ccpool", $$v);
      },
      expression: "gzform.ccpool"
    }
  }, _vm._l(_vm.uisCcPoolList, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.name + "" } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "虚拟交换机" } }, [_c("Select", {
    attrs: {
      placeholder: "请选择虚拟交换机",
      "label-in-value": true
    },
    on: {
      "on-change": _vm.onUisvswitch,
      "on-open-change": _vm.getUisvswitch
    },
    model: {
      value: _vm.gzform.vswitchid,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "vswitchid", $$v);
      },
      expression: "gzform.vswitchid"
    }
  }, _vm._l(_vm.uisVswitchlist, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.id, label: item.name }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "集群ID:", prop: "caspool" } }, [_c("Select", {
    attrs: {
      placeholder: "请选择存集群",
      "label-in-value": true
    },
    on: {
      "on-change": _vm.onUisChange,
      "on-open-change": _vm.getUisChange
    },
    model: {
      value: _vm.gzform.uispool,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "uispool", $$v);
      },
      expression: "gzform.uispool"
    }
  }, _vm._l(_vm.uispoollist, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.id } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("div", {
    staticStyle: {
      display: "flex",
      "justify-content": "flex-start",
      "align-items": "center",
      "margin-top": "-50px"
    }
  }, [_c("Checkbox", {
    on: { "on-change": _vm.PowerOn },
    model: {
      value: _vm.restart,
      callback: function callback($$v) {
        _vm.restart = $$v;
      },
      expression: "restart"
    }
  }, [_vm._v("完成后启动虚拟机")]), _vm._v(" "), _c("div", {
    staticStyle: {
      display: "flex",
      "justify-content": "flex-start",
      "align-items": "center"
    }
  }, [_c("p", { staticStyle: { width: "12.25rem", color: "#515a6e" } }, [_c("Checkbox", {
    model: {
      value: _vm.fictitious,
      callback: function callback($$v) {
        _vm.fictitious = $$v;
      },
      expression: "fictitious"
    }
  }, [_vm._v("重命名虚拟机")])], 1), _vm._v(" "), _c("Input", {
    attrs: { type: "text", disabled: !_vm.fictitious },
    on: { input: _vm.onRename },
    model: {
      value: _vm.gzform.rename,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "rename", $$v);
      },
      expression: "gzform.rename"
    }
  })], 1)], 1)], 1)], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-27e7d58c", esExports);
  }
}

/***/ }),

/***/ 3736:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3737);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("7235bd28", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-589a6956\",\"scoped\":false,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./casOpt.vue", function() {
     var newContent = require("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-589a6956\",\"scoped\":false,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./casOpt.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3737:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "", ""]);

// exports


/***/ }),

/***/ 3738:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", [_c("Form", {
    ref: "gzform",
    attrs: { "label-width": 90, model: _vm.gzform, rules: _vm.gzformRule }
  }, [_c("FormItem", { attrs: { label: "平台地址：" } }, [_c("Select", {
    attrs: { placeholder: "请选择实例", "label-in-value": true },
    on: {
      "on-change": _vm.changesAddress,
      "on-open-change": _vm.caseChange
    },
    model: {
      value: _vm.gzform.case,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "case", $$v);
      },
      expression: "gzform.case"
    }
  }, _vm._l(_vm.example, function (item) {
    return _c("Option", { key: item.host, attrs: { value: item.host + "" } }, [_vm._v(_vm._s(item.host))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "主机池：", prop: "hostpool" } }, [_c("Select", {
    attrs: {
      placeholder: "请选择主机池",
      "label-in-value": true
    },
    on: {
      "on-change": _vm.onHostChange,
      "on-open-change": _vm.hostChange
    },
    model: {
      value: _vm.gzform.addr,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "addr", $$v);
      },
      expression: "gzform.addr"
    }
  }, _vm._l(_vm.hostlist, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.id + "", label: item.name }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "集群:", prop: "colony" } }, [_c("Select", {
    attrs: {
      placeholder: "请选择存集群",
      "label-in-value": true
    },
    on: {
      "on-change": _vm.onPoolChange,
      "on-open-change": _vm.poolChange
    },
    model: {
      value: _vm.gzform.pool,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "pool", $$v);
      },
      expression: "gzform.pool"
    }
  }, _vm._l(_vm.poollist, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.id } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "主机:", prop: "host" } }, [_c("Select", {
    attrs: { placeholder: "请选择主机", "label-in-value": true },
    on: {
      "on-change": _vm.onUisHostChange,
      "on-open-change": _vm.uisHostChange
    },
    model: {
      value: _vm.gzform.cashost,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "cashost", $$v);
      },
      expression: "gzform.cashost"
    }
  }, _vm._l(_vm.casHostList, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.id } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "存储池：" } }, [_c("Select", {
    attrs: {
      placeholder: "请选择存储池",
      "label-in-value": true
    },
    on: {
      "on-change": _vm.onCcPoolChange,
      "on-open-change": _vm.getCasCcPool
    },
    model: {
      value: _vm.gzform.ccpool,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "ccpool", $$v);
      },
      expression: "gzform.ccpool"
    }
  }, _vm._l(_vm.casCcPoolList, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.name + "" } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "虚拟交换机" } }, [_c("Select", {
    attrs: {
      placeholder: "请选择虚拟交换机",
      "label-in-value": true
    },
    on: {
      "on-change": _vm.onvswitch,
      "on-open-change": _vm.getvswitch
    },
    model: {
      value: _vm.gzform.vswitchid,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "vswitchid", $$v);
      },
      expression: "gzform.vswitchid"
    }
  }, _vm._l(_vm.vswitchlist, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.id, label: item.name }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("div", {
    staticStyle: {
      display: "flex",
      "justify-content": "flex-start",
      "align-items": "center",
      "margin-top": "-50px"
    }
  }, [_c("Checkbox", {
    on: { "on-change": _vm.PowerOn },
    model: {
      value: _vm.gzform.starVm,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "starVm", $$v);
      },
      expression: "gzform.starVm"
    }
  }, [_vm._v("挂载后启动虚拟机")]), _vm._v(" "), _c("div", {
    staticStyle: {
      display: "flex",
      "justify-content": "flex-start",
      "align-items": "center"
    }
  }, [_c("p", { staticStyle: { width: "12.25rem", color: "#515a6e" } }, [_c("Checkbox", {
    model: {
      value: _vm.fictitious,
      callback: function callback($$v) {
        _vm.fictitious = $$v;
      },
      expression: "fictitious"
    }
  }, [_vm._v("重命名虚拟机")])], 1), _vm._v(" "), _c("Input", {
    attrs: { type: "text", disabled: !_vm.fictitious },
    on: { input: _vm.onCasRename },
    model: {
      value: _vm.gzform.rename,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "rename", $$v);
      },
      expression: "gzform.rename"
    }
  })], 1)], 1)], 1)], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-589a6956", esExports);
  }
}

/***/ }),

/***/ 3739:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", [_c("Form", {
    ref: "gzform",
    attrs: { "label-width": 90, model: _vm.gzform, rules: _vm.gzformRule }
  }, [_c("Steps", {
    staticStyle: { margin: "20px 0" },
    attrs: { current: _vm.stecur }
  }, [_c("Step", { attrs: { title: "选择资源" } }), _vm._v(" "), _c("Step", { attrs: { title: "选择平台" } }), _vm._v(" "), _c("Step", { attrs: { title: "选项" } })], 1), _vm._v(" "), (_vm.stecur == 0 ? true : false) ? _c("div", { staticStyle: { "text-align": "right" } }, [_c("Table", {
    ref: "selection",
    attrs: {
      border: "",
      columns: _vm.uisCol,
      data: _vm.uisTableList
    },
    on: {
      "on-row-click": _vm.torowdata,
      "on-selection-change": _vm.tovawlist
    }
  })], 1) : _vm._e(), _vm._v(" "), (_vm.stecur == 1 ? true : false) ? _c("div", [_c("FormItem", { attrs: { label: "平台类型" } }, [_c("Select", {
    attrs: {
      placeholder: "请选择平台",
      "label-in-value": true
    },
    on: {
      "on-change": _vm.changeyjid,
      "on-open-change": _vm.openPlat
    },
    model: {
      value: _vm.platformrestype,
      callback: function callback($$v) {
        _vm.platformrestype = $$v;
      },
      expression: "platformrestype"
    }
  }, _vm._l(_vm.yjlist, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.id, label: item.name }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "客户端", prop: "client" } }, [_c("Select", {
    attrs: {
      placeholder: "请选择客户端",
      disabled: _vm.typedis
    },
    on: {
      "on-open-change": _vm.openClient,
      "on-change": _vm.optionId
    },
    model: {
      value: _vm.gzform.client,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "client", $$v);
      },
      expression: "gzform.client"
    }
  }, _vm._l(_vm.clientSelect, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.id } }, [_vm._v(_vm._s(item.machine))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "协议", prop: "agre" } }, [_c("Select", {
    attrs: { placeholder: "请选择协议", disabled: "" },
    on: { "on-change": _vm.agreOptionId },
    model: {
      value: _vm.gzform.agre,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "agre", $$v);
      },
      expression: "gzform.agre"
    }
  }, _vm._l(_vm.agreArr, function (item) {
    return _c("Option", { key: item, attrs: { value: item } }, [_vm._v(_vm._s(item))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "路径", prop: "path" } }, [_c("Input", {
    model: {
      value: _vm.gzform.path,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "path", $$v);
      },
      expression: "gzform.path"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "备注" } }, [_c("Input", {
    model: {
      value: _vm.gzform.desc,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "desc", $$v);
      },
      expression: "gzform.desc"
    }
  })], 1)], 1) : _vm._e(), _vm._v(" "), (_vm.stecur == 2 ? true : false) ? _c("div", [_vm.platformrestype == 327680 ? _c("vmOpt", {
    attrs: { clientId: _vm.clientId, sqltype: _vm.sqltype },
    on: {
      getVmCase: _vm.getVmCase,
      getVmDc: _vm.getVmDc,
      getVmHosts: _vm.getVmHosts,
      getVmNetWork: _vm.getVmNetWork,
      getVmDataStroe: _vm.getVmDataStroe,
      getVmDistMode: _vm.getVmDistMode,
      getVmRename: _vm.getVmRename
    }
  }) : _vm._e(), _vm._v(" "), _vm.platformrestype == 1441792 ? _c("casOpt", {
    attrs: { clientId: _vm.clientId, sqltype: _vm.sqltype },
    on: {
      getCasCase: _vm.getCasCase,
      getCasHpid: _vm.getCasHpid,
      getCasHostE: _vm.getCasHostE,
      getCasVswitch: _vm.getCasVswitch,
      getCasClusterId: _vm.getCasClusterId,
      getCasRename: _vm.getCasRename,
      getCasCcPool: _vm.getCasCcPool
    }
  }) : _vm._e(), _vm._v(" "), _vm.platformrestype == 1507328 ? _c("uisOpt", {
    attrs: { clientId: _vm.clientId, sqltype: _vm.sqltype },
    on: {
      getUisCase: _vm.getUisCase,
      getUisHpid: _vm.getUisHpid,
      getUispool: _vm.getUispool,
      getUisvswitch: _vm.getUisvswitch,
      getUisRestarte: _vm.getUisRestarte,
      getUisRename: _vm.getUisRename,
      getUisCcPool: _vm.getUisCcPool
    }
  }) : _vm._e()], 1) : _vm._e()], 1), _vm._v(" "), _c("div", { staticClass: "modefooter", staticStyle: { width: "100%" } }, [(_vm.stecur > 0 ? true : false) ? _c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { icon: "md-arrow-back" },
    on: { click: _vm.upstep }
  }, [_vm._v("上一步")]) : _vm._e(), _vm._v(" "), _c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { icon: "md-close-circle" },
    on: { click: _vm.cancel }
  }, [_vm._v("取消")]), _vm._v(" "), (_vm.stecur < 2 ? true : false) ? _c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { icon: "md-arrow-forward" },
    on: { click: _vm.nextstep }
  }, [_vm._v("下一步")]) : _vm._e(), _vm._v(" "), (_vm.stecur == 2 ? true : false) ? _c("Button", {
    attrs: { type: "primary", icon: "md-checkmark-circle" },
    on: { click: _vm.onmount }
  }, [_vm._v("确定")]) : _vm._e()], 1)], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-b83bcac2", esExports);
  }
}

/***/ }),

/***/ 3740:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_H3cCas_vue__ = __webpack_require__(2819);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_H3cCas_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_H3cCas_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_H3cCas_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_H3cCas_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_43e9a9d6_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_H3cCas_vue__ = __webpack_require__(3743);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_43e9a9d6_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_H3cCas_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_43e9a9d6_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_H3cCas_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3741)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = null
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_H3cCas_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_43e9a9d6_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_H3cCas_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/dupMag/components/H3cCas.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-43e9a9d6", Component.options)
  } else {
    hotAPI.reload("data-v-43e9a9d6", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ }),

/***/ 3741:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3742);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("8ecec444", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-43e9a9d6\",\"scoped\":false,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./H3cCas.vue", function() {
     var newContent = require("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-43e9a9d6\",\"scoped\":false,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./H3cCas.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3742:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "", ""]);

// exports


/***/ }),

/***/ 3743:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", [_c("Form", {
    ref: "gzform",
    attrs: { "label-width": 90, model: _vm.gzform, rules: _vm.gzformRule }
  }, [_c("Steps", {
    staticStyle: { margin: "20px 0" },
    attrs: { current: _vm.stecur }
  }, [_c("Step", { attrs: { title: "选择资源" } }), _vm._v(" "), _c("Step", { attrs: { title: "选择平台" } }), _vm._v(" "), _c("Step", { attrs: { title: "选项" } })], 1), _vm._v(" "), (_vm.stecur == 0 ? true : false) ? _c("div", { staticStyle: { "text-align": "right" } }, [_c("Table", {
    ref: "selection",
    attrs: {
      border: "",
      columns: _vm.casCol,
      data: _vm.casTableList
    },
    on: {
      "on-row-click": _vm.torowdata,
      "on-selection-change": _vm.tovawlist
    }
  })], 1) : _vm._e(), _vm._v(" "), (_vm.stecur == 1 ? true : false) ? _c("div", [_c("FormItem", { attrs: { label: "平台类型" } }, [_c("Select", {
    attrs: {
      placeholder: "请选择平台",
      "label-in-value": true
    },
    on: {
      "on-change": _vm.changeyjid,
      "on-open-change": _vm.openPlat
    },
    model: {
      value: _vm.platformrestype,
      callback: function callback($$v) {
        _vm.platformrestype = $$v;
      },
      expression: "platformrestype"
    }
  }, _vm._l(_vm.yjlist, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.id, label: item.name }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "客户端", prop: "client" } }, [_c("Select", {
    attrs: {
      placeholder: "请选择客户端",
      disabled: _vm.typedis
    },
    on: {
      "on-open-change": _vm.openClient,
      "on-change": _vm.optionId
    },
    model: {
      value: _vm.gzform.client,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "client", $$v);
      },
      expression: "gzform.client"
    }
  }, _vm._l(_vm.clientSelect, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.id } }, [_vm._v(_vm._s(item.machine))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "协议", prop: "agre" } }, [_c("Select", {
    attrs: { placeholder: "请选择协议", disabled: "" },
    on: { "on-change": _vm.agreOptionId },
    model: {
      value: _vm.gzform.agre,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "agre", $$v);
      },
      expression: "gzform.agre"
    }
  }, _vm._l(_vm.agreArr, function (item) {
    return _c("Option", { key: item, attrs: { value: item } }, [_vm._v(_vm._s(item))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "路径", prop: "path" } }, [_c("Input", {
    model: {
      value: _vm.gzform.path,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "path", $$v);
      },
      expression: "gzform.path"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "备注" } }, [_c("Input", {
    model: {
      value: _vm.gzform.desc,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "desc", $$v);
      },
      expression: "gzform.desc"
    }
  })], 1)], 1) : _vm._e(), _vm._v(" "), (_vm.stecur == 2 ? true : false) ? _c("div", [_vm.platformrestype == 327680 ? _c("vmOpt", {
    attrs: { clientId: _vm.clientId, sqltype: _vm.sqltype },
    on: {
      getVmCase: _vm.getVmCase,
      getVmDc: _vm.getVmDc,
      getVmHosts: _vm.getVmHosts,
      getVmNetWork: _vm.getVmNetWork,
      getVmDataStroe: _vm.getVmDataStroe,
      getVmDistMode: _vm.getVmDistMode,
      getVmRename: _vm.getVmRename
    }
  }) : _vm._e(), _vm._v(" "), _vm.platformrestype == 1441792 ? _c("casOpt", {
    attrs: { clientId: _vm.clientId },
    on: {
      getCasCase: _vm.getCasCase,
      getCasHpid: _vm.getCasHpid,
      getCasHostE: _vm.getCasHostE,
      getCasVswitch: _vm.getCasVswitch,
      getCasClusterId: _vm.getCasClusterId,
      getCasRename: _vm.getCasRename,
      getCasCcPool: _vm.getCasCcPool
    }
  }) : _vm._e(), _vm._v(" "), _vm.platformrestype == 1507328 ? _c("uisOpt", {
    attrs: { clientId: _vm.clientId },
    on: {
      getUisCase: _vm.getUisCase,
      getUisHpid: _vm.getUisHpid,
      getUispool: _vm.getUispool,
      getUisvswitch: _vm.getUisvswitch,
      getUisRestarte: _vm.getUisRestarte,
      getUisRename: _vm.getUisRename,
      getUisCcPool: _vm.getUisCcPool
    }
  }) : _vm._e()], 1) : _vm._e()], 1), _vm._v(" "), _c("div", { staticClass: "modefooter", staticStyle: { width: "100%" } }, [(_vm.stecur > 0 ? true : false) ? _c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { icon: "md-arrow-back" },
    on: { click: _vm.upstep }
  }, [_vm._v("上一步")]) : _vm._e(), _vm._v(" "), _c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { icon: "md-close-circle" },
    on: { click: _vm.cancel }
  }, [_vm._v("取消")]), _vm._v(" "), (_vm.stecur < 2 ? true : false) ? _c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { icon: "md-arrow-forward" },
    on: { click: _vm.nextstep }
  }, [_vm._v("下一步")]) : _vm._e(), _vm._v(" "), (_vm.stecur == 2 ? true : false) ? _c("Button", {
    attrs: { type: "primary", icon: "md-checkmark-circle" },
    on: { click: _vm.onmount }
  }, [_vm._v("确定")]) : _vm._e()], 1)], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-43e9a9d6", esExports);
  }
}

/***/ }),

/***/ 3744:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_Vmware_vue__ = __webpack_require__(2820);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_Vmware_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_Vmware_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_Vmware_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_Vmware_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_3e600d8c_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_Vmware_vue__ = __webpack_require__(3747);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_3e600d8c_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_Vmware_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_3e600d8c_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_Vmware_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3745)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = null
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_Vmware_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_3e600d8c_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_Vmware_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/dupMag/components/Vmware.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-3e600d8c", Component.options)
  } else {
    hotAPI.reload("data-v-3e600d8c", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ }),

/***/ 3745:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3746);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("24517dd0", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-3e600d8c\",\"scoped\":false,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./Vmware.vue", function() {
     var newContent = require("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-3e600d8c\",\"scoped\":false,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./Vmware.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3746:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "", ""]);

// exports


/***/ }),

/***/ 3747:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", [_c("Form", {
    ref: "gzform",
    attrs: { "label-width": 90, model: _vm.gzform, rules: _vm.gzformRule }
  }, [_c("Steps", {
    staticStyle: { margin: "20px 0" },
    attrs: { current: _vm.stecur }
  }, [_c("Step", { attrs: { title: "选择资源" } }), _vm._v(" "), _c("Step", { attrs: { title: "选择平台" } }), _vm._v(" "), _c("Step", { attrs: { title: "选项" } })], 1), _vm._v(" "), (_vm.stecur == 0 ? true : false) ? _c("div", { staticStyle: { "text-align": "right" } }, [_c("Table", {
    ref: "selection",
    attrs: {
      border: "",
      columns: _vm.vmwarecol,
      data: _vm.vamTableList
    },
    on: {
      "on-row-click": _vm.torowdata,
      "on-selection-change": _vm.tovawlist
    }
  })], 1) : _vm._e(), _vm._v(" "), (_vm.stecur == 1 ? true : false) ? _c("div", [_c("FormItem", { attrs: { label: "平台类型" } }, [_c("Select", {
    attrs: {
      placeholder: "请选择平台",
      "label-in-value": true
    },
    on: {
      "on-change": _vm.changeyjid,
      "on-open-change": _vm.openPlat
    },
    model: {
      value: _vm.platformrestype,
      callback: function callback($$v) {
        _vm.platformrestype = $$v;
      },
      expression: "platformrestype"
    }
  }, _vm._l(_vm.yjlist, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.id, label: item.name }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "客户端", prop: "client" } }, [_c("Select", {
    attrs: {
      placeholder: "请选择客户端",
      disabled: _vm.typedis = false
    },
    on: {
      "on-open-change": _vm.openClient,
      "on-change": _vm.optionId
    },
    model: {
      value: _vm.gzform.client,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "client", $$v);
      },
      expression: "gzform.client"
    }
  }, _vm._l(_vm.clientSelect, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.id } }, [_vm._v(_vm._s(item.machine))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "协议", prop: "agre" } }, [_c("Select", {
    attrs: { placeholder: "请选择协议", disabled: "" },
    on: { "on-change": _vm.agreOptionId },
    model: {
      value: _vm.gzform.agre,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "agre", $$v);
      },
      expression: "gzform.agre"
    }
  }, _vm._l(_vm.agreArr, function (item) {
    return _c("Option", { key: item, attrs: { value: item } }, [_vm._v(_vm._s(item))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "路径", prop: "path" } }, [_c("Input", {
    model: {
      value: _vm.gzform.path,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "path", $$v);
      },
      expression: "gzform.path"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "备注" } }, [_c("Input", {
    model: {
      value: _vm.gzform.desc,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "desc", $$v);
      },
      expression: "gzform.desc"
    }
  })], 1)], 1) : _vm._e(), _vm._v(" "), (_vm.stecur == 2 ? true : false) ? _c("div", [_vm.platformrestype == 327680 ? _c("vmOpt", {
    attrs: {
      clientId: _vm.clientId,
      sqltype: _vm.sqltype
    },
    on: {
      getVmCase: _vm.getVmCase,
      getVmDc: _vm.getVmDc,
      getVmHosts: _vm.getVmHosts,
      getVmNetWork: _vm.getVmNetWork,
      getVmDataStroe: _vm.getVmDataStroe,
      getVmDistMode: _vm.getVmDistMode,
      getVmRename: _vm.getVmRename,
      getPowerType: _vm.getPowerType,
      getPowerVal: _vm.getPowerVal
    }
  }, [_vm._v("\n\t\t\t\t>")]) : _vm._e(), _vm._v(" "), _vm.platformrestype == 1441792 ? _c("casOpt", {
    attrs: { clientId: _vm.clientId },
    on: {
      getCasCase: _vm.getCasCase,
      getCasHpid: _vm.getCasHpid,
      getCasHostE: _vm.getCasHostE,
      getCasVswitch: _vm.getCasVswitch,
      getCasClusterId: _vm.getCasClusterId,
      getCasRename: _vm.getCasRename,
      getCasPowerType: _vm.getCasPowerType,
      getCasPowerVal: _vm.getCasPowerVal,
      getCasCcPool: _vm.getCasCcPool
    }
  }) : _vm._e(), _vm._v(" "), _vm.platformrestype == 1507328 ? _c("uisOpt", {
    attrs: { clientId: _vm.clientId },
    on: {
      getUisCase: _vm.getUisCase,
      getUisHpid: _vm.getUisHpid,
      getUispool: _vm.getUispool,
      getUisvswitch: _vm.getUisvswitch,
      getUisRestarte: _vm.getUisRestarte,
      getUisRename: _vm.getUisRename,
      getUisPowerType: _vm.getUisPowerType,
      getUIsPowerVal: _vm.getUIsPowerVal,
      getUisCcPool: _vm.getUisCcPool
    }
  }) : _vm._e()], 1) : _vm._e()], 1), _vm._v(" "), _c("div", { staticClass: "modefooter", staticStyle: { width: "100%" } }, [(_vm.stecur > 0 ? true : false) ? _c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { icon: "md-arrow-back" },
    on: { click: _vm.upstep }
  }, [_vm._v("上一步")]) : _vm._e(), _vm._v(" "), _c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { icon: "md-close-circle" },
    on: { click: _vm.cancel }
  }, [_vm._v("取消")]), _vm._v(" "), (_vm.stecur < 2 ? true : false) ? _c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { icon: "md-arrow-forward" },
    on: {
      click: function click($event) {
        return _vm.nextstep(_vm.stecur);
      }
    }
  }, [_vm._v("下一步")]) : _vm._e(), _vm._v(" "), (_vm.stecur == 2 ? true : false) ? _c("Button", {
    attrs: { type: "primary", icon: "md-checkmark-circle" },
    on: { click: _vm.onmount }
  }, [_vm._v("确定")]) : _vm._e()], 1)], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-3e600d8c", esExports);
  }
}

/***/ }),

/***/ 3748:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_vTree_vue__ = __webpack_require__(2821);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_vTree_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_vTree_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_vTree_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_vTree_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_210872a8_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_vTree_vue__ = __webpack_require__(3751);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_210872a8_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_vTree_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_210872a8_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_vTree_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3749)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = "data-v-210872a8"
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_vTree_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_210872a8_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_vTree_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/dupMag/components/vTree.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-210872a8", Component.options)
  } else {
    hotAPI.reload("data-v-210872a8", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ }),

/***/ 3749:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3750);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("44bf0921", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-210872a8\",\"scoped\":true,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./vTree.vue", function() {
     var newContent = require("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-210872a8\",\"scoped\":true,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./vTree.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3750:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.tree-wrap[data-v-210872a8]{width:100%;height:350px\n}", ""]);

// exports


/***/ }),

/***/ 3751:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", { staticClass: "tree-wrap" }, [_c("ul", {
    directives: [{
      name: "show",
      rawName: "v-show",
      value: _vm.ztreeArray.length,
      expression: "ztreeArray.length"
    }],
    staticClass: "ztree",
    staticStyle: { height: "100%", "overflow-y": "auto" },
    attrs: { id: "treeDemoTactics" }
  })]);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-210872a8", esExports);
  }
}

/***/ }),

/***/ 3752:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", [_c("Row", [_c("Col", { staticClass: "breadstyle", attrs: { span: "24" } }, [_c("Breadcrumb", [_c("BreadcrumbItem", {
    staticClass: "breadlist",
    staticStyle: { display: "flex", "align-items": "center" }
  }, [_c("img", {
    staticStyle: {
      width: "1.2rem",
      height: "1.2rem",
      "margin-right": "0.3rem"
    },
    attrs: { src: __webpack_require__(318) }
  }), _vm._v(" "), _c("span", [_vm._v("数据副本 / 副本")])])], 1)], 1)], 1), _vm._v(" "), _c("Row", [_c("Col", { attrs: { span: "5" } }, [_c("el-card", {
    staticClass: "mar-box",
    staticStyle: { height: "calc(100vh - 145px)" }
  }, [_vm.zvoldata.length > 0 ? _c("div", { staticClass: "tree-search-box" }, [_c("el-input", {
    staticClass: "tree-input",
    attrs: { placeholder: "搜索过滤", size: "small" },
    on: { input: _vm.filterTree },
    model: {
      value: _vm.filterText,
      callback: function callback($$v) {
        _vm.filterText = $$v;
      },
      expression: "filterText"
    }
  })], 1) : _vm._e(), _vm._v(" "), _vm.zvoldata.length > 0 ? _c("div", { staticClass: "tree-box" }, [_c("Tree", {
    ref: "clitree",
    staticClass: "demo-tree-render",
    attrs: {
      data: _vm.zvoldata,
      render: _vm.renderContent
    }
  })], 1) : _vm._e(), _vm._v(" "), _vm.zvoldata.length <= 0 ? _c("p", { staticClass: "no-data-img" }, [_c("img", {
    staticClass: "noImgUrl",
    attrs: { src: _vm.noImgUrl }
  }), _vm._v(" "), _c("span", [_vm._v("暂无数据")])]) : _vm._e()])], 1), _vm._v(" "), _c("Col", { attrs: { span: "19" } }, [_c("el-card", {
    staticClass: "mar-box mar-l",
    staticStyle: { height: "calc(100vh - 145px)" }
  }, [_c("Table", {
    attrs: {
      loading: _vm.loading,
      columns: _vm.onecol,
      height: _vm.tableHeight,
      "row-class-name": _vm.tishi,
      data: _vm.dupdata,
      "no-data-text": "<div class='no-img-url-text'><img class='noImgUrl' src=" + _vm.noImgUrl + " /><div class='text'>暂无数据</div><div>"
    },
    on: { "on-row-click": _vm.getRowData }
  }), _vm._v(" "), _c("div", { staticClass: "page-box" }, [_vm.policytotal > 0 ? _c("el-pagination", {
    staticClass: "page-wrap",
    attrs: {
      background: "",
      "current-page": _vm.curentPage,
      "page-sizes": [10, 20, 30, 40],
      "page-size": 10,
      layout: "total, sizes, prev, pager, next, jumper",
      total: _vm.policytotal
    },
    on: {
      "size-change": _vm.handleSizeChange,
      "current-change": _vm.changePage
    }
  }) : _vm._e()], 1)], 1)], 1)], 1), _vm._v(" "), _c("Drawer", {
    attrs: {
      title: _vm.sqltype == "恢复" ? "恢复" : "挂载",
      closable: false,
      "mask-closable": false,
      width: "50%"
    },
    on: { "on-close": _vm.closeDraBox },
    model: {
      value: _vm.onemodel,
      callback: function callback($$v) {
        _vm.onemodel = $$v;
      },
      expression: "onemodel"
    }
  }, [_c("div", { staticClass: "modeContent" }, [_c("Form", {
    ref: "gzform",
    attrs: {
      "label-width": 120,
      model: _vm.gzform,
      rules: _vm.gzformRule
    }
  }, [(_vm.resTypeVal == 1441792 || _vm.resTypeVal == 327680 || _vm.resTypeVal == 1507328 ? false : true) ? _c("div", [(_vm.mountType != "VMWARE" && _vm.stecur == 0 || _vm.mountType == "DB2" ? true : false) ? _c("FormItem", { attrs: { label: "客户端", prop: "client" } }, [_c("Select", {
    attrs: {
      placeholder: "请选择客户端",
      disabled: _vm.typedis
    },
    on: {
      "on-open-change": _vm.openClient,
      "on-change": _vm.optionId
    },
    model: {
      value: _vm.gzform.client,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "client", $$v);
      },
      expression: "gzform.client"
    }
  }, _vm._l(_vm.clientSelect, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.id }
    }, [_vm._v(_vm._s(item.machine))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), _vm.mountType == "KINGBASE" ? _c("FormItem", { attrs: { label: "实例名", prop: "id" } }, [_c("Select", {
    attrs: { placeholder: "请选择实例名" },
    on: { "on-change": _vm.oraclechange },
    model: {
      value: _vm.gzform.cliName,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "cliName", $$v);
      },
      expression: "gzform.cliName"
    }
  }, _vm._l(_vm.dmsldata, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.name }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), _vm.mountType == "瀚高数据库" || _vm.mountType == "PostgreSQL" || _vm.mountType == "HLSQL" ? _c("FormItem", { attrs: { label: "实例名", prop: "id" } }, [_c("Select", {
    attrs: { placeholder: "请选择实例名" },
    on: { "on-change": _vm.oraclechange },
    model: {
      value: _vm.gzform.cliName,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "cliName", $$v);
      },
      expression: "gzform.cliName"
    }
  }, _vm._l(_vm.dmsldata, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.host }
    }, [_vm._v(_vm._s(item.host))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), (_vm.mountType == "VMWARE" ? false : true) ? _c("FormItem", { attrs: { label: "协议", prop: "agre" } }, [_c("Select", {
    attrs: { placeholder: "请选择协议" },
    on: {
      "on-open-change": _vm.openAgre,
      "on-change": _vm.agreOptionId
    },
    model: {
      value: _vm.gzform.agre,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "agre", $$v);
      },
      expression: "gzform.agre"
    }
  }, _vm._l(_vm.agreArr, function (item) {
    return _c("Option", { key: item, attrs: { value: item } }, [_vm._v(_vm._s(item))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), (_vm.mountType != "VMWARE" && _vm.stecur == 0 && _vm.mountType != "SQLSERVER" && _vm.mountType != "GBase" && _vm.mountType != "Informix数据库" && _vm.osType == 1 ? true : false) ? _c("FormItem", { attrs: { label: "路径", prop: "path" } }, [_c("Input", {
    model: {
      value: _vm.gzform.path,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "path", $$v);
      },
      expression: "gzform.path"
    }
  })], 1) : _vm._e(), _vm._v(" "), _vm.mountType == "瀚高数据库" || _vm.mountType == "PostgreSQL" || _vm.mountType == "HLSQL" || _vm.mountType == "达梦" ? _c("FormItem", { attrs: { label: "恢复时间点" } }, [_c("DatePicker", {
    staticStyle: { width: "200px" },
    attrs: {
      type: "datetime",
      placeholder: "请选择恢复时间节点"
    },
    on: { "on-change": _vm.oracleTime }
  })], 1) : _vm._e(), _vm._v(" "), _vm.mountType == "MYSQL" && _vm.tableRow.backup_type == "日志备份" ? _c("FormItem", { attrs: { label: "恢复时间点" } }, [_c("DatePicker", {
    staticStyle: { width: "200px" },
    attrs: {
      type: "datetime",
      placeholder: "请选择恢复时间节点"
    },
    on: { "on-change": _vm.oracleTime }
  })], 1) : _vm._e(), _vm._v(" "), (_vm.mountType == "VMWARE" || _vm.mountType == "文件" || _vm.resTypeVal == "1441792" ? false : true) ? _c("FormItem", { attrs: { label: "任务类型" } }, [_c("RadioGroup", {
    on: { "on-change": _vm.ontasktype },
    model: {
      value: _vm.taskType,
      callback: function callback($$v) {
        _vm.taskType = $$v;
      },
      expression: "taskType"
    }
  }, [_c("Radio", {
    attrs: { label: "挂载数据库" }
  }), _vm._v(" "), _vm.sqltype != "恢复" ? _c("Radio", {
    attrs: { label: "挂载文件" }
  }) : _vm._e()], 1)], 1) : _vm._e(), _vm._v(" "), (_vm.mountType == "VMWARE" || _vm.resTypeVal == 1441792 ? false : true) ? _c("FormItem", { attrs: { label: "备注" } }, [_c("Input", {
    model: {
      value: _vm.gzform.desc,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "desc", $$v);
      },
      expression: "gzform.desc"
    }
  })], 1) : _vm._e(), _vm._v(" "), (_vm.mountType != "VMWARE" && _vm.stecur == 0 && _vm.mountType != "SQLSERVER" && _vm.osType == 1 && _vm.sqltype == "恢复" || _vm.mountType == "PostgreSQL" || _vm.mountType == "HLSQL" ? true : false) ? _c("vTree", {
    attrs: { setRowData: _vm.setRowData },
    on: { getResPost: _vm.getResPost }
  }) : _vm._e()], 1) : _vm._e(), _vm._v(" "), _vm.resTypeVal == "1441792" && _vm.platformrestype == 1441792 && (_vm.sqltype == "挂载" || _vm.sqltype == "恢复") ? _c("div", [_c("H3cCas", {
    ref: "H3cCas",
    attrs: {
      casTableList: _vm.casTableList,
      sqltype: _vm.sqltype,
      volid: _vm.volid,
      resTypeVal: _vm.resTypeVal,
      platformrestype: _vm.platformrestype
    },
    on: { closeDrawer: _vm.closeDrawer }
  })], 1) : _vm._e(), _vm._v(" "), (_vm.resTypeVal == "1507328" || _vm.platformrestype == 1507328) && (_vm.sqltype == "挂载" || _vm.sqltype == "恢复") ? _c("div", [_c("H3cUis", {
    ref: "H3cUis",
    attrs: {
      uisTableList: _vm.uisTableList,
      sqltype: _vm.sqltype,
      volid: _vm.volid,
      resTypeVal: _vm.resTypeVal,
      platformrestype: _vm.platformrestype
    },
    on: { closeDrawer: _vm.closeDrawer }
  })], 1) : _vm._e(), _vm._v(" "), (_vm.resTypeVal == "327680" || _vm.platformrestype == 327680) && (_vm.sqltype == "挂载" || _vm.sqltype == "恢复") ? _c("div", [_c("Vmware", {
    ref: "Vmware",
    attrs: {
      vamTableList: _vm.vamTableList,
      sqltype: _vm.sqltype,
      volid: _vm.volid,
      platformrestype: _vm.platformrestype,
      resTypeVal: _vm.resTypeVal,
      typedis: _vm.typedis
    },
    on: { closeDrawer: _vm.closeDrawer }
  })], 1) : _vm._e(), _vm._v(" "), _vm.mountType == "ORACLE" ? _c("div", [_c("FormItem", { attrs: { label: "", "label-width": 0 } }, [_c("RadioGroup", {
    on: { "on-change": _vm.onscntime },
    model: {
      value: _vm.scntimeshow,
      callback: function callback($$v) {
        _vm.scntimeshow = $$v;
      },
      expression: "scntimeshow"
    }
  }, [_c("Radio", { attrs: { label: "1" } }, [_vm._v("恢复到指定的SCN")]), _vm._v(" "), _c("Radio", { attrs: { label: "2" } }, [_vm._v("恢复时间点")])], 1)], 1), _vm._v(" "), _vm.scntimeshow == "1" ? _c("FormItem", {
    attrs: {
      label: "恢复到指定的SCN",
      prop: "scn"
    }
  }, [_c("Input", {
    attrs: {
      placeholder: "请输入恢复到指定的SCN"
    },
    on: { "on-blur": _vm.getscnFun },
    model: {
      value: _vm.gzform.scn,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "scn", $$v);
      },
      expression: "gzform.scn"
    }
  })], 1) : _vm._e(), _vm._v(" "), _vm.scntimeshow == "2" ? _c("div", [(_vm.taskType == "挂载数据库" || _vm.oracletasttype == 1 ? true : false) ? _c("FormItem", { attrs: { label: "恢复时间点" } }, [_c("DatePicker", {
    staticStyle: { width: "200px" },
    attrs: {
      type: "datetime",
      placeholder: "请选择恢复时间节点"
    },
    on: { "on-change": _vm.oracleTime }
  })], 1) : _vm._e()], 1) : _vm._e(), _vm._v(" "), (_vm.taskType == "挂载数据库" || _vm.oracletasttype == 1 ? true : false) ? _c("FormItem", { attrs: { label: "服务名", prop: "id" } }, [_c("Select", {
    attrs: { placeholder: "请选择服务名" },
    on: { "on-change": _vm.oraclechange },
    model: {
      value: _vm.gzform.cliName,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "cliName", $$v);
      },
      expression: "gzform.cliName"
    }
  }, _vm._l(_vm.dmsldata, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.name }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1) : _vm._e()], 1) : _vm._e(), _vm._v(" "), _vm.mountType == "SQLSERVER" ? _c("div", [(_vm.oracletasttype == 2 ? false : true) ? _c("FormItem", { attrs: { label: "实例名", prop: "id" } }, [_c("Select", {
    attrs: { placeholder: "请选择实例名" },
    on: { "on-change": _vm.sqlchange },
    model: {
      value: _vm.gzform.id,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "id", $$v);
      },
      expression: "gzform.id"
    }
  }, _vm._l(_vm.sqlsldata, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.server }
    }, [_vm._v(_vm._s(item.server))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), (_vm.sqltype == "恢复" ? true : false) ? _c("FormItem", {
    attrs: {
      label: "数据文件路径",
      prop: "datapath"
    }
  }, [_c("Input", {
    attrs: { type: "text" },
    model: {
      value: _vm.gzform.datapath,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "datapath", $$v);
      },
      expression: "gzform.datapath"
    }
  })], 1) : _vm._e(), _vm._v(" "), (_vm.oracletasttype == 2 ? false : true) ? _c("FormItem", { attrs: { label: "重命名", prop: "name" } }, [_c("Input", {
    attrs: { type: "text" },
    model: {
      value: _vm.gzform.name,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "name", $$v);
      },
      expression: "gzform.name"
    }
  })], 1) : _vm._e()], 1) : _vm._e(), _vm._v(" "), _vm.mountType == "MYSQL" ? _c("div", [_vm.taskType == "挂载数据库" ? _c("FormItem", { attrs: { label: "实例名", prop: "id" } }, [_c("Select", {
    attrs: { placeholder: "请选择实例名" },
    on: { "on-change": _vm.oraclechange },
    model: {
      value: _vm.gzform.cliName,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "cliName", $$v);
      },
      expression: "gzform.cliName"
    }
  }, _vm._l(_vm.dmsldata, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.host }
    }, [_vm._v(_vm._s(item.host))]);
  }), 1)], 1) : _vm._e()], 1) : _vm._e(), _vm._v(" "), _vm.mountType == "DB2" || _vm.mountType == "SQLSERVER" ? _c("div", [_c("div", { staticClass: "tree-box" }, [_c("div", { staticClass: "tree" }, [_c("ul", {
    staticClass: "ztree",
    attrs: { id: "treeDemo" }
  })])])]) : _vm._e(), _vm._v(" "), _vm.mountType == "达梦" ? _c("div", [_c("FormItem", { attrs: { label: "实例名", prop: "id" } }, [_c("Select", {
    attrs: { placeholder: "请选择实例名" },
    on: { "on-change": _vm.sqlchange },
    model: {
      value: _vm.gzform.id,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "id", $$v);
      },
      expression: "gzform.id"
    }
  }, _vm._l(_vm.dmsldata, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.name }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1)], 1) : _vm._e(), _vm._v(" "), _vm.mountType == "SQLSERVER" ? _c("div", [_c("p", {
    staticStyle: {
      color: "#ff0000",
      "word-wrap": "break-word"
    }
  }, [_vm._v("\n\t\t\t\t\t\t\t请确认客户端认证方式为windows认证,且NT AUTHORITYSYSTEM用户权限是否足够\n\t\t\t\t\t\t")])]) : _vm._e(), _vm._v(" "), _vm.mountType == "GBase" ? _c("div", [_vm.taskType == "挂载数据库" ? _c("FormItem", { attrs: { label: "实例名", prop: "id" } }, [_c("Select", {
    attrs: { placeholder: "请选择实例名" },
    on: { "on-change": _vm.oraclechange },
    model: {
      value: _vm.gzform.cliName,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "cliName", $$v);
      },
      expression: "gzform.cliName"
    }
  }, _vm._l(_vm.dmsldata, function (item) {
    return _c("Option", {
      key: item.client,
      attrs: { value: item.name }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), _c("FormItem", { attrs: { label: "是否恢复日志" } }, [_c("RadioGroup", {
    model: {
      value: _vm.restoreLogsVal,
      callback: function callback($$v) {
        _vm.restoreLogsVal = $$v;
      },
      expression: "restoreLogsVal"
    }
  }, [_c("Radio", { attrs: { label: "1" } }, [_vm._v("恢复")]), _vm._v(" "), _c("Radio", { attrs: { label: "2" } }, [_vm._v("不恢复")])], 1)], 1)], 1) : _vm._e()])], 1), _vm._v(" "), (_vm.resTypeVal == "1507328" || _vm.resTypeVal == "1441792" || _vm.resTypeVal == "327680" || _vm.mountType == "VMWARE" ? false : true) ? _c("div", { staticClass: "modefooter", staticStyle: { width: "100%" } }, [(_vm.mountType == "VMWARE" && _vm.stecur == 0 || (_vm.resTypeVal == "1441792" || _vm.resTypeVal == "1507328") && _vm.stecur == 0 ? false : true) ? _c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { icon: "md-close-circle" },
    on: { click: _vm.cancel }
  }, [_vm._v("取消")]) : _vm._e(), _vm._v(" "), (_vm.mountType == "VMWARE" && _vm.stecur == 0 || (_vm.resTypeVal == "1441792" || _vm.resTypeVal == "1507328") && _vm.stecur == 0 ? false : true) ? _c("Button", {
    attrs: {
      type: "primary",
      icon: "md-checkmark-circle"
    },
    on: { click: _vm.onmount }
  }, [_vm._v("确定")]) : _vm._e()], 1) : _vm._e()]), _vm._v(" "), _c("Modal", {
    attrs: { closable: false, "footer-hide": true, width: "540" },
    model: {
      value: _vm.expiremodel,
      callback: function callback($$v) {
        _vm.expiremodel = $$v;
      },
      expression: "expiremodel"
    }
  }, [_c("div", { staticClass: "addPolicyModeStyle" }, [_c("h3", [_vm._v("过期")]), _vm._v(" "), _c("span", { staticClass: "iconfont", on: { click: _vm.cancel } }, [_vm._v("")])]), _vm._v(" "), _c("div", {
    staticClass: "modeContent",
    staticStyle: { "margin-left": "20px" }
  }, [_c("p", { staticStyle: { color: "#f60" } }, [_c("span", { staticClass: "iconfont" }, [_vm._v("")]), _vm._v(" "), _c("span", [_vm._v("过期操作会导致数据副本无法正常使用，请确认是否进行过期操作")])])]), _vm._v(" "), _c("div", { staticClass: "modefooter", staticStyle: { width: "100%" } }, [_c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { size: "large", icon: "md-close-circle" },
    on: { click: _vm.cancel }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      size: "large",
      icon: "md-checkmark-circle"
    },
    on: { click: _vm.onexpire }
  }, [_vm._v("确定")])], 1)]), _vm._v(" "), _c("Modal", {
    attrs: { closable: false, "footer-hide": true, width: "640" },
    model: {
      value: _vm.placeonfileModel,
      callback: function callback($$v) {
        _vm.placeonfileModel = $$v;
      },
      expression: "placeonfileModel"
    }
  }, [_c("div", { staticClass: "addPolicyModeStyle" }, [_c("h3", [_vm._v("归档")]), _vm._v(" "), _c("span", { staticClass: "iconfont", on: { click: _vm.cancel } }, [_vm._v("")])]), _vm._v(" "), _c("div", {
    staticClass: "modeContent",
    staticStyle: { "margin-left": "20px" }
  }, [_c("Form", {
    ref: "placeonfileDate",
    attrs: {
      model: _vm.placeonfileDate,
      rules: _vm.ruleValidate,
      "label-width": 100
    }
  }, [_c("FormItem", { attrs: { label: "归档设备", prop: "arc_device" } }, [_c("Select", {
    attrs: { placeholder: "请选择归档设备" },
    model: {
      value: _vm.placeonfileDate.arc_device,
      callback: function callback($$v) {
        _vm.$set(_vm.placeonfileDate, "arc_device", $$v);
      },
      expression: "placeonfileDate.arc_device"
    }
  }, _vm._l(_vm.deviceArr, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.id } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "归档介质池", prop: "arc_pool" } }, [_c("Select", {
    attrs: { placeholder: "请选择归档介质池" },
    model: {
      value: _vm.placeonfileDate.arc_pool,
      callback: function callback($$v) {
        _vm.$set(_vm.placeonfileDate, "arc_pool", $$v);
      },
      expression: "placeonfileDate.arc_pool"
    }
  }, _vm._l(_vm.volpoolsArr, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.id } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1)], 1)], 1), _vm._v(" "), _c("div", { staticClass: "modefooter", staticStyle: { width: "100%" } }, [_c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { size: "large", icon: "md-close-circle" },
    on: { click: _vm.cancel }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      size: "large",
      icon: "md-checkmark-circle"
    },
    on: { click: _vm.onPlaceonfile }
  }, [_vm._v("确定")])], 1)])], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-65dd5c28", esExports);
  }
}

/***/ }),

/***/ 584:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_replicaMag_vue__ = __webpack_require__(2814);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_replicaMag_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_replicaMag_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_replicaMag_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_replicaMag_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_65dd5c28_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_replicaMag_vue__ = __webpack_require__(3752);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_65dd5c28_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_replicaMag_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_65dd5c28_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_replicaMag_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3723)
  __webpack_require__(3725)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = "data-v-65dd5c28"
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_replicaMag_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_65dd5c28_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_replicaMag_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/dupMag/replicaMag.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-65dd5c28", Component.options)
  } else {
    hotAPI.reload("data-v-65dd5c28", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ })

});