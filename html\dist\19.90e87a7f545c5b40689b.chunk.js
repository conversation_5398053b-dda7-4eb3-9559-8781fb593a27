webpackJsonp([19],{

/***/ 1538:
/***/ (function(module, exports, __webpack_require__) {

module.exports = { "default": __webpack_require__(1596), __esModule: true };

/***/ }),

/***/ 1596:
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(1597);
module.exports = __webpack_require__(31).Object.assign;


/***/ }),

/***/ 1597:
/***/ (function(module, exports, __webpack_require__) {

// ******** Object.assign(target, source)
var $export = __webpack_require__(43);

$export($export.S + $export.F, 'Object', { assign: __webpack_require__(1598) });


/***/ }),

/***/ 1598:
/***/ (function(module, exports, __webpack_require__) {

"use strict";

// ******** Object.assign(target, source, ...)
var DESCRIPTORS = __webpack_require__(58);
var getKeys = __webpack_require__(212);
var gOPS = __webpack_require__(320);
var pIE = __webpack_require__(213);
var toObject = __webpack_require__(133);
var IObject = __webpack_require__(319);
var $assign = Object.assign;

// should work with symbols and should have deterministic property order (V8 bug)
module.exports = !$assign || __webpack_require__(114)(function () {
  var A = {};
  var B = {};
  // eslint-disable-next-line no-undef
  var S = Symbol();
  var K = 'abcdefghijklmnopqrst';
  A[S] = 7;
  K.split('').forEach(function (k) { B[k] = k; });
  return $assign({}, A)[S] != 7 || Object.keys($assign({}, B)).join('') != K;
}) ? function assign(target, source) { // eslint-disable-line no-unused-vars
  var T = toObject(target);
  var aLen = arguments.length;
  var index = 1;
  var getSymbols = gOPS.f;
  var isEnum = pIE.f;
  while (aLen > index) {
    var S = IObject(arguments[index++]);
    var keys = getSymbols ? getKeys(S).concat(getSymbols(S)) : getKeys(S);
    var length = keys.length;
    var j = 0;
    var key;
    while (length > j) {
      key = keys[j++];
      if (!DESCRIPTORS || isEnum.call(S, key)) T[key] = S[key];
    }
  } return T;
} : $assign;


/***/ }),

/***/ 1617:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


exports.__esModule = true;

var _assign = __webpack_require__(1538);

var _assign2 = _interopRequireDefault(_assign);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

exports.default = _assign2.default || function (target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i];

    for (var key in source) {
      if (Object.prototype.hasOwnProperty.call(source, key)) {
        target[key] = source[key];
      }
    }
  }

  return target;
};

/***/ }),

/***/ 1714:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getSystemLicense = exports.upCatalog = exports.getConlogSetInfo = exports.getDevice = exports.onceInspecct = exports.getInspecctRes = exports.getPutSet = exports.savePutSet = exports.getInspectioList = exports.sendEmail = undefined;

var _request = __webpack_require__(316);

var _request2 = _interopRequireDefault(_request);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

var sendEmail = exports.sendEmail = function sendEmail(data) {
    return (0, _request2.default)({
        method: 'GET',
        url: '/rest-ful/v3.0/system/email/send'
    });
};

var getInspectioList = exports.getInspectioList = function getInspectioList(curentPage, Nums) {
    return (0, _request2.default)({
        method: 'GET',
        url: '/rest-ful/v3.0/report/inspect?pageno=' + curentPage + '&nums=' + Nums
    });
};
var savePutSet = exports.savePutSet = function savePutSet(data) {
    return (0, _request2.default)({
        method: 'put',
        url: '/rest-ful/v3.0/system/param',
        data: data
    });
};

var getPutSet = exports.getPutSet = function getPutSet() {
    return (0, _request2.default)({
        method: 'get',
        url: '/rest-ful/v3.0/system/param'
    });
};

var getInspecctRes = exports.getInspecctRes = function getInspecctRes(id) {
    return (0, _request2.default)({
        method: 'get',
        url: '/rest-ful/v3.0/report/inspect/detail/' + id
    });
};

var onceInspecct = exports.onceInspecct = function onceInspecct(val) {
    return (0, _request2.default)({
        method: 'get',
        url: '/rest-ful/v3.0/system/inspect/manual?start_time=' + val
    });
};

var getDevice = exports.getDevice = function getDevice() {
    return (0, _request2.default)({
        method: 'get',
        url: 'rest-ful/v3.0/devices?type=0'
    });
};

var getConlogSetInfo = exports.getConlogSetInfo = function getConlogSetInfo() {
    return (0, _request2.default)({
        method: 'get',
        url: 'rest-ful/v3.0/catalog/config'
    });
};

var upCatalog = exports.upCatalog = function upCatalog(data) {
    return (0, _request2.default)({
        method: 'post',
        url: '/rest-ful/v3.0/catalog/config',
        data: data
    });
};

var getSystemLicense = exports.getSystemLicense = function getSystemLicense() {
    return (0, _request2.default)({
        method: 'get',
        url: '/rest-ful/v3.0/system/license'
    });
};

/***/ }),

/***/ 2018:
/***/ (function(module, exports, __webpack_require__) {

module.exports = { "default": __webpack_require__(2019), __esModule: true };

/***/ }),

/***/ 2019:
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(2020);
module.exports = __webpack_require__(31).Object.values;


/***/ }),

/***/ 2020:
/***/ (function(module, exports, __webpack_require__) {

// https://github.com/tc39/proposal-object-values-entries
var $export = __webpack_require__(43);
var $values = __webpack_require__(2021)(false);

$export($export.S, 'Object', {
  values: function values(it) {
    return $values(it);
  }
});


/***/ }),

/***/ 2021:
/***/ (function(module, exports, __webpack_require__) {

var DESCRIPTORS = __webpack_require__(58);
var getKeys = __webpack_require__(212);
var toIObject = __webpack_require__(115);
var isEnum = __webpack_require__(213).f;
module.exports = function (isEntries) {
  return function (it) {
    var O = toIObject(it);
    var keys = getKeys(O);
    var length = keys.length;
    var i = 0;
    var result = [];
    var key;
    while (length > i) {
      key = keys[i++];
      if (!DESCRIPTORS || isEnum.call(O, key)) {
        result.push(isEntries ? [key, O[key]] : O[key]);
      }
    }
    return result;
  };
};


/***/ }),

/***/ 2804:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _extends2 = __webpack_require__(1617);

var _extends3 = _interopRequireDefault(_extends2);

var _values = __webpack_require__(2018);

var _values2 = _interopRequireDefault(_values);

var _stringify = __webpack_require__(21);

var _stringify2 = _interopRequireDefault(_stringify);

var _util = __webpack_require__(16);

var _util2 = _interopRequireDefault(_util);

var _downloud = __webpack_require__(3683);

var _downloud2 = _interopRequireDefault(_downloud);

var _index = __webpack_require__(1714);

var _ErrorCode = __webpack_require__(3685);

var _ErrorCode2 = _interopRequireDefault(_ErrorCode);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
	components: {
		downloud: _downloud2.default,
		ErrorCode: _ErrorCode2.default
	},
	data: function data() {
		var _this2 = this;

		console.trace();
		var validatePass = function validatePass(rule, value, callback) {
			if (value === '') {
				callback(new Error('请输入密码'));
			} else {
				callback();
			}
		};
		var validatePassCheck = function validatePassCheck(rule, value, callback) {
			if (_this2.formItem.password != '' && value === '') {
				callback(new Error('确认密码不能为空'));
			} else if (_this2.formItem.password != value) {
				callback(new Error('新密码和确认密码应相同'));
			} else {
				callback();
			}
		};
		return {
			LicenseType: '',
			effectiveData: '',
			EffectiveDays: '',

			Checkpasswordbox: false,

			value: '1000',
			value1: '1000',

			value2: 100,
			disabledo: true,
			disabledt: true,
			disabledth: true,

			tableHeight: 0,
			sqvalue: '1',
			headers: {},
			token: '',

			versionList: [],
			versionNum: null,

			addRuleValidate: {
				user: [{ required: true, message: '请输入用户名', trigger: 'blur', type: 'string' }],
				password: [{ required: true, message: '请输入密码', trigger: 'blur', type: 'string' }],
				version: [{ required: true, message: '请选择版本', trigger: 'blur', type: 'string' }],
				port: [{ required: true, message: '请输入端口号', trigger: 'blur', type: 'string' }],
				address: [{ required: true, message: '请输入地址', trigger: 'blur', type: 'string' }]
			},
			editRuleValidate: {
				user: [{ required: true, message: '请输入用户名', trigger: 'blur', type: 'number' }],
				password: [{ required: true, message: '请输入密码', trigger: 'blur', type: 'number' }],
				version: [{ required: true, message: '请选择版本', trigger: 'change', type: 'number' }],
				port: [{ required: true, message: '请输入端口号', trigger: 'change', type: 'number' }],
				address: [{ required: true, message: '请输入地址', trigger: 'change', type: 'number' }]
			},

			AddModal: false,
			EditModal: false,
			delModal: false,
			addSnmpMd: {
				user: '',
				password: '',
				version: '',
				port: '',
				address: '',
				id: ''
			},

			activeName: 'second',

			actionTabs: localStorage.getItem('roleUid') == 2 ? '安全策略' : localStorage.getItem('roleUid') == 4 ? '安装包管理' : localStorage.getItem('roleUid') == 1 || localStorage.getItem('roleUid') == 5 ? '基本配置' : 'SNMP配置',

			trapColumns: [{
				title: 'SNMP版本',
				key: 'version'
			}, {
				title: 'IP地址/域名',
				key: 'address'
			}, {
				title: '端口',
				key: 'port'
			}, {
				title: '操作',
				key: 'operation',
				width: 120,
				render: function render(h, params) {
					return h('div', {
						'class': {
							role: true
						},
						style: {
							display: 'flex',
							justifyContent: 'center',
							alignItems: 'center'
						}
					}, [h('div', {
						style: {
							fontSize: '18px',

							marginRight: '5px',
							width: '30px',
							height: '30px',
							borderRadius: '3px',
							textAlign: 'center',
							lineHeight: '30px'
						}
					}, [h('span', {
						'class': 'iconfont',
						style: {
							fontSize: '20px',
							color: '#3377ff'
						},
						domProps: {
							innerHTML: '&#xe63d;'
						},
						on: {
							click: function click() {
								_this2.EditModal = true;
							}
						},
						directives: [{
							name: 'tooltip',
							value: '编缉'
						}]
					})]), h('div', {
						style: {
							fontSize: '18px',

							marginRight: '5px',
							width: '30px',
							height: '30px',
							borderRadius: '3px',
							textAlign: 'center',
							lineHeight: '30px'
						}
					}, [h('span', {
						'class': 'iconfont',
						style: {
							fontSize: '22px',
							color: '#f56c6c'
						},
						domProps: {
							innerHTML: '&#xe625;'
						},
						on: {
							click: function click() {
								_this2.delModal = true;
							}
						},
						directives: [{
							name: 'tooltip',
							value: '删除'
						}]
					})])]);
				}
			}],
			trapData: [],

			recordColumns: [{
				title: 'SNMP版本',
				key: 'name'
			}, {
				title: 'IP地址/域名',
				key: 'age'
			}, {
				title: '端口',
				key: 'address'
			}, {
				title: '推送状态',
				key: 'address'
			}],
			recordData: [{
				name: 'name',
				age: 18,
				address: 'address',
				date: 'date'
			}, {
				name: 'name',
				age: 24,
				address: 'address',
				date: 'date'
			}],

			overtime: '',
			validity: '',

			logoutTimes: '',
			passwordExp: 0,
			passwordLength: {
				max: 32,
				min: 8
			},
			passwordComplexity: '',
			LoginFaiureTimes: '',
			loginIpLimit: {
				start: null,
				end: null
			},

			columnstt: [{
				title: '用户账号',
				key: 'name'
			}, {
				title: '',
				key: 'age'
			}, {
				title: 'Address',
				key: 'address'
			}],

			upgradeTitle: '软件升级包正在上传，请耐心等待',
			upgradeModal: false,
			disabledUpload: false,
			percentages: null,
			fixedCapacity: 250,
			sysseletValue: [],
			sysUpsrc: '',
			sysUrl: '',
			sysValue: 0,
			sysData: [],
			upLoadName: '',
			messageValue: '',
			modal2: false,
			errerModal: {
				title: '',
				titlecon: ''
			},
			copyBtn: null,
			Base: {
				LicenseType: '1',
				LicenseMode: ['0'],
				VssBackup: false,
				Dedump: false,
				Capacity: '0'
			},
			clientList: [{
				title: '操作系统类型',
				key: 'OS',
				align: 'center'
			}, {
				title: '授权数量',
				key: 'Value',
				align: 'center'
			}],
			storageList: [{
				title: '操作系统类型',
				key: 'OS',
				align: 'center'
			}, {
				title: '授权数量',
				key: 'Value',
				align: 'center'
			}],
			agentList: [{
				title: '代理类型',
				key: 'Agent',
				align: 'center'
			}, {
				title: '系统类型',
				key: 'OS',
				align: 'center'
			}, {
				title: '授权数量',
				key: 'Value',
				align: 'center'
			}],
			Client: [],
			Storage: [],
			Agent: [],
			syssetCode: '',
			valueA: '1',
			modelA: true,
			numNowList: [],

			ruleValidate: {
				password: [{
					required: true,
					message: '密码不能为空',
					pattern: /^[a-zA-Z]{1}([a-zA-Z0-9]|[@#$&]){5,19}$/,
					validator: validatePass,
					trigger: 'blur'
				}, {
					min: 6,
					message: '请输入8-20个以字母开头、可带数字、“@”、“#”、“$”、“_”、“&”的密码且必须有一个指定的特殊字符'
				}],
				rpassword: [{
					required: true,
					validator: validatePassCheck,
					trigger: 'blur'
				}, {
					min: 1
				}]
			},

			ruleEmail: {
				admin: [{
					required: true,
					message: '请输入正确格式的E-mail',
					pattern: /^[0-9A-Za-z][\.-_0-9A-Za-z]*@[0-9A-Za-z]+(?:\.[0-9A-Za-z]+)+$/,
					trigger: 'blur'
				}, {
					type: 'email',
					message: '请输入正确格式的E-mail'
				}],
				port: [{
					required: true,
					message: '请输入正确的SMTP服务器端口号',
					pattern: /^[0-9]*$/,
					trigger: 'blur'
				}, {
					min: 1
				}]
			},

			ruleMail: {
				mailPath: [{
					required: true,
					message: '请输入正确格式的E-mail地址',
					pattern: /^[0-9A-Za-z][\.-_0-9A-Za-z]*@[0-9A-Za-z]+(?:\.[0-9A-Za-z]+)+$/,
					trigger: 'blur'
				}, {
					min: 1
				}]
			},
			model: '1',
			formItem: {
				old: '',
				password: '',
				rpassword: ''
			},
			parameter: {
				dispatch: 1,
				recovery: 5,
				state: 5,
				task: {},
				log: 180,
				time: 15,
				siteId: '',
				log_mbytes: ''
			},
			mailbox: {
				admin: '',
				password: '',
				path: '',
				port: '',
				addressID: ''
			},
			Emailpath: {
				mailPath: ''
			},
			levelSelect: '',
			securityLevel: [],
			emails: [{
				title: 'ID',
				key: 'id',
				align: 'center'
			}, {
				title: 'Email',
				key: 'email',
				align: 'center'
			}],
			emailData: [],
			systemInfo: {
				cpu_mode: '',
				cpus: '',
				total_disk_size: '',
				total_mem: '',
				used_disk_size: '',
				used_mem: ''
			},
			safeid: '',
			arrtrap: []
		};
	},
	created: function created() {
		this.$store.dispatch('getPrivilege', this.$store.state.power.module.sysset);
		_util2.default.restfullCall('/rest-ful/v3.0/system/param', null, 'get', this.systemList);
		_util2.default.restfullCall('/rest-ful/v3.0/system/license', null, 'get', this.licenseList);
		_util2.default.restfullCall('/rest-ful/v3.0/system/registcode', null, 'get', this.registcodeValue);
		_util2.default.restfullCall('/rest-ful/v3.0/system/info', null, 'get', this.getSystemInfo);

		_util2.default.restfullCall('/rest-ful/v3.0/system/snmp/version', null, 'get', this.getVersion);
	},
	mounted: function mounted() {
		this.effectiveData = localStorage.getItem('getEffectiveDays');
		this.LicenseType = localStorage.getItem('getLicenseType');

		this.token = localStorage.getItem('tokenjm');
		this.headers = { Token: this.token };

		this.copyBtn = new this.clipboard(this.$refs.copy);
		this.getTableHeight();
	},

	computed: {
		navIcon: function navIcon() {
			return this.$store.state.index.siderNameIcon;
		},
		getPrivilege: function getPrivilege() {
			return this.$store.state.index.privilegeData;
		},
		getPower: function getPower() {
			return this.$store.state.power.module;
		}
	},
	watch: {
		getPrivilege: function getPrivilege(data) {
			this.numNowList = data;
		},

		'addSnmpMd.version': {
			handler: function handler(newVal, oldVal) {
				if (newVal !== 3) {
					this.editRuleValidate = {
						version: [{ required: true, message: '请选择版本', trigger: 'change', type: 'number' }],
						port: [{ required: true, message: '请输入端口号', trigger: 'change' }],
						address: [{ required: true, message: '请输入地址', trigger: 'change' }]
					};
				} else {
					this.editRuleValidate = {
						user: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
						password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
						version: [{ required: true, message: '请选择版本', trigger: 'change', type: 'number' }],
						port: [{ required: true, message: '请输入端口号', trigger: 'change' }],
						address: [{ required: true, message: '请输入地址', trigger: 'change' }]
					};
				}
			},

			deep: true,
			immediate: true
		}
	},
	methods: {
		closeBox: function closeBox() {
			this.AddModal = false;
			this.EditModal = false;
			this.delModal = false;
			this.modal2 = false;
			this.upgradeModal = false;
			this.addSnmpMd.user = '';
			this.addSnmpMd.password = '';
			this.addSnmpMd.version = '';
			this.addSnmpMd.port = '';
			this.addSnmpMd.address = '';
			this.addSnmpMd.id = '';
		},
		tishi: function tishi(currentRow, index) {
			return 'trbgshow_a';
		},
		sendEmail: function sendEmail() {
			var _this3 = this;

			(0, _index.sendEmail)().then(function (res) {
				if (res.code == 0) {
					_this3.$message.success('发送成功');
				} else {
					_this3.$message.error('发送失败' + res.message);
				}
			});
		},
		tapeset: function tapeset() {
			this.disabledo = !this.disabledo;
		},
		deviceset: function deviceset() {
			this.disabledt = !this.disabledt;
		},
		taskset: function taskset() {
			this.disabledth = !this.disabledth;
		},
		getTableHeight: function getTableHeight() {
			this.tableHeight = window.innerHeight - 290;
		},
		onCont: function onCont(val) {
			if (val == undefined) {
				this.parameter.log = 180;
			}
		},
		onDelSnmp: function onDelSnmp() {
			this.addSnmpMd.id;
			_util2.default.restfullCall('/rest-ful/v3.0/system/param/' + this.addSnmpMd.id, null, 'DELETE', this.snmpDel);
		},
		snmpDel: function snmpDel(val) {
			if (val.data.code == 0) {
				this.delModal = false;
				this.trapData = [];
				this.$Message.success(val.data.message);
				_util2.default.restfullCall('/rest-ful/v3.0/system/param', null, 'get', this.systemList);
			} else {
				this.$Message.warning(val.data.message);
			}
		},
		changesVersion: function changesVersion(val) {
			this.versionNum = val;
		},
		getVersion: function getVersion(val) {
			this.versionList = val.data.data;
		},
		addTrap: function addTrap() {
			this.AddModal = true;
		},
		onAddSnmp: function onAddSnmp() {
			if (this.addSnmpMd.version != 3) {
				if (this.addSnmpMd.address != '' && this.addSnmpMd.port != '' && this.addSnmpMd.version != '') {
					var snmpAddData = [];
					var jsonStr = {
						address: this.addSnmpMd.address,
						port: this.addSnmpMd.port,
						version: this.addSnmpMd.version,
						user: this.versionNum != 3 ? '' : this.addSnmpMd.user,
						password: this.versionNum != 3 ? '' : this.addSnmpMd.password
					};
					snmpAddData.push({ id: -1, type: 5, value: (0, _stringify2.default)(jsonStr) });

					_util2.default.restfullCall('/rest-ful/v3.0/system/param', snmpAddData, 'put', this.snmpAddlData);
				} else {
					this.$Message.warning('必填项不能为空');
				}
			} else {
				if (this.addSnmpMd.address != '' && this.addSnmpMd.port != '' && this.addSnmpMd.version != '' && this.addSnmpMd.user != '' && this.addSnmpMd.password != '') {
					var _snmpAddData = [];
					var _jsonStr = {
						address: this.addSnmpMd.address,
						port: this.addSnmpMd.port,
						version: this.addSnmpMd.version,
						user: this.versionNum != 3 ? '' : this.addSnmpMd.user,
						password: this.versionNum != 3 ? '' : this.addSnmpMd.password
					};
					_snmpAddData.push({ id: -1, type: 5, value: (0, _stringify2.default)(_jsonStr) });

					_util2.default.restfullCall('/rest-ful/v3.0/system/param', _snmpAddData, 'put', this.snmpAddlData);
				} else {
					this.$Message.warning('必填项不能为空');
				}
			}
		},
		snmpAddlData: function snmpAddlData(obj) {
			if (obj.data.code == 0) {
				this.AddModal = false;

				this.$Message.success(obj.data.message);
				this.trapData = [];
				_util2.default.restfullCall('/rest-ful/v3.0/system/param', null, 'get', this.systemList);
				this.addSnmpMd.address = '';
				this.addSnmpMd.port = '';
				this.addSnmpMd.version = '';
				this.addSnmpMd.user = '';
				this.addSnmpMd.password = '';
			} else {
				this.$Message.warning(obj.data.message);
			}
		},
		editTrap: function editTrap() {
			this.EditModal = true;
		},
		getRowData: function getRowData(obj) {
			this.addSnmpMd.address = obj.address, this.addSnmpMd.port = obj.port, this.addSnmpMd.version = obj.version, this.addSnmpMd.user = obj.user, this.addSnmpMd.password = obj.password;
			this.addSnmpMd.id = obj.id;
		},
		onEditSnmp: function onEditSnmp() {
			if (this.addSnmpMd.version != 3) {
				if (this.addSnmpMd.address != '' && this.addSnmpMd.port != '' && this.addSnmpMd.version != '') {
					var snmpAddData = [];
					var jsonStr = {
						address: this.addSnmpMd.address,
						port: this.addSnmpMd.port,
						version: this.addSnmpMd.version,
						user: this.versionNum != 3 ? '' : this.addSnmpMd.user,
						password: this.versionNum != 3 ? '' : this.addSnmpMd.user
					};
					snmpAddData.push({ id: this.addSnmpMd.id, type: 5, value: (0, _stringify2.default)(jsonStr) });

					_util2.default.restfullCall('/rest-ful/v3.0/system/param', snmpAddData, 'put', this.snmpEditData);
				} else {
					this.$Message.warning('必填项不能为空');
				}
			} else {
				if (this.addSnmpMd.address != '' && this.addSnmpMd.port != '' && this.addSnmpMd.version != '' && this.addSnmpMd.user != '' && this.addSnmpMd.password != '') {
					var _snmpAddData2 = [];
					var _jsonStr2 = {
						address: this.addSnmpMd.address,
						port: this.addSnmpMd.port,
						version: this.addSnmpMd.version,
						user: this.versionNum != 3 ? '' : this.addSnmpMd.user,
						password: this.versionNum != 3 ? '' : this.addSnmpMd.user
					};
					_snmpAddData2.push({ id: this.addSnmpMd.id, type: 5, value: (0, _stringify2.default)(_jsonStr2) });

					_util2.default.restfullCall('/rest-ful/v3.0/system/param', _snmpAddData2, 'put', this.snmpEditData);
				} else {
					this.$Message.warning('必填项不能为空');
				}
			}
		},
		snmpEditData: function snmpEditData(obj) {
			if (obj.data.code == 0) {
				this.EditModal = false;

				this.$Message.success(obj.data.message);
				this.trapData = [];
				_util2.default.restfullCall('/rest-ful/v3.0/system/param', null, 'get', this.systemList);
				this.addSnmpMd.address = '';
				this.addSnmpMd.port = '';
				this.addSnmpMd.version = '';
				this.addSnmpMd.user = '';
				this.addSnmpMd.password = '';
			} else {
				this.$Message.warning(obj.data.message);
			}
		},
		testTrap: function testTrap() {},
		stratTrap: function stratTrap() {},
		closeTrap: function closeTrap() {},
		setTrap: function setTrap() {},
		downTrap: function downTrap() {
			var downUrl = '/mibs/THEVRTS.mib';
			fetch(downUrl).then(function (response) {
				return response.blob();
			}).then(function (blob) {
				var link = document.createElement('a');
				link.href = URL.createObjectURL(blob);
				link.download = 'THEVRTS.mib';
				link.click();

				URL.revokeObjectURL(link.href);
			}).catch(function (error) {
				return console.error('下载失败:', error);
			});
		},
		delTrap: function delTrap() {},
		handleClick: function handleClick(val) {},
		getSystemInfo: function getSystemInfo(obj) {
			this.systemInfo = obj.data;
		},
		sysImport: function sysImport() {
			if (!this.sysValue) {
				this.$Message.error('请选择需要要导入的配置项！');
			}
			return false;
		},
		syscancle: function syscancle() {
			this.sysUpsrc = '';
			this.sysUrl = '';
			this.sysData = [];
			this.sysValue = 0;
		},
		sysExport: function sysExport() {
			if (!this.sysValue) {
				this.$Message.error('请选择需要要导出的配置项！');
			}
		},
		sysselet: function sysselet(seletvalue) {
			var _this4 = this;

			this.sysseletValue = seletvalue;
			var value = 0;
			if (seletvalue.length > 0) {
				this.sysData.map(function (item) {
					value = Number(item) + value;
					_this4.sysValue = value;
					_this4.sysUrl = 'rest-ful/v3.0/config/export?type=' + value;
					_this4.sysUpsrc = 'rest-ful/v3.0/config/import?type=' + value;
				});
			} else {
				this.sysValue = 0;
				this.sysUpsrc = '';
				this.sysUrl = '';
			}
		},
		errerok: function errerok() {
			this.modal2 = false;
			this.messageValue = '';
			this.errerModal = {
				title: '',
				titlecon: ''
			};
		},
		upgradeSoftware: function upgradeSoftware() {
			var upgradeForm = new FormData();
			upgradeForm.file = test.tar.gz;

			_util2.default.restfullCall('/rest-ful/v3.0/system/upgradepackage', {
				file: test.tar.gz
			}, 'POST', this.upgradeData);
		},
		upgradeData: function upgradeData(data) {},
		importError: function importError(file) {
			this.$Message.error(file.name + '格式错误');
		},
		handleFormatError: function handleFormatError(file) {
			this.$Message.error(file.name + '格式错误');
		},
		upLoad: function upLoad(a, b, c) {
			this.$Message.error(b.name + '上传失败');
		},
		importOK: function importOK(response, file, fileList) {
			if (response.code == 0) {
				this.$Message.success('上传成功');
			} else {
				this.$Message.error(file.name + '上传失败');
			}
		},
		upLoads: function upLoads(a, b, c) {
			if (a.code == 0) {
				this.upLoadName = b.name;
				this.$Message.success('上传成功');
				_util2.default.restfullCall('/rest-ful/v3.0/system/license', null, 'get', this.licenseList);
			} else {
				this.$Message.error(b.name + '上传失败');
			}
		},
		upgradeSucces: function upgradeSucces(a, b, c) {
			if (a.code == 0) {
				this.upLoadName = b.name;
				this.$Message.success('上传成功');
			} else {
				this.$Message.error(b.name + '上传失败');
			}
		},
		upgradeError: function upgradeError(a, b, c) {
			this.$Message.error(b.name + '上传失败');
		},
		upgradeFormEro: function upgradeFormEro(file) {
			this.$Message.error(file.name + '格式错误');
		},
		handleProgress: function handleProgress(event, file, fileList) {
			var _this5 = this;

			this.upgradeModal = true;
			event.target.onprogress = function (event) {
				var uploadPercent = (event.loaded / event.total * 100).toFixed(1);
				if (uploadPercent > 0 && uploadPercent !== 100) {
					_this5.disabledUpload = true;
				} else if (uploadPercent == 100) ;
				{
					_this5.upgradeTitle = '升级软件已完成上传';
					_this5.disabledUpload = false;
				}
				_this5.percentages = uploadPercent;
			};
		},
		fanhuiok: function fanhuiok() {
			this.disabledUpload = false;
			this.upgradeModal = false;
		},
		copyValues: function copyValues(value) {
			var _this = this;
			var clipboard = _this.copyBtn;
			var oInput = document.createElement('input');
			oInput.value = value;
			document.body.appendChild(oInput);
			oInput.select();
			document.execCommand('Copy');
			this.$Message.success('复制成功');
		},
		registcodeValue: function registcodeValue(obj) {
			if (obj.data.code == 0) {
				this.syssetCode = obj.data.data.toString();
			}
		},
		liceseShow2: function liceseShow2(value) {
			var data = (0, _values2.default)(value);
			data = data.toString();
			switch (data) {
				case ['1'].toString():
					return true;
					break;
				case ['0', '1'].toString():
					return true;

				default:
					return false;
			}
		},
		liceseShow: function liceseShow(value) {
			var data = (0, _values2.default)(value);
			data = data.toString();
			switch (data) {
				case ['0'].toString():
					return true;
					break;
				case ['0', '1'].toString():
					return true;
				default:
					return false;
			}
		},
		licenseList: function licenseList(obj) {
			if (obj.data.code == 0) {
				this.Base.LicenseType = obj.data.data.Base.LicenseType.toString();
				switch (obj.data.data.Base.LicenseMode) {
					case 0:
						this.Base.LicenseMode = ['0'];
						break;
					case 1:
						this.Base.LicenseMode = ['1'];
						break;
					case 2:
						this.Base.LicenseMode = ['0', '1'];
						break;
				}
				this.Base.VssBackup = obj.data.data.Base.VssBackup == 0 ? false : true;
				this.Base.Dedump = obj.data.data.Base.Dedump == 0 ? false : true;
				this.Base.Capacity = obj.data.data.Base.Capacity.toString();
				this.Client = obj.data.data.Client;
				this.Storage = obj.data.data.Storage;
				this.Agent = obj.data.data.Agent;
				this.EffectiveDays = obj.data.data.EffectiveDays;
			} else {
				this.$Message.warning('license连接失败');
			}
		},
		nowShow: function nowShow(num) {
			if (this.numNowList.indexOf(num) != -1) {
				return true;
			} else {
				return false;
			}
		},

		openSafe: function openSafe(open) {
			this.securityLevel = [{
				id: '3',
				level: '高'
			}, {
				id: '2',
				level: '中'
			}, {
				id: '1',
				level: '低'
			}];
		},

		onSafe: function onSafe(id) {
			_util2.default.restfullCall('/rest-ful/v3.0/system/param', [{
				type: 1,
				value: id
			}], 'put', this.onSafeData);
		},

		onSafeData: function onSafeData(obj) {
			if (obj.data.code !== 0) {
				this.$Message.error(obj.data.message);
			}
		},

		col1: function col1(ca) {
			var _this6 = this;

			if (ca == '邮件通知') {
				_util2.default.restfullCall('/rest-ful/v3.0/system/emails', null, 'get', this.emailsData);
			}
			if (ca == 'SNP配置') {
				setTimeout(function () {
					_util2.default.restfullCall('/rest-ful/v3.0/system/param', null, 'get', _this6.systemList);
				});
			}
			return false;
		},

		systemList: function systemList(obj) {
			var _this7 = this;

			for (var i = 0; i < obj.data.data.length; i++) {
				if (obj.data.data[i].type == 2) {

					this.safeid = obj.data.data[i].type == 2 ? obj.data.data[i].id : 0;

					var safe = JSON.parse(obj.data.data[i].value);

					this.parameter.time = safe.logout_times;
					this.logoutTimes = safe.logout_times;
					this.passwordExp = safe.password_exp;
					this.passwordLength.min = safe.password_length.min;
					this.passwordLength.max = safe.password_length.max;
					this.passwordComplexity = safe.password_complexity;
					this.LoginFaiureTimes = safe.LoginFaiureTimes;
					this.loginIpLimit.start = safe.login_ip_limit.start;
					this.loginIpLimit.end = safe.login_ip_limit.end;
				} else if (obj.data.data[i].type == 3) {
					var emailList = JSON.parse(obj.data.data[i].value);

					this.mailbox.admin = emailList.send_accout;
					this.mailbox.password = emailList.password;
					this.mailbox.port = emailList.smtp_port.toString();
					this.mailbox.path = emailList.smtp_server;
					this.mailbox.version = emailList.smtp_server;
				} else if (obj.data.data[i].type == 4) {} else if (obj.data.data[i].type == 5) {} else if (obj.data.data[i].type == 10) {
					this.parameter.log = obj.data.data[i].value - 0;
				} else if (obj.data.data[i].type == 6) {} else if (obj.data.data[i].type == 7) {} else if (obj.data.data[i].type == 8) {} else if (obj.data.data[i].type == 9) {} else if (obj.data.data[i].type == 11) {
					this.parameter.time = Number(obj.data.data[i].value);
				} else if (obj.data.data[i].type == 1) {
					(function () {
						var setcs = JSON.parse(obj.data.data[i].value);

						_this7.$nextTick(function () {
							_this7.parameter.dispatch = setcs.schedule_interval;
							_this7.parameter.recovery = setcs.vol_recycle_interval;
							_this7.parameter.log = setcs.log_save_days;
							_this7.parameter.log_mbytes = setcs.log_mbytes;
						});
					})();
				}
			}

			var mergedValuesWithId = obj.data.data.filter(function (item) {
				return item.type === 5;
			}).map(function (item) {
				return (0, _extends3.default)({
					id: item.id
				}, JSON.parse(item.value));
			});
			this.trapData = mergedValuesWithId;
			console.log('-trapData--------------', mergedValuesWithId);
		},

		confirm: function confirm() {
			var word = /^[a-zA-Z]{1}([a-zA-Z0-9]|[@#$&]){5,19}$/;
			var user = JSON.parse(localStorage.getItem('userInfo'));
			if (this.formItem.password == this.formItem.rpassword && word.test(this.formItem.password)) {
				_util2.default.restfullCall('/rest-ful/v3.0/usr/password/' + user.uid, {
					oldpassword: this.formItem.old,
					newpassword: this.formItem.password
				}, 'put', this.passwordData);
			} else {
				this.$Message.error('输入密码格式错误！修改失败');
			}
		},

		Reset: function Reset() {
			this.formItem.old = '';
			this.formItem.password = '';
			this.formItem.rpassword = '';
		},

		passwordData: function passwordData(obj) {
			if (obj.data.code == 0) {
				this.$Message.success('密码修改成功！');
				this.formItem.old = '';
				this.formItem.password = '';
				this.formItem.rpassword = '';
			} else {
				this.errerModal = {
					title: '系统安全设置',
					titlecon: '修改密码失败'
				};
				this.messageValue = obj.data.message;
				this.modal2 = true;
			}
		},

		emailsData: function emailsData(obj) {
			var array = new Array();
			for (var i = 0; i < obj.data.data.length; i++) {
				array.push({
					id: obj.data.data[i].id,
					email: obj.data.data[i].email
				});
			}
			this.emailData = array;
		},

		onSystem: function onSystem() {
			var putData = [];
			var strval = {
				schedule_interval: this.parameter.dispatch,
				vol_recycle_interval: this.parameter.recovery,
				log_save_days: this.parameter.log == null || this.parameter.log == '' || this.parameter.log == undefined ? 180 : this.parameter.log,
				log_mbytes: Number(this.parameter.log_mbytes) <= 0 ? 0 : Number(this.parameter.log_mbytes)
			};

			putData.push({ id: 1, type: 1, value: (0, _stringify2.default)(strval) });
			_util2.default.restfullCall('/rest-ful/v3.0/system/param', putData, 'put', this.systemData);
		},
		isValidPositiveInteger: function isValidPositiveInteger(value) {
			var regex = /^[1-9]\d*$/;
			return regex.test(value);
		},

		onSafeCl: function onSafeCl() {
			var putData = [];
			var strval = {
				logout_times: Number(this.logoutTimes),
				password_exp: Number(this.passwordExp),
				enable_password_exp: this.Checkpasswordbox,
				password_length: {
					min: Number(this.passwordLength.min),
					max: Number(this.passwordLength.max)
				},
				password_complexity: Number(this.passwordComplexity),
				LoginFaiureTimes: Number(this.LoginFaiureTimes),
				login_ip_limit: {
					start: this.loginIpLimit.start,
					end: this.loginIpLimit.end
				}
			};

			putData.push({ id: this.safeid, type: 2, value: (0, _stringify2.default)(strval) });

			if (this.logoutTimes == null) {
				this.$Message.warning('超时下时间不能为空');
			} else if (this.isValidPositiveInteger(this.logoutTimes) == false) {
				this.$Message.warning('超时下时间为正整数');
			} else if (this.passwordLength.min == null || this.passwordLength.max == null) {
				this.$Message.warning('密码的长度不能为空');
			} else if (this.isValidPositiveInteger(this.passwordLength.min) == false && this.isValidPositiveInteger(this.passwordLength.max) == false) {
				this.$Message.warning('密码的长度为正整数');
			} else if (this.passwordExp == null) {
				this.$Message.warning('密码的有效期不能为空');
			} else if (this.passwordExp > 0 && this.isValidPositiveInteger(this.passwordExp) == false) {
				this.$Message.warning('密码的有效期为正整数');
			} else if (this.LoginFaiureTimes == null) {
				this.$Message.warning('登录锁定不能为空');
			} else if (this.isValidPositiveInteger(this.LoginFaiureTimes) == false) {
				this.$Message.warning('登录锁定为正整数');
			} else if (!this.$isNull(this.loginIpLimit.start)) {
				if (this.$isValidIPv4(this.loginIpLimit.start) == false) {
					this.$Message.warning('开始登录IP格式不正确');
				}
			} else if (!this.$isNull(this.loginIpLimit.end)) {
				if (this.$isValidIPv4(this.loginIpLimit.end) == false) {
					this.$Message.warning('结束登录IP格式不正确');
				}
			} else {
				_util2.default.restfullCall('/rest-ful/v3.0/system/param', putData, 'put', this.safeData);
			}
		},

		systemData: function systemData(obj) {
			if (obj.data.code !== 0) {

				this.errerModal = {
					title: '系统参数设置',
					titlecon: '保存失败'
				};
				this.messageValue = obj.data.message;
				this.modal2 = true;
			} else {
				this.parameter.log = this.parameter.log == 180 ? 180 : this.parameter.log;
				_util2.default.restfullCall('/rest-ful/v3.0/system/param', null, 'get', this.systemList);
				this.$Message.success('系统参数设置成功');
			}
		},
		safeData: function safeData(obj) {
			if (obj.data.code !== 0) {
				this.errerModal = {
					title: '保存系统参数设置',
					titlecon: '保存失败'
				};
				this.messageValue = obj.data.message;
				this.modal2 = true;
			} else {
				this.$Message.success('保存系统参数设置成功');
			}
		},

		onMailbox: function onMailbox() {
			var email = /^[0-9A-Za-z][\.-_0-9A-Za-z]*@[0-9A-Za-z]+(?:\.[0-9A-Za-z]+)+$/;
			var number = /^[0-9]*$/;
			var mailData = [];
			var mailStr = {
				send_accout: this.mailbox.admin,
				password: this.mailbox.password,
				smtp_server: this.mailbox.path,
				smtp_port: Number(this.mailbox.port)
			};
			mailData.push({ id: 3, type: 3, value: (0, _stringify2.default)(mailStr) });
			if (email.test(this.mailbox.admin) && number.test(this.mailbox.port)) {
				_util2.default.restfullCall('/rest-ful/v3.0/system/param', mailData, 'put', this.setEmailData);
			} else {
				this.$Message.error('输入的格式有误！');
			}
		},

		setEmailData: function setEmailData(obj) {
			if (obj.data.code !== 0) {
				this.errerModal = {
					title: '邮件设置',
					titlecon: '保存失败'
				};
				this.messageValue = callback.data.message;
				this.modal2 = true;
			} else {
				this.$Message.success(obj.data.message);
			}
		},
		onReset: function onReset() {
			this.mailbox.admin = '';
			this.mailbox.password = '';
			this.mailbox.port = '465';
			this.mailbox.path = '';
			var mailData = [];
			var mailStr = {
				send_accout: this.mailbox.admin,
				password: this.mailbox.password,
				smtp_server: this.mailbox.path,
				smtp_port: 465
			};
			mailData.push({ id: 3, type: 3, value: (0, _stringify2.default)(mailStr) });
			_util2.default.restfullCall('/rest-ful/v3.0/system/param', mailData, 'put', this.setEmailReset);
		},

		setEmailReset: function setEmailReset(obj) {
			if (obj.data.code == 0) {
				this.$Message.success(obj.data.message);
			} else {
				this.$Message.success('配置失败');
			}
		},

		addEmail: function addEmail() {
			var pathEma = /^[0-9A-Za-z][\.-_0-9A-Za-z]*@[0-9A-Za-z]+(?:\.[0-9A-Za-z]+)+$/;

			if (pathEma.test(this.Emailpath.mailPath)) {
				_util2.default.restfullCall('/rest-ful/v3.0/system/email', {
					email: this.Emailpath.mailPath
				}, 'POST', this.accountData);
			} else {
				this.$Message.error('添加的E-mail格式错误！');
			}
		},

		accountData: function accountData(obj) {
			if (obj.data.code !== 0) {
				this.errerModal = {
					title: '邮件设置',
					titlecon: '添加失败'
				};
				this.messageValue = obj.data.message;
				this.modal2 = true;
			} else {
				_util2.default.restfullCall('/rest-ful/v3.0/system/emails', null, 'get', this.emailsData);
			}
		},

		rowClick: function rowClick(row) {
			this.mailbox.addressID = row.id;
			this.Emailpath.mailPath = row.email;
		},

		preservation: function preservation() {
			var pathEma = /^[0-9A-Za-z][\.-_0-9A-Za-z]*@[0-9A-Za-z]+(?:\.[0-9A-Za-z]+)+$/;

			if (!this.Emailpath.mailPath) {
				this.errerModal = {
					title: '邮件设置',
					titlecon: '保存失败'
				};
				this.messageValue = '请选择Emall';
				this.modal2 = true;
				return false;
			}
			if (pathEma.test(this.Emailpath.mailPath)) {
				_util2.default.restfullCall('/rest-ful/v3.0/system/email', {
					id: Number(this.mailbox.addressID),
					email: this.Emailpath.mailPath
				}, 'PUT', this.calbakData);
			} else {
				this.errerModal = {
					title: '邮件设置',
					titlecon: '保存失败'
				};
				this.messageValue = '要保存的E-mail格式错误！';
				this.modal2 = true;
				return false;
			}
		},

		calbakData: function calbakData(obj) {
			if (obj.data.code !== 0) {
				this.errerModal = {
					title: '邮件设置',
					titlecon: '保存失败'
				};
				this.messageValue = obj.data.message;
				this.modal2 = true;
			} else {
				this.$message.success(obj.data.message);
				_util2.default.restfullCall('/rest-ful/v3.0/system/emails', null, 'get', this.emailsData);
			}
		},

		deleteEmail: function deleteEmail() {
			if (!!this.mailbox.addressID, this.mailbox.addressID) {
				_util2.default.restfullCall('/rest-ful/v3.0/system/email/' + this.mailbox.addressID, null, 'delete', this.emailDelete);
			} else {
				this.errerModal = {
					title: '邮件设置',
					titlecon: '删除失败'
				};
				this.messageValue = '请选择Emall';
				this.modal2 = true;
			}
		},

		emailDelete: function emailDelete(obj) {
			if (obj.data.code !== 0) {
				this.errerModal = {
					title: '邮件设置',
					titlecon: '删除失败'
				};
				this.messageValue = callback.data.message;
				this.modal2 = true;
			} else {
				_util2.default.restfullCall('/rest-ful/v3.0/system/emails', null, 'get', this.emailsData);
			}
		}
	}
};

/***/ }),

/***/ 2805:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _util = __webpack_require__(16);

var _util2 = _interopRequireDefault(_util);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
	data: function data() {
		return {
			modal1: true,
			install: '',
			installSelect: [],
			system: '',
			systemSelect: [],
			number: '',
			numberSelect: [],
			downList: [{ title: '程序名称', key: 'name' }, { title: '版本', key: 'version', width: 100 }, { title: '描述', key: 'desc' }, {
				title: '操作',
				width: 80,
				render: function render(h, params) {
					return h('div', [h('a', {
						attrs: {
							href: params.row.url
						},
						style: {
							color: '#fb6902'
						},
						on: {
							click: function click() {}
						}
					}, '下载')]);
				}
			}],
			downData: []
		};
	},
	created: function created() {
		_util2.default.restfullCall('/rest-ful/v3.0/installtype', null, 'get', this.typeData);

		_util2.default.restfullCall('/rest-ful/v3.0/ostype', null, 'get', this.ostypeData);

		_util2.default.restfullCall('/rest-ful/v3.0/osbits', null, 'get', this.osbitsData);
	},

	computed: {
		address: function address() {
			var install = this.install,
			    system = this.system,
			    number = this.number;

			return {
				install: install,
				system: system,
				number: number
			};
		}
	},
	watch: {
		address: {
			handler: function handler(newval, oldval) {
				if (newval.install && newval.system && newval.number) _util2.default.restfullCall('/rest-ful/v3.0/package?insttype=' + newval.install + '&ostype=' + newval.system + '&bits=' + newval.number, null, 'get', this.packageData);
			},
			deep: true
		}
	},
	methods: {
		tishi: function tishi(currentRow, index) {
			return 'trbgshow_a';
		},

		downModel: function downModel() {
			this.modal1 = true;
		},

		typeData: function typeData(obj) {
			var array = new Array();
			for (var i = 0; i < obj.data.data.length; i++) {
				array.push({
					type: obj.data.data[i].type,
					name: obj.data.data[i].name
				});
			}
			this.installSelect = array;
			this.install = array[0].type;
		},

		onInstall: function onInstall(type) {
			this.install = type;
		},

		ostypeData: function ostypeData(obj) {
			var array = new Array();
			for (var i = 0; i < obj.data.data.length; i++) {
				array.push({
					type: obj.data.data[i].type,
					name: obj.data.data[i].name
				});
			}
			this.systemSelect = array;
			this.system = array[0].type;
		},

		onSystem: function onSystem(type) {
			this.system = type;
		},

		osbitsData: function osbitsData(obj) {
			var array = new Array();
			for (var i = 0; i < obj.data.data.length; i++) {
				array.push({
					type: obj.data.data[i].type,
					name: obj.data.data[i].name
				});
			}
			this.numberSelect = array;
			this.number = array[0].type;
		},

		onNumber: function onNumber(type) {
			this.number = type;
		},

		packageData: function packageData(obj) {
			var array = new Array();
			for (var i = 0; i < obj.data.data.length; i++) {
				array.push({
					desc: obj.data.data[i].desc,
					name: obj.data.data[i].name,
					url: obj.data.data[i].url,
					version: obj.data.data[i].version
				});
			}
			this.downData = array;
		},
		ok: function ok() {
			this.$Message.info('点击了确定');
		},
		cancel: function cancel() {}
	}
};

/***/ }),

/***/ 2806:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _stringify = __webpack_require__(21);

var _stringify2 = _interopRequireDefault(_stringify);

var _api = __webpack_require__(3688);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
	data: function data() {
		return {
			form: {
				code: ''
			},
			tableLoading: false,
			noImgUrl: __webpack_require__(317),
			tableData: [],
			columns: [{
				title: '错误码',
				key: 'Code'
			}, {
				title: '错误描述',
				key: 'Message'
			}],
			tableHeight: window.innerHeight - 300
		};
	},
	mounted: function mounted() {
		this.getErrorCodeInit();
	},
	created: function created() {},


	watch: {},

	computed: {},

	methods: {
		handleInput: function handleInput(value) {
			var validNumber = value.replace(/[^0-9]/g, '');
			if (validNumber !== '' && parseInt(validNumber, 10) > 0) {
				this.form.code = validNumber;
			} else {
				this.form.code = '';
			}
		},
		getErrorCodeInit: function getErrorCodeInit() {
			var _this = this;

			(0, _api.systemErrors)().then(function (res) {
				_this.tableData = res.data;
				_this.copyData = JSON.parse((0, _stringify2.default)(_this.tableData));
			});
		},
		tishi: function tishi() {
			return 'trbgshow_a';
		},
		onSearch: function onSearch() {
			var _this2 = this;

			if (this.form.code == '') {
				this.tableData = [];
				return;
			} else {
				this.tableData = this.copyData.filter(function (item) {
					return item.Code == _this2.form.code;
				});
			}
		},
		onClear: function onClear() {
			this.form.code = '';
			this.tableData = this.copyData;
		}
	}
};

/***/ }),

/***/ 3679:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3680);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("5a06d445", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-14996c25\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./sysset.vue", function() {
     var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-14996c25\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./sysset.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3680:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.marBox[data-v-14996c25]{margin:20px 15px;overflow-y:auto\n}\n.mode-content[data-v-14996c25]{text-align:center;margin-bottom:20px;margin-bottom:1.25rem;font-size:16px;font-size:1rem;color:#f24e4e\n}\n.operateBox[data-v-14996c25]{padding:5px 0 15px;border-bottom:1px solid #e1e2e8;margin-bottom:10px\n}\n.alarbox ul[data-v-14996c25]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-ms-flex-wrap:wrap;flex-wrap:wrap\n}\n.alarbox ul li[data-v-14996c25]{width:33%;display:block;margin-bottom:15px\n}\n.rateTitle[data-v-14996c25]{border-bottom:1px solid #ccc;padding:0 0 8px 5px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:start;-ms-flex-pack:start;justify-content:flex-start;-webkit-box-align:center;-ms-flex-align:center;align-items:center\n}\n.rateTitle span[data-v-14996c25]{display:block;width:4px;height:15px;background:#ff6902;margin-right:10px\n}\n.frame-con[data-v-14996c25]{margin-top:10px\n}", ""]);

// exports


/***/ }),

/***/ 3681:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3682);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("086b2839", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-14996c25\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/less-loader/dist/cjs.js!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=1!./sysset.vue", function() {
     var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-14996c25\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/less-loader/dist/cjs.js!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=1!./sysset.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3682:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.opt-mail-bnt[data-v-14996c25]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center\n}\n[data-v-14996c25] .ivu-btn-error{color:#fff\n}\n.safe p[data-v-14996c25]{line-height:2rem\n}\n.license .ivu-upload[data-v-14996c25]{display:inline-block\n}\n.license .mr10[data-v-14996c25]{margin-left:20px\n}\n.license .ivu-table-body[data-v-14996c25]{height:420px;overflow:auto\n}\n.ivu-table th[data-v-14996c25],.license .ivu-table td[data-v-14996c25]{height:30px\n}\n.license .ivu-tabs[data-v-14996c25]{height:550px\n}\n.license .blanks[data-v-14996c25]{width:100%;padding:5px 0\n}\n.license .frame[data-v-14996c25]{padding:20px 10px 10px;border:1px solid #ededed;border-radius:5px;margin-bottom:20px;position:relative\n}\n.license .frame .titles[data-v-14996c25]{position:absolute;left:30px;top:-11px;background-color:#fff;padding:0 6px;font-size:0.875rem\n}\n.sysexport .frame .br[data-v-14996c25]{display:inline-block;width:100%;height:10px;margin-bottom:20px\n}\n.sysexport .ivu-upload-list[data-v-14996c25]{width:300px\n}\n.sysexport .button[data-v-14996c25]{width:80px;display:inline-block;text-align:center\n}\n.sysexport .button a[data-v-14996c25]{color:#fff\n}\n.sysexport .frame .buttonA[data-v-14996c25]{height:32px\n}\n.sysexport .frame[data-v-14996c25]{padding:20px 10px 10px;border:1px solid #ededed;border-radius:5px;margin-bottom:20px;position:relative\n}\n.sysexport .frame .titles[data-v-14996c25]{position:absolute;left:30px;top:-11px;background-color:#fff;padding:0 6px;font-size:0.875rem\n}\n.xt[data-v-14996c25]{font-weight:600;margin-left:10px\n}\n.security-select[data-v-14996c25]{margin:0 0 20px\n}\n.setPassword[data-v-14996c25]{padding-top:30px;padding-bottom:30px;position:relative;border:1px solid #e8eaec;width:100%;margin-bottom:10px\n}\n._title[data-v-14996c25]{padding:0 5px;font-weight:600;position:absolute;background:#fff;top:-10px;left:10px\n}\n.Email[data-v-14996c25]{padding-top:30px;position:relative;border:2px solid #ccc;height:350px;margin-bottom:10px\n}\n._titles[data-v-14996c25]{padding:0 5px;font-weight:600;position:absolute;background:#fff;top:-42px;left:10px\n}\n._button[data-v-14996c25],._tabs[data-v-14996c25]{margin-top:10px\n}\n._button[data-v-14996c25]{color:#fff\n}\n.email-but[data-v-14996c25]{margin-left:40%\n}\n.title-size[data-v-14996c25]{font-size:0.8125rem;font-weight:500;margin-left:10px\n}\n.lable-color[data-v-14996c25]{font-size:0.75rem;color:#6f6f6f\n}\n.line[data-v-14996c25]{width:40%;height:15px;border-bottom:1px solid #e5e5e5;margin-bottom:10px\n}\n.ml10[data-v-14996c25]{margin-left:10px\n}\n.title-size-pas[data-v-14996c25]{margin-left:-8px\n}", ""]);

// exports


/***/ }),

/***/ 3683:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_downloud_vue__ = __webpack_require__(2805);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_downloud_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_downloud_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_downloud_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_downloud_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_47ef9ae5_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_downloud_vue__ = __webpack_require__(3684);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_47ef9ae5_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_downloud_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_47ef9ae5_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_downloud_vue__);
var disposed = false
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = null
/* scopeId */
var __vue_scopeId__ = null
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_downloud_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_47ef9ae5_hasScoped_false_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_downloud_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/navigation/downloud.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-47ef9ae5", Component.options)
  } else {
    hotAPI.reload("data-v-47ef9ae5", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ }),

/***/ 3684:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", [_c("div", {
    staticStyle: { height: "700px" },
    attrs: { width: "1400" },
    model: {
      value: _vm.modal1,
      callback: function callback($$v) {
        _vm.modal1 = $$v;
      },
      expression: "modal1"
    }
  }, [_c("Form", { attrs: { "label-width": 110, "label-position": "left" } }, [_c("FormItem", { attrs: { label: "安装类型" } }, [_c("Select", {
    on: { "on-change": _vm.onInstall },
    model: {
      value: _vm.install,
      callback: function callback($$v) {
        _vm.install = $$v;
      },
      expression: "install"
    }
  }, _vm._l(_vm.installSelect, function (item) {
    return _c("Option", { key: item.type, attrs: { value: item.type } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "系统类型" } }, [_c("Select", {
    on: { "on-change": _vm.onSystem },
    model: {
      value: _vm.system,
      callback: function callback($$v) {
        _vm.system = $$v;
      },
      expression: "system"
    }
  }, _vm._l(_vm.systemSelect, function (item) {
    return _c("Option", { key: item.type, attrs: { value: item.type } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "系统架构" } }, [_c("Select", {
    on: { "on-change": _vm.onNumber },
    model: {
      value: _vm.number,
      callback: function callback($$v) {
        _vm.number = $$v;
      },
      expression: "number"
    }
  }, _vm._l(_vm.numberSelect, function (item) {
    return _c("Option", { key: item.type, attrs: { value: item.type } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1)], 1), _vm._v(" "), _c("Table", {
    attrs: {
      columns: _vm.downList,
      "row-class-name": _vm.tishi,
      data: _vm.downData,
      height: "520",
      id: "btnn"
    }
  }), _vm._v(" "), _c("div", {
    staticStyle: { display: "flex", "justify-content": "flex-end" }
  })], 1)]);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-47ef9ae5", esExports);
  }
}

/***/ }),

/***/ 3685:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_ErrorCode_vue__ = __webpack_require__(2806);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_ErrorCode_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_ErrorCode_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_ErrorCode_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_ErrorCode_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_6691cc6c_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_ErrorCode_vue__ = __webpack_require__(3689);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_6691cc6c_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_ErrorCode_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_6691cc6c_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_ErrorCode_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3686)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = "data-v-6691cc6c"
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_ErrorCode_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_6691cc6c_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_ErrorCode_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/sysset/components/ErrorCode.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-6691cc6c", Component.options)
  } else {
    hotAPI.reload("data-v-6691cc6c", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ }),

/***/ 3686:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3687);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("3bbb5fa4", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-6691cc6c\",\"scoped\":true,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./ErrorCode.vue", function() {
     var newContent = require("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-6691cc6c\",\"scoped\":true,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./ErrorCode.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3687:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n[data-v-6691cc6c] .trbgshow_a{cursor:default!important\n}\n.policy-name[data-v-6691cc6c]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;margin-left:15px;padding-bottom:10px;border-bottom:1px solid #ddd;margin-bottom:10px\n}\n.policy-name .lable[data-v-6691cc6c]{font-size:0.8125rem;padding-right:8px\n}\n.button-box[data-v-6691cc6c]{margin-left:50px\n}\n.button-box[data-v-6691cc6c],.button-box .export-but-wrap[data-v-6691cc6c]{display:-webkit-box;display:-ms-flexbox;display:flex\n}\n.button-box .export-but-wrap[data-v-6691cc6c]{width:80px;height:35px;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;font-size:0.875rem;margin-right:10px\n}\n.button-box .clear-but[data-v-6691cc6c]:hover,.button-box .sarch-but[data-v-6691cc6c]:hover{background:#f7cbad!important;color:#fff!important;border-color:#f7cbad!important\n}", ""]);

// exports


/***/ }),

/***/ 3688:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});
exports.systemErrors = undefined;

var _request = __webpack_require__(316);

var _request2 = _interopRequireDefault(_request);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

var systemErrors = exports.systemErrors = function systemErrors(params) {
	return (0, _request2.default)({
		method: 'get',
		url: '/rest-ful/v3.0/system/errors',
		params: params
	});
};

/***/ }),

/***/ 3689:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", [_c("div", { staticClass: "policy-name" }, [_c("div", { staticClass: "lable" }, [_vm._v("错误码")]), _vm._v(" "), _c("div", [_c("Input", {
    attrs: { type: "number", min: 0, placeholder: "请输入错误码" },
    on: { input: _vm.handleInput },
    model: {
      value: _vm.form.code,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "code", $$v);
      },
      expression: "form.code"
    }
  })], 1), _vm._v(" "), _c("div", { staticClass: "button-box" }, [_c("Button", {
    staticClass: "export-but-wrap sarch-but",
    attrs: { type: "primary", icon: "ios-search" },
    on: { click: _vm.onSearch }
  }, [_vm._v("搜索")]), _vm._v(" "), _c("Button", {
    staticClass: "export-but-wrap clear-but",
    attrs: { icon: "ios-sync" },
    on: { click: _vm.onClear }
  }, [_vm._v("重置")])], 1)]), _vm._v(" "), _c("Table", {
    attrs: {
      columns: _vm.columns,
      data: _vm.tableData,
      loading: _vm.tableLoading,
      "row-class-name": _vm.tishi,
      height: _vm.tableHeight,
      "no-data-text": "<div class='no-img-url-text'><img class='noImgUrl' src=" + _vm.noImgUrl + " /><div class='text'>暂无数据</div><div>"
    }
  })], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-6691cc6c", esExports);
  }
}

/***/ }),

/***/ 3690:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", [_c("Row", [_c("Col", { staticClass: "breadstyle", attrs: { span: "24" } }, [_c("Breadcrumb", [_c("BreadcrumbItem", {
    staticClass: "breadlist",
    staticStyle: { display: "flex", "align-items": "center" }
  }, [_c("img", {
    staticStyle: {
      width: "1.2rem",
      height: "1.2rem",
      "margin-right": "0.3rem"
    },
    attrs: { src: __webpack_require__(318) }
  }), _vm._v(" "), _c("span", [_vm._v("系统管理 / 参数设置")])])], 1)], 1)], 1), _vm._v(" "), _c("el-card", {
    staticClass: "marBox",
    staticStyle: { height: "calc(100vh - 150px)" }
  }, [_c("div", [_c("Tabs", {
    attrs: { type: "card", animated: false },
    on: { "on-click": _vm.col1 },
    model: {
      value: _vm.actionTabs,
      callback: function callback($$v) {
        _vm.actionTabs = $$v;
      },
      expression: "actionTabs"
    }
  }, [this.hasPrivilege(_vm.getPower.VRTS_FUNC_BASIC_CON) ? _c("TabPane", {
    staticStyle: { position: "relative" },
    attrs: { label: "基本配置", name: "基本配置" }
  }, [_c("div", { staticClass: "dqchange dqchange1" }), _vm._v(" "), _c("div", { staticClass: "setPassword" }, [_c("Form", { attrs: { "label-width": 150, inline: "" } }, [_c("FormItem", { attrs: { label: "调度间隔时间：" } }, [_c("InputNumber", {
    attrs: {
      max: 5,
      min: 1,
      "active-change": false
    },
    model: {
      value: _vm.parameter.dispatch,
      callback: function callback($$v) {
        _vm.$set(_vm.parameter, "dispatch", $$v);
      },
      expression: "parameter.dispatch"
    }
  }), _vm._v("（ 单位/分 ）\n\t\t\t\t\t\t\t\t")], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "介质回收间隔时间：" } }, [_c("InputNumber", {
    attrs: {
      max: 30,
      min: 5,
      "active-change": false
    },
    model: {
      value: _vm.parameter.recovery,
      callback: function callback($$v) {
        _vm.$set(_vm.parameter, "recovery", $$v);
      },
      expression: "parameter.recovery"
    }
  }), _vm._v("（ 单位/分 ）\n\t\t\t\t\t\t\t\t")], 1), _vm._v(" "), _c("FormItem", {
    attrs: { label: "系统日志保留最长时间" }
  }, [_c("InputNumber", {
    attrs: {
      min: 180,
      "active-change": false
    },
    on: { "on-blur": _vm.onCont },
    model: {
      value: _vm.parameter.log,
      callback: function callback($$v) {
        _vm.$set(_vm.parameter, "log", $$v);
      },
      expression: "parameter.log"
    }
  }), _vm._v("（ 单位/天 ）\n\t\t\t\t\t\t\t\t")], 1), _vm._v(" "), _c("div", {
    staticStyle: {
      display: "flex",
      "justify-content": "flex-start",
      "align-items": "center"
    }
  }, [_c("FormItem", { attrs: { label: "日志空间上限：" } }, [_c("InputNumber", {
    attrs: {
      max: 10240,
      min: 1,
      "active-change": false
    },
    model: {
      value: _vm.parameter.log_mbytes,
      callback: function callback($$v) {
        _vm.$set(_vm.parameter, "log_mbytes", $$v);
      },
      expression: "parameter.log_mbytes"
    }
  }), _vm._v("MB\n\t\t\t\t\t\t\t\t\t\t")], 1)], 1), _vm._v(" "), _c("FormItem", [_c("Button", {
    staticClass: "buttonA",
    attrs: {
      type: "success",
      disabled: !_vm.nowShow(_vm.getPower.setsysset)
    },
    on: { click: _vm.onSystem }
  }, [_vm._v("保存")])], 1)], 1)], 1)]) : _vm._e(), _vm._v(" "), this.hasPrivilege(_vm.getPower.VRTS_FUNC_EMAIL_CON) ? _c("TabPane", {
    staticStyle: { position: "relative" },
    attrs: { label: "邮件通知", name: "邮件通知" }
  }, [_c("div", { staticClass: "dqchange dqchange2" }), _vm._v(" "), _c("div", { staticClass: "setEmail" }, [_c("Row", {
    staticClass: "Email",
    attrs: { slot: "content" },
    slot: "content"
  }, [_c("Col", { attrs: { span: "12" } }, [_c("span", { staticClass: "_titles" }, [_vm._v("发送邮件账户设置")]), _vm._v(" "), _c("Form", {
    ref: "mailbox",
    attrs: {
      "label-width": 160,
      model: _vm.mailbox,
      rules: _vm.ruleEmail
    }
  }, [_c("FormItem", {
    attrs: {
      label: "邮 件 账 户：",
      prop: "admin"
    }
  }, [_c("Input", {
    staticStyle: { width: "70%" },
    attrs: {
      type: "text",
      placeholder: "请输入邮件账户"
    },
    model: {
      value: _vm.mailbox.admin,
      callback: function callback($$v) {
        _vm.$set(_vm.mailbox, "admin", $$v);
      },
      expression: "mailbox.admin"
    }
  })], 1), _vm._v(" "), _c("FormItem", {
    attrs: {
      label: "邮 件 密 码：",
      prop: "password"
    }
  }, [_c("Input", {
    staticStyle: { width: "70%" },
    attrs: {
      type: "password",
      placeholder: "请输入邮件密码"
    },
    model: {
      value: _vm.mailbox.password,
      callback: function callback($$v) {
        _vm.$set(_vm.mailbox, "password", $$v);
      },
      expression: "mailbox.password"
    }
  })], 1), _vm._v(" "), _c("FormItem", {
    attrs: {
      label: "SMTP服务器地址：",
      prop: "path"
    }
  }, [_c("Input", {
    staticStyle: { width: "70%" },
    attrs: {
      placeholder: "重新输入SMTP服务器地址"
    },
    model: {
      value: _vm.mailbox.path,
      callback: function callback($$v) {
        _vm.$set(_vm.mailbox, "path", $$v);
      },
      expression: "mailbox.path"
    }
  })], 1), _vm._v(" "), _c("FormItem", {
    attrs: {
      label: "SMTP服务器端口号：",
      prop: "port"
    }
  }, [_c("Input", {
    staticStyle: { width: "70%" },
    attrs: {
      placeholder: "重新输入SMTP服务器端口号"
    },
    model: {
      value: _vm.mailbox.port,
      callback: function callback($$v) {
        _vm.$set(_vm.mailbox, "port", $$v);
      },
      expression: "mailbox.port"
    }
  })], 1)], 1), _vm._v(" "), _c("div", { staticClass: "opt-mail-bnt" }, [_c("Button", {
    staticClass: "buttonA",
    attrs: {
      type: "success",
      disabled: !_vm.nowShow(_vm.getPower.setEmail)
    },
    on: { click: _vm.onMailbox }
  }, [_vm._v(" 保 存 ")]), _vm._v("  \n\t\t\t\t\t\t\t\t\t\t"), _c("Button", {
    staticClass: "buttonA",
    attrs: { type: "success" },
    on: { click: _vm.onReset }
  }, [_vm._v(" 清空配置 ")]), _vm._v("  \n\t\t\t\t\t\t\t\t\t\t"), _c("Button", {
    staticClass: "buttonA",
    attrs: {
      type: "success",
      disabled: !_vm.nowShow(_vm.getPower.setEmail)
    },
    on: { click: _vm.sendEmail }
  }, [_vm._v(" 发送测试邮件 ")])], 1)], 1), _vm._v(" "), _c("Col", { attrs: { span: "12" } }, [_c("Form", {
    ref: "Emailpath",
    attrs: {
      model: _vm.Emailpath,
      rules: _vm.ruleMail,
      "label-width": 110,
      "label-position": "left"
    }
  }, [_c("FormItem", {
    attrs: {
      label: "EMAIL地址：",
      prop: "mailPath"
    }
  }, [_c("Input", {
    staticStyle: { width: "70%" },
    attrs: {
      placeholder: "请输入EMAIL地址"
    },
    model: {
      value: _vm.Emailpath.mailPath,
      callback: function callback($$v) {
        _vm.$set(_vm.Emailpath, "mailPath", $$v);
      },
      expression: "Emailpath.mailPath"
    }
  })], 1)], 1), _vm._v(" "), _c("div", { staticClass: "_button" }, [_c("Button", {
    attrs: {
      type: "error",
      disabled: !_vm.nowShow(_vm.getPower.setEmail)
    },
    on: { click: _vm.addEmail }
  }, [_vm._v("添加")]), _vm._v(" "), _c("Button", {
    staticClass: "buttonA",
    attrs: {
      type: "warning",
      disabled: !_vm.nowShow(_vm.getPower.setEmail)
    },
    on: { click: _vm.preservation }
  }, [_vm._v("保存")]), _vm._v(" "), _c("Button", {
    staticClass: "buttonB",
    attrs: {
      type: "error",
      disabled: !_vm.nowShow(_vm.getPower.setEmail)
    },
    on: { click: _vm.deleteEmail }
  }, [_c("span", {
    staticStyle: { color: "#fff" }
  }, [_vm._v("删除")])])], 1), _vm._v(" "), _c("Table", {
    staticClass: "_tabs",
    attrs: {
      "highlight-row": "",
      border: "",
      columns: _vm.emails,
      data: _vm.emailData,
      height: "190"
    },
    on: { "on-row-click": _vm.rowClick }
  })], 1)], 1)], 1)]) : _vm._e(), _vm._v(" "), this.hasPrivilege(_vm.getPower.VRTS_FUNC_AUTH_MAG) ? _c("TabPane", {
    staticStyle: { position: "relative" },
    attrs: { label: "授权管理", name: "授权管理" }
  }, [_c("div", { staticClass: "dqchange dqchange3" }), _vm._v(" "), _c("div", { staticClass: "ss" }, [_c("Row", { attrs: { slot: "content" }, slot: "content" }, [_c("Col", { attrs: { span: "24" } }, [_c("Collapse", {
    attrs: { accordion: "" },
    model: {
      value: _vm.sqvalue,
      callback: function callback($$v) {
        _vm.sqvalue = $$v;
      },
      expression: "sqvalue"
    }
  }, [_c("Panel", { attrs: { name: "1" } }, [_vm._v("\n\t\t\t\t\t\t\t\t\t\t\t基本信息\n\n\t\t\t\t\t\t\t\t\t\t\t"), _c("div", {
    attrs: { slot: "content" },
    slot: "content"
  }, [_c("div", { staticClass: "frame" }, [_c("p", {
    staticClass: "titles"
  }, [_vm._v("授权方式")]), _vm._v(" "), _c("CheckboxGroup", {
    model: {
      value: _vm.Base.LicenseMode,
      callback: function callback($$v) {
        _vm.$set(_vm.Base, "LicenseMode", $$v);
      },
      expression: "Base.LicenseMode"
    }
  }, [_c("div", {
    staticStyle: {
      "margin-left": "17px"
    }
  }, [_c("Checkbox", {
    attrs: {
      disabled: "",
      label: "0"
    }
  }, [_vm._v("功能授权")]), _vm._v(" "), _c("p", {
    staticClass: "mr10",
    staticStyle: {
      "margin-left": "17px"
    }
  }, [_vm._v("\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t按功能模块进行授权,当相应模块未授权或者超过授权数量时,对应的模块或超过数量部分将不能够被使用。备份的总存储容量不受限制\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t")])], 1), _vm._v(" "), _c("p", {
    staticClass: "blanks"
  }), _vm._v(" "), _c("div", {
    staticStyle: {
      "margin-left": "17px"
    }
  }, [_c("Checkbox", {
    attrs: {
      disabled: "",
      label: "1"
    }
  }, [_vm._v("容量授权")]), _vm._v(" "), _c("p", {
    staticClass: "mr10",
    staticStyle: {
      "margin-left": "17px"
    }
  }, [_vm._v("\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t按备份的存储容量进行授权,功能模块不受限制,当备份保留的总数据量超过授权容量时,将会备份失败。\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t")])], 1)]), _vm._v(" "), _c("div", {
    staticStyle: {
      "margin-left": "17px"
    }
  }, [_c("span", {
    staticStyle: {
      "margin-left": "18px"
    }
  }, [_vm._v("授权容量:")]), _vm._v(" "), _c("Input", {
    staticStyle: {
      width: "30%"
    },
    attrs: {
      readonly: "readonly",
      disabled: ""
    },
    model: {
      value: _vm.Base.Capacity,
      callback: function callback($$v) {
        _vm.$set(_vm.Base, "Capacity", $$v);
      },
      expression: "Base.Capacity"
    }
  }), _vm._v("  T\n\t\t\t\t\t\t\t\t\t\t\t\t\t")], 1)], 1), _vm._v(" "), _c("div", { staticClass: "frame" }, [_c("p", {
    staticClass: "titles"
  }, [_vm._v("授权类型")]), _vm._v(" "), _c("RadioGroup", {
    staticStyle: {
      "margin-left": "17px"
    },
    model: {
      value: _vm.Base.LicenseType,
      callback: function callback($$v) {
        _vm.$set(_vm.Base, "LicenseType", $$v);
      },
      expression: "Base.LicenseType"
    }
  }, [_c("Radio", {
    attrs: {
      disabled: "",
      label: "0"
    }
  }, [_vm._v("测试授权")]), _vm._v(" "), _c("p", {
    staticClass: "mr10",
    staticStyle: {
      "margin-left": "17px"
    }
  }, [_vm._v("\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t用于测试使用,测试授权固定只有30天的使用期限。"), (_vm.LicenseType != 1 ? true : false) ? _c("span", [_vm._v("您的授权还有" + _vm._s(_vm.EffectiveDays) + "天到期")]) : _vm._e()]), _vm._v(" "), _c("p", {
    staticClass: "blanks"
  }), _vm._v(" "), _c("Radio", {
    attrs: {
      disabled: "",
      label: "1"
    }
  }, [_vm._v("商用授权")]), _vm._v(" "), _c("p", {
    staticClass: "mr10",
    staticStyle: {
      "margin-left": "17px"
    }
  }, [_vm._v("\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t商用授权为销售授权,没有使用时限的限制\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t")])], 1)], 1), _vm._v(" "), _c("p", {
    staticClass: "blanks"
  }), _vm._v(" "), _c("Form", {
    staticStyle: {
      "margin-left": "17px"
    },
    attrs: {
      "label-width": 90
    }
  }, [_c("FormItem", {
    attrs: {
      label: "系统注册码:"
    }
  }, [_c("Input", {
    staticStyle: {
      width: "30%"
    },
    attrs: {
      id: "copyValue",
      readonly: "readonly"
    },
    model: {
      value: _vm.syssetCode,
      callback: function callback($$v) {
        _vm.syssetCode = $$v;
      },
      expression: "syssetCode"
    }
  }), _vm._v(" "), _c("button", {
    ref: "copy",
    staticClass: "ivu-btn ivu-btn-warning",
    attrs: {
      type: "button",
      "data-clipboard-action": "copy",
      "data-clipboard-target": "#copyValue"
    },
    on: {
      click: function click($event) {
        $event.stopPropagation();
        return _vm.copyValues(_vm.syssetCode);
      }
    }
  }, [_vm._v("\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t复制注册码\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t")])], 1), _vm._v(" "), _vm.Base.LicenseType == "0" ? _c("div", {
    staticClass: "frame"
  }, [_c("div", {
    staticStyle: {
      width: "49%",
      display: "inline-block"
    }
  }, [_c("Checkbox", {
    attrs: {
      disabled: "",
      label: "备份前运行脚本"
    },
    model: {
      value: _vm.Base.VssBackup,
      callback: function callback($$v) {
        _vm.$set(_vm.Base, "VssBackup", $$v);
      },
      expression: "Base.VssBackup"
    }
  }, [_vm._v("打开文件备份")]), _vm._v(" "), _c("p", {
    staticClass: "mr10"
  }, [_vm._v("此授权用于控制高级文件备份功能")])], 1), _vm._v(" "), _c("div", {
    staticStyle: {
      width: "49%",
      display: "inline-block"
    }
  }, [_c("Checkbox", {
    attrs: {
      disabled: ""
    },
    model: {
      value: _vm.Base.Dedump,
      callback: function callback($$v) {
        _vm.$set(_vm.Base, "Dedump", $$v);
      },
      expression: "Base.Dedump"
    }
  }, [_vm._v("重复数据删除")]), _vm._v(" "), _c("p", {
    staticClass: "mr10"
  }, [_vm._v("此授权用于控制重复数据删除功能")])], 1)]) : _vm._e(), _vm._v(" "), _c("div", {
    staticClass: "frame-con"
  }, [_c("Upload", {
    attrs: {
      action: "/rest-ful/v3.0/system/license/import",
      headers: _vm.headers,
      format: ["dat"],
      "show-upload-list": false,
      "on-success": _vm.upLoads,
      "on-error": _vm.upLoad,
      "on-format-error": _vm.handleFormatError
    }
  }, [_c("Button", {
    attrs: {
      type: "warning"
    }
  }, [_vm._v("导入授权文件")])], 1)], 1)], 1)], 1)]), _vm._v(" "), _c("Panel", { attrs: { name: "2" } }, [_vm._v("\n\t\t\t\t\t\t\t\t\t\t\t存储服务授权\n\t\t\t\t\t\t\t\t\t\t\t"), _c("div", {
    attrs: { slot: "content" },
    slot: "content"
  }, [_c("Table", {
    attrs: {
      "highlight-row": "",
      border: "",
      "row-class-name": _vm.tishi,
      columns: _vm.storageList,
      data: _vm.Storage
    }
  })], 1)]), _vm._v(" "), _c("Panel", { attrs: { name: "3" } }, [_vm._v("\n\t\t\t\t\t\t\t\t\t\t\t客户端授权\n\t\t\t\t\t\t\t\t\t\t\t"), _c("div", {
    attrs: { slot: "content" },
    slot: "content"
  }, [_c("Table", {
    attrs: {
      "highlight-row": "",
      border: "",
      "row-class-name": _vm.tishi,
      columns: _vm.clientList,
      data: _vm.Client
    }
  })], 1)]), _vm._v(" "), _c("Panel", { attrs: { name: "4" } }, [_vm._v("\n\t\t\t\t\t\t\t\t\t\t\t备份代理授权\n\t\t\t\t\t\t\t\t\t\t\t"), _c("div", {
    attrs: { slot: "content" },
    slot: "content"
  }, [_c("Table", {
    attrs: {
      "highlight-row": "",
      border: "",
      "row-class-name": _vm.tishi,
      columns: _vm.agentList,
      data: _vm.Agent
    }
  })], 1)])], 1)], 1)], 1)], 1)]) : _vm._e(), _vm._v(" "), this.hasPrivilege(_vm.getPower.VRTS_FUNC_SET_BOX_MAG) ? _c("TabPane", {
    staticStyle: { position: "relative" },
    attrs: { label: "安装包管理", name: "安装包管理" }
  }, [_c("div", { staticClass: "dqchange dqchange4" }), _vm._v(" "), _c("div", [_c("downloud", { ref: "downloud" })], 1)]) : _vm._e(), _vm._v(" "), this.hasPrivilege(_vm.getPower.VRTS_FUNC_SAFE_PLAN) ? _c("TabPane", {
    staticStyle: { position: "relative" },
    attrs: { label: "安全策略", name: "安全策略" }
  }, [_c("div", { staticClass: "dqchange dqchange5" }), _vm._v(" "), _c("div", [_c("div", { staticClass: "safe" }, [_c("p", [_c("span", { staticClass: "title-size" }, [_vm._v("超时下线：")]), _vm._v(" "), _c("InputNumber", {
    attrs: {
      max: 30,
      min: 1,
      "active-change": false
    },
    model: {
      value: _vm.logoutTimes,
      callback: function callback($$v) {
        _vm.logoutTimes = $$v;
      },
      expression: "logoutTimes"
    }
  }), _vm._v(" "), _c("span", { staticClass: "title-size" }, [_vm._v("分钟")])], 1), _vm._v(" "), _c("p", { staticClass: "lable-color" }, [_vm._v("\n\t\t\t\t\t\t\t\t\t设置用户登录状态下未对系统进行任何操作的时长,超过此时长当前用户账号自动退出登录状态,单位:分钟\n\t\t\t\t\t\t\t\t")]), _vm._v(" "), _c("div", { staticClass: "line" }), _vm._v(" "), _c("p", [_c("Checkbox", {
    staticClass: "ml10",
    model: {
      value: _vm.Checkpasswordbox,
      callback: function callback($$v) {
        _vm.Checkpasswordbox = $$v;
      },
      expression: "Checkpasswordbox"
    }
  }), _vm._v(" "), _c("span", {
    staticClass: "title-size title-size-pas"
  }, [_vm._v("密码有效期：")]), _c("InputNumber", {
    attrs: {
      disabled: !_vm.Checkpasswordbox,
      max: 7,
      min: 1,
      "active-change": false
    },
    model: {
      value: _vm.passwordExp,
      callback: function callback($$v) {
        _vm.passwordExp = $$v;
      },
      expression: "passwordExp"
    }
  }), _vm._v(" "), _c("span", { staticClass: "title-size" }, [_vm._v("天")])], 1), _vm._v(" "), _c("p", { staticClass: "lable-color" }, [_vm._v("\n\t\t\t\t\t\t\t\t\t用户账号的密码的有效期限,超过此期限用户账号需要重新设置密码,单位:天\n\t\t\t\t\t\t\t\t")]), _vm._v(" "), _c("div", { staticClass: "line" }), _vm._v(" "), _c("p", [_c("span", { staticClass: "title-size" }, [_vm._v("密码长度：")]), _vm._v(" "), _c("InputNumber", {
    attrs: {
      max: 32,
      min: 8,
      "active-change": false
    },
    model: {
      value: _vm.passwordLength.min,
      callback: function callback($$v) {
        _vm.$set(_vm.passwordLength, "min", $$v);
      },
      expression: "passwordLength.min"
    }
  }), _vm._v("\n\t\t\t\t\t\t\t\t\t~\n\t\t\t\t\t\t\t\t\t"), _c("InputNumber", {
    attrs: {
      max: 32,
      min: 8,
      "active-change": false
    },
    model: {
      value: _vm.passwordLength.max,
      callback: function callback($$v) {
        _vm.$set(_vm.passwordLength, "max", $$v);
      },
      expression: "passwordLength.max"
    }
  })], 1), _vm._v(" "), _c("p", { staticClass: "lable-color" }, [_vm._v("用户账号的密码的长度范围,单位:字符")]), _vm._v(" "), _c("div", { staticClass: "line" }), _vm._v(" "), _c("p", [_c("span", { staticClass: "title-size" }, [_vm._v("登录ip段：")]), _c("el-input", {
    staticStyle: { width: "150px" },
    attrs: { size: "mini", placeholder: "" },
    model: {
      value: _vm.loginIpLimit.start,
      callback: function callback($$v) {
        _vm.$set(_vm.loginIpLimit, "start", $$v);
      },
      expression: "loginIpLimit.start"
    }
  }), _vm._v("\n\t\t\t\t\t\t\t\t\t~\n\t\t\t\t\t\t\t\t\t"), _c("el-input", {
    staticStyle: { width: "150px" },
    attrs: { size: "mini", placeholder: "" },
    model: {
      value: _vm.loginIpLimit.end,
      callback: function callback($$v) {
        _vm.$set(_vm.loginIpLimit, "end", $$v);
      },
      expression: "loginIpLimit.end"
    }
  })], 1), _vm._v(" "), _c("p", { staticClass: "lable-color" }, [_vm._v("用户账号可登录系统的网络IP段范围,超出此范围无法登录系统")]), _vm._v(" "), _c("div", { staticClass: "line" }), _vm._v(" "), _c("p", [_c("span", { staticClass: "title-size" }, [_vm._v("登录锁定：")]), _vm._v(" "), _c("InputNumber", {
    attrs: {
      max: 5,
      min: 1,
      "active-change": false
    },
    model: {
      value: _vm.LoginFaiureTimes,
      callback: function callback($$v) {
        _vm.LoginFaiureTimes = $$v;
      },
      expression: "LoginFaiureTimes"
    }
  })], 1), _vm._v(" "), _c("p", { staticClass: "lable-color" }, [_vm._v("\n\t\t\t\t\t\t\t\t\t用户账号的密码输入错误的次数达到设置的数值，此用户账号将被锁定无法登录系统,单位:次\n\t\t\t\t\t\t\t\t")]), _vm._v(" "), _c("div", { staticClass: "line" }), _vm._v(" "), _c("p", [_c("Button", {
    staticClass: "buttonA",
    attrs: { type: "success" },
    on: { click: _vm.onSafeCl }
  }, [_vm._v("保存")])], 1)])])]) : _vm._e(), _vm._v(" "), this.hasPrivilege(_vm.getPower.VRTS_FUNC_SNMP_CON) ? _c("TabPane", {
    staticStyle: { position: "relative" },
    attrs: { label: "SNMP配置", name: "SNP配置" }
  }, [_c("div", { staticClass: "dqchange dqchange6" }), _vm._v(" "), _c("div", [_c("div", { staticClass: "operateBox" }, [_c("Button", {
    attrs: {
      type: "primary",
      icon: "ios-search"
    },
    on: { click: _vm.addTrap }
  }, [_vm._v("添加")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      icon: "ios-search"
    },
    on: { click: _vm.testTrap }
  }, [_vm._v("测试")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      icon: "ios-search"
    },
    on: { click: _vm.downTrap }
  }, [_vm._v("下载MIB文件")])], 1), _vm._v(" "), _c("div", [_c("Table", {
    attrs: {
      columns: _vm.trapColumns,
      height: _vm.tableHeight,
      data: _vm.trapData,
      "row-class-name": _vm.tishi
    },
    on: { "on-row-click": _vm.getRowData }
  })], 1)])]) : _vm._e(), _vm._v(" "), this.hasPrivilege(_vm.getPower.VRTS_FUNC_ALARM_MAG) ? _c("TabPane", {
    staticStyle: { position: "relative" },
    attrs: { label: "告警管理", name: "告警管理" }
  }, [_c("div", { staticClass: "dqchange dqchange7" }), _vm._v(" "), _c("div", [_c("div", { staticClass: "alarbox" }, [_c("ul", [_c("li", [_c("Checkbox", { on: { "on-change": _vm.tapeset } }, [_vm._v("磁带可使用数量下限告警")]), _vm._v(" "), _c("InputNumber", {
    attrs: {
      max: 10000,
      min: 1,
      "active-change": false,
      disabled: _vm.disabledo
    },
    model: {
      value: _vm.value,
      callback: function callback($$v) {
        _vm.value = $$v;
      },
      expression: "value"
    }
  })], 1), _vm._v(" "), _c("li", [_c("Checkbox", { on: { "on-change": _vm.deviceset } }, [_vm._v("设备容量下限告警")]), _vm._v(" "), _c("InputNumber", {
    attrs: {
      disabled: _vm.disabledt,
      max: 100,
      "active-change": false,
      formatter: function formatter(value) {
        return value + "%";
      },
      parser: function parser(value) {
        return value.replace("%", "");
      }
    },
    model: {
      value: _vm.value2,
      callback: function callback($$v) {
        _vm.value2 = $$v;
      },
      expression: "value2"
    }
  })], 1), _vm._v(" "), _c("li", [_c("Checkbox", [_vm._v("用户登录失败锁定告警")])], 1), _vm._v(" "), _c("li", [_c("Checkbox", [_vm._v("任务失败告警")])], 1), _vm._v(" "), _c("li", [_c("Checkbox", [_vm._v("策略未按计划调度告警")])], 1), _vm._v(" "), _c("li", [_c("Checkbox", [_vm._v("客户端离线告警")])], 1), _vm._v(" "), _c("li", [_c("Checkbox", [_vm._v("介质服务离线告警")])], 1), _vm._v(" "), _c("li", [_c("Checkbox", [_vm._v("设备离线告警")])], 1), _vm._v(" "), _c("li", [_c("Checkbox", [_vm._v("授权临期告警")])], 1), _vm._v(" "), _c("li", [_c("Checkbox", [_vm._v("服务离线告警")])], 1), _vm._v(" "), _c("li", [_c("Checkbox", { on: { "on-change": _vm.taskset } }, [_vm._v("任务长时间未完成告警")]), _vm._v(" "), _c("InputNumber", {
    attrs: {
      max: 100,
      min: 1,
      "active-change": false,
      disabled: _vm.disabledth
    },
    model: {
      value: _vm.value1,
      callback: function callback($$v) {
        _vm.value1 = $$v;
      },
      expression: "value1"
    }
  })], 1)])])])]) : _vm._e(), _vm._v(" "), this.hasPrivilege(_vm.getPower.VRTS_FUNC_ERROR_CODE) ? _c("TabPane", {
    staticStyle: { position: "relative" },
    attrs: { label: "错误码", name: "错误码" }
  }, [_c("ErrorCode")], 1) : _vm._e()], 1), _vm._v(" "), _c("Modal", {
    attrs: {
      closable: false,
      "mask-closable": false,
      "footer-hide": true,
      width: "540"
    },
    model: {
      value: _vm.AddModal,
      callback: function callback($$v) {
        _vm.AddModal = $$v;
      },
      expression: "AddModal"
    }
  }, [_c("div", { staticClass: "addPolicyModeStyle" }, [_c("h3", [_vm._v("添加SNMP Trap管理")]), _vm._v(" "), _c("span", { staticClass: "iconfont", on: { click: _vm.closeBox } }, [_vm._v("")])]), _vm._v(" "), _c("Form", {
    ref: "addSnmpMd",
    attrs: {
      model: _vm.addSnmpMd,
      rules: _vm.editRuleValidate,
      "label-position": "left",
      "label-width": 100
    }
  }, [_c("FormItem", { attrs: { label: "IP地址/域名", prop: "address" } }, [_c("Input", {
    model: {
      value: _vm.addSnmpMd.address,
      callback: function callback($$v) {
        _vm.$set(_vm.addSnmpMd, "address", $$v);
      },
      expression: "addSnmpMd.address"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "端口", prop: "port" } }, [_c("Input", {
    model: {
      value: _vm.addSnmpMd.port,
      callback: function callback($$v) {
        _vm.$set(_vm.addSnmpMd, "port", $$v);
      },
      expression: "addSnmpMd.port"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "SNMP版本", prop: "version" } }, [_c("Select", {
    on: { "on-change": _vm.changesVersion },
    model: {
      value: _vm.addSnmpMd.version,
      callback: function callback($$v) {
        _vm.$set(_vm.addSnmpMd, "version", $$v);
      },
      expression: "addSnmpMd.version"
    }
  }, _vm._l(_vm.versionList, function (item) {
    return _c("Option", {
      key: item.value,
      attrs: { value: item.value }
    }, [_vm._v(_vm._s(item.version))]);
  }), 1)], 1), _vm._v(" "), (_vm.versionNum != 3 ? false : true) ? _c("FormItem", { attrs: { label: "用户名", prop: "user" } }, [_c("Input", {
    model: {
      value: _vm.addSnmpMd.user,
      callback: function callback($$v) {
        _vm.$set(_vm.addSnmpMd, "user", $$v);
      },
      expression: "addSnmpMd.user"
    }
  })], 1) : _vm._e(), _vm._v(" "), (_vm.versionNum != 3 ? false : true) ? _c("FormItem", { attrs: { label: "密码", prop: "password" } }, [_c("Input", {
    attrs: { type: "password" },
    model: {
      value: _vm.addSnmpMd.password,
      callback: function callback($$v) {
        _vm.$set(_vm.addSnmpMd, "password", $$v);
      },
      expression: "addSnmpMd.password"
    }
  })], 1) : _vm._e()], 1), _vm._v(" "), _c("div", {
    staticClass: "modefooter",
    staticStyle: { width: "100%" }
  }, [_c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { size: "large", icon: "md-close-circle" },
    on: { click: _vm.closeBox }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      size: "large",
      icon: "md-checkmark-circle"
    },
    on: { click: _vm.onAddSnmp }
  }, [_vm._v("确定")])], 1)], 1), _vm._v(" "), _c("Modal", {
    attrs: {
      closable: false,
      "mask-closable": false,
      "footer-hide": true,
      width: "540"
    },
    model: {
      value: _vm.EditModal,
      callback: function callback($$v) {
        _vm.EditModal = $$v;
      },
      expression: "EditModal"
    }
  }, [_c("div", { staticClass: "addPolicyModeStyle" }, [_c("h3", [_vm._v("修改SNMP Trap管理")]), _vm._v(" "), _c("span", { staticClass: "iconfont", on: { click: _vm.closeBox } }, [_vm._v("")])]), _vm._v(" "), _c("Form", {
    ref: "addSnmpMd",
    attrs: {
      model: _vm.addSnmpMd,
      rules: _vm.editRuleValidate,
      "label-position": "left",
      "label-width": 100
    }
  }, [_c("FormItem", { attrs: { label: "IP地址/域名", prop: "address" } }, [_c("Input", {
    model: {
      value: _vm.addSnmpMd.address,
      callback: function callback($$v) {
        _vm.$set(_vm.addSnmpMd, "address", $$v);
      },
      expression: "addSnmpMd.address"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "端口", prop: "port" } }, [_c("Input", {
    model: {
      value: _vm.addSnmpMd.port,
      callback: function callback($$v) {
        _vm.$set(_vm.addSnmpMd, "port", $$v);
      },
      expression: "addSnmpMd.port"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "SNMP版本", prop: "version" } }, [_c("Select", {
    on: { "on-change": _vm.changesVersion },
    model: {
      value: _vm.addSnmpMd.version,
      callback: function callback($$v) {
        _vm.$set(_vm.addSnmpMd, "version", $$v);
      },
      expression: "addSnmpMd.version"
    }
  }, _vm._l(_vm.versionList, function (item) {
    return _c("Option", {
      key: item.value,
      attrs: { value: item.value }
    }, [_vm._v(_vm._s(item.version))]);
  }), 1)], 1), _vm._v(" "), (_vm.addSnmpMd.version != 3 ? false : true) ? _c("FormItem", { attrs: { label: "用户名", prop: "user" } }, [_c("Input", {
    model: {
      value: _vm.addSnmpMd.user,
      callback: function callback($$v) {
        _vm.$set(_vm.addSnmpMd, "user", $$v);
      },
      expression: "addSnmpMd.user"
    }
  })], 1) : _vm._e(), _vm._v(" "), (_vm.addSnmpMd.version != 3 ? false : true) ? _c("FormItem", { attrs: { label: "密码", prop: "password" } }, [_c("Input", {
    attrs: { type: "password" },
    model: {
      value: _vm.addSnmpMd.password,
      callback: function callback($$v) {
        _vm.$set(_vm.addSnmpMd, "password", $$v);
      },
      expression: "addSnmpMd.password"
    }
  })], 1) : _vm._e()], 1), _vm._v(" "), _c("div", {
    staticClass: "modefooter",
    staticStyle: { width: "100%" }
  }, [_c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { size: "large", icon: "md-close-circle" },
    on: { click: _vm.closeBox }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      size: "large",
      icon: "md-checkmark-circle"
    },
    on: { click: _vm.onEditSnmp }
  }, [_vm._v("确定")])], 1)], 1), _vm._v(" "), _c("Modal", {
    attrs: {
      closable: false,
      "mask-closable": false,
      "footer-hide": true,
      width: "360"
    },
    model: {
      value: _vm.delModal,
      callback: function callback($$v) {
        _vm.delModal = $$v;
      },
      expression: "delModal"
    }
  }, [_c("div", { staticClass: "addPolicyModeStyle" }, [_c("h3", [_vm._v("删除")]), _vm._v(" "), _c("span", { staticClass: "iconfont", on: { click: _vm.closeBox } }, [_vm._v("")])]), _vm._v(" "), _c("div", { staticClass: "modeContent" }, [_c("p", [_vm._v("确定删除此项吗")])]), _vm._v(" "), _c("div", {
    staticClass: "modefooter",
    staticStyle: { width: "100%" }
  }, [_c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { size: "large", icon: "md-close-circle" },
    on: { click: _vm.closeBox }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      size: "large",
      icon: "md-checkmark-circle"
    },
    on: { click: _vm.onDelSnmp }
  }, [_vm._v("确定")])], 1)]), _vm._v(" "), _c("Modal", {
    attrs: {
      width: "360",
      closable: false,
      "mask-closable": false,
      "footer-hide": true
    },
    model: {
      value: _vm.modal2,
      callback: function callback($$v) {
        _vm.modal2 = $$v;
      },
      expression: "modal2"
    }
  }, [_c("div", { staticClass: "addPolicyModeStyle" }, [_c("h3", [_vm._v(_vm._s(this.errerModal.title))]), _vm._v(" "), _c("span", { staticClass: "iconfont", on: { click: _vm.closeBox } }, [_vm._v("")])]), _vm._v(" "), _c("div", { staticStyle: { "margin-left": "20px" } }, [_c("p", { staticStyle: { color: "#f60" } }, [_c("Icon", { attrs: { type: "information-circled" } }), _vm._v(" "), _c("span", [_vm._v(_vm._s(this.errerModal.titlecon) + "：")]), _vm._v(" "), _c("span", [_vm._v(_vm._s(_vm.messageValue))])], 1)]), _vm._v(" "), _c("div", {
    staticClass: "modefooter",
    staticStyle: { width: "100%" }
  }, [_c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { size: "large", icon: "md-close-circle" },
    on: { click: _vm.closeBox }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      size: "large",
      icon: "md-checkmark-circle"
    },
    on: { click: _vm.errerok }
  }, [_vm._v("确定")])], 1)]), _vm._v(" "), _c("Modal", {
    attrs: {
      width: "500",
      closable: false,
      "mask-closable": false,
      "footer-hide": true
    },
    model: {
      value: _vm.upgradeModal,
      callback: function callback($$v) {
        _vm.upgradeModal = $$v;
      },
      expression: "upgradeModal"
    }
  }, [_c("div", { staticClass: "addPolicyModeStyle" }, [_c("h3", [_vm._v("上传升级软件进度")]), _vm._v(" "), _c("span", { staticClass: "iconfont", on: { click: _vm.closeBox } }, [_vm._v("")])]), _vm._v(" "), _c("div", { staticStyle: { "margin-left": "20px" } }, [_c("p", [_vm._v(_vm._s(this.upgradeTitle))]), _vm._v(" "), _c("Progress", { attrs: { percent: this.percentages } })], 1), _vm._v(" "), _c("div", {
    staticClass: "modefooter",
    staticStyle: { width: "100%" }
  }, [_c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { size: "large", icon: "md-close-circle" },
    on: { click: _vm.closeBox }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      size: "large",
      icon: "md-checkmark-circle"
    },
    on: { click: _vm.fanhuiok }
  }, [_vm._v("确定")])], 1)])], 1)])], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-14996c25", esExports);
  }
}

/***/ }),

/***/ 578:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_sysset_vue__ = __webpack_require__(2804);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_sysset_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_sysset_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_sysset_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_sysset_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_14996c25_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_sysset_vue__ = __webpack_require__(3690);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_14996c25_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_sysset_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_14996c25_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_sysset_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3679)
  __webpack_require__(3681)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = "data-v-14996c25"
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_sysset_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_14996c25_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_sysset_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/sysset/sysset.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-14996c25", Component.options)
  } else {
    hotAPI.reload("data-v-14996c25", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ })

});