#docute {
    margin: 0;
    color: var(--text-color);
    background: var(--page-background);
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    font: 16px/1.7 -apple-system, 微软雅黑, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif
}

#docute * {
    box-sizing: border-box
}

#docute a {
    /* color: var(--link-color); */
    text-decoration: none
}

#docute .Header {
    position: fixed !important;
}

#docute .Sidebar {
    border: solid 1px #eaeaea;
    left: 0;
    top: 40px;
}

#docute .Wrap {
    width: 100%;
}

#docute .header-left .router-link-active {
    padding-left: 50px;
    background: url(./img/logo.ico) left top no-repeat;
    background-size: 40px;
    background-position-y: -4px;
}

.Content a:hover {
    text-decoration: underline
}

.external-link-icon {
    color: #aaa;
    display: inline-block
}

.medium-zoom-image--opened,
.medium-zoom-overlay {
    z-index: 99
}

.Wrap {
    /* max-width: 1180px */
}

.layout-wide .Wrap {
    max-width: 100%
}

.layout-narrow .Wrap {
    margin: 0 auto
}

.docute-banner {
    margin-bottom: 10px
}

.docute-banner>:first-child {
    margin-top: 0
}

.docute-banner>:last-child {
    margin-bottom: 0
}

.docute-footer {
    padding-top: 60px
}

.InjectedComponents[data-position="mobile-sidebar:start"],
.InjectedComponents[data-position="sidebar:start"] {
    margin-bottom: 25px
}

.InjectedComponents[data-position="mobile-sidebar:start"]>:first-child,
.InjectedComponents[data-position="sidebar:start"]>:first-child {
    margin-top: 0
}

.InjectedComponents[data-position="sidebar:end"],
.InjectedComponents[data-position="sidebar:post-end"] {
    margin-top: 25px
}

.InjectedComponents[data-position="sidebar:end"]>:first-child,
.InjectedComponents[data-position="sidebar:post-end"]>:first-child {
    margin-top: 0
}

.InjectedComponents[data-position="content:start"] {
    margin-bottom: 35px
}

.InjectedComponents[data-position="content:start"]>:first-child {
    margin-top: 0
}

.InjectedComponents[data-position="content:start"]>:last-child {
    margin-bottom: 0
}

.InjectedComponents[data-position="content:end"] {
    margin-top: 30px
}

.InjectedComponents[data-position="header-right:start"]+.header-nav {
    margin-left: 30px
}

@media (min-width:768px) {

    .InjectedComponents[data-position="mobile-sidebar:end"],
    .InjectedComponents[data-position="mobile-sidebar:start"] {
        display: none
    }
}

.external-link-icon {
    margin-left: 3px
}

.header-nav[data-v-93d08526] {
    display: flex;
    align-items: center;
    font-size: 1rem
}

@media (max-width:768px) {
    .header-nav[data-v-93d08526] {
        display: none
    }
}

[data-v-93d08526] a {
    color: var(--nav-link-color)
}

.header-nav-item[data-v-93d08526] {
    height: 100%
}

.header-nav-item[data-v-93d08526]:not(:first-child) {
    margin-left: 25px
}

.header-nav-item[data-v-93d08526]>a {
    display: inline-flex;
    align-items: center;
    line-height: 1.4;
    height: 100%;
    position: relative
}

.header-nav-item[data-v-93d08526]>a:after {
    content: "";
    height: 2px;
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: block
}

.header-nav-item[data-v-93d08526]>a.router-link-exact-active {
    color: var(--accent-color)
}

.header-nav-item[data-v-93d08526]>a.router-link-exact-active:after {
    background-color: var(--nav-link-border-color)
}

.mobile-header-nav[data-v-93d08526] {
    display: block;
    margin-bottom: 30px;
    padding: 0 20px 30px;
    border-bottom: 1px solid var(--border-color)
}

.mobile-header-nav .header-nav-item[data-v-93d08526]:not(:first-child) {
    margin-left: 0
}

@media (min-width:768px) {
    .mobile-header-nav[data-v-93d08526] {
        display: none
    }
}

.arrow[data-v-93d08526] {
    display: inline-block;
    vertical-align: middle;
    margin-top: -1px;
    margin-left: 6px;
    margin-right: -14px;
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 5px solid #ccc
}

.dropdown-wrapper[data-v-93d08526] {
    position: relative
}

.dropdown-wrapper:hover .dropdown-list[data-v-93d08526] {
    display: block
}

.dropdown-trigger[data-v-93d08526]:hover {
    cursor: default
}

.dropdown-list[data-v-93d08526] {
    display: none;
    list-style: none;
    margin: 0;
    padding: 5px 0;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    position: absolute;
    right: 0;
    top: 100%;
    background: var(--header-background)
}

@media (max-width:768px) {
    .dropdown-list[data-v-93d08526] {
        position: relative
    }
}

.dropdown-item[data-v-93d08526] {
    line-height: 1.6
}

.dropdown-item[data-v-93d08526] a {
    padding: 2px 1.5rem 2px 1.25rem;
    white-space: nowrap;
    display: block;
    position: relative
}

.dropdown-item[data-v-93d08526] a.router-link-exact-active {
    color: var(--accent-color)
}

.dropdown-item[data-v-93d08526] a.router-link-exact-active:before {
    content: "";
    width: 0;
    height: 0;
    border-left: 5px solid #3eaf7c;
    border-top: 3px solid transparent;
    border-bottom: 3px solid transparent;
    position: absolute;
    top: calc(50% - 2px);
    left: 9px
}

.PageToc[data-v-b4f38364] {
    border-left: 1px solid var(--border-color);
    margin-left: 16px;
    margin-top: 10px
}

.PageTocHeading[data-v-b4f38364] {
    display: flex;
    line-height: 1;
    position: relative
}

.PageTocHeading[data-v-b4f38364]:not(:last-child) {
    margin-bottom: 8px
}

.PageTocHeading[data-level="2"][data-v-b4f38364] {
    margin-left: 16px
}

.PageTocHeading.router-link-exact-active[data-v-b4f38364] {
    font-weight: 700;
    color: var(--sidebar-link-active-color)
}

.SidebarItem[data-v-794d5154]:not(:last-child) {
    margin-bottom: 10px
}

.SidebarItem[data-v-794d5154] {
    font-size: .875rem
}

.SidebarItem[data-v-794d5154] a {
    color: var(--sidebar-link-color)
}

.SidebarItem[data-v-794d5154] a:hover {
    color: var(--sidebar-link-active-color)
}

.ItemTitle[data-v-794d5154] {
    padding: 0 20px;
    margin-bottom: 10px;
    position: relative;
    color: var(--sidebar-link-color);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    font-size: 0
}

.ItemTitle.collapsable[data-v-794d5154]:hover {
    cursor: pointer;
    color: var(--sidebar-link-active-color)
}

.ItemTitle span[data-v-794d5154] {
    font-size: 1.1rem
}

.ItemLink[data-v-794d5154] {
    margin: 0 20px;
    display: flex;
    align-items: center
}

.ItemLink[data-v-794d5154]:before {
    content: "";
    display: block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: var(--sidebar-link-arrow-color);
    margin-right: 8px
}

.ItemLink.active[data-v-794d5154] {
    color: var(--sidebar-link-active-color);
    font-weight: 700
}

.ItemLinkToc[data-v-794d5154] {
    margin: 0 8px
}

.ItemChildren[data-v-794d5154] {
    border-left: 1px solid var(--border-color);
    margin: 0 20px
}

.ItemChild[data-v-794d5154]:not(:last-child) {
    margin-bottom: 10px
}

.ItemChildLink[data-v-794d5154] {
    padding-left: 16px;
    display: flex;
    position: relative;
    line-height: 1
}

.ItemChildLink.active[data-v-794d5154] {
    font-weight: 700
}

a[data-v-794d5154] {
    text-decoration: none;
    color: var(--text-color)
}

.arrow[data-v-794d5154] {
    width: 16px;
    display: inline-block;
    color: var(--sidebar-link-arrow-color)
}

.arrow svg[data-v-794d5154] {
    transition: all .15s ease
}

.arrow.open svg[data-v-794d5154] {
    transform: rotate(90deg)
}

.Sidebar[data-v-578c0cb2] {
    width: var(--sidebar-width);
    background: var(--sidebar-background);
    position: fixed;
    top: var(--header-height);
    bottom: 0;
    z-index: 9;
    overflow-y: auto;
    padding: 40px 0 30px;
    word-break: break-word
}

.Sidebar a[data-v-578c0cb2] {
    text-decoration: none;
    color: var(--text-color)
}

@media (max-width:768px) {
    .Sidebar[data-v-578c0cb2] {
        left: 0;
        transform: translateX(-100%);
        width: 80%;
        transition: transform .5s cubic-bezier(.5, .32, .01, 1);
        padding: 30px 0;
        border-right: 1px solid var(--border-color)
    }

    .Sidebar.isShown[data-v-578c0cb2] {
        transform: translateX(0)
    }
}

@media print {
    .Sidebar[data-v-578c0cb2] {
        display: none
    }
}

.SidebarMask[data-v-69b97706] {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 8
}

.sidebar-toggle[data-v-bc0f6e38] {
    display: flex;
    height: 100%;
    align-items: center
}

@media (min-width:768px) {
    .sidebar-toggle[data-v-bc0f6e38] {
        display: none
    }
}

@media print {
    .sidebar-toggle[data-v-bc0f6e38] {
        display: none
    }
}

.sidebar-toggle svg[data-v-bc0f6e38] {
    width: 20px;
    height: 20px;
    margin-right: 10px
}

.Header[data-v-07005330] {
    height: var(--header-height);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 33;
    border-bottom: 1px solid var(--border-color);
    background: var(--header-background);
    color: var(--header-text-color)
}

@media print {
    .Header[data-v-07005330] {
        position: static
    }
}

.Wrap[data-v-07005330] {
    height: 100%
}

.site-title[data-v-07005330] {
    font-weight: 400;
    margin: 0 25px 0 0;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    white-space: nowrap
}

.site-title a[data-v-07005330] {
    color: inherit;
    text-decoration: none
}

.header-inner[data-v-07005330] {
    height: 100%;
    padding: 0 20px;
    position: relative;
    display: flex;
    align-items: center
}

@media print {
    .header-inner[data-v-07005330] {
        padding: 0
    }
}

.header-left[data-v-07005330] {
    display: flex
}

.header-right[data-v-07005330] {
    display: flex;
    position: absolute;
    right: 20px;
    top: 0;
    height: 100%;
    background: var(--header-background)
}

@media print {
    .header-right[data-v-07005330] {
        display: flex;
        right: 0;
        padding-right: 0
    }

    .header-right[data-v-07005330] .header-nav {
        display: flex
    }
}

.prev-next-links[data-v-28184c9e] {
    overflow: auto;
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid var(--border-color)
}

@media print {
    .prev-next-links[data-v-28184c9e] {
        display: none
    }
}

.prev-link[data-v-28184c9e] {
    float: left
}

.next-link[data-v-28184c9e] {
    float: right
}

.EditLink[data-v-49573232] {
    margin-top: 50px
}

.EditLink a[data-v-49573232] {
    display: inline-flex;
    align-items: center
}

.icon[data-v-49573232] {
    width: 18px;
    height: 18px;
    margin-right: 10px
}

.token.cdata,
.token.comment,
.token.prolog {
    color: #637777;
    font-style: italic
}

.token.punctuation {
    color: #c792ea
}

.namespace {
    color: #b2ccd6
}

.token.deleted {
    color: rgba(239, 83, 80, .56);
    font-style: italic
}

.token.property,
.token.symbol {
    color: #80cbc4
}

.token.keyword,
.token.operator,
.token.tag {
    color: #7fdbca
}

.token.boolean {
    color: #ff5874
}

.token.number {
    color: #f78c6c
}

.token.builtin,
.token.char,
.token.constant,
.token.function {
    color: #82aaff
}

.token.doctype,
.token.selector {
    color: #c792ea;
    font-style: italic
}

.token.attr-name,
.token.inserted {
    color: #addb67;
    font-style: italic
}

.language-css .token.string,
.style .token.string,
.token.entity,
.token.string,
.token.url {
    color: #addb67
}

.token.atrule,
.token.attr-value,
.token.class-name {
    color: #ffcb8b
}

.token.important,
.token.regex,
.token.variable {
    color: #d6deeb
}

.token.bold,
.token.important {
    font-weight: 700
}

.token.italic {
    font-style: italic
}

.page-content>:first-child {
    margin-top: 0
}

.page-content.has-page-title>h2:first-child {
    margin-top: 3rem
}

.page-content h1,
.page-content h2,
.page-content h3,
.page-content h4,
.page-content h5,
.page-content h6 {
    font-weight: 300;
    line-height: 1.2
}

.page-content h1 {
    font-size: 3rem;
    margin-bottom: 1.4rem
}

.page-content h2 {
    font-size: 2rem;
    border-bottom: 1px solid var(--border-color);
    margin-top: 3rem;
    padding-bottom: 5px
}

.page-content h3 {
    font-size: 1.7rem;
    margin: 40px 0 30px
}

.page-content h4 {
    font-size: 1.4rem
}

.page-content h5 {
    font-size: 1.1rem
}

.page-content p {
    margin: 15px 0
}

.page-content table {
    width: 100%;
    border-spacing: 0;
    border-collapse: separate
}

.page-content table td,
.page-content table th {
    padding: 12px 10px;
    border-bottom: 1px solid var(--border-color);
    text-align: left
}

.page-content thead th {
    color: var(--table-header-color);
    background: var(--table-header-background);
    border-bottom: 1px solid var(--border-color);
    border-top: 1px solid var(--border-color);
    font-weight: 400;
    font-size: 12px;
    padding: 10px
}

.page-content thead th:first-child {
    border-left: 1px solid var(--border-color);
    border-radius: 4px 0 0 4px
}

.page-content thead th:last-child {
    border-right: 1px solid var(--border-color);
    border-radius: 0 4px 4px 0
}

.page-content .pre-wrapper {
    margin: 2rem 0;
    position: relative;
    border-radius: 4px;
    background: var(--code-block-background);
    box-shadow: inset 0 0 0 var(--code-block-shadow-width) var(--code-block-shadow-color)
}

.page-content .pre-wrapper:before {
    content: attr(data-lang);
    position: absolute;
    top: 5px;
    right: 10px;
    font-size: 12px;
    color: #cacaca
}

.page-content .pre-wrapper code {
    color: var(--code-block-text-color)
}

.page-content .code-mask,
.page-content pre {
    overflow: auto;
    position: relative;
    margin: 0;
    z-index: 2;
    font-family: var(--code-font);
    white-space: pre
}

.page-content .code-mask code,
.page-content pre code {
    box-shadow: none;
    margin: 0;
    padding: 0;
    border: none;
    font-size: 1em;
    background: transparent
}

@media print {

    .page-content .code-mask,
    .page-content pre {
        white-space: pre-wrap;
        word-break: break-word
    }
}

.page-content pre {
    padding: 20px
}

.page-content .code-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1;
    padding-top: 20px;
    border: none;
    color: transparent
}

.page-content .code-line {
    display: block;
    padding: 0 20px
}

.page-content .code-line.highlighted {
    background: var(--highlighted-line-background);
    position: relative
}

.page-content .code-line.highlighted:before {
    content: "";
    display: block;
    width: 3px;
    top: 0;
    left: 0;
    bottom: 0;
    background: var(--highlighted-line-border-color);
    position: absolute
}

.page-content code {
    font-family: var(--code-font);
    font-size: 90%;
    background: var(--inline-code-background);
    border-radius: 4px;
    padding: 3px 5px;
    color: var(--inline-code-color)
}

.page-content>ol,
.page-content>ul {
    padding-left: 20px;
    margin: 1rem 0
}

.page-content .contains-task-list {
    list-style: none;
    padding-left: 0
}

.page-content img {
    max-width: 100%
}

.page-content blockquote {
    background: #f1f1f1;
    border-left: 8px solid #ccc;
    margin: 20px 0;
    padding: 14px 16px;
    color: #6a737d
}

.page-content blockquote p {
    margin: 15px 0 0
}

.page-content blockquote>:first-child {
    margin-top: 0
}

.page-content hr {
    height: 1px;
    padding: 0;
    margin: 3rem 0;
    background-color: #e1e4e8;
    border: 0
}

.page-content .header-anchor {
    float: left;
    line-height: 1;
    margin-left: -20px;
    padding-right: 4px;
    opacity: 0;
    border-bottom: none
}

.page-content .header-anchor:hover {
    opacity: 1;
    border-bottom: none
}

.page-content .header-anchor .anchor-icon {
    vertical-align: middle;
    fill: currentColor
}

.page-content .markdown-header:focus,
.page-content .markdown-header:hover {
    outline: none
}

.page-content .markdown-header:focus .header-anchor,
.page-content .markdown-header:hover .header-anchor {
    opacity: 1
}

.Main[data-v-2f23fdf8] {
    padding-left: var(--sidebar-width);
    padding-top: calc(var(--header-height) + 40px);
    padding-bottom: 2rem;
    background: var(--main-background)
}

@media screen and (max-width:768px) {
    .Main[data-v-2f23fdf8] {
        padding-left: 0
    }
}

@media print {
    .Main[data-v-2f23fdf8] {
        padding-left: 0;
        padding-top: 30px
    }
}

.Content[data-v-2f23fdf8] {
    padding: 0 20px 0 80px
}

@media screen and (max-width:768px) {
    .Content[data-v-2f23fdf8] {
        padding: 0 20px
    }
}

.layout-wide .Content[data-v-2f23fdf8] {
    max-width: 770px;
    margin: 0 auto;
    padding: 0 2.5rem
}

@media screen and (max-width:768px) {
    .layout-wide .Content[data-v-2f23fdf8] {
        max-width: 100%;
        padding: 0 20px
    }
}

@media print {
    .layout-wide .Content[data-v-2f23fdf8] {
        padding: 0
    }
}

.page-title[data-v-2f23fdf8] {
    font-size: 3rem;
    margin: 0 0 1.4rem;
    font-weight: 300;
    line-height: 1.1
}

.ImageZoom[data-v-f17c6258] {
    display: inline-block;
    font-size: 0
}

.with-border[data-v-f17c6258] {
    border: 1px solid var(--border-color);
    padding: 10px;
    border-radius: 4px
}

.badge {
    display: inline-block;
    vertical-align: top;
    font-size: 12px;
    height: 18px;
    line-height: 18px;
    border-radius: 9px;
    padding: 0 6px;
    color: #fff;
    margin-right: 5px;
    background: #666
}

.badge.is-tip {
    background: var(--tip-color)
}

.badge.is-warning {
    background: var(--warning-color)
}

.badge.is-danger {
    background: var(--danger-color)
}

.badge.is-success {
    background: var(--success-color)
}

.DocuteSelect[data-v-091fbc3d] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    display: inline-flex;
    height: var(--docute-select-height);
    outline: none;
    border: 1px solid var(--border-color);
    font-size: 12px;
    text-transform: uppercase;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    font-weight: 100;
    position: relative;
    overflow: hidden;
    transition: border .2s, background .2s, color .2s ease-out, box-shadow .2s ease;
    border-radius: 5px;
    white-space: nowrap;
    line-height: 0;
    width: 100%
}

.DocuteSelect[data-v-091fbc3d]:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, .1);
    border-color: #ddd
}

.DocuteSelect:hover .arrow[data-v-091fbc3d] {
    border-color: #ddd
}

.select[data-v-091fbc3d] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    height: 100%;
    border: none;
    box-shadow: none;
    background: transparent;
    background-image: none;
    line-height: var(--docute-select-height);
    font-size: 14px;
    margin-right: -20px;
    padding: 0 76px 0 16px;
    text-transform: none;
    width: 100%;
    outline: none;
    color: var(--text-color)
}

.arrow[data-v-091fbc3d] {
    color: var(--text-color);
    border-left: 1px solid var(--border-color);
    width: 40px;
    height: 100%;
    position: absolute;
    right: 0;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center
}

.note {
    line-height: 1;
    padding: 15px 20px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    margin: 20px 0
}

.note>:not(.note-label) {
    line-height: 1.7
}

.note>:first-child {
    margin-top: 0
}

.note>:last-child {
    margin-bottom: 0
}

.note.is-tip {
    border-color: var(--tip-color)
}

.note.is-tip .note-label {
    color: var(--tip-color)
}

.note.is-danger {
    border-color: var(--danger-color)
}

.note.is-danger .note-label {
    color: var(--danger-color)
}

.note.is-warning {
    border-color: var(--warning-color)
}

.note.is-warning .note-label {
    color: var(--warning-color)
}

.note.is-success {
    border-color: var(--success-color)
}

.note.is-success .note-label {
    color: var(--success-color)
}

.note-label {
    text-transform: uppercase;
    font-weight: 500;
    display: inline-block
}

.gist table td {
    border-bottom: none
}

.loading[data-v-4f620c69] {
    display: flex;
    align-items: center
}

.dots[data-v-4f620c69] {
    pointer-events: none;
    color: transparent;
    font-size: 0
}

.dots span[data-v-4f620c69] {
    -webkit-animation-name: blink-data-v-4f620c69;
    animation-name: blink-data-v-4f620c69;
    -webkit-animation-duration: 1.4s;
    animation-duration: 1.4s;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    width: 4px;
    height: 4px;
    background-color: #444;
    display: inline-block;
    border-radius: 50%;
    margin: 0 1px
}

.dots span[data-v-4f620c69]:nth-child(2) {
    -webkit-animation-delay: .2s;
    animation-delay: .2s
}

.dots span[data-v-4f620c69]:nth-child(3) {
    -webkit-animation-delay: .4s;
    animation-delay: .4s
}

.loading-text[data-v-4f620c69] {
    margin-left: 8px;
    font-size: 11px;
    opacity: .7
}

@-webkit-keyframes blink-data-v-4f620c69 {
    0% {
        opacity: .2
    }

    20% {
        opacity: 1
    }

    to {
        opacity: .2
    }
}

@keyframes blink-data-v-4f620c69 {
    0% {
        opacity: .2
    }

    20% {
        opacity: 1
    }

    to {
        opacity: .2
    }
}

.LanguageSelector[data-v-d5ba05fe],
.VersionsSelector[data-v-7fba7fb5] {
    padding: 0 20px;
    margin-top: 10px
}

.dark-theme-toggler[data-v-d69f2884] {
    display: flex;
    align-items: center;
    height: 100%
}

@media screen and (max-width:768px) {
    .dark-theme-toggler[data-v-d69f2884] {
        padding: 0 20px
    }
}

[data-position="sidebar:post-end"] .dark-theme-toggler[data-v-d69f2884] {
    padding: 0 20px
}

.toggle[data-v-d69f2884] {
    touch-action: pan-x;
    display: inline-block;
    position: relative;
    cursor: pointer;
    background-color: transparent;
    border: 0;
    padding: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent
}

.toggle-track[data-v-d69f2884] {
    width: 50px;
    height: 22px;
    padding: 0;
    border-radius: 30px;
    background-color: #0f1114;
    transition: all .2s ease
}

.toggle-track-check[data-v-d69f2884] {
    left: 5px;
    opacity: 0;
    transition: opacity .25s ease
}

.toggle-track-check[data-v-d69f2884],
.toggle-track-x[data-v-d69f2884] {
    position: absolute;
    width: 17px;
    height: 17px;
    top: 0;
    bottom: 0;
    margin-top: auto;
    margin-bottom: auto;
    line-height: 0
}

.toggle-track-x[data-v-d69f2884] {
    right: 5px
}

.toggle-thumb[data-v-d69f2884] {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: #fff;
    box-sizing: border-box;
    transition: all .5s cubic-bezier(.23, 1, .32, 1) 0ms;
    transform: translateX(0)
}

.checked .toggle-track-check[data-v-d69f2884],
.toggle-track-x[data-v-d69f2884] {
    opacity: 1;
    transition: opacity .25s ease
}

.checked .toggle-track-x[data-v-d69f2884] {
    opacity: 0
}

.checked .toggle-thumb[data-v-d69f2884] {
    transform: translateX(26px);
    border-color: #19ab27
}

.toggler-screen-reader-only[data-v-d69f2884] {
    width: 1px;
    height: 1px;
    clip: rect(0 0 0 0);
    position: absolute;
    overflow: hidden;
    border: none;
    margin: -1px
}

.search[data-v-026453b4] {
    display: flex;
    height: 100%;
    align-items: center;
    position: relative
}

.search.is-focused .search-icon[data-v-026453b4] {
    color: var(--search-focus-icon-color)
}

.search.is-focused .search-input-wrapper[data-v-026453b4] {
    border-color: var(--search-focus-border-color)
}

.search.is-focused .search-result[data-v-026453b4] {
    display: block
}

@media print {
    .search[data-v-026453b4] {
        display: none
    }
}

.search-input-wrapper[data-v-026453b4] {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 0;
    height: 50%;
    position: relative;
    width: 240px
}

@media (max-width:768px) {
    .search-input-wrapper[data-v-026453b4] {
        width: 28px;
        overflow: hidden
    }

    .is-focused .search-input-wrapper[data-v-026453b4] {
        width: 200px;
        overflow: visible;
        overflow: initial
    }
}

.search-icon[data-v-026453b4] {
    color: var(--search-icon-color);
    position: absolute;
    top: 50%;
    left: 7px;
    transform: translateY(-50%);
    margin-top: 2px
}

.search-input[data-v-026453b4] {
    border: none;
    outline: none;
    background: transparent;
    color: var(--text-color);
    position: absolute;
    padding: 0 8px 0 28px;
    width: 100%;
    height: 100%
}

.search-result[data-v-026453b4] {
    display: none;
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    width: 20rem;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, .12);
    z-index: 9999;
    background: var(--header-background)
}

.search-result-item[data-v-026453b4] {
    padding: 10px;
    display: block;
    background: var(--search-result-background)
}

.search-result-item[data-v-026453b4]:not(:last-child) {
    border-bottom: 1px solid var(--border-color)
}

.search-result-item[data-v-026453b4]:hover {
    background: var(--search-result-hover-background)
}

.item-title[data-v-026453b4] {
    font-size: 1.1rem;
    font-weight: 500;
    display: inline;
    line-height: 1
}

.item-desc[data-v-026453b4] {
    font-size: .875rem;
    margin-top: 10px
}

.item-label[data-v-026453b4] {
    border-radius: 4px;
    padding: 0 5px;
    height: 22px;
    display: inline-block;
    border: 1px solid var(--border-color);
    font-size: 13px;
    margin-left: 10px
}