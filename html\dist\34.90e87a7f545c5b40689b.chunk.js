webpackJsonp([34],{

/***/ 2830:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _util = __webpack_require__(16);

var _util2 = _interopRequireDefault(_util);

var _ModalBox = __webpack_require__(540);

var _ModalBox2 = _interopRequireDefault(_ModalBox);

var _index = __webpack_require__(210);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
	components: {
		ModalBox: _ModalBox2.default
	},
	data: function data() {
		var _this = this;

		return {
			noImgUrl: __webpack_require__(317),
			showModalBox: false,
			modelProps: {},
			bgid: '',
			addSitebox: false,
			editSitebox: false,
			tableHeight: 0,
			userList: [],
			deviceList: [],
			poolList: [],
			siteId: null,
			siteRowId: null,
			modalDelete: false,
			site: {
				id: '',
				name: '',
				user: '',
				device: '',
				pool: '',
				remark: ''
			},
			siterule: {
				name: [{ required: true, message: '请输入站点名称', trigger: 'blur' }],
				user: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择登陆用户',
					trigger: 'change'
				}],
				device: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择设备',
					trigger: 'change'
				}],
				pool: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择介质池',
					trigger: 'change'
				}]
			},
			columns: [{
				title: '站点ID',
				key: 'id'
			}, {
				title: '站点名称',
				key: 'name'
			}, {
				title: '登录用户',
				key: 'username'
			}, {
				title: '设备',
				key: 'devicename'
			}, {
				title: '介质池',
				key: 'poolname'
			}, {
				title: '备注',
				key: 'remark'
			}, {
				title: '操作',
				key: 'operation',
				render: function render(h, params) {
					return h('div', {
						'class': {
							role: true
						},
						style: {
							display: 'flex',
							justifyContent: 'center',
							alignItems: 'center'
						}
					}, [_this.hasPrivilege(_this.getPower.VRTS_FUNC_MODIFY_STATION) ? h('div', {
						style: {
							marginRight: '5px',
							width: '30px',
							height: '30px',
							borderRadius: '3px',
							textAlign: 'center'
						}
					}, [h('span', {
						'class': 'iconfont',
						style: {
							fontSize: '20px',
							color: '#3377ff'
						},
						domProps: {
							innerHTML: '&#xe63d;'
						},
						on: {
							click: function click() {
								_this.siteId = params.row.id;
								_this.editSitebox = true;

								_this.siteRowId = params.row.id;
								_this.site.id = params.row.id, _this.site.name = params.row.name, _this.site.user = params.row.user, _this.site.device = params.row.device, _this.site.pool = params.row.pool, _this.site.remark = params.row.remark;
							}
						},
						directives: [{
							name: 'tooltip',
							value: '修改'
						}]
					})]) : '', _this.hasPrivilege(_this.getPower.VRTS_FUNC_DELETE_STATION) ? h('div', {
						style: {
							fontSize: '22px',

							marginRight: '5px',
							width: '30px',
							height: '30px',
							borderRadius: '3px',
							textAlign: 'center'
						}
					}, [h('span', {
						'class': 'iconfont',
						style: {
							fontSize: '22px',
							color: '#f56c6c'
						},
						domProps: {
							innerHTML: '&#xe625;'
						},
						on: {
							click: function click() {
								_this.siteId = params.row.id;
								_this.showModalBox = true;
								_this.modelProps = {
									title: '删除站点',
									width: '540',
									messageValue: '确认要删除该站点',
									type: _index.MODEL_TYPE_LIST.warning.type,
									color: _index.MODEL_TYPE_LIST.warning.color,
									row: params.row
								};
							}
						},
						directives: [{
							name: 'tooltip',
							value: '删除'
						}]
					})]) : '']);
				}
			}],
			siteList: [],
			DataGuardId: null
		};
	},
	created: function created() {
		this.$store.dispatch('getPrivilege', this.$store.state.power.module.remCon);
		this.getUserList();
		this.getDeviceList();
		this.getPoolList();
		this.getSiteData();
	},
	mounted: function mounted() {
		window.addEventListener('resize', this.getTableHeight);
		this.getTableHeight();
	},

	computed: {
		navIcon: function navIcon() {
			return this.$store.state.index.siderNameIcon;
		},
		getPower: function getPower() {
			return this.$store.state.power.module;
		}
	},
	methods: {
		tishi: function tishi(currentRow, index) {
			if (currentRow.id == this.bgid) {
				return 'trbgshow';
			}
			return 'trbgshow_a';
		},
		addmodel: function addmodel() {
			this.addSitebox = true;
			this.site.name = '';
			this.site.user = '';
			this.site.device = '';
			this.site.pool = '';
			this.site.remark = '';
		},
		closeMode: function closeMode() {
			this.addSitebox = false;
			this.editSitebox = false;
		},
		getTableHeight: function getTableHeight() {
			this.tableHeight = window.innerHeight - 375;
		},
		getUserList: function getUserList() {
			_util2.default.restfullCall('rest-ful/v3.0/users', null, 'get', this.userData);
		},
		userData: function userData(res) {
			var array = [];
			for (var i = 0; i < res.data.data.length; i++) {
				array.push({
					name: res.data.data[i].name,
					id: Number(res.data.data[i].id)
				});
			}
			this.userList = array;
		},
		getDeviceList: function getDeviceList() {
			_util2.default.restfullCall('/rest-ful/v3.0/devices?type=0', null, 'get', this.deviceData);
		},
		deviceData: function deviceData(res) {
			var array = [];
			for (var i = 0; i < res.data.data.length; i++) {
				array.push({
					name: res.data.data[i].name,
					id: res.data.data[i].id
				});
			}
			this.deviceList = array;
		},
		getPoolList: function getPoolList() {
			_util2.default.restfullCall('/rest-ful/v3.0/volpools', null, 'get', this.poolData);
		},
		poolData: function poolData(res) {
			var array = [];
			for (var i = 0; i < res.data.data.length; i++) {
				array.push({
					name: res.data.data[i].name,
					id: res.data.data[i].id
				});
			}
			this.poolList = array;
		},
		getSiteData: function getSiteData() {
			_util2.default.restfullCall('/rest-ful/v3.0/disaster/stations', null, 'get', this.callbackSite);
		},
		callbackSite: function callbackSite(res) {
			this.siteList = res.data.data;
		},
		secUser: function secUser(id) {
			this.site.username = id;
		},
		secDevice: function secDevice(id) {},
		secPool: function secPool(id) {
			this.site.pool = id;
		},
		cancelSite: function cancelSite() {
			this.modalDelete = false;
		},
		deleteSite: function deleteSite() {
			_util2.default.restfullCall('/rest-ful/v3.0/disaster/station/' + this.siteId, null, 'delete', this.deleteData);
		},

		deleteData: function deleteData(data) {
			if (data.data.code == 0) {
				this.modalDelete = false;
				this.$Message.success('删除成功');
				this.site.name = '', this.site.user = '', this.site.device = '', this.site.pool = '', this.site.remark = '', this.getSiteData();
			} else {
				this.modalDelete = false;
				this.$Message.success('删除失败');
			}
		},
		addSite: function addSite() {
			var siteOjb = {
				name: this.site.name,
				user: this.site.user,
				device: this.site.device,
				pool: this.site.pool,
				remark: this.site.remark
			};
			if (this.site.name != '' && this.site.user != '' && this.site.user != undefined && this.site.device != '' && this.site.device != undefined && this.site.pool != '' && this.site.pool != undefined) {
				_util2.default.restfullCall('/rest-ful/v3.0/disaster/station', siteOjb, 'POST', this.callbackaddSite);

				this.site.name = '', this.site.user = '', this.site.device = '', this.site.pool = '', this.site.remark = '';
			} else {
				this.$Message.warning('请填写必填信息');
				this.site.name = '', this.site.user = '', this.site.device = '', this.site.pool = '', this.site.remark = '';
			}
		},
		callbackaddSite: function callbackaddSite(res) {
			if (res.data.code == 0) {
				this.$Message.success(res.data.message);
				this.getSiteData();
				this.site.name = '', this.site.user = '', this.site.device = '', this.site.pool = '', this.site.remark = '';
				this.addSitebox = false;
			} else {
				this.$Message.warning(res.data.message);
			}
		},
		getRowData: function getRowData(row) {
			this.siteRowId = row.id;
			this.site.id = row.id, this.bgid = row.id, this.site.name = row.name, this.site.user = row.user, this.site.device = row.device, this.site.pool = row.pool, this.site.remark = row.remark;
		},
		editSite: function editSite() {
			var modifySteOjb = {
				id: Number(this.site.id),
				name: this.site.name,
				user: this.site.user,
				device: this.site.device,
				pool: this.site.pool,
				remark: this.site.remark
			};
			_util2.default.restfullCall('/rest-ful/v3.0/disaster/station', modifySteOjb, 'PUT', this.editMessage);
		},
		editMessage: function editMessage(res) {
			if (res.data.code == 0) {
				this.$Message.success(res.data.message);
				this.getSiteData();

				this.site.name = '', this.site.user = '', this.site.device = '', this.site.pool = '', this.site.remark = '';
				this.editSitebox = false;
			} else {
				this.$Message.warning(res.data.message);
			}
		},
		cancelDataGuard: function cancelDataGuard() {},
		postStatus: function postStatus(type, params) {
			this.siteId = params.row.id;
			if (type === true) {
				this.deleteSite();
			}
		}
	}
};

/***/ }),

/***/ 3784:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3785);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("250aba20", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-5b33850a\",\"scoped\":false,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./siteMag.vue", function() {
     var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-5b33850a\",\"scoped\":false,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./siteMag.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3785:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.restore1{border:1px dashed #fb784d;padding:20px 20px 0;border-radius:5px;height:130px;margin-bottom:20px;min-width:1000px;background-color:#f3f3f3\n}\n.trbgshow{position:relative\n}\n.trbgshow,.trbgshow_a{cursor:pointer\n}\n.trbgshow td{background:transparent\n}\n.ivu-table-stripe .ivu-table-body tr.trbgshow td,.ivu-table-stripe .ivu-table-fixed-body tr.ivu-table-row-hover td,tr.ivu-table-row-hover td{background-color:transparent\n}", ""]);

// exports


/***/ }),

/***/ 3786:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3787);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("6c5c5cd8", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-5b33850a\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=1!./siteMag.vue", function() {
     var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-5b33850a\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=1!./siteMag.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3787:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.marBox[data-v-5b33850a]{margin:15px;overflow-y:auto\n}\n.restore1[data-v-5b33850a]{border:1px dashed #fb784d;padding:20px 20px 0;border-radius:5px;height:130px;margin-bottom:20px;min-width:1000px;background-color:#f3f3f3\n}", ""]);

// exports


/***/ }),

/***/ 3788:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", [_c("Row", [_c("Col", { staticClass: "breadstyle", attrs: { span: "24" } }, [_c("Breadcrumb", [_c("BreadcrumbItem", {
    staticClass: "breadlist",
    staticStyle: { display: "flex", "align-items": "center" }
  }, [_c("img", {
    staticStyle: {
      width: "1.2rem",
      height: "1.2rem",
      "margin-right": "0.3rem"
    },
    attrs: { src: __webpack_require__(318) }
  }), _vm._v(" "), _c("span", [_vm._v("站点容灾 / 站点管理")])])], 1), _vm._v(" "), _c("div", [this.hasPrivilege(_vm.getPower.VRTS_FUNC_ADD_STATION) ? _c("Button", {
    staticStyle: { "margin-right": "15px" },
    attrs: { type: "primary", icon: "md-add-circle" },
    on: { click: _vm.addmodel }
  }, [_vm._v("\n\t\t\t\t\t\t新建站点\n\t\t\t\t\t")]) : _vm._e()], 1)], 1)], 1), _vm._v(" "), _c("el-card", {
    staticClass: "marBox",
    staticStyle: { height: "calc(100vh - 145px)" }
  }, [_c("div", { staticStyle: { width: "100%" } }, [_c("Table", {
    staticClass: "rowTable auto-column-size-table",
    attrs: {
      border: "",
      columns: _vm.columns,
      data: _vm.siteList,
      "row-class-name": _vm.tishi,
      height: _vm.tableHeight,
      "no-data-text": "<div class='no-img-url-text'><img class='noImgUrl' src=" + _vm.noImgUrl + " /><div class='text'>暂无数据</div><div>"
    },
    on: { "on-row-click": _vm.getRowData }
  })], 1)]), _vm._v(" "), _c("Modal", {
    attrs: { closable: false, "footer-hide": true, width: "540" },
    model: {
      value: _vm.addSitebox,
      callback: function callback($$v) {
        _vm.addSitebox = $$v;
      },
      expression: "addSitebox"
    }
  }, [_c("div", { staticClass: "addPolicyModeStyle" }, [_c("h3", [_vm._v("添加站点")]), _vm._v(" "), _c("span", { staticClass: "iconfont", on: { click: _vm.closeMode } }, [_vm._v("")])]), _vm._v(" "), _c("div", {
    staticClass: "modeContent",
    staticStyle: { "margin-left": "20px" }
  }, [_c("Form", {
    ref: "site",
    attrs: {
      model: _vm.site,
      rules: _vm.siterule,
      "label-width": 80
    }
  }, [_c("div", [_c("FormItem", { attrs: { label: "站点名称", prop: "name" } }, [_c("Input", {
    attrs: { clearable: "", placeholder: "站点名称" },
    model: {
      value: _vm.site.name,
      callback: function callback($$v) {
        _vm.$set(_vm.site, "name", $$v);
      },
      expression: "site.name"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "登录用户", prop: "user" } }, [_c("Select", {
    attrs: { clearable: "" },
    on: { "on-change": _vm.secUser },
    model: {
      value: _vm.site.user,
      callback: function callback($$v) {
        _vm.$set(_vm.site, "user", $$v);
      },
      expression: "site.user"
    }
  }, _vm._l(_vm.userList, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.id, label: item.name }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "设备", prop: "device" } }, [_c("Select", {
    attrs: { clearable: "" },
    on: { "on-change": _vm.secDevice },
    model: {
      value: _vm.site.device,
      callback: function callback($$v) {
        _vm.$set(_vm.site, "device", $$v);
      },
      expression: "site.device"
    }
  }, _vm._l(_vm.deviceList, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.id, label: item.name }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "介质池", prop: "pool" } }, [_c("Select", {
    attrs: { clearable: "" },
    on: { "on-change": _vm.secPool },
    model: {
      value: _vm.site.pool,
      callback: function callback($$v) {
        _vm.$set(_vm.site, "pool", $$v);
      },
      expression: "site.pool"
    }
  }, _vm._l(_vm.poolList, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.id, label: item.name }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "备注" } }, [_c("Input", {
    attrs: { placeholder: "备注", type: "textarea" },
    model: {
      value: _vm.site.remark,
      callback: function callback($$v) {
        _vm.$set(_vm.site, "remark", $$v);
      },
      expression: "site.remark"
    }
  })], 1)], 1)])], 1), _vm._v(" "), _c("div", { staticClass: "modefooter", staticStyle: { width: "100%" } }, [_c("vr-button", {
    on: { onCancel: _vm.closeMode, onConfirm: _vm.addSite }
  })], 1)]), _vm._v(" "), _c("Modal", {
    attrs: { closable: false, "footer-hide": true, width: "540" },
    model: {
      value: _vm.editSitebox,
      callback: function callback($$v) {
        _vm.editSitebox = $$v;
      },
      expression: "editSitebox"
    }
  }, [_c("div", { staticClass: "addPolicyModeStyle" }, [_c("h3", [_vm._v("修改站点")]), _vm._v(" "), _c("span", { staticClass: "iconfont", on: { click: _vm.closeMode } }, [_vm._v("")])]), _vm._v(" "), _c("div", {
    staticClass: "modeContent",
    staticStyle: { "margin-left": "20px" }
  }, [_c("Form", {
    ref: "site",
    attrs: {
      model: _vm.site,
      rules: _vm.siterule,
      "label-width": 80
    }
  }, [_c("div", [_c("FormItem", { attrs: { label: "站点名称", prop: "name" } }, [_c("Input", {
    attrs: { clearable: "", placeholder: "站点名称" },
    model: {
      value: _vm.site.name,
      callback: function callback($$v) {
        _vm.$set(_vm.site, "name", $$v);
      },
      expression: "site.name"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "登录用户", prop: "user" } }, [_c("Select", {
    attrs: { clearable: "" },
    on: { "on-change": _vm.secUser },
    model: {
      value: _vm.site.user,
      callback: function callback($$v) {
        _vm.$set(_vm.site, "user", $$v);
      },
      expression: "site.user"
    }
  }, _vm._l(_vm.userList, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.id, label: item.name }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "设备", prop: "device" } }, [_c("Select", {
    attrs: { clearable: "" },
    on: { "on-change": _vm.secDevice },
    model: {
      value: _vm.site.device,
      callback: function callback($$v) {
        _vm.$set(_vm.site, "device", $$v);
      },
      expression: "site.device"
    }
  }, _vm._l(_vm.deviceList, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.id, label: item.name }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "介质池", prop: "pool" } }, [_c("Select", {
    attrs: { clearable: "" },
    on: { "on-change": _vm.secPool },
    model: {
      value: _vm.site.pool,
      callback: function callback($$v) {
        _vm.$set(_vm.site, "pool", $$v);
      },
      expression: "site.pool"
    }
  }, _vm._l(_vm.poolList, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.id, label: item.name }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "备注" } }, [_c("Input", {
    attrs: { placeholder: "备注" },
    model: {
      value: _vm.site.remark,
      callback: function callback($$v) {
        _vm.$set(_vm.site, "remark", $$v);
      },
      expression: "site.remark"
    }
  })], 1)], 1)])], 1), _vm._v(" "), _c("div", { staticClass: "modefooter", staticStyle: { width: "100%" } }, [_c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { size: "large", icon: "md-close-circle" },
    on: { click: _vm.closeMode }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      size: "large",
      icon: "md-checkmark-circle"
    },
    on: { click: _vm.editSite }
  }, [_vm._v("确定")])], 1)]), _vm._v(" "), _vm.showModalBox ? _c("ModalBox", {
    attrs: { modelProps: _vm.modelProps },
    on: { postStatus: _vm.postStatus },
    model: {
      value: _vm.showModalBox,
      callback: function callback($$v) {
        _vm.showModalBox = $$v;
      },
      expression: "showModalBox"
    }
  }) : _vm._e()], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-5b33850a", esExports);
  }
}

/***/ }),

/***/ 587:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_siteMag_vue__ = __webpack_require__(2830);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_siteMag_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_siteMag_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_siteMag_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_siteMag_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_5b33850a_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_siteMag_vue__ = __webpack_require__(3788);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_5b33850a_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_siteMag_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_5b33850a_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_siteMag_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3784)
  __webpack_require__(3786)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = "data-v-5b33850a"
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_siteMag_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_5b33850a_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_siteMag_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/remoteControl/siteMag.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-5b33850a", Component.options)
  } else {
    hotAPI.reload("data-v-5b33850a", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ })

});