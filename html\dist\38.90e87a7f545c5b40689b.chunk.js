webpackJsonp([38],{

/***/ 2647:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _regenerator = __webpack_require__(537);

var _regenerator2 = _interopRequireDefault(_regenerator);

var _asyncToGenerator2 = __webpack_require__(538);

var _asyncToGenerator3 = _interopRequireDefault(_asyncToGenerator2);

var _ModalBox = __webpack_require__(540);

var _ModalBox2 = _interopRequireDefault(_ModalBox);

var _index = __webpack_require__(210);

var _util = __webpack_require__(16);

var _util2 = _interopRequireDefault(_util);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
	components: {
		ModalBox: _ModalBox2.default
	},
	data: function data() {
		return {
			showModalBox: false,
			tableHeight: 0,
			changArr: [],
			modelProps: {},
			messagesData: [],
			messagesCol: [{
				type: 'selection',
				width: 60,
				align: 'center'
			}, {
				title: 'ID',
				key: 'id'
			}, {
				title: '描述',
				key: 'desc'
			}, {
				title: '来源',
				key: 'src'
			}, {
				title: '时间',
				key: 'time'
			}]
		};
	},
	created: function created() {},
	updated: function updated() {},
	mounted: function mounted() {
		this.$route.query.key;

		this.getmessdata();
		this.getTableHeight();
	},

	methods: {
		tishi: function tishi(currentRow, index) {
			if (currentRow.id == this.bgid) {
				return 'trbgshow';
			}
			return 'trbgshow_a';
		},
		getTableHeight: function getTableHeight() {
			this.tableHeight = window.innerHeight - 300;
		},
		postStatus: function postStatus(val) {
			if (val) {
				_util2.default.restfullCall('/rest-ful/v3.0/message/confirm/all', null, 'post', this.cleanMess);
			}
		},
		cleanOk: function () {
			var _ref = (0, _asyncToGenerator3.default)(_regenerator2.default.mark(function _callee() {
				return _regenerator2.default.wrap(function _callee$(_context) {
					while (1) {
						switch (_context.prev = _context.next) {
							case 0:
								this.modelProps = {
									title: '清空全部消息',
									width: '540',
									messageValue: _index.MODEL_TYPE_LIST.warning.name,
									type: _index.MODEL_TYPE_LIST.warning.type,
									color: _index.MODEL_TYPE_LIST.warning.color,
									messagesText: '：确认全部清空吗？'
								};
								this.showModalBox = true;

							case 2:
							case 'end':
								return _context.stop();
						}
					}
				}, _callee, this);
			}));

			function cleanOk() {
				return _ref.apply(this, arguments);
			}

			return cleanOk;
		}(),
		cleanMess: function cleanMess(res) {
			if (res.data.code == 0) {
				this.$Message.success('确认所有的消息成功');
				this.getmessdata();
				this.getmesgData();
			} else {
				this.$Message.warning('确认所有的消息失败');
			}
		},
		selChange: function selChange(res) {
			var arr1 = [];
			res.forEach(function (item) {
				arr1.push(item.id);
			});
			this.changArr = arr1;
		},
		getmessdata: function getmessdata() {
			_util2.default.restfullCall('/rest-ful/v3.0/homepage/messages', null, 'get', this.callbackMessage);
		},
		callbackMessage: function callbackMessage(res) {
			var _this = this;

			if (res.status == 200) {
				this.getmesgData();

				this.$nextTick(function () {
					_this.messagesData = res.data;
				});
			} else {}
		},
		messOk: function () {
			var _ref2 = (0, _asyncToGenerator3.default)(_regenerator2.default.mark(function _callee2() {
				return _regenerator2.default.wrap(function _callee2$(_context2) {
					while (1) {
						switch (_context2.prev = _context2.next) {
							case 0:
								if (this.changArr.length) {
									_context2.next = 3;
									break;
								}

								this.$Message.warning('请选择确认要删除的数据');
								return _context2.abrupt('return');

							case 3:
								_util2.default.restfullCall('/rest-ful/v3.0/message/confirm', this.changArr, 'POST', this.confirmMessage);

							case 4:
							case 'end':
								return _context2.stop();
						}
					}
				}, _callee2, this);
			}));

			function messOk() {
				return _ref2.apply(this, arguments);
			}

			return messOk;
		}(),
		confirmMessage: function confirmMessage(res) {
			if (res.data.code == 0) {
				this.$Message.success('确认成功');
				this.getmessdata();
				this.getmesgData();
				this.changArr = [];
			} else {
				this.$Message.warning('确认失败');
			}
		},
		fanhui: function fanhui() {
			this.$router.push({
				path: 'home'
			});
		},
		getmesgData: function getmesgData() {
			_util2.default.restfullCall('/rest-ful/v3.0/homepage/statistics', null, 'get', this.statisticsInfo);
		},
		statisticsInfo: function statisticsInfo(res) {
			this.messages = res.data.messages;
			this.$bus.$emit('symessages', this.messages);

			localStorage.setItem('messcont', this.messages);
		},
		onRowClick: function onRowClick(row) {}
	}
};

/***/ }),

/***/ 3038:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3039);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("13172b96", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-cfd23586\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./messages.vue", function() {
     var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-cfd23586\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./messages.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3039:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.marBox[data-v-cfd23586]{margin:0 15px 20px\n}\n.mbxBox[data-v-cfd23586]{width:100%;background:#f2f2f2;line-height:2.5rem;margin-bottom:15px\n}", ""]);

// exports


/***/ }),

/***/ 3040:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", [_c("Row", [_c("Col", { staticClass: "breadstyle", attrs: { span: "24" } }, [_c("Breadcrumb", [_c("BreadcrumbItem", {
    staticClass: "breadlist",
    staticStyle: { display: "flex", "align-items": "center" },
    attrs: { to: "/" }
  }, [_c("img", {
    staticStyle: {
      width: "1.2rem",
      height: "1.2rem",
      "margin-right": "0.3rem"
    },
    attrs: { src: __webpack_require__(318) }
  }), _vm._v(" "), _c("span", [_vm._v("消息提醒")])])], 1)], 1)], 1), _vm._v(" "), _c("Row", [_c("Col", { attrs: { span: "24" } }, [_c("el-card", { staticClass: "searchBox" }, [_c("div", {
    staticStyle: {
      display: "flex",
      "justify-content": "space-between",
      "align-items": "center"
    }
  }, [_c("div", {
    staticStyle: {
      display: "flex",
      "justify-content": "space-between"
    }
  }, [_c("Button", {
    staticStyle: { "margin-right": "15px" },
    attrs: { type: "primary", icon: "md-trash" },
    on: { click: _vm.cleanOk }
  }, [_vm._v("清空")]), _vm._v(" "), _c("Button", {
    staticStyle: { "margin-right": "15px" },
    attrs: {
      type: "primary",
      icon: "md-checkmark-circle"
    },
    on: { click: _vm.messOk }
  }, [_vm._v("确认")]), _vm._v(" "), _c("Button", {
    staticStyle: { "margin-right": "15px" },
    attrs: { type: "primary", icon: "ios-undo" },
    on: { click: _vm.fanhui }
  }, [_vm._v("返回")])], 1), _vm._v(" "), _c("div")])])], 1)], 1), _vm._v(" "), _c("el-card", {
    staticClass: "marBox",
    staticStyle: { height: "calc(100vh - 250px)" }
  }, [_c("Table", {
    ref: "exp",
    attrs: {
      data: _vm.messagesData,
      columns: _vm.messagesCol,
      "row-class-name": _vm.tishi,
      height: _vm.tableHeight
    },
    on: {
      "on-row-dblclick": _vm.onRowClick,
      "on-selection-change": _vm.selChange
    }
  })], 1), _vm._v(" "), _c("ModalBox", {
    attrs: { modelProps: _vm.modelProps },
    on: { postStatus: _vm.postStatus },
    model: {
      value: _vm.showModalBox,
      callback: function callback($$v) {
        _vm.showModalBox = $$v;
      },
      expression: "showModalBox"
    }
  })], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-cfd23586", esExports);
  }
}

/***/ }),

/***/ 567:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_messages_vue__ = __webpack_require__(2647);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_messages_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_messages_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_messages_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_messages_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_cfd23586_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_messages_vue__ = __webpack_require__(3040);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_cfd23586_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_messages_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_cfd23586_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_messages_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3038)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = "data-v-cfd23586"
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_messages_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_cfd23586_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_messages_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/navigation/messages.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-cfd23586", Component.options)
  } else {
    hotAPI.reload("data-v-cfd23586", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ })

});