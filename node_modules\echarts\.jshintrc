{"bitwise": false, "camelcase": true, "curly": true, "eqeqeq": false, "forin": false, "immed": true, "latedef": false, "newcap": true, "noarg": false, "noempty": true, "nonew": true, "plusplus": false, "quotmark": "single", "regexp": false, "undef": true, "unused": "vars", "strict": false, "trailing": false, "maxparams": 20, "maxdepth": 6, "maxlen": 200, "asi": false, "boss": false, "debug": false, "eqnull": true, "esversion": 6, "module": true, "evil": true, "expr": true, "funcscope": false, "globalstrict": false, "iterator": false, "lastsemic": false, "laxbreak": true, "laxcomma": false, "loopfunc": false, "multistr": false, "onecase": false, "proto": false, "regexdash": false, "scripturl": false, "smarttabs": false, "shadow": true, "sub": true, "supernew": false, "validthis": true, "browser": true, "couch": false, "devel": true, "dojo": false, "jquery": true, "mootools": false, "node": false, "nonstandard": false, "prototypejs": false, "rhino": false, "wsh": false, "nomen": false, "onevar": false, "passfail": false, "white": false, "predef": ["global"]}