<style scoped lang='less'>
/* @import './style/blackindex.css'; */
/* @import './style/lightindex.css'; */
/deep/ .ivu-spin-dot {
	background-color: #fe6902;
}
</style>

<template>
	<div>
		<router-view></router-view>
		<div id="docute"></div>
		<button id="docuteButton" @click="goBack">回到主页</button>

		<!-- <button  @click="changeTheme">切换bg</button> -->
	</div>
</template>

<script>
// import {Douctes} from './press/script.js'
import { myfun } from './press/test';
//  var vuedom = document.getElementsByClassName("layout");
// var docute = document.getElementById("docute");

//       var state = false;
//       function threeFn() {
//         if (!state) {
//           docute.style.display = "block";
//           vuedom[0].style.display = "none";
//           // location.assign("/index.html#/");x
//           // location.href = "/index.html#/"
//           // window.location.href = "/index.html#/"
//           state = true;
//         } else {
//           docute.style.display = "none";
//           vuedom[0].style.display = "block";
//           // window.location.href = "#/system"
//           state = false;

//
//         }
//
//       }
//       window.addEventListener(
//         "popstate",
//         function(e) {
//           docute.style.display = "none";
//           vuedom[0].style.display = "block";
//           state = false;
//
//
//         },
//         false
//       );
// document.body.onselectstart = function() {
//   return false;
// };
//
import util from './libs/util.js';
export default {
	data() {
		return {
			messageDataNum: null
		};
	},
	mounted() {
		myfun();
		this.getmesgData();
		this.messageInterval(); //5秒请求一次信息激励

		// let link = document.createElement('link')
		// link.type = 'text/css'
		// link.id = 'theme'
		// link.rel = 'stylesheet'
		// link.href = './style/lightindex.css'
		// document.getElementsByTagName('head')[0].appendChild(link)

		//
	},
	//   created() {
	//     window.addEventListener('beforeunload', this.beforeWindowClose)
	//   },
	destroyed() {
		// window.removeEventListener('beforeunload', this.onWindowClose)
		window.removeEventListener('unload', this.onWindowClose);
	},
	methods: {
		
		//5秒请求一次信息激励
		messageInterval() {
			this.messageDataNum = setInterval(() => {
				//定时5秒查询一次消息信息
				this.getmesgData();
			}, 5000);
		},
		//清除定时器
		clearMessageInterval() {
			clearInterval(this.messageDataNum);
		},
		// changeTheme(){
		//   document.getElementById('theme').href = './style/blackindex.css'
		//
		// },

		handleSpinCustom() {
			this.$Spin.show({
				render: h => {
					return h('div', [
						h('Icon', {
							class: 'demo-spin-icon-load',
							props: {
								type: 'ios-loading',
								size: 18
							}
						}),
						h('div', 'Loading')
					]);
				}
			});
			setTimeout(() => {
				this.$Spin.hide();
			}, 2000);
		},

		getmesgData() {
			//首页统计信息
			util.restfullCall('/rest-ful/v3.0/homepage/statistics', null, 'get', this.statisticsInfo);
			$.fn.zTree.init($('#treeshow'), this.setting, null);
		},
		statisticsInfo(res) {
			// this.messages = res.data.messages //消息提醒
			this.$bus.$emit('symessages', res.data.data.messages);
			this.$store.commit('messagesData', res.data.data.messages);
			localStorage.setItem('messcont', res.data.data.messages);
		},
		goBack() {
			window.threeFn(true);
		},
		onWindowClose() {
			// 处理需要做的逻辑
		}
	}
};
</script>
