webpackJsonp([33],{

/***/ 2812:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _util = __webpack_require__(16);

var _util2 = _interopRequireDefault(_util);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
	name: 'serverMag',
	data: function data() {
		var _this = this;

		return {
			noImgUrl: __webpack_require__(317),
			number: '',
			currentPage: 1,
			pageSize: 10,
			bgid: '',
			stop: false,
			start: false,
			restart: false,
			serverColumns: [{
				title: 'ip地址',
				key: 'address'
			}, {
				title: '服务类型',
				key: 'srvname'
			}, {
				title: '状态',
				key: 'status'
			}, {
				title: '健康状态',
				key: 'health',
				render: function render(h, params) {
					if (params.row.health == 0) {
						return h('div', {
							style: {
								marginRight: '5px',

								width: '30px',
								height: '30px',
								borderRadius: '3px',
								textAlign: 'center',
								margin: '10px auto'
							}
						}, [h('span', {
							style: {
								color: 'red',
								marginRight: '5px',

								width: '30px',
								height: '30px',
								borderRadius: '3px',
								textAlign: 'center',

								display: 'flex',
								justifyContent: 'center',
								alignItems: 'center',
								marginTop: '-3px'
							}
						}, '异常')]);
					} else {
						return h('div', {
							style: {
								marginRight: '5px',

								width: '30px',
								height: '30px',
								borderRadius: '3px',
								textAlign: 'center',

								margin: '1px auto'
							}
						}, [h('span', {
							style: {
								color: '#42bd21'
							}
						}, '正常')]);
					}
				}
			}, {
				title: '操作',
				width: 150,
				key: 'operation',
				render: function render(h, params) {
					if (params.row.status == '运行') {
						return h('div', {
							'class': {
								role: true
							},
							style: {
								display: 'flex',
								justifyContent: 'center',
								alignItems: 'center'
							}
						}, [_this.hasPrivilege(_this.getPower.VRTS_FUNC_OPT_SERVER) ? h('div', {
							style: {
								marginRight: '5px',

								width: '30px',
								height: '30px',
								borderRadius: '3px',
								textAlign: 'center'
							}
						}, [h('span', {
							'class': 'iconfont',
							style: {
								fontSize: '22px',
								color: '#ff9130'
							},
							domProps: {
								innerHTML: '&#xe616;'
							},
							on: {
								click: function click() {
									_this.restart = true;
								}
							},
							directives: [{
								name: 'tooltip',
								value: '重启'
							}]
						})]) : '', _this.hasPrivilege(_this.getPower.VRTS_FUNC_OPT_SERVER) ? h('div', {
							style: {
								marginRight: '5px',

								width: '30px',
								height: '30px',
								borderRadius: '3px',
								textAlign: 'center'
							}
						}, [h('span', {
							'class': 'iconfont',
							style: {
								fontSize: '22px',
								color: '#c60b0b'
							},
							domProps: {
								innerHTML: '&#xe679;'
							},
							on: {
								click: function click() {
									_this.stop = true;
								}
							},
							directives: [{
								name: 'tooltip',
								value: '停止'
							}]
						})]) : '', _this.hasPrivilege(_this.getPower.VRTS_FUNC_OPT_SERVER) ? h('div', {
							style: {
								marginRight: '5px',

								width: '30px',
								height: '30px',
								borderRadius: '3px',
								textAlign: 'center'
							}
						}, [h('span', {
							'class': 'iconfont',
							style: {
								fontSize: '22px',
								color: '#625f5f'
							},
							domProps: {
								innerHTML: '&#xe663;'
							},
							on: {
								click: function click() {}
							},
							directives: [{
								name: 'tooltip',
								value: '已禁止操作'
							}]
						})]) : '']);
					} else if (params.row.status == '停止') {
						return h('div', {
							'class': {
								role: true
							},
							style: {
								display: 'flex',
								justifyContent: 'center',
								alignItems: 'center'
							}
						}, [_this.hasPrivilege(_this.getPower.VRTS_FUNC_OPT_SERVER) ? h('div', {
							style: {
								marginRight: '5px',

								width: '30px',
								height: '30px',
								borderRadius: '3px',
								textAlign: 'center'
							}
						}, [h('span', {
							'class': 'iconfont',
							style: {
								fontSize: '22px',
								color: '#ff9130'
							},
							domProps: {
								innerHTML: '&#xe616;'
							},
							on: {
								click: function click() {
									_this.restart = true;
								}
							},
							directives: [{
								name: 'tooltip',
								value: '重启'
							}]
						})]) : '', _this.hasPrivilege(_this.getPower.VRTS_FUNC_OPT_SERVER) ? h('div', {
							style: {
								marginRight: '5px',

								width: '30px',
								height: '30px',
								borderRadius: '3px',
								textAlign: 'center'
							}
						}, [h('span', {
							'class': 'iconfont',
							style: {
								fontSize: '22px',
								color: '#c60b0b'
							},
							domProps: {
								innerHTML: '&#xe679;'
							},
							on: {
								click: function click() {}
							},
							directives: [{
								name: 'tooltip',
								value: '停止'
							}]
						})]) : '', _this.hasPrivilege(_this.getPower.VRTS_FUNC_OPT_SERVER) ? h('div', {
							style: {
								marginRight: '5px',

								width: '30px',
								height: '30px',
								borderRadius: '3px',
								textAlign: 'center'
							}
						}, [h('span', {
							'class': 'iconfont',
							style: {
								fontSize: '22px',
								color: '#009a2b'
							},
							domProps: {
								innerHTML: '&#xe621;'
							},
							on: {
								click: function click() {
									_this.start = true;
								}
							},
							directives: [{
								name: 'tooltip',
								value: '启动'
							}]
						})]) : '']);
					}
				}
			}],
			serverList: [],
			address: '',
			srvtype: '',
			curentPage: 1
		};
	},
	created: function created() {
		this.getserdata();
	},
	mounted: function mounted() {
		window.addEventListener('resize', this.getTableHeight);
		this.getTableHeight();
	},

	computed: {
		navIcon: function navIcon() {
			return this.$store.state.index.siderNameIcon;
		},
		getPower: function getPower() {
			return this.$store.state.power.module;
		}
	},
	methods: {
		getTableHeight: function getTableHeight() {
			this.tableHeight = window.innerHeight - 205;
		},
		handleSpinCustom: function handleSpinCustom() {
			var _this2 = this;

			this.$Spin.show({
				render: function render(h) {
					return h('div', [h('Icon', {
						'class': 'demo-spin-icon-load',
						props: {
							type: 'ios-loading',
							size: 18
						}
					}), h('div', 'Loading')]);
				}
			});
			setTimeout(function () {
				_this2.$Spin.hide();
			}, 3000);
		},
		handleSizeChange: function handleSizeChange(val) {
			this.pageSize = val;

			_util2.default.restfullCall('/rest-ful/v3.0/services?' + 'pageno=' + this.curentPage + '&nums=' + val, null, 'get', this.servicesData);
		},
		changePage: function changePage(index) {
			this.curentPage = index;
			_util2.default.restfullCall('/rest-ful/v3.0/services?' + 'pageno=' + index + '&nums=' + this.pageSize, null, 'get', this.servicesData);
		},
		tishi: function tishi(currentRow, index) {
			if (currentRow.id == this.bgid) {
				return 'trbgshow';
			}
			return 'trbgshow_a';
		},
		shuaxin: function shuaxin() {
			this.getserdata();
			this.$Message.success('刷新成功');
		},
		getserdata: function getserdata() {
			_util2.default.restfullCall('/rest-ful/v3.0/services?' + 'pageno=' + 1 + '&nums=' + 10, null, 'get', this.servicesData);
		},
		servicesData: function servicesData(res) {
			if (res.data.code == 0) {
				var array = [];
				for (var i = 0; i < res.data.data.services.length; i++) {
					array.push({
						address: res.data.data.services[i].address,
						srvtype: res.data.data.services[i].srvtype,
						srvname: res.data.data.services[i].srvname,
						status: res.data.data.services[i].status,
						health: res.data.data.services[i].health,
						id: i
					});
				}
				this.serverList = array;
				this.number = res.data.data.totals;
				this.curentPage = res.data.data.page_no + 1;
			} else {
				this.handleSpinCustom();
			}
		},
		getRowData: function getRowData(row) {
			this.address = row.address;
			this.srvtype = row.srvtype;
			this.bgid = row.id;
		},
		onstop: function onstop() {
			_util2.default.restfullCall('/rest-ful/v3.0/service/operate?address=' + this.address + '&srvType=' + this.srvtype + '&op=' + 2, null, 'get', this.callbackData);
		},
		onstart: function onstart() {
			_util2.default.restfullCall('/rest-ful/v3.0/service/operate?address=' + this.address + '&srvType=' + this.srvtype + '&op=' + 1, null, 'get', this.callbackData);
		},
		onrestart: function onrestart() {
			_util2.default.restfullCall('/rest-ful/v3.0/service/operate?address=' + this.address + '&srvType=' + this.srvtype + '&op=' + 3, null, 'get', this.callbackData);
		},
		callbackData: function callbackData(res) {
			var _this3 = this;

			this.restart = false;
			this.stop = false;
			this.start = false;
			if (res.data.code == 0) {
				this.handleSpinCustom();
				setTimeout(function () {
					_util2.default.restfullCall('/rest-ful/v3.0/services?' + 'pageno=' + 1 + '&nums=' + 10, null, 'get', _this3.servicesData);
					_this3.$Message.success('操作成功');
				}, 5000);
			} else {
				this.$Message.success(res.data.message);
			}
		},
		cancel: function cancel() {
			this.start = false;
			this.restart = false;
			this.stop = false;
		}
	}
};

/***/ }),

/***/ 3714:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3715);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("3bff8ebe", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-5a86c827\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./serverMag.vue", function() {
     var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-5a86c827\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./serverMag.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3715:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.trbgshow_a[data-v-5a86c827]{cursor:pointer\n}\n.trbgshow td[data-v-5a86c827]{background:transparent\n}\n.ivu-table-stripe .ivu-table-body tr.trbgshow td[data-v-5a86c827],.ivu-table-stripe .ivu-table-fixed-body tr.ivu-table-row-hover td[data-v-5a86c827],tr.ivu-table-row-hover td[data-v-5a86c827]{background-color:transparent\n}\n.demo-spin-col .circular[data-v-5a86c827]{width:25px;height:25px\n}\n.demo-spin-icon-load[data-v-5a86c827]{-webkit-animation:a-data-v-5a86c827 1s linear infinite;animation:a-data-v-5a86c827 1s linear infinite\n}\n@-webkit-keyframes a-data-v-5a86c827{\n0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)\n}\n50%{-webkit-transform:rotate(180deg);transform:rotate(180deg)\n}\nto{-webkit-transform:rotate(1turn);transform:rotate(1turn)\n}\n}\n@keyframes a-data-v-5a86c827{\n0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)\n}\n50%{-webkit-transform:rotate(180deg);transform:rotate(180deg)\n}\nto{-webkit-transform:rotate(1turn);transform:rotate(1turn)\n}\n}\n.demo-spin-col[data-v-5a86c827]{height:100px;position:relative;border:1px solid #eee\n}\n.page-box[data-v-5a86c827],.page-wrap[data-v-5a86c827]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:end;-ms-flex-pack:end;justify-content:flex-end\n}\n.page-wrap[data-v-5a86c827]{position:absolute;bottom:32px;right:35px\n}", ""]);

// exports


/***/ }),

/***/ 3716:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3717);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("079441a2", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-5a86c827\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=1!./serverMag.vue", function() {
     var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-5a86c827\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=1!./serverMag.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3717:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.marBox[data-v-5a86c827]{margin:15px;overflow-y:auto\n}\n.mbxBox[data-v-5a86c827]{width:100%;line-height:2.5rem;margin-bottom:15px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:start;-ms-flex-pack:start;justify-content:flex-start;-webkit-box-align:center;-ms-flex-align:center;align-items:center\n}", ""]);

// exports


/***/ }),

/***/ 3718:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", [_c("Row", [_c("Col", { staticClass: "breadstyle", attrs: { span: "24" } }, [_c("Breadcrumb", [_c("BreadcrumbItem", {
    staticClass: "breadlist",
    staticStyle: { display: "flex", "align-items": "center" }
  }, [_c("img", {
    staticStyle: {
      width: "1.2rem",
      height: "1.2rem",
      "margin-right": "0.3rem"
    },
    attrs: { src: __webpack_require__(318) }
  }), _vm._v(" "), _c("span", [_vm._v("系统管理 / 系统服务")])])], 1), _vm._v(" "), _c("Button", {
    staticStyle: { "margin-right": "15px" },
    attrs: { type: "primary" },
    on: { click: _vm.shuaxin }
  }, [_c("i", { staticClass: "iconfont" }, [_vm._v("")]), _vm._v(" 刷新")])], 1)], 1), _vm._v(" "), _c("el-card", {
    staticClass: "marBox",
    staticStyle: { height: "calc(100vh - 145px)", overflow: "hidden" }
  }, [_c("div", { staticStyle: { width: "100%" } }, [_c("Table", {
    attrs: {
      columns: _vm.serverColumns,
      data: _vm.serverList,
      "row-class-name": _vm.tishi,
      height: _vm.tableHeight,
      "no-data-text": "<div class='no-img-url-text'><img class='noImgUrl' src=" + _vm.noImgUrl + " /><div class='text'>暂无数据</div><div>"
    },
    on: { "on-row-click": _vm.getRowData }
  }), _vm._v(" "), _c("div", { staticClass: "page-box" }, [_c("el-pagination", {
    staticClass: "page-wrap",
    attrs: {
      background: "",
      "current-page": _vm.curentPage,
      "page-sizes": [10, 20, 30, 40],
      "page-size": 10,
      layout: "total, sizes, prev, pager, next, jumper",
      total: _vm.number
    },
    on: {
      "size-change": _vm.handleSizeChange,
      "current-change": _vm.changePage
    }
  })], 1)], 1), _vm._v(" "), _c("Modal", {
    attrs: { closable: false, "footer-hide": true, width: "540" },
    model: {
      value: _vm.stop,
      callback: function callback($$v) {
        _vm.stop = $$v;
      },
      expression: "stop"
    }
  }, [_c("div", { staticClass: "addPolicyModeStyle" }, [_c("h3", [_vm._v("停止")]), _vm._v(" "), _c("span", { staticClass: "iconfont", on: { click: _vm.cancel } }, [_vm._v("")])]), _vm._v(" "), _c("div", {
    staticClass: "modeContent",
    staticStyle: { "margin-left": "20px" }
  }, [_c("p", { staticStyle: { color: "#f60" } }, [_c("span", { staticClass: "iconfont" }, [_vm._v("")]), _vm._v(" "), _c("span", [_vm._v("确定停止操作吗？")])])]), _vm._v(" "), _c("div", { staticClass: "modefooter", staticStyle: { width: "100%" } }, [_c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { size: "large", icon: "md-close-circle" },
    on: { click: _vm.cancel }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      size: "large",
      icon: "md-checkmark-circle"
    },
    on: { click: _vm.onstop }
  }, [_vm._v("确定")])], 1)]), _vm._v(" "), _c("Modal", {
    attrs: { closable: false, "footer-hide": true, width: "540" },
    model: {
      value: _vm.start,
      callback: function callback($$v) {
        _vm.start = $$v;
      },
      expression: "start"
    }
  }, [_c("div", { staticClass: "addPolicyModeStyle" }, [_c("h3", [_vm._v("启动")]), _vm._v(" "), _c("span", { staticClass: "iconfont", on: { click: _vm.cancel } }, [_vm._v("")])]), _vm._v(" "), _c("div", {
    staticClass: "modeContent",
    staticStyle: { "margin-left": "20px" }
  }, [_c("p", { staticStyle: { color: "#f60" } }, [_c("span", { staticClass: "iconfont" }, [_vm._v("")]), _vm._v(" "), _c("span", [_vm._v("确定启动操作吗？")])])]), _vm._v(" "), _c("div", { staticClass: "modefooter", staticStyle: { width: "100%" } }, [_c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { size: "large", icon: "md-close-circle" },
    on: { click: _vm.cancel }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      size: "large",
      icon: "md-checkmark-circle"
    },
    on: { click: _vm.onstart }
  }, [_vm._v("确定")])], 1)]), _vm._v(" "), _c("Modal", {
    attrs: { closable: false, "footer-hide": true, width: "540" },
    model: {
      value: _vm.restart,
      callback: function callback($$v) {
        _vm.restart = $$v;
      },
      expression: "restart"
    }
  }, [_c("div", { staticClass: "addPolicyModeStyle" }, [_c("h3", [_vm._v("重启")]), _vm._v(" "), _c("span", { staticClass: "iconfont", on: { click: _vm.cancel } }, [_vm._v("")])]), _vm._v(" "), _c("div", {
    staticClass: "modeContent",
    staticStyle: { "margin-left": "20px" }
  }, [_c("p", { staticStyle: { color: "#f60" } }, [_c("span", { staticClass: "iconfont" }, [_vm._v("")]), _vm._v(" "), _c("span", [_vm._v("确定重启操作吗？")])])]), _vm._v(" "), _c("div", { staticClass: "modefooter", staticStyle: { width: "100%" } }, [_c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { size: "large", icon: "md-close-circle" },
    on: { click: _vm.cancel }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      size: "large",
      icon: "md-checkmark-circle"
    },
    on: { click: _vm.onrestart }
  }, [_vm._v("确定")])], 1)])], 1)], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-5a86c827", esExports);
  }
}

/***/ }),

/***/ 582:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_serverMag_vue__ = __webpack_require__(2812);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_serverMag_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_serverMag_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_serverMag_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_serverMag_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_5a86c827_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_serverMag_vue__ = __webpack_require__(3718);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_5a86c827_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_serverMag_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_5a86c827_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_serverMag_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3714)
  __webpack_require__(3716)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = "data-v-5a86c827"
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_serverMag_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_5a86c827_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_serverMag_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/serverMag/serverMag.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-5a86c827", Component.options)
  } else {
    hotAPI.reload("data-v-5a86c827", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ })

});