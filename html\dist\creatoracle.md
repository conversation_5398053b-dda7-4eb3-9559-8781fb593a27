#	创建Oracle数据库备份策略
 
-	点击左侧策略管理，进入策略管理界面；
-	在策略管理界面，点击右上角的“新建策略”，进入“新建策略”界面；
    ![创建备份策略](/dist/img/4-4-01.png)
 
-	在新建策略的基本信息界面，填写策略名称，选择策略类型、储存设备、介质池、优先级、策略最大调度任务、压缩级别、启用加密等选项；
-	在策略类型中，选择ORACLE备份；
-	储存设备选择“THEVRTS”；
-	下一步，点击右侧的备份资源列表，将进入备份资源列表界面；
    ![创建备份策略](/dist/img/4-4-02.png)
 
-	在备份资源列表界面，选择要备份的服务器机器名；
-	下一步，在该机器名下选择要备份的资源；
-	下一步，点击右侧的备份选项，进入备份选项界面；
    ![创建备份策略](/dist/img/4-4-03.png)
 
-	在备份选项界面有指定脚本备份、备份选项、归档备份范围、配置通道个数、指定（filesperset参数）、启动ORACLE压缩等选项； 
-	下一步，点击右侧调度计划，进入调度计划界面；
    ![创建备份策略](/dist/img/4-4-04.png)
 
-	在调度计划界面，调度类型有三种分别时“日期”、“周”、“时间间隔”；
-	备份类型分为全量备份、增量备份、差量备份、日志备份；
-	创建调度计划，选择时间间隔、全量备份、开始日期、结束日期、间隔多少小时；
-	下一步，点击添加计划，该备份计划添加完成；
    ![创建备份策略](/dist/img/4-4-05.png)