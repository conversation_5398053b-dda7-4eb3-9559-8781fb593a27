webpackJsonp([35],{

/***/ 2811:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _util = __webpack_require__(16);

var _util2 = _interopRequireDefault(_util);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
	data: function data() {
		var _this = this;

		return {
			tableHeight: 0,
			userList: [],
			deviceList: [],
			poolList: [],
			siteId: null,
			siteRowId: null,
			modalDelete: false,
			modalDeleteGrard: false,
			site: {
				id: '',
				name: '',
				user: '',
				device: '',
				pool: '',
				remark: ''
			},
			siterule: {
				name: [{ required: true, message: '请输入站点名称', trigger: 'blur' }],
				user: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择登陆用户',
					trigger: 'change'
				}],
				device: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择设备',
					trigger: 'change'
				}],
				pool: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择介质池',
					trigger: 'change'
				}]
			},
			DataGuard: {
				id: null,
				name: '',
				user: '',
				password: '',
				address: '',
				remark: '',
				port: ''
			},
			DataGuardrule: {
				name: [{ required: true, message: '请输入站点名称', trigger: 'blur' }],
				user: [{ required: true, message: '请输入登录用户', trigger: 'blur' }],
				password: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],

				address: [{ required: true, message: '请输入访问地址', trigger: 'blur' }]
			},

			columns: [{
				title: '站点ID',
				key: 'id'
			}, {
				title: '站点名称',
				key: 'name'
			}, {
				title: '登录用户',
				key: 'username'
			}, {
				title: '设备',
				key: 'devicename'
			}, {
				title: '介质池',
				key: 'poolname'
			}, {
				title: '备注',
				key: 'remark'
			}, {
				title: '操作',
				key: 'operation',
				render: function render(h, params) {
					return h('div', {
						'class': {
							role: true
						},
						style: {
							display: 'flex',
							justifyContent: 'center',
							alignItems: 'center'
						}
					}, [h('div', {
						style: {
							fontSize: '18px',

							marginRight: '5px',
							width: '30px',
							height: '30px',
							borderRadius: '3px',
							textAlign: 'center',
							lineHeight: '30px'
						}
					}, [h('span', {
						'class': 'iconfont',
						style: {
							fontSize: '18px',
							color: '#625f5f'
						},
						domProps: {
							innerHTML: '&#xe61c;'
						},
						on: {
							click: function click() {
								_this.siteId = params.row.id;
								_this.modalDelete = true;
							}
						},
						directives: [{
							name: 'tooltip',
							value: '删除'
						}]
					})])]);
				}
			}],

			siteList: [],
			dataGuardList: [],
			DataGuardId: null,

			DataGuardCol: [{
				title: '站点ID',
				key: 'id'
			}, {
				title: '站点名称',
				key: 'name'
			}, {
				title: '登录用户',
				key: 'user'
			}, {
				title: '访问地址',
				key: 'address'
			}, {
				title: '索引服务端口号',
				key: 'port'
			}, {
				title: '备注',
				key: 'remark'
			}, {
				title: '操作',
				key: 'operation',
				render: function render(h, params) {
					return h('div', {
						'class': {
							role: true
						},
						style: {
							display: 'flex',
							justifyContent: 'center',
							alignItems: 'center'
						}
					}, [h('div', {
						style: {
							fontSize: '18px',

							marginRight: '5px',
							width: '30px',
							height: '30px',
							borderRadius: '3px',
							textAlign: 'center',
							lineHeight: '30px'
						}
					}, [h('span', {
						'class': 'iconfont',
						style: {
							fontSize: '18px',
							color: '#625f5f'
						},
						domProps: {
							innerHTML: '&#xe61c;'
						},
						on: {
							click: function click() {
								_this.DataGuardId = params.row.id;
								_this.modalDeleteGrard = true;
							}
						},
						directives: [{
							name: 'tooltip',
							value: '删除'
						}]
					})])]);
				}
			}]
		};
	},
	created: function created() {
		this.$store.dispatch('getPrivilege', this.$store.state.power.module.remCon);

		this.getUserList();
		this.getDeviceList();
		this.getPoolList();

		this.getSiteData();
		this.getDataGuard();
	},
	mounted: function mounted() {
		window.addEventListener('resize', this.getTableHeight);
		this.getTableHeight();
	},

	computed: {
		navIcon: function navIcon() {
			return this.$store.state.index.siderNameIcon;
		}
	},
	methods: {
		getTableHeight: function getTableHeight() {
			this.tableHeight = window.innerHeight - 325;
		},
		togettable: function togettable(val) {
			if (val == 0) {
				this.getSiteData();
			}
			if (val == 1) {
				this.getDataGuard();
			}
		},
		getUserList: function getUserList() {
			_util2.default.restfullCall('rest-ful/v3.0/users', null, 'get', this.userData);
		},
		userData: function userData(res) {
			var array = [];
			for (var i = 0; i < res.data.length; i++) {
				array.push({
					name: res.data[i].name,
					id: Number(res.data[i].id)
				});
			}
			this.userList = array;
		},
		getDeviceList: function getDeviceList() {
			_util2.default.restfullCall('/rest-ful/v3.0/devices?type=0', null, 'get', this.deviceData);
		},
		deviceData: function deviceData(res) {
			var array = [];
			for (var i = 0; i < res.data.length; i++) {
				array.push({
					name: res.data[i].name,
					id: res.data[i].id
				});
			}
			this.deviceList = array;
		},
		getPoolList: function getPoolList() {
			_util2.default.restfullCall('/rest-ful/v3.0/volpools', null, 'get', this.poolData);
		},
		poolData: function poolData(res) {
			var array = [];
			for (var i = 0; i < res.data.data.length; i++) {
				array.push({
					name: res.data.data[i].name,
					id: res.data.data[i].id
				});
			}
			this.poolList = array;
		},
		getSiteData: function getSiteData() {
			_util2.default.restfullCall('/rest-ful/v3.0/disaster/stations', null, 'get', this.callbackSite);
		},
		callbackSite: function callbackSite(res) {
			this.siteList = res.data.data;
		},
		getDataGuard: function getDataGuard() {
			_util2.default.restfullCall('/rest-ful/v3.0/remote/stations', null, 'get', this.callbackDataGuard);
		},
		callbackDataGuard: function callbackDataGuard(res) {
			this.dataGuardList = res.data.data;
		},
		secUser: function secUser(id) {
			this.site.username = id;
		},
		secDevice: function secDevice(id) {},
		secPool: function secPool(id) {
			this.site.pool = id;
		},
		cancelSite: function cancelSite() {
			this.modalDelete = false;
		},
		deleteSite: function deleteSite() {
			_util2.default.restfullCall('/rest-ful/v3.0/disaster/station/' + this.siteId, null, 'delete', this.deleteData);
		},

		deleteData: function deleteData(data) {
			if (data.data.code == 0) {
				this.modalDelete = false;
				this.$Message.success('删除成功');
				this.site.name = '', this.site.user = '', this.site.device = '', this.site.pool = '', this.site.remark = '', this.getSiteData();
			} else {
				this.modalDelete = false;
				this.$Message.success('删除失败');
			}
		},
		addSite: function addSite() {
			var siteOjb = {
				name: this.site.name,
				user: this.site.user,
				device: this.site.device,
				pool: this.site.pool,
				remark: this.site.remark
			};

			if (this.site.name != '' && this.site.user != '' && this.site.user != undefined && this.site.device != '' && this.site.device != undefined && this.site.pool != '' && this.site.pool != undefined) {
				_util2.default.restfullCall('/rest-ful/v3.0/disaster/station', siteOjb, 'POST', this.callbackaddSite);

				this.site.name = '', this.site.user = '', this.site.device = '', this.site.pool = '', this.site.remark = '';
			} else {
				this.$Message.warning('请填写必填信息');
				this.site.name = '', this.site.user = '', this.site.device = '', this.site.pool = '', this.site.remark = '';
			}
		},
		callbackaddSite: function callbackaddSite(res) {
			if (res.data.code == 0) {
				this.$Message.success(res.data.message);
				this.getSiteData();
				this.site.name = '', this.site.user = '', this.site.device = '', this.site.pool = '', this.site.remark = '';
			} else {
				this.$Message.warning(res.data.message);
			}
		},
		getRowData: function getRowData(row) {
			this.siteRowId = row.id;
			this.site.id = row.id, this.site.name = row.name, this.site.user = row.user, this.site.device = row.device, this.site.pool = row.pool, this.site.remark = row.remark;
		},
		submitSite: function submitSite() {
			var modifySteOjb = {
				id: Number(this.site.id),
				name: this.site.name,
				user: this.site.user,
				device: this.site.device,
				pool: this.site.pool,
				remark: this.site.remark
			};
			_util2.default.restfullCall('/rest-ful/v3.0/disaster/station', modifySteOjb, 'PUT', this.editMessage);
		},
		editMessage: function editMessage(res) {
			if (res.data.code == 0) {
				this.$Message.success(res.data.message);
				this.getSiteData();

				this.site.name = '', this.site.user = '', this.site.device = '', this.site.pool = '', this.site.remark = '';
			} else {
				this.$Message.warning(res.data.message);
			}
		},
		addDataGuard: function addDataGuard() {
			var dataGuardOjb = {
				name: this.DataGuard.name,
				user: this.DataGuard.user,
				password: this.DataGuard.password,
				address: this.DataGuard.address,
				remark: this.DataGuard.remark,
				port: Number(this.DataGuard.port)
			};

			if (this.DataGuard.name != '' && this.DataGuard.user != '' && this.DataGuard.password != '' && this.DataGuard.address != '') {
				_util2.default.restfullCall('/rest-ful/v3.0/remote/station', dataGuardOjb, 'POST', this.callbackMessage);

				this.DataGuard.name = '';
				this.DataGuard.user = '';
				this.DataGuard.password = '';
				this.DataGuard.address = '';
				this.DataGuard.remark = '';
				this.DataGuard.port = '';
			} else {
				this.$Message.warning('请填写必填信息');
			}
		},
		callbackMessage: function callbackMessage(res) {
			if (res.data.code == 0) {
				this.$Message.success(res.data.message);
				this.getDataGuard();
			} else {
				this.$Message.warning(res.data.message);
			}
		},
		getRowDataGuard: function getRowDataGuard(row) {
			this.siteRowId = row.id;
			this.DataGuard.id = row.id, this.DataGuard.name = row.name, this.DataGuard.user = row.user, this.DataGuard.password = row.password, this.DataGuard.address = row.address, this.DataGuard.remark = row.remark, this.DataGuard.port = row.port;
		},
		submitDataGuard: function submitDataGuard() {
			var modifyDataGuard = {
				id: Number(this.DataGuard.id),
				name: this.DataGuard.name,
				user: this.DataGuard.user,
				password: this.DataGuard.password,
				address: this.DataGuard.address,
				remark: this.DataGuard.remark,
				port: Number(this.DataGuard.port)
			};
			if (this.DataGuard.name != '' && this.DataGuard.user != '' && this.DataGuard.password != '' && this.DataGuard.address != '') {
				_util2.default.restfullCall('/rest-ful/v3.0/remote/station', modifyDataGuard, 'PUT', this.editDataGuard);
			} else {
				this.$Message.warning('请填写必填信息');
			}
		},
		editDataGuard: function editDataGuard(res) {
			if (res.data.code == 0) {
				this.$Message.success(res.data.message);
				this.getDataGuard();

				this.DataGuard.name = '';
				this.DataGuard.user = '';
				this.DataGuard.password = '';
				this.DataGuard.address = '';
				this.DataGuard.remark = '';
				this.DataGuard.port = '';
			} else {
				this.$Message.warning(res.data.message);
			}
		},
		deleteDataGuard: function deleteDataGuard() {
			_util2.default.restfullCall('/rest-ful/v3.0/remote/station/' + this.DataGuardId, null, 'delete', this.delDataGuard);
		},
		delDataGuard: function delDataGuard(res) {
			if (res.data.code == 0) {
				this.$Message.success(res.data.message);
				this.modalDeleteGrard = false;
				this.getDataGuard();
			} else {
				this.$Message.warning(res.data.message);
			}
		},
		cancelDataGuard: function cancelDataGuard() {
			this.modalDeleteGrard = false;
		}
	}
};

/***/ }),

/***/ 3709:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3710);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("2a7b8dd8", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-11cac8b5\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./remoteControl.vue", function() {
     var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-11cac8b5\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./remoteControl.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3710:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.marBox[data-v-11cac8b5]{margin:15px;overflow-y:auto\n}\n.restore1[data-v-11cac8b5]{border:1px dashed #fb784d;padding:20px 20px 0;border-radius:5px;height:130px;margin-bottom:20px;min-width:1000px;background-color:#f3f3f3\n}", ""]);

// exports


/***/ }),

/***/ 3711:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3712);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("c93fb4a8", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-11cac8b5\",\"scoped\":false,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=1!./remoteControl.vue", function() {
     var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-11cac8b5\",\"scoped\":false,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=1!./remoteControl.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3712:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "", ""]);

// exports


/***/ }),

/***/ 3713:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", [_c("el-card", {
    staticClass: "marBox",
    staticStyle: { height: "calc(100vh - 120px)", margin: "15px" }
  }, [_c("Tabs", {
    attrs: { type: "card", animated: false },
    on: { "on-click": _vm.togettable }
  }, [_c("TabPane", {
    staticStyle: { position: "relative" },
    attrs: { label: "站点管理" }
  }, [_c("div", { staticClass: "dqchange dqchange1" }), _vm._v(" "), _c("div", { staticClass: "restore1" }, [_c("Form", {
    ref: "site",
    staticStyle: {
      width: "100%",
      display: "flex",
      "justify-content": "space-between"
    },
    attrs: {
      model: _vm.site,
      rules: _vm.siterule,
      "label-width": 80,
      inline: ""
    }
  }, [_c("div", [_c("div", {
    staticStyle: {
      width: "100%",
      display: "flex",
      "justify-content": "flex-start"
    }
  }, [_c("FormItem", {
    attrs: { label: "站点名称", prop: "name" }
  }, [_c("Input", {
    attrs: {
      clearable: "",
      placeholder: "站点名称"
    },
    model: {
      value: _vm.site.name,
      callback: function callback($$v) {
        _vm.$set(_vm.site, "name", $$v);
      },
      expression: "site.name"
    }
  })], 1), _vm._v(" "), _c("FormItem", {
    attrs: { label: "登录用户", prop: "user" }
  }, [_c("Select", {
    staticStyle: { width: "160px" },
    attrs: { clearable: "" },
    on: { "on-change": _vm.secUser },
    model: {
      value: _vm.site.user,
      callback: function callback($$v) {
        _vm.$set(_vm.site, "user", $$v);
      },
      expression: "site.user"
    }
  }, _vm._l(_vm.userList, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: {
        value: item.id,
        label: item.name
      }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "设备", prop: "device" } }, [_c("Select", {
    staticStyle: { width: "160px" },
    attrs: { clearable: "" },
    on: { "on-change": _vm.secDevice },
    model: {
      value: _vm.site.device,
      callback: function callback($$v) {
        _vm.$set(_vm.site, "device", $$v);
      },
      expression: "site.device"
    }
  }, _vm._l(_vm.deviceList, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: {
        value: item.id,
        label: item.name
      }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "介质池", prop: "pool" } }, [_c("Select", {
    staticStyle: { width: "160px" },
    attrs: { clearable: "" },
    on: { "on-change": _vm.secPool },
    model: {
      value: _vm.site.pool,
      callback: function callback($$v) {
        _vm.$set(_vm.site, "pool", $$v);
      },
      expression: "site.pool"
    }
  }, _vm._l(_vm.poolList, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: {
        value: item.id,
        label: item.name
      }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1)], 1), _vm._v(" "), _c("div", {
    staticStyle: {
      width: "100%",
      display: "flex",
      "justify-content": "flex-start"
    }
  }, [_c("div", [_c("FormItem", { attrs: { label: "备注" } }, [_c("Input", {
    staticStyle: { width: "370px" },
    attrs: { placeholder: "备注" },
    model: {
      value: _vm.site.remark,
      callback: function callback($$v) {
        _vm.$set(_vm.site, "remark", $$v);
      },
      expression: "site.remark"
    }
  })], 1)], 1), _vm._v(" "), _c("div", [_c("Button", {
    attrs: { type: "primary" },
    on: { click: _vm.addSite }
  }, [_vm._v("添加")]), _vm._v(" "), _c("Button", {
    attrs: { type: "primary" },
    on: { click: _vm.submitSite }
  }, [_vm._v("确定")])], 1)])])])], 1), _vm._v(" "), _c("div", { staticStyle: { width: "100%" } }, [_c("Table", {
    staticClass: "rowTable auto-column-size-table",
    attrs: {
      border: "",
      columns: _vm.columns,
      data: _vm.siteList,
      height: _vm.tableHeight
    },
    on: { "on-row-click": _vm.getRowData }
  })], 1)]), _vm._v(" "), _c("TabPane", {
    staticStyle: { position: "relative" },
    attrs: { label: "容灾管理" }
  }, [_c("div", { staticClass: "dqchange dqchange2" }), _vm._v(" "), _c("div", { staticClass: "restore1" }, [_c("Form", {
    ref: "DataGuard",
    staticStyle: {
      display: "flex",
      "justify-content": "flex-start"
    },
    attrs: {
      model: _vm.DataGuard,
      rules: _vm.DataGuardrule,
      "label-width": 80,
      inline: ""
    }
  }, [_c("div", { staticStyle: { width: "100%" } }, [_c("div", {
    staticStyle: {
      width: "100%",
      display: "flex",
      "justify-content": "flex-start"
    }
  }, [_c("FormItem", {
    attrs: { label: "站点名称", prop: "name" }
  }, [_c("Input", {
    attrs: { placeholder: "站点名称" },
    model: {
      value: _vm.DataGuard.name,
      callback: function callback($$v) {
        _vm.$set(_vm.DataGuard, "name", $$v);
      },
      expression: "DataGuard.name"
    }
  })], 1), _vm._v(" "), _c("FormItem", {
    staticStyle: {
      width: "300px",
      "margin-left": "-60px"
    },
    attrs: { label: "", prop: "port" }
  }, [_c("div", {
    staticStyle: {
      display: "flex",
      "justify-content": "center",
      "align-items": "center",
      "margin-left": "0px"
    }
  }, [_c("span", { staticStyle: { width: "140px" } }, [_vm._v("索引服务端口号")]), _vm._v(" "), _c("Input", {
    attrs: {
      placeholder: "索引服务端口号"
    },
    model: {
      value: _vm.DataGuard.port,
      callback: function callback($$v) {
        _vm.$set(_vm.DataGuard, "port", $$v);
      },
      expression: "DataGuard.port"
    }
  })], 1)]), _vm._v(" "), _c("FormItem", {
    attrs: { label: "登录用户", prop: "user" }
  }, [_c("Input", {
    attrs: { placeholder: "登录用户" },
    model: {
      value: _vm.DataGuard.user,
      callback: function callback($$v) {
        _vm.$set(_vm.DataGuard, "user", $$v);
      },
      expression: "DataGuard.user"
    }
  })], 1), _vm._v(" "), _c("FormItem", {
    attrs: {
      label: "登录密码",
      prop: "password"
    }
  }, [_c("Input", {
    attrs: {
      type: "password",
      placeholder: "登录密码"
    },
    model: {
      value: _vm.DataGuard.password,
      callback: function callback($$v) {
        _vm.$set(_vm.DataGuard, "password", $$v);
      },
      expression: "DataGuard.password"
    }
  })], 1), _vm._v(" "), _c("FormItem", {
    attrs: {
      label: "访问地址",
      prop: "address"
    }
  }, [_c("Input", {
    attrs: { placeholder: "访问地址" },
    model: {
      value: _vm.DataGuard.address,
      callback: function callback($$v) {
        _vm.$set(_vm.DataGuard, "address", $$v);
      },
      expression: "DataGuard.address"
    }
  })], 1)], 1), _vm._v(" "), _c("div", {
    staticStyle: {
      width: "100%",
      display: "flex",
      "justify-content": "flex-start"
    }
  }, [_c("div", [_c("FormItem", { attrs: { label: "备注" } }, [_c("Input", {
    staticStyle: { width: "400px" },
    attrs: { placeholder: "备注" },
    model: {
      value: _vm.DataGuard.remark,
      callback: function callback($$v) {
        _vm.$set(_vm.DataGuard, "remark", $$v);
      },
      expression: "DataGuard.remark"
    }
  })], 1)], 1), _vm._v(" "), _c("div", [_c("Button", {
    attrs: { type: "primary" },
    on: { click: _vm.addDataGuard }
  }, [_vm._v("添加")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      icon: "md-checkmark-circle"
    },
    on: { click: _vm.submitDataGuard }
  }, [_vm._v("确定")])], 1)])])])], 1), _vm._v(" "), _c("div", { staticStyle: { width: "100%" } }, [_c("Table", {
    staticClass: "rowTable auto-column-size-table",
    attrs: {
      border: "",
      columns: _vm.DataGuardCol,
      data: _vm.dataGuardList,
      height: _vm.tableHeight
    },
    on: { "on-row-click": _vm.getRowDataGuard }
  })], 1)])], 1), _vm._v(" "), _c("Modal", {
    attrs: {
      "ok-text": "确认删除",
      "cancel-text": "取消",
      closable: false,
      "class-name": "vertical-center-modal"
    },
    model: {
      value: _vm.modalDelete,
      callback: function callback($$v) {
        _vm.modalDelete = $$v;
      },
      expression: "modalDelete"
    }
  }, [_c("p", { attrs: { slot: "header" }, slot: "header" }, [_vm._v("删除该站点")]), _vm._v(" "), _c("div", { staticStyle: { "margin-left": "20px" } }, [_c("p", { staticStyle: { color: "#f60" } }, [_c("Icon", { attrs: { type: "information-circled" } }), _vm._v(" "), _c("span", [_vm._v("确认是否删除该站点")])], 1)]), _vm._v(" "), _c("div", { attrs: { slot: "footer" }, slot: "footer" }, [_c("Button", {
    attrs: { icon: "md-close-circle" },
    on: { click: _vm.cancelSite }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: { type: "warning", icon: "md-checkmark-circle" },
    on: { click: _vm.deleteSite }
  }, [_vm._v("确定")])], 1)]), _vm._v(" "), _c("Modal", {
    attrs: {
      "ok-text": "确认删除",
      "cancel-text": "取消",
      closable: false,
      "class-name": "vertical-center-modal"
    },
    model: {
      value: _vm.modalDeleteGrard,
      callback: function callback($$v) {
        _vm.modalDeleteGrard = $$v;
      },
      expression: "modalDeleteGrard"
    }
  }, [_c("p", { attrs: { slot: "header" }, slot: "header" }, [_vm._v("删除该站点")]), _vm._v(" "), _c("div", { staticStyle: { "margin-left": "20px" } }, [_c("p", { staticStyle: { color: "#f60" } }, [_c("Icon", { attrs: { type: "information-circled" } }), _vm._v(" "), _c("span", [_vm._v("确认是否删除该站点")])], 1)]), _vm._v(" "), _c("div", { attrs: { slot: "footer" }, slot: "footer" }, [_c("Button", {
    attrs: { icon: "md-close-circle" },
    on: { click: _vm.cancelDataGuard }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: { type: "warning", icon: "md-checkmark-circle" },
    on: { click: _vm.deleteDataGuard }
  }, [_vm._v("确定")])], 1)])], 1)], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-11cac8b5", esExports);
  }
}

/***/ }),

/***/ 581:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_remoteControl_vue__ = __webpack_require__(2811);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_remoteControl_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_remoteControl_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_remoteControl_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_remoteControl_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_11cac8b5_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_remoteControl_vue__ = __webpack_require__(3713);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_11cac8b5_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_remoteControl_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_11cac8b5_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_remoteControl_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3709)
  __webpack_require__(3711)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = "data-v-11cac8b5"
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_remoteControl_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_11cac8b5_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_remoteControl_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/remoteControl/remoteControl.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-11cac8b5", Component.options)
  } else {
    hotAPI.reload("data-v-11cac8b5", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ })

});