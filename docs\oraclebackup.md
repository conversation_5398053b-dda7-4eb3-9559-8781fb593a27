# Oracle数据库的备份与恢复
##	Oracle数据库手动备份
-	点击左侧策略管理，进入策略管理界面；
-	在策略管理界面，在要手动发起的备份策略右边，点击全量备份、增量备份、差量备份等；

    ![MySQL数据库的备份与恢复](/dist/img/5-4-1-01.png)
 
##	Oracle数据库本地恢复
-	在客户端进入THE VRTS安装目录；
-	添加环境变量export VRTS_MASTER=xxxx.xxxx.xxx.xxx；
-	执行source profile.vrts；
-	执行./VRTSOracleBackup –l查看备份信息；
 
    ![MySQL数据库的备份与恢复](/dist/img/5-4-2-01.png)
 
-	进入Oracle数据库，调整Oracle为MOUNTED模式；
-	通过“select status from v$instance”确认当前模式为MOUNTED;
    ![MySQL数据库的备份与恢复](/dist/img/5-4-2-02.png)
 
-	使用rman target / 进入rman模式；
-	在RMAN模式下执行下列命令恢复Oracle数据库数据；
    ![MySQL数据库的备份与恢复](/dist/img/5-4-2-03.png)
 
-	点击左侧报表管理，在报表管理界面，可以看到恢复的任务状态，是否成功；
    ![MySQL数据库的备份与恢复](/dist/img/5-4-2-04.png)
 
##	Oracle数据库重定向恢复
-	执行./VRTSOracleBackup –l查看备份信息；
    ![MySQL数据库的备份与恢复](/dist/img/5-4-3-01.png)
 
-	进入Oracle数据库，调整Oracle为NOMOUNT模式；
    ![MySQL数据库的备份与恢复](/dist/img/5-4-3-02.png)
 
-	使用rman target / 进入rman模式；
-	在RMAN模式下执行下列命令恢复Oracle数据库控制文件；
    ![MySQL数据库的备份与恢复](/dist/img/5-4-3-03.png)
 
-	通过export ORACLE_SID=orcl命令更改数据库实例命；
    ![MySQL数据库的备份与恢复](/dist/img/5-4-3-04.png)
 
 
-	进入Oracle安装目录，到了log所在目录，删除Oracle归档日志；
    ![MySQL数据库的备份与恢复](/dist/img/5-4-3-05.png)
 
-	通过sqlplus / as sysdba进入Oracle数据库；
-	执行startup mount 更改Oracle数据库模式为MOUNT；
 
    ![MySQL数据库的备份与恢复](/dist/img/5-4-3-06.png)
 
-	通过rman target /命令进入rman模式；
-	执行下列命令，重定向恢复数据库数据；
    ![MySQL数据库的备份与恢复](/dist/img/5-4-3-07.png)
 
-	点击左侧报表管理，在报表管理界面，可以看到恢复的任务状态，是否成功；
    ![MySQL数据库的备份与恢复](/dist/img/5-4-3-08.png)
