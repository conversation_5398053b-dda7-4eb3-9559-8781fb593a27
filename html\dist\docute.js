!function (e, t) { "object" === typeof exports && "object" === typeof module ? module.exports = t() : "function" === typeof define && define.amd ? define([], t) : "object" === typeof exports ? exports.Docute = t() : e.Docute = t() }(this, (function () { return function (e) { var t = {}; function n(r) { if (t[r]) return t[r].exports; var i = t[r] = { i: r, l: !1, exports: {} }; return e[r].call(i.exports, i, i.exports, n), i.l = !0, i.exports } return n.m = e, n.c = t, n.d = function (e, t, r) { n.o(e, t) || Object.defineProperty(e, t, { enumerable: !0, get: r }) }, n.r = function (e) { "undefined" !== typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(e, "__esModule", { value: !0 }) }, n.t = function (e, t) { if (1 & t && (e = n(e)), 8 & t) return e; if (4 & t && "object" === typeof e && e && e.__esModule) return e; var r = Object.create(null); if (n.r(r), Object.defineProperty(r, "default", { enumerable: !0, value: e }), 2 & t && "string" != typeof e) for (var i in e) n.d(r, i, function (t) { return e[t] }.bind(null, i)); return r }, n.n = function (e) { var t = e && e.__esModule ? function () { return e.default } : function () { return e }; return n.d(t, "a", t), t }, n.o = function (e, t) { return Object.prototype.hasOwnProperty.call(e, t) }, n.p = "/", n(n.s = 32) }([function (e, t, n) { "use strict"; (function (e, n) { var r = Object.freeze({}); function i(e) { return void 0 === e || null === e } function o(e) { return void 0 !== e && null !== e } function a(e) { return !0 === e } function s(e) { return "string" === typeof e || "number" === typeof e || "symbol" === typeof e || "boolean" === typeof e } function c(e) { return null !== e && "object" === typeof e } var u = Object.prototype.toString; function l(e) { return "[object Object]" === u.call(e) } function f(e) { return "[object RegExp]" === u.call(e) } function p(e) { var t = parseFloat(String(e)); return t >= 0 && Math.floor(t) === t && isFinite(e) } function d(e) { return o(e) && "function" === typeof e.then && "function" === typeof e.catch } function h(e) { return null == e ? "" : Array.isArray(e) || l(e) && e.toString === u ? JSON.stringify(e, null, 2) : String(e) } function v(e) { var t = parseFloat(e); return isNaN(t) ? e : t } function m(e, t) { for (var n = Object.create(null), r = e.split(","), i = 0; i < r.length; i++)n[r[i]] = !0; return t ? function (e) { return n[e.toLowerCase()] } : function (e) { return n[e] } } var g = m("slot,component", !0), y = m("key,ref,slot,slot-scope,is"); function b(e, t) { if (e.length) { var n = e.indexOf(t); if (n > -1) return e.splice(n, 1) } } var _ = Object.prototype.hasOwnProperty; function k(e, t) { return _.call(e, t) } function w(e) { var t = Object.create(null); return function (n) { return t[n] || (t[n] = e(n)) } } var x = /-(\w)/g, C = w((function (e) { return e.replace(x, (function (e, t) { return t ? t.toUpperCase() : "" })) })), A = w((function (e) { return e.charAt(0).toUpperCase() + e.slice(1) })), $ = /\B([A-Z])/g, S = w((function (e) { return e.replace($, "-$1").toLowerCase() })); var O = Function.prototype.bind ? function (e, t) { return e.bind(t) } : function (e, t) { function n(n) { var r = arguments.length; return r ? r > 1 ? e.apply(t, arguments) : e.call(t, n) : e.call(t) } return n._length = e.length, n }; function E(e, t) { t = t || 0; for (var n = e.length - t, r = new Array(n); n--;)r[n] = e[n + t]; return r } function T(e, t) { for (var n in t) e[n] = t[n]; return e } function L(e) { for (var t = {}, n = 0; n < e.length; n++)e[n] && T(t, e[n]); return t } function j(e, t, n) { } var I = function (e, t, n) { return !1 }, F = function (e) { return e }; function M(e, t) { if (e === t) return !0; var n = c(e), r = c(t); if (!n || !r) return !n && !r && String(e) === String(t); try { var i = Array.isArray(e), o = Array.isArray(t); if (i && o) return e.length === t.length && e.every((function (e, n) { return M(e, t[n]) })); if (e instanceof Date && t instanceof Date) return e.getTime() === t.getTime(); if (i || o) return !1; var a = Object.keys(e), s = Object.keys(t); return a.length === s.length && a.every((function (n) { return M(e[n], t[n]) })) } catch (u) { return !1 } } function R(e, t) { for (var n = 0; n < e.length; n++)if (M(e[n], t)) return n; return -1 } function P(e) { var t = !1; return function () { t || (t = !0, e.apply(this, arguments)) } } var N = "data-server-rendered", z = ["component", "directive", "filter"], D = ["beforeCreate", "created", "beforeMount", "mounted", "beforeUpdate", "updated", "beforeDestroy", "destroyed", "activated", "deactivated", "errorCaptured", "serverPrefetch"], H = { optionMergeStrategies: Object.create(null), silent: !1, productionTip: !1, devtools: !1, performance: !1, errorHandler: null, warnHandler: null, ignoredElements: [], keyCodes: Object.create(null), isReservedTag: I, isReservedAttr: I, isUnknownElement: I, getTagNamespace: j, parsePlatformTagName: F, mustUseProp: I, async: !0, _lifecycleHooks: D }, B = /a-zA-Z\u00B7\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u037D\u037F-\u1FFF\u200C-\u200D\u203F-\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD/; function G(e) { var t = (e + "").charCodeAt(0); return 36 === t || 95 === t } function U(e, t, n, r) { Object.defineProperty(e, t, { value: n, enumerable: !!r, writable: !0, configurable: !0 }) } var Z = new RegExp("[^" + B.source + ".$_\\d]"); var q, V = "__proto__" in {}, K = "undefined" !== typeof window, J = "undefined" !== typeof WXEnvironment && !!WXEnvironment.platform, W = J && WXEnvironment.platform.toLowerCase(), Y = K && window.navigator.userAgent.toLowerCase(), X = Y && /msie|trident/.test(Y), Q = Y && Y.indexOf("msie 9.0") > 0, ee = Y && Y.indexOf("edge/") > 0, te = (Y && Y.indexOf("android"), Y && /iphone|ipad|ipod|ios/.test(Y) || "ios" === W), ne = (Y && /chrome\/\d+/.test(Y), Y && /phantomjs/.test(Y), Y && Y.match(/firefox\/(\d+)/)), re = {}.watch, ie = !1; if (K) try { var oe = {}; Object.defineProperty(oe, "passive", { get: function () { ie = !0 } }), window.addEventListener("test-passive", null, oe) } catch (hs) { } var ae = function () { return void 0 === q && (q = !K && !J && "undefined" !== typeof e && (e.process && "server" === e.process.env.VUE_ENV)), q }, se = K && window.__VUE_DEVTOOLS_GLOBAL_HOOK__; function ce(e) { return "function" === typeof e && /native code/.test(e.toString()) } var ue, le = "undefined" !== typeof Symbol && ce(Symbol) && "undefined" !== typeof Reflect && ce(Reflect.ownKeys); ue = "undefined" !== typeof Set && ce(Set) ? Set : function () { function e() { this.set = Object.create(null) } return e.prototype.has = function (e) { return !0 === this.set[e] }, e.prototype.add = function (e) { this.set[e] = !0 }, e.prototype.clear = function () { this.set = Object.create(null) }, e }(); var fe = j, pe = 0, de = function () { this.id = pe++ , this.subs = [] }; de.prototype.addSub = function (e) { this.subs.push(e) }, de.prototype.removeSub = function (e) { b(this.subs, e) }, de.prototype.depend = function () { de.target && de.target.addDep(this) }, de.prototype.notify = function () { var e = this.subs.slice(); for (var t = 0, n = e.length; t < n; t++)e[t].update() }, de.target = null; var he = []; function ve(e) { he.push(e), de.target = e } function me() { he.pop(), de.target = he[he.length - 1] } var ge = function (e, t, n, r, i, o, a, s) { this.tag = e, this.data = t, this.children = n, this.text = r, this.elm = i, this.ns = void 0, this.context = o, this.fnContext = void 0, this.fnOptions = void 0, this.fnScopeId = void 0, this.key = t && t.key, this.componentOptions = a, this.componentInstance = void 0, this.parent = void 0, this.raw = !1, this.isStatic = !1, this.isRootInsert = !0, this.isComment = !1, this.isCloned = !1, this.isOnce = !1, this.asyncFactory = s, this.asyncMeta = void 0, this.isAsyncPlaceholder = !1 }, ye = { child: { configurable: !0 } }; ye.child.get = function () { return this.componentInstance }, Object.defineProperties(ge.prototype, ye); var be = function (e) { void 0 === e && (e = ""); var t = new ge; return t.text = e, t.isComment = !0, t }; function _e(e) { return new ge(void 0, void 0, void 0, String(e)) } function ke(e) { var t = new ge(e.tag, e.data, e.children && e.children.slice(), e.text, e.elm, e.context, e.componentOptions, e.asyncFactory); return t.ns = e.ns, t.isStatic = e.isStatic, t.key = e.key, t.isComment = e.isComment, t.fnContext = e.fnContext, t.fnOptions = e.fnOptions, t.fnScopeId = e.fnScopeId, t.asyncMeta = e.asyncMeta, t.isCloned = !0, t } var we = Array.prototype, xe = Object.create(we);["push", "pop", "shift", "unshift", "splice", "sort", "reverse"].forEach((function (e) { var t = we[e]; U(xe, e, (function () { for (var n = [], r = arguments.length; r--;)n[r] = arguments[r]; var i, o = t.apply(this, n), a = this.__ob__; switch (e) { case "push": case "unshift": i = n; break; case "splice": i = n.slice(2) }return i && a.observeArray(i), a.dep.notify(), o })) })); var Ce = Object.getOwnPropertyNames(xe), Ae = !0; function $e(e) { Ae = e } var Se = function (e) { this.value = e, this.dep = new de, this.vmCount = 0, U(e, "__ob__", this), Array.isArray(e) ? (V ? function (e, t) { e.__proto__ = t }(e, xe) : function (e, t, n) { for (var r = 0, i = n.length; r < i; r++) { var o = n[r]; U(e, o, t[o]) } }(e, xe, Ce), this.observeArray(e)) : this.walk(e) }; function Oe(e, t) { var n; if (c(e) && !(e instanceof ge)) return k(e, "__ob__") && e.__ob__ instanceof Se ? n = e.__ob__ : Ae && !ae() && (Array.isArray(e) || l(e)) && Object.isExtensible(e) && !e._isVue && (n = new Se(e)), t && n && n.vmCount++ , n } function Ee(e, t, n, r, i) { var o = new de, a = Object.getOwnPropertyDescriptor(e, t); if (!a || !1 !== a.configurable) { var s = a && a.get, c = a && a.set; s && !c || 2 !== arguments.length || (n = e[t]); var u = !i && Oe(n); Object.defineProperty(e, t, { enumerable: !0, configurable: !0, get: function () { var t = s ? s.call(e) : n; return de.target && (o.depend(), u && (u.dep.depend(), Array.isArray(t) && function e(t) { for (var n = void 0, r = 0, i = t.length; r < i; r++)(n = t[r]) && n.__ob__ && n.__ob__.dep.depend(), Array.isArray(n) && e(n) }(t))), t }, set: function (t) { var r = s ? s.call(e) : n; t === r || t !== t && r !== r || s && !c || (c ? c.call(e, t) : n = t, u = !i && Oe(t), o.notify()) } }) } } function Te(e, t, n) { if (Array.isArray(e) && p(t)) return e.length = Math.max(e.length, t), e.splice(t, 1, n), n; if (t in e && !(t in Object.prototype)) return e[t] = n, n; var r = e.__ob__; return e._isVue || r && r.vmCount ? n : r ? (Ee(r.value, t, n), r.dep.notify(), n) : (e[t] = n, n) } function Le(e, t) { if (Array.isArray(e) && p(t)) e.splice(t, 1); else { var n = e.__ob__; e._isVue || n && n.vmCount || k(e, t) && (delete e[t], n && n.dep.notify()) } } Se.prototype.walk = function (e) { for (var t = Object.keys(e), n = 0; n < t.length; n++)Ee(e, t[n]) }, Se.prototype.observeArray = function (e) { for (var t = 0, n = e.length; t < n; t++)Oe(e[t]) }; var je = H.optionMergeStrategies; function Ie(e, t) { if (!t) return e; for (var n, r, i, o = le ? Reflect.ownKeys(t) : Object.keys(t), a = 0; a < o.length; a++)"__ob__" !== (n = o[a]) && (r = e[n], i = t[n], k(e, n) ? r !== i && l(r) && l(i) && Ie(r, i) : Te(e, n, i)); return e } function Fe(e, t, n) { return n ? function () { var r = "function" === typeof t ? t.call(n, n) : t, i = "function" === typeof e ? e.call(n, n) : e; return r ? Ie(r, i) : i } : t ? e ? function () { return Ie("function" === typeof t ? t.call(this, this) : t, "function" === typeof e ? e.call(this, this) : e) } : t : e } function Me(e, t) { var n = t ? e ? e.concat(t) : Array.isArray(t) ? t : [t] : e; return n ? function (e) { for (var t = [], n = 0; n < e.length; n++)-1 === t.indexOf(e[n]) && t.push(e[n]); return t }(n) : n } function Re(e, t, n, r) { var i = Object.create(e || null); return t ? T(i, t) : i } je.data = function (e, t, n) { return n ? Fe(e, t, n) : t && "function" !== typeof t ? e : Fe(e, t) }, D.forEach((function (e) { je[e] = Me })), z.forEach((function (e) { je[e + "s"] = Re })), je.watch = function (e, t, n, r) { if (e === re && (e = void 0), t === re && (t = void 0), !t) return Object.create(e || null); if (!e) return t; var i = {}; for (var o in T(i, e), t) { var a = i[o], s = t[o]; a && !Array.isArray(a) && (a = [a]), i[o] = a ? a.concat(s) : Array.isArray(s) ? s : [s] } return i }, je.props = je.methods = je.inject = je.computed = function (e, t, n, r) { if (!e) return t; var i = Object.create(null); return T(i, e), t && T(i, t), i }, je.provide = Fe; var Pe = function (e, t) { return void 0 === t ? e : t }; function Ne(e, t, n) { if ("function" === typeof t && (t = t.options), function (e, t) { var n = e.props; if (n) { var r, i, o = {}; if (Array.isArray(n)) for (r = n.length; r--;)"string" === typeof (i = n[r]) && (o[C(i)] = { type: null }); else if (l(n)) for (var a in n) i = n[a], o[C(a)] = l(i) ? i : { type: i }; else 0; e.props = o } }(t), function (e, t) { var n = e.inject; if (n) { var r = e.inject = {}; if (Array.isArray(n)) for (var i = 0; i < n.length; i++)r[n[i]] = { from: n[i] }; else if (l(n)) for (var o in n) { var a = n[o]; r[o] = l(a) ? T({ from: o }, a) : { from: a } } else 0 } }(t), function (e) { var t = e.directives; if (t) for (var n in t) { var r = t[n]; "function" === typeof r && (t[n] = { bind: r, update: r }) } }(t), !t._base && (t.extends && (e = Ne(e, t.extends, n)), t.mixins)) for (var r = 0, i = t.mixins.length; r < i; r++)e = Ne(e, t.mixins[r], n); var o, a = {}; for (o in e) s(o); for (o in t) k(e, o) || s(o); function s(r) { var i = je[r] || Pe; a[r] = i(e[r], t[r], n, r) } return a } function ze(e, t, n, r) { if ("string" === typeof n) { var i = e[t]; if (k(i, n)) return i[n]; var o = C(n); if (k(i, o)) return i[o]; var a = A(o); return k(i, a) ? i[a] : i[n] || i[o] || i[a] } } function De(e, t, n, r) { var i = t[e], o = !k(n, e), a = n[e], s = Ge(Boolean, i.type); if (s > -1) if (o && !k(i, "default")) a = !1; else if ("" === a || a === S(e)) { var c = Ge(String, i.type); (c < 0 || s < c) && (a = !0) } if (void 0 === a) { a = function (e, t, n) { if (!k(t, "default")) return; var r = t.default; 0; if (e && e.$options.propsData && void 0 === e.$options.propsData[n] && void 0 !== e._props[n]) return e._props[n]; return "function" === typeof r && "Function" !== He(t.type) ? r.call(e) : r }(r, i, e); var u = Ae; $e(!0), Oe(a), $e(u) } return a } function He(e) { var t = e && e.toString().match(/^\s*function (\w+)/); return t ? t[1] : "" } function Be(e, t) { return He(e) === He(t) } function Ge(e, t) { if (!Array.isArray(t)) return Be(t, e) ? 0 : -1; for (var n = 0, r = t.length; n < r; n++)if (Be(t[n], e)) return n; return -1 } function Ue(e, t, n) { ve(); try { if (t) for (var r = t; r = r.$parent;) { var i = r.$options.errorCaptured; if (i) for (var o = 0; o < i.length; o++)try { if (!1 === i[o].call(r, e, t, n)) return } catch (hs) { qe(hs, r, "errorCaptured hook") } } qe(e, t, n) } finally { me() } } function Ze(e, t, n, r, i) { var o; try { (o = n ? e.apply(t, n) : e.call(t)) && !o._isVue && d(o) && !o._handled && (o.catch((function (e) { return Ue(e, r, i + " (Promise/async)") })), o._handled = !0) } catch (hs) { Ue(hs, r, i) } return o } function qe(e, t, n) { if (H.errorHandler) try { return H.errorHandler.call(null, e, t, n) } catch (hs) { hs !== e && Ve(hs, null, "config.errorHandler") } Ve(e, t, n) } function Ve(e, t, n) { if (!K && !J || "undefined" === typeof console) throw e; console.error(e) } var Ke, Je = !1, We = [], Ye = !1; function Xe() { Ye = !1; var e = We.slice(0); We.length = 0; for (var t = 0; t < e.length; t++)e[t]() } if ("undefined" !== typeof Promise && ce(Promise)) { var Qe = Promise.resolve(); Ke = function () { Qe.then(Xe), te && setTimeout(j) }, Je = !0 } else if (X || "undefined" === typeof MutationObserver || !ce(MutationObserver) && "[object MutationObserverConstructor]" !== MutationObserver.toString()) Ke = "undefined" !== typeof n && ce(n) ? function () { n(Xe) } : function () { setTimeout(Xe, 0) }; else { var et = 1, tt = new MutationObserver(Xe), nt = document.createTextNode(String(et)); tt.observe(nt, { characterData: !0 }), Ke = function () { et = (et + 1) % 2, nt.data = String(et) }, Je = !0 } function rt(e, t) { var n; if (We.push((function () { if (e) try { e.call(t) } catch (hs) { Ue(hs, t, "nextTick") } else n && n(t) })), Ye || (Ye = !0, Ke()), !e && "undefined" !== typeof Promise) return new Promise((function (e) { n = e })) } var it = new ue; function ot(e) { !function e(t, n) { var r, i; var o = Array.isArray(t); if (!o && !c(t) || Object.isFrozen(t) || t instanceof ge) return; if (t.__ob__) { var a = t.__ob__.dep.id; if (n.has(a)) return; n.add(a) } if (o) for (r = t.length; r--;)e(t[r], n); else for (i = Object.keys(t), r = i.length; r--;)e(t[i[r]], n) }(e, it), it.clear() } var at = w((function (e) { var t = "&" === e.charAt(0), n = "~" === (e = t ? e.slice(1) : e).charAt(0), r = "!" === (e = n ? e.slice(1) : e).charAt(0); return { name: e = r ? e.slice(1) : e, once: n, capture: r, passive: t } })); function st(e, t) { function n() { var e = arguments, r = n.fns; if (!Array.isArray(r)) return Ze(r, null, arguments, t, "v-on handler"); for (var i = r.slice(), o = 0; o < i.length; o++)Ze(i[o], null, e, t, "v-on handler") } return n.fns = e, n } function ct(e, t, n, r, o, s) { var c, u, l, f; for (c in e) u = e[c], l = t[c], f = at(c), i(u) || (i(l) ? (i(u.fns) && (u = e[c] = st(u, s)), a(f.once) && (u = e[c] = o(f.name, u, f.capture)), n(f.name, u, f.capture, f.passive, f.params)) : u !== l && (l.fns = u, e[c] = l)); for (c in t) i(e[c]) && r((f = at(c)).name, t[c], f.capture) } function ut(e, t, n) { var r; e instanceof ge && (e = e.data.hook || (e.data.hook = {})); var s = e[t]; function c() { n.apply(this, arguments), b(r.fns, c) } i(s) ? r = st([c]) : o(s.fns) && a(s.merged) ? (r = s).fns.push(c) : r = st([s, c]), r.merged = !0, e[t] = r } function lt(e, t, n, r, i) { if (o(t)) { if (k(t, n)) return e[n] = t[n], i || delete t[n], !0; if (k(t, r)) return e[n] = t[r], i || delete t[r], !0 } return !1 } function ft(e) { return s(e) ? [_e(e)] : Array.isArray(e) ? function e(t, n) { var r = []; var c, u, l, f; for (c = 0; c < t.length; c++)i(u = t[c]) || "boolean" === typeof u || (l = r.length - 1, f = r[l], Array.isArray(u) ? u.length > 0 && (pt((u = e(u, (n || "") + "_" + c))[0]) && pt(f) && (r[l] = _e(f.text + u[0].text), u.shift()), r.push.apply(r, u)) : s(u) ? pt(f) ? r[l] = _e(f.text + u) : "" !== u && r.push(_e(u)) : pt(u) && pt(f) ? r[l] = _e(f.text + u.text) : (a(t._isVList) && o(u.tag) && i(u.key) && o(n) && (u.key = "__vlist" + n + "_" + c + "__"), r.push(u))); return r }(e) : void 0 } function pt(e) { return o(e) && o(e.text) && !1 === e.isComment } function dt(e, t) { if (e) { for (var n = Object.create(null), r = le ? Reflect.ownKeys(e) : Object.keys(e), i = 0; i < r.length; i++) { var o = r[i]; if ("__ob__" !== o) { for (var a = e[o].from, s = t; s;) { if (s._provided && k(s._provided, a)) { n[o] = s._provided[a]; break } s = s.$parent } if (!s) if ("default" in e[o]) { var c = e[o].default; n[o] = "function" === typeof c ? c.call(t) : c } else 0 } } return n } } function ht(e, t) { if (!e || !e.length) return {}; for (var n = {}, r = 0, i = e.length; r < i; r++) { var o = e[r], a = o.data; if (a && a.attrs && a.attrs.slot && delete a.attrs.slot, o.context !== t && o.fnContext !== t || !a || null == a.slot) (n.default || (n.default = [])).push(o); else { var s = a.slot, c = n[s] || (n[s] = []); "template" === o.tag ? c.push.apply(c, o.children || []) : c.push(o) } } for (var u in n) n[u].every(vt) && delete n[u]; return n } function vt(e) { return e.isComment && !e.asyncFactory || " " === e.text } function mt(e, t, n) { var i, o = Object.keys(t).length > 0, a = e ? !!e.$stable : !o, s = e && e.$key; if (e) { if (e._normalized) return e._normalized; if (a && n && n !== r && s === n.$key && !o && !n.$hasNormal) return n; for (var c in i = {}, e) e[c] && "$" !== c[0] && (i[c] = gt(t, c, e[c])) } else i = {}; for (var u in t) u in i || (i[u] = yt(t, u)); return e && Object.isExtensible(e) && (e._normalized = i), U(i, "$stable", a), U(i, "$key", s), U(i, "$hasNormal", o), i } function gt(e, t, n) { var r = function () { var e = arguments.length ? n.apply(null, arguments) : n({}); return (e = e && "object" === typeof e && !Array.isArray(e) ? [e] : ft(e)) && (0 === e.length || 1 === e.length && e[0].isComment) ? void 0 : e }; return n.proxy && Object.defineProperty(e, t, { get: r, enumerable: !0, configurable: !0 }), r } function yt(e, t) { return function () { return e[t] } } function bt(e, t) { var n, r, i, a, s; if (Array.isArray(e) || "string" === typeof e) for (n = new Array(e.length), r = 0, i = e.length; r < i; r++)n[r] = t(e[r], r); else if ("number" === typeof e) for (n = new Array(e), r = 0; r < e; r++)n[r] = t(r + 1, r); else if (c(e)) if (le && e[Symbol.iterator]) { n = []; for (var u = e[Symbol.iterator](), l = u.next(); !l.done;)n.push(t(l.value, n.length)), l = u.next() } else for (a = Object.keys(e), n = new Array(a.length), r = 0, i = a.length; r < i; r++)s = a[r], n[r] = t(e[s], s, r); return o(n) || (n = []), n._isVList = !0, n } function _t(e, t, n, r) { var i, o = this.$scopedSlots[e]; o ? (n = n || {}, r && (n = T(T({}, r), n)), i = o(n) || t) : i = this.$slots[e] || t; var a = n && n.slot; return a ? this.$createElement("template", { slot: a }, i) : i } function kt(e) { return ze(this.$options, "filters", e) || F } function wt(e, t) { return Array.isArray(e) ? -1 === e.indexOf(t) : e !== t } function xt(e, t, n, r, i) { var o = H.keyCodes[t] || n; return i && r && !H.keyCodes[t] ? wt(i, r) : o ? wt(o, e) : r ? S(r) !== t : void 0 } function Ct(e, t, n, r, i) { if (n) if (c(n)) { var o; Array.isArray(n) && (n = L(n)); var a = function (a) { if ("class" === a || "style" === a || y(a)) o = e; else { var s = e.attrs && e.attrs.type; o = r || H.mustUseProp(t, s, a) ? e.domProps || (e.domProps = {}) : e.attrs || (e.attrs = {}) } var c = C(a), u = S(a); c in o || u in o || (o[a] = n[a], i && ((e.on || (e.on = {}))["update:" + a] = function (e) { n[a] = e })) }; for (var s in n) a(s) } else; return e } function At(e, t) { var n = this._staticTrees || (this._staticTrees = []), r = n[e]; return r && !t ? r : (St(r = n[e] = this.$options.staticRenderFns[e].call(this._renderProxy, null, this), "__static__" + e, !1), r) } function $t(e, t, n) { return St(e, "__once__" + t + (n ? "_" + n : ""), !0), e } function St(e, t, n) { if (Array.isArray(e)) for (var r = 0; r < e.length; r++)e[r] && "string" !== typeof e[r] && Ot(e[r], t + "_" + r, n); else Ot(e, t, n) } function Ot(e, t, n) { e.isStatic = !0, e.key = t, e.isOnce = n } function Et(e, t) { if (t) if (l(t)) { var n = e.on = e.on ? T({}, e.on) : {}; for (var r in t) { var i = n[r], o = t[r]; n[r] = i ? [].concat(i, o) : o } } else; return e } function Tt(e, t, n, r) { t = t || { $stable: !n }; for (var i = 0; i < e.length; i++) { var o = e[i]; Array.isArray(o) ? Tt(o, t, n) : o && (o.proxy && (o.fn.proxy = !0), t[o.key] = o.fn) } return r && (t.$key = r), t } function Lt(e, t) { for (var n = 0; n < t.length; n += 2) { var r = t[n]; "string" === typeof r && r && (e[t[n]] = t[n + 1]) } return e } function jt(e, t) { return "string" === typeof e ? t + e : e } function It(e) { e._o = $t, e._n = v, e._s = h, e._l = bt, e._t = _t, e._q = M, e._i = R, e._m = At, e._f = kt, e._k = xt, e._b = Ct, e._v = _e, e._e = be, e._u = Tt, e._g = Et, e._d = Lt, e._p = jt } function Ft(e, t, n, i, o) { var s, c = this, u = o.options; k(i, "_uid") ? (s = Object.create(i))._original = i : (s = i, i = i._original); var l = a(u._compiled), f = !l; this.data = e, this.props = t, this.children = n, this.parent = i, this.listeners = e.on || r, this.injections = dt(u.inject, i), this.slots = function () { return c.$slots || mt(e.scopedSlots, c.$slots = ht(n, i)), c.$slots }, Object.defineProperty(this, "scopedSlots", { enumerable: !0, get: function () { return mt(e.scopedSlots, this.slots()) } }), l && (this.$options = u, this.$slots = this.slots(), this.$scopedSlots = mt(e.scopedSlots, this.$slots)), u._scopeId ? this._c = function (e, t, n, r) { var o = Gt(s, e, t, n, r, f); return o && !Array.isArray(o) && (o.fnScopeId = u._scopeId, o.fnContext = i), o } : this._c = function (e, t, n, r) { return Gt(s, e, t, n, r, f) } } function Mt(e, t, n, r, i) { var o = ke(e); return o.fnContext = n, o.fnOptions = r, t.slot && ((o.data || (o.data = {})).slot = t.slot), o } function Rt(e, t) { for (var n in t) e[C(n)] = t[n] } It(Ft.prototype); var Pt = { init: function (e, t) { if (e.componentInstance && !e.componentInstance._isDestroyed && e.data.keepAlive) { var n = e; Pt.prepatch(n, n) } else { (e.componentInstance = function (e, t) { var n = { _isComponent: !0, _parentVnode: e, parent: t }, r = e.data.inlineTemplate; o(r) && (n.render = r.render, n.staticRenderFns = r.staticRenderFns); return new e.componentOptions.Ctor(n) }(e, Qt)).$mount(t ? e.elm : void 0, t) } }, prepatch: function (e, t) { var n = t.componentOptions; !function (e, t, n, i, o) { 0; var a = i.data.scopedSlots, s = e.$scopedSlots, c = !!(a && !a.$stable || s !== r && !s.$stable || a && e.$scopedSlots.$key !== a.$key), u = !!(o || e.$options._renderChildren || c); e.$options._parentVnode = i, e.$vnode = i, e._vnode && (e._vnode.parent = i); if (e.$options._renderChildren = o, e.$attrs = i.data.attrs || r, e.$listeners = n || r, t && e.$options.props) { $e(!1); for (var l = e._props, f = e.$options._propKeys || [], p = 0; p < f.length; p++) { var d = f[p], h = e.$options.props; l[d] = De(d, h, t, e) } $e(!0), e.$options.propsData = t } n = n || r; var v = e.$options._parentListeners; e.$options._parentListeners = n, Xt(e, n, v), u && (e.$slots = ht(o, i.context), e.$forceUpdate()); 0 }(t.componentInstance = e.componentInstance, n.propsData, n.listeners, t, n.children) }, insert: function (e) { var t, n = e.context, r = e.componentInstance; r._isMounted || (r._isMounted = !0, rn(r, "mounted")), e.data.keepAlive && (n._isMounted ? ((t = r)._inactive = !1, an.push(t)) : nn(r, !0)) }, destroy: function (e) { var t = e.componentInstance; t._isDestroyed || (e.data.keepAlive ? function e(t, n) { if (n && (t._directInactive = !0, tn(t))) return; if (!t._inactive) { t._inactive = !0; for (var r = 0; r < t.$children.length; r++)e(t.$children[r]); rn(t, "deactivated") } }(t, !0) : t.$destroy()) } }, Nt = Object.keys(Pt); function zt(e, t, n, s, u) { if (!i(e)) { var l = n.$options._base; if (c(e) && (e = l.extend(e)), "function" === typeof e) { var f; if (i(e.cid) && void 0 === (e = function (e, t) { if (a(e.error) && o(e.errorComp)) return e.errorComp; if (o(e.resolved)) return e.resolved; var n = Zt; n && o(e.owners) && -1 === e.owners.indexOf(n) && e.owners.push(n); if (a(e.loading) && o(e.loadingComp)) return e.loadingComp; if (n && !o(e.owners)) { var r = e.owners = [n], s = !0, u = null, l = null; n.$on("hook:destroyed", (function () { return b(r, n) })); var f = function (e) { for (var t = 0, n = r.length; t < n; t++)r[t].$forceUpdate(); e && (r.length = 0, null !== u && (clearTimeout(u), u = null), null !== l && (clearTimeout(l), l = null)) }, p = P((function (n) { e.resolved = qt(n, t), s ? r.length = 0 : f(!0) })), h = P((function (t) { o(e.errorComp) && (e.error = !0, f(!0)) })), v = e(p, h); return c(v) && (d(v) ? i(e.resolved) && v.then(p, h) : d(v.component) && (v.component.then(p, h), o(v.error) && (e.errorComp = qt(v.error, t)), o(v.loading) && (e.loadingComp = qt(v.loading, t), 0 === v.delay ? e.loading = !0 : u = setTimeout((function () { u = null, i(e.resolved) && i(e.error) && (e.loading = !0, f(!1)) }), v.delay || 200)), o(v.timeout) && (l = setTimeout((function () { l = null, i(e.resolved) && h(null) }), v.timeout)))), s = !1, e.loading ? e.loadingComp : e.resolved } }(f = e, l))) return function (e, t, n, r, i) { var o = be(); return o.asyncFactory = e, o.asyncMeta = { data: t, context: n, children: r, tag: i }, o }(f, t, n, s, u); t = t || {}, $n(e), o(t.model) && function (e, t) { var n = e.model && e.model.prop || "value", r = e.model && e.model.event || "input"; (t.attrs || (t.attrs = {}))[n] = t.model.value; var i = t.on || (t.on = {}), a = i[r], s = t.model.callback; o(a) ? (Array.isArray(a) ? -1 === a.indexOf(s) : a !== s) && (i[r] = [s].concat(a)) : i[r] = s }(e.options, t); var p = function (e, t, n) { var r = t.options.props; if (!i(r)) { var a = {}, s = e.attrs, c = e.props; if (o(s) || o(c)) for (var u in r) { var l = S(u); lt(a, c, u, l, !0) || lt(a, s, u, l, !1) } return a } }(t, e); if (a(e.options.functional)) return function (e, t, n, i, a) { var s = e.options, c = {}, u = s.props; if (o(u)) for (var l in u) c[l] = De(l, u, t || r); else o(n.attrs) && Rt(c, n.attrs), o(n.props) && Rt(c, n.props); var f = new Ft(n, c, a, i, e), p = s.render.call(null, f._c, f); if (p instanceof ge) return Mt(p, n, f.parent, s, f); if (Array.isArray(p)) { for (var d = ft(p) || [], h = new Array(d.length), v = 0; v < d.length; v++)h[v] = Mt(d[v], n, f.parent, s, f); return h } }(e, p, t, n, s); var h = t.on; if (t.on = t.nativeOn, a(e.options.abstract)) { var v = t.slot; t = {}, v && (t.slot = v) } !function (e) { for (var t = e.hook || (e.hook = {}), n = 0; n < Nt.length; n++) { var r = Nt[n], i = t[r], o = Pt[r]; i === o || i && i._merged || (t[r] = i ? Dt(o, i) : o) } }(t); var m = e.options.name || u; return new ge("vue-component-" + e.cid + (m ? "-" + m : ""), t, void 0, void 0, void 0, n, { Ctor: e, propsData: p, listeners: h, tag: u, children: s }, f) } } } function Dt(e, t) { var n = function (n, r) { e(n, r), t(n, r) }; return n._merged = !0, n } var Ht = 1, Bt = 2; function Gt(e, t, n, r, u, l) { return (Array.isArray(n) || s(n)) && (u = r, r = n, n = void 0), a(l) && (u = Bt), function (e, t, n, r, s) { if (o(n) && o(n.__ob__)) return be(); o(n) && o(n.is) && (t = n.is); if (!t) return be(); 0; Array.isArray(r) && "function" === typeof r[0] && ((n = n || {}).scopedSlots = { default: r[0] }, r.length = 0); s === Bt ? r = ft(r) : s === Ht && (r = function (e) { for (var t = 0; t < e.length; t++)if (Array.isArray(e[t])) return Array.prototype.concat.apply([], e); return e }(r)); var u, l; if ("string" === typeof t) { var f; l = e.$vnode && e.$vnode.ns || H.getTagNamespace(t), u = H.isReservedTag(t) ? new ge(H.parsePlatformTagName(t), n, r, void 0, void 0, e) : n && n.pre || !o(f = ze(e.$options, "components", t)) ? new ge(t, n, r, void 0, void 0, e) : zt(f, n, e, r, t) } else u = zt(t, n, e, r); return Array.isArray(u) ? u : o(u) ? (o(l) && function e(t, n, r) { t.ns = n; "foreignObject" === t.tag && (n = void 0, r = !0); if (o(t.children)) for (var s = 0, c = t.children.length; s < c; s++) { var u = t.children[s]; o(u.tag) && (i(u.ns) || a(r) && "svg" !== u.tag) && e(u, n, r) } }(u, l), o(n) && function (e) { c(e.style) && ot(e.style); c(e.class) && ot(e.class) }(n), u) : be() }(e, t, n, r, u) } var Ut, Zt = null; function qt(e, t) { return (e.__esModule || le && "Module" === e[Symbol.toStringTag]) && (e = e.default), c(e) ? t.extend(e) : e } function Vt(e) { return e.isComment && e.asyncFactory } function Kt(e) { if (Array.isArray(e)) for (var t = 0; t < e.length; t++) { var n = e[t]; if (o(n) && (o(n.componentOptions) || Vt(n))) return n } } function Jt(e, t) { Ut.$on(e, t) } function Wt(e, t) { Ut.$off(e, t) } function Yt(e, t) { var n = Ut; return function r() { var i = t.apply(null, arguments); null !== i && n.$off(e, r) } } function Xt(e, t, n) { Ut = e, ct(t, n || {}, Jt, Wt, Yt, e), Ut = void 0 } var Qt = null; function en(e) { var t = Qt; return Qt = e, function () { Qt = t } } function tn(e) { for (; e && (e = e.$parent);)if (e._inactive) return !0; return !1 } function nn(e, t) { if (t) { if (e._directInactive = !1, tn(e)) return } else if (e._directInactive) return; if (e._inactive || null === e._inactive) { e._inactive = !1; for (var n = 0; n < e.$children.length; n++)nn(e.$children[n]); rn(e, "activated") } } function rn(e, t) { ve(); var n = e.$options[t], r = t + " hook"; if (n) for (var i = 0, o = n.length; i < o; i++)Ze(n[i], e, null, e, r); e._hasHookEvent && e.$emit("hook:" + t), me() } var on = [], an = [], sn = {}, cn = !1, un = !1, ln = 0; var fn = 0, pn = Date.now; if (K && !X) { var dn = window.performance; dn && "function" === typeof dn.now && pn() > document.createEvent("Event").timeStamp && (pn = function () { return dn.now() }) } function hn() { var e, t; for (fn = pn(), un = !0, on.sort((function (e, t) { return e.id - t.id })), ln = 0; ln < on.length; ln++)(e = on[ln]).before && e.before(), t = e.id, sn[t] = null, e.run(); var n = an.slice(), r = on.slice(); ln = on.length = an.length = 0, sn = {}, cn = un = !1, function (e) { for (var t = 0; t < e.length; t++)e[t]._inactive = !0, nn(e[t], !0) }(n), function (e) { var t = e.length; for (; t--;) { var n = e[t], r = n.vm; r._watcher === n && r._isMounted && !r._isDestroyed && rn(r, "updated") } }(r), se && H.devtools && se.emit("flush") } var vn = 0, mn = function (e, t, n, r, i) { this.vm = e, i && (e._watcher = this), e._watchers.push(this), r ? (this.deep = !!r.deep, this.user = !!r.user, this.lazy = !!r.lazy, this.sync = !!r.sync, this.before = r.before) : this.deep = this.user = this.lazy = this.sync = !1, this.cb = n, this.id = ++vn, this.active = !0, this.dirty = this.lazy, this.deps = [], this.newDeps = [], this.depIds = new ue, this.newDepIds = new ue, this.expression = "", "function" === typeof t ? this.getter = t : (this.getter = function (e) { if (!Z.test(e)) { var t = e.split("."); return function (e) { for (var n = 0; n < t.length; n++) { if (!e) return; e = e[t[n]] } return e } } }(t), this.getter || (this.getter = j)), this.value = this.lazy ? void 0 : this.get() }; mn.prototype.get = function () { var e; ve(this); var t = this.vm; try { e = this.getter.call(t, t) } catch (hs) { if (!this.user) throw hs; Ue(hs, t, 'getter for watcher "' + this.expression + '"') } finally { this.deep && ot(e), me(), this.cleanupDeps() } return e }, mn.prototype.addDep = function (e) { var t = e.id; this.newDepIds.has(t) || (this.newDepIds.add(t), this.newDeps.push(e), this.depIds.has(t) || e.addSub(this)) }, mn.prototype.cleanupDeps = function () { for (var e = this.deps.length; e--;) { var t = this.deps[e]; this.newDepIds.has(t.id) || t.removeSub(this) } var n = this.depIds; this.depIds = this.newDepIds, this.newDepIds = n, this.newDepIds.clear(), n = this.deps, this.deps = this.newDeps, this.newDeps = n, this.newDeps.length = 0 }, mn.prototype.update = function () { this.lazy ? this.dirty = !0 : this.sync ? this.run() : function (e) { var t = e.id; if (null == sn[t]) { if (sn[t] = !0, un) { for (var n = on.length - 1; n > ln && on[n].id > e.id;)n--; on.splice(n + 1, 0, e) } else on.push(e); cn || (cn = !0, rt(hn)) } }(this) }, mn.prototype.run = function () { if (this.active) { var e = this.get(); if (e !== this.value || c(e) || this.deep) { var t = this.value; if (this.value = e, this.user) try { this.cb.call(this.vm, e, t) } catch (hs) { Ue(hs, this.vm, 'callback for watcher "' + this.expression + '"') } else this.cb.call(this.vm, e, t) } } }, mn.prototype.evaluate = function () { this.value = this.get(), this.dirty = !1 }, mn.prototype.depend = function () { for (var e = this.deps.length; e--;)this.deps[e].depend() }, mn.prototype.teardown = function () { if (this.active) { this.vm._isBeingDestroyed || b(this.vm._watchers, this); for (var e = this.deps.length; e--;)this.deps[e].removeSub(this); this.active = !1 } }; var gn = { enumerable: !0, configurable: !0, get: j, set: j }; function yn(e, t, n) { gn.get = function () { return this[t][n] }, gn.set = function (e) { this[t][n] = e }, Object.defineProperty(e, n, gn) } function bn(e) { e._watchers = []; var t = e.$options; t.props && function (e, t) { var n = e.$options.propsData || {}, r = e._props = {}, i = e.$options._propKeys = []; e.$parent && $e(!1); var o = function (o) { i.push(o); var a = De(o, t, n, e); Ee(r, o, a), o in e || yn(e, "_props", o) }; for (var a in t) o(a); $e(!0) }(e, t.props), t.methods && function (e, t) { e.$options.props; for (var n in t) e[n] = "function" !== typeof t[n] ? j : O(t[n], e) }(e, t.methods), t.data ? function (e) { var t = e.$options.data; l(t = e._data = "function" === typeof t ? function (e, t) { ve(); try { return e.call(t, t) } catch (hs) { return Ue(hs, t, "data()"), {} } finally { me() } }(t, e) : t || {}) || (t = {}); var n = Object.keys(t), r = e.$options.props, i = (e.$options.methods, n.length); for (; i--;) { var o = n[i]; 0, r && k(r, o) || G(o) || yn(e, "_data", o) } Oe(t, !0) }(e) : Oe(e._data = {}, !0), t.computed && function (e, t) { var n = e._computedWatchers = Object.create(null), r = ae(); for (var i in t) { var o = t[i], a = "function" === typeof o ? o : o.get; 0, r || (n[i] = new mn(e, a || j, j, _n)), i in e || kn(e, i, o) } }(e, t.computed), t.watch && t.watch !== re && function (e, t) { for (var n in t) { var r = t[n]; if (Array.isArray(r)) for (var i = 0; i < r.length; i++)Cn(e, n, r[i]); else Cn(e, n, r) } }(e, t.watch) } var _n = { lazy: !0 }; function kn(e, t, n) { var r = !ae(); "function" === typeof n ? (gn.get = r ? wn(t) : xn(n), gn.set = j) : (gn.get = n.get ? r && !1 !== n.cache ? wn(t) : xn(n.get) : j, gn.set = n.set || j), Object.defineProperty(e, t, gn) } function wn(e) { return function () { var t = this._computedWatchers && this._computedWatchers[e]; if (t) return t.dirty && t.evaluate(), de.target && t.depend(), t.value } } function xn(e) { return function () { return e.call(this, this) } } function Cn(e, t, n, r) { return l(n) && (r = n, n = n.handler), "string" === typeof n && (n = e[n]), e.$watch(t, n, r) } var An = 0; function $n(e) { var t = e.options; if (e.super) { var n = $n(e.super); if (n !== e.superOptions) { e.superOptions = n; var r = function (e) { var t, n = e.options, r = e.sealedOptions; for (var i in n) n[i] !== r[i] && (t || (t = {}), t[i] = n[i]); return t }(e); r && T(e.extendOptions, r), (t = e.options = Ne(n, e.extendOptions)).name && (t.components[t.name] = e) } } return t } function Sn(e) { this._init(e) } function On(e) { e.cid = 0; var t = 1; e.extend = function (e) { e = e || {}; var n = this, r = n.cid, i = e._Ctor || (e._Ctor = {}); if (i[r]) return i[r]; var o = e.name || n.options.name; var a = function (e) { this._init(e) }; return (a.prototype = Object.create(n.prototype)).constructor = a, a.cid = t++ , a.options = Ne(n.options, e), a.super = n, a.options.props && function (e) { var t = e.options.props; for (var n in t) yn(e.prototype, "_props", n) }(a), a.options.computed && function (e) { var t = e.options.computed; for (var n in t) kn(e.prototype, n, t[n]) }(a), a.extend = n.extend, a.mixin = n.mixin, a.use = n.use, z.forEach((function (e) { a[e] = n[e] })), o && (a.options.components[o] = a), a.superOptions = n.options, a.extendOptions = e, a.sealedOptions = T({}, a.options), i[r] = a, a } } function En(e) { return e && (e.Ctor.options.name || e.tag) } function Tn(e, t) { return Array.isArray(e) ? e.indexOf(t) > -1 : "string" === typeof e ? e.split(",").indexOf(t) > -1 : !!f(e) && e.test(t) } function Ln(e, t) { var n = e.cache, r = e.keys, i = e._vnode; for (var o in n) { var a = n[o]; if (a) { var s = En(a.componentOptions); s && !t(s) && jn(n, o, r, i) } } } function jn(e, t, n, r) { var i = e[t]; !i || r && i.tag === r.tag || i.componentInstance.$destroy(), e[t] = null, b(n, t) } !function (e) { e.prototype._init = function (e) { var t = this; t._uid = An++ , t._isVue = !0, e && e._isComponent ? function (e, t) { var n = e.$options = Object.create(e.constructor.options), r = t._parentVnode; n.parent = t.parent, n._parentVnode = r; var i = r.componentOptions; n.propsData = i.propsData, n._parentListeners = i.listeners, n._renderChildren = i.children, n._componentTag = i.tag, t.render && (n.render = t.render, n.staticRenderFns = t.staticRenderFns) }(t, e) : t.$options = Ne($n(t.constructor), e || {}, t), t._renderProxy = t, t._self = t, function (e) { var t = e.$options, n = t.parent; if (n && !t.abstract) { for (; n.$options.abstract && n.$parent;)n = n.$parent; n.$children.push(e) } e.$parent = n, e.$root = n ? n.$root : e, e.$children = [], e.$refs = {}, e._watcher = null, e._inactive = null, e._directInactive = !1, e._isMounted = !1, e._isDestroyed = !1, e._isBeingDestroyed = !1 }(t), function (e) { e._events = Object.create(null), e._hasHookEvent = !1; var t = e.$options._parentListeners; t && Xt(e, t) }(t), function (e) { e._vnode = null, e._staticTrees = null; var t = e.$options, n = e.$vnode = t._parentVnode, i = n && n.context; e.$slots = ht(t._renderChildren, i), e.$scopedSlots = r, e._c = function (t, n, r, i) { return Gt(e, t, n, r, i, !1) }, e.$createElement = function (t, n, r, i) { return Gt(e, t, n, r, i, !0) }; var o = n && n.data; Ee(e, "$attrs", o && o.attrs || r, null, !0), Ee(e, "$listeners", t._parentListeners || r, null, !0) }(t), rn(t, "beforeCreate"), function (e) { var t = dt(e.$options.inject, e); t && ($e(!1), Object.keys(t).forEach((function (n) { Ee(e, n, t[n]) })), $e(!0)) }(t), bn(t), function (e) { var t = e.$options.provide; t && (e._provided = "function" === typeof t ? t.call(e) : t) }(t), rn(t, "created"), t.$options.el && t.$mount(t.$options.el) } }(Sn), function (e) { var t = { get: function () { return this._data } }, n = { get: function () { return this._props } }; Object.defineProperty(e.prototype, "$data", t), Object.defineProperty(e.prototype, "$props", n), e.prototype.$set = Te, e.prototype.$delete = Le, e.prototype.$watch = function (e, t, n) { if (l(t)) return Cn(this, e, t, n); (n = n || {}).user = !0; var r = new mn(this, e, t, n); if (n.immediate) try { t.call(this, r.value) } catch (i) { Ue(i, this, 'callback for immediate watcher "' + r.expression + '"') } return function () { r.teardown() } } }(Sn), function (e) { var t = /^hook:/; e.prototype.$on = function (e, n) { var r = this; if (Array.isArray(e)) for (var i = 0, o = e.length; i < o; i++)r.$on(e[i], n); else (r._events[e] || (r._events[e] = [])).push(n), t.test(e) && (r._hasHookEvent = !0); return r }, e.prototype.$once = function (e, t) { var n = this; function r() { n.$off(e, r), t.apply(n, arguments) } return r.fn = t, n.$on(e, r), n }, e.prototype.$off = function (e, t) { var n = this; if (!arguments.length) return n._events = Object.create(null), n; if (Array.isArray(e)) { for (var r = 0, i = e.length; r < i; r++)n.$off(e[r], t); return n } var o, a = n._events[e]; if (!a) return n; if (!t) return n._events[e] = null, n; for (var s = a.length; s--;)if ((o = a[s]) === t || o.fn === t) { a.splice(s, 1); break } return n }, e.prototype.$emit = function (e) { var t = this, n = t._events[e]; if (n) { n = n.length > 1 ? E(n) : n; for (var r = E(arguments, 1), i = 'event handler for "' + e + '"', o = 0, a = n.length; o < a; o++)Ze(n[o], t, r, t, i) } return t } }(Sn), function (e) { e.prototype._update = function (e, t) { var n = this, r = n.$el, i = n._vnode, o = en(n); n._vnode = e, n.$el = i ? n.__patch__(i, e) : n.__patch__(n.$el, e, t, !1), o(), r && (r.__vue__ = null), n.$el && (n.$el.__vue__ = n), n.$vnode && n.$parent && n.$vnode === n.$parent._vnode && (n.$parent.$el = n.$el) }, e.prototype.$forceUpdate = function () { this._watcher && this._watcher.update() }, e.prototype.$destroy = function () { var e = this; if (!e._isBeingDestroyed) { rn(e, "beforeDestroy"), e._isBeingDestroyed = !0; var t = e.$parent; !t || t._isBeingDestroyed || e.$options.abstract || b(t.$children, e), e._watcher && e._watcher.teardown(); for (var n = e._watchers.length; n--;)e._watchers[n].teardown(); e._data.__ob__ && e._data.__ob__.vmCount-- , e._isDestroyed = !0, e.__patch__(e._vnode, null), rn(e, "destroyed"), e.$off(), e.$el && (e.$el.__vue__ = null), e.$vnode && (e.$vnode.parent = null) } } }(Sn), function (e) { It(e.prototype), e.prototype.$nextTick = function (e) { return rt(e, this) }, e.prototype._render = function () { var e, t = this, n = t.$options, r = n.render, i = n._parentVnode; i && (t.$scopedSlots = mt(i.data.scopedSlots, t.$slots, t.$scopedSlots)), t.$vnode = i; try { Zt = t, e = r.call(t._renderProxy, t.$createElement) } catch (hs) { Ue(hs, t, "render"), e = t._vnode } finally { Zt = null } return Array.isArray(e) && 1 === e.length && (e = e[0]), e instanceof ge || (e = be()), e.parent = i, e } }(Sn); var In = [String, RegExp, Array], Fn = { KeepAlive: { name: "keep-alive", abstract: !0, props: { include: In, exclude: In, max: [String, Number] }, created: function () { this.cache = Object.create(null), this.keys = [] }, destroyed: function () { for (var e in this.cache) jn(this.cache, e, this.keys) }, mounted: function () { var e = this; this.$watch("include", (function (t) { Ln(e, (function (e) { return Tn(t, e) })) })), this.$watch("exclude", (function (t) { Ln(e, (function (e) { return !Tn(t, e) })) })) }, render: function () { var e = this.$slots.default, t = Kt(e), n = t && t.componentOptions; if (n) { var r = En(n), i = this.include, o = this.exclude; if (i && (!r || !Tn(i, r)) || o && r && Tn(o, r)) return t; var a = this.cache, s = this.keys, c = null == t.key ? n.Ctor.cid + (n.tag ? "::" + n.tag : "") : t.key; a[c] ? (t.componentInstance = a[c].componentInstance, b(s, c), s.push(c)) : (a[c] = t, s.push(c), this.max && s.length > parseInt(this.max) && jn(a, s[0], s, this._vnode)), t.data.keepAlive = !0 } return t || e && e[0] } } }; !function (e) { var t = { get: function () { return H } }; Object.defineProperty(e, "config", t), e.util = { warn: fe, extend: T, mergeOptions: Ne, defineReactive: Ee }, e.set = Te, e.delete = Le, e.nextTick = rt, e.observable = function (e) { return Oe(e), e }, e.options = Object.create(null), z.forEach((function (t) { e.options[t + "s"] = Object.create(null) })), e.options._base = e, T(e.options.components, Fn), function (e) { e.use = function (e) { var t = this._installedPlugins || (this._installedPlugins = []); if (t.indexOf(e) > -1) return this; var n = E(arguments, 1); return n.unshift(this), "function" === typeof e.install ? e.install.apply(e, n) : "function" === typeof e && e.apply(null, n), t.push(e), this } }(e), function (e) { e.mixin = function (e) { return this.options = Ne(this.options, e), this } }(e), On(e), function (e) { z.forEach((function (t) { e[t] = function (e, n) { return n ? ("component" === t && l(n) && (n.name = n.name || e, n = this.options._base.extend(n)), "directive" === t && "function" === typeof n && (n = { bind: n, update: n }), this.options[t + "s"][e] = n, n) : this.options[t + "s"][e] } })) }(e) }(Sn), Object.defineProperty(Sn.prototype, "$isServer", { get: ae }), Object.defineProperty(Sn.prototype, "$ssrContext", { get: function () { return this.$vnode && this.$vnode.ssrContext } }), Object.defineProperty(Sn, "FunctionalRenderContext", { value: Ft }), Sn.version = "2.6.10"; var Mn = m("style,class"), Rn = m("input,textarea,option,select,progress"), Pn = function (e, t, n) { return "value" === n && Rn(e) && "button" !== t || "selected" === n && "option" === e || "checked" === n && "input" === e || "muted" === n && "video" === e }, Nn = m("contenteditable,draggable,spellcheck"), zn = m("events,caret,typing,plaintext-only"), Dn = function (e, t) { return Zn(t) || "false" === t ? "false" : "contenteditable" === e && zn(t) ? t : "true" }, Hn = m("allowfullscreen,async,autofocus,autoplay,checked,compact,controls,declare,default,defaultchecked,defaultmuted,defaultselected,defer,disabled,enabled,formnovalidate,hidden,indeterminate,inert,ismap,itemscope,loop,multiple,muted,nohref,noresize,noshade,novalidate,nowrap,open,pauseonexit,readonly,required,reversed,scoped,seamless,selected,sortable,translate,truespeed,typemustmatch,visible"), Bn = "http://www.w3.org/1999/xlink", Gn = function (e) { return ":" === e.charAt(5) && "xlink" === e.slice(0, 5) }, Un = function (e) { return Gn(e) ? e.slice(6, e.length) : "" }, Zn = function (e) { return null == e || !1 === e }; function qn(e) { for (var t = e.data, n = e, r = e; o(r.componentInstance);)(r = r.componentInstance._vnode) && r.data && (t = Vn(r.data, t)); for (; o(n = n.parent);)n && n.data && (t = Vn(t, n.data)); return function (e, t) { if (o(e) || o(t)) return Kn(e, Jn(t)); return "" }(t.staticClass, t.class) } function Vn(e, t) { return { staticClass: Kn(e.staticClass, t.staticClass), class: o(e.class) ? [e.class, t.class] : t.class } } function Kn(e, t) { return e ? t ? e + " " + t : e : t || "" } function Jn(e) { return Array.isArray(e) ? function (e) { for (var t, n = "", r = 0, i = e.length; r < i; r++)o(t = Jn(e[r])) && "" !== t && (n && (n += " "), n += t); return n }(e) : c(e) ? function (e) { var t = ""; for (var n in e) e[n] && (t && (t += " "), t += n); return t }(e) : "string" === typeof e ? e : "" } var Wn = { svg: "http://www.w3.org/2000/svg", math: "http://www.w3.org/1998/Math/MathML" }, Yn = m("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,h1,h2,h3,h4,h5,h6,hgroup,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,rtc,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,menuitem,summary,content,element,shadow,template,blockquote,iframe,tfoot"), Xn = m("svg,animate,circle,clippath,cursor,defs,desc,ellipse,filter,font-face,foreignObject,g,glyph,image,line,marker,mask,missing-glyph,path,pattern,polygon,polyline,rect,switch,symbol,text,textpath,tspan,use,view", !0), Qn = function (e) { return Yn(e) || Xn(e) }; function er(e) { return Xn(e) ? "svg" : "math" === e ? "math" : void 0 } var tr = Object.create(null); var nr = m("text,number,password,search,email,tel,url"); function rr(e) { if ("string" === typeof e) { var t = document.querySelector(e); return t || document.createElement("div") } return e } var ir = Object.freeze({ createElement: function (e, t) { var n = document.createElement(e); return "select" !== e ? n : (t.data && t.data.attrs && void 0 !== t.data.attrs.multiple && n.setAttribute("multiple", "multiple"), n) }, createElementNS: function (e, t) { return document.createElementNS(Wn[e], t) }, createTextNode: function (e) { return document.createTextNode(e) }, createComment: function (e) { return document.createComment(e) }, insertBefore: function (e, t, n) { e.insertBefore(t, n) }, removeChild: function (e, t) { e.removeChild(t) }, appendChild: function (e, t) { e.appendChild(t) }, parentNode: function (e) { return e.parentNode }, nextSibling: function (e) { return e.nextSibling }, tagName: function (e) { return e.tagName }, setTextContent: function (e, t) { e.textContent = t }, setStyleScope: function (e, t) { e.setAttribute(t, "") } }), or = { create: function (e, t) { ar(t) }, update: function (e, t) { e.data.ref !== t.data.ref && (ar(e, !0), ar(t)) }, destroy: function (e) { ar(e, !0) } }; function ar(e, t) { var n = e.data.ref; if (o(n)) { var r = e.context, i = e.componentInstance || e.elm, a = r.$refs; t ? Array.isArray(a[n]) ? b(a[n], i) : a[n] === i && (a[n] = void 0) : e.data.refInFor ? Array.isArray(a[n]) ? a[n].indexOf(i) < 0 && a[n].push(i) : a[n] = [i] : a[n] = i } } var sr = new ge("", {}, []), cr = ["create", "activate", "update", "remove", "destroy"]; function ur(e, t) { return e.key === t.key && (e.tag === t.tag && e.isComment === t.isComment && o(e.data) === o(t.data) && function (e, t) { if ("input" !== e.tag) return !0; var n, r = o(n = e.data) && o(n = n.attrs) && n.type, i = o(n = t.data) && o(n = n.attrs) && n.type; return r === i || nr(r) && nr(i) }(e, t) || a(e.isAsyncPlaceholder) && e.asyncFactory === t.asyncFactory && i(t.asyncFactory.error)) } function lr(e, t, n) { var r, i, a = {}; for (r = t; r <= n; ++r)o(i = e[r].key) && (a[i] = r); return a } var fr = { create: pr, update: pr, destroy: function (e) { pr(e, sr) } }; function pr(e, t) { (e.data.directives || t.data.directives) && function (e, t) { var n, r, i, o = e === sr, a = t === sr, s = hr(e.data.directives, e.context), c = hr(t.data.directives, t.context), u = [], l = []; for (n in c) r = s[n], i = c[n], r ? (i.oldValue = r.value, i.oldArg = r.arg, mr(i, "update", t, e), i.def && i.def.componentUpdated && l.push(i)) : (mr(i, "bind", t, e), i.def && i.def.inserted && u.push(i)); if (u.length) { var f = function () { for (var n = 0; n < u.length; n++)mr(u[n], "inserted", t, e) }; o ? ut(t, "insert", f) : f() } l.length && ut(t, "postpatch", (function () { for (var n = 0; n < l.length; n++)mr(l[n], "componentUpdated", t, e) })); if (!o) for (n in s) c[n] || mr(s[n], "unbind", e, e, a) }(e, t) } var dr = Object.create(null); function hr(e, t) { var n, r, i = Object.create(null); if (!e) return i; for (n = 0; n < e.length; n++)(r = e[n]).modifiers || (r.modifiers = dr), i[vr(r)] = r, r.def = ze(t.$options, "directives", r.name); return i } function vr(e) { return e.rawName || e.name + "." + Object.keys(e.modifiers || {}).join(".") } function mr(e, t, n, r, i) { var o = e.def && e.def[t]; if (o) try { o(n.elm, e, n, r, i) } catch (hs) { Ue(hs, n.context, "directive " + e.name + " " + t + " hook") } } var gr = [or, fr]; function yr(e, t) { var n = t.componentOptions; if ((!o(n) || !1 !== n.Ctor.options.inheritAttrs) && (!i(e.data.attrs) || !i(t.data.attrs))) { var r, a, s = t.elm, c = e.data.attrs || {}, u = t.data.attrs || {}; for (r in o(u.__ob__) && (u = t.data.attrs = T({}, u)), u) a = u[r], c[r] !== a && br(s, r, a); for (r in (X || ee) && u.value !== c.value && br(s, "value", u.value), c) i(u[r]) && (Gn(r) ? s.removeAttributeNS(Bn, Un(r)) : Nn(r) || s.removeAttribute(r)) } } function br(e, t, n) { e.tagName.indexOf("-") > -1 ? _r(e, t, n) : Hn(t) ? Zn(n) ? e.removeAttribute(t) : (n = "allowfullscreen" === t && "EMBED" === e.tagName ? "true" : t, e.setAttribute(t, n)) : Nn(t) ? e.setAttribute(t, Dn(t, n)) : Gn(t) ? Zn(n) ? e.removeAttributeNS(Bn, Un(t)) : e.setAttributeNS(Bn, t, n) : _r(e, t, n) } function _r(e, t, n) { if (Zn(n)) e.removeAttribute(t); else { if (X && !Q && "TEXTAREA" === e.tagName && "placeholder" === t && "" !== n && !e.__ieph) { var r = function (t) { t.stopImmediatePropagation(), e.removeEventListener("input", r) }; e.addEventListener("input", r), e.__ieph = !0 } e.setAttribute(t, n) } } var kr = { create: yr, update: yr }; function wr(e, t) { var n = t.elm, r = t.data, a = e.data; if (!(i(r.staticClass) && i(r.class) && (i(a) || i(a.staticClass) && i(a.class)))) { var s = qn(t), c = n._transitionClasses; o(c) && (s = Kn(s, Jn(c))), s !== n._prevClass && (n.setAttribute("class", s), n._prevClass = s) } } var xr, Cr, Ar, $r, Sr, Or, Er = { create: wr, update: wr }, Tr = /[\w).+\-_$\]]/; function Lr(e) { var t, n, r, i, o, a = !1, s = !1, c = !1, u = !1, l = 0, f = 0, p = 0, d = 0; for (r = 0; r < e.length; r++)if (n = t, t = e.charCodeAt(r), a) 39 === t && 92 !== n && (a = !1); else if (s) 34 === t && 92 !== n && (s = !1); else if (c) 96 === t && 92 !== n && (c = !1); else if (u) 47 === t && 92 !== n && (u = !1); else if (124 !== t || 124 === e.charCodeAt(r + 1) || 124 === e.charCodeAt(r - 1) || l || f || p) { switch (t) { case 34: s = !0; break; case 39: a = !0; break; case 96: c = !0; break; case 40: p++; break; case 41: p--; break; case 91: f++; break; case 93: f--; break; case 123: l++; break; case 125: l-- }if (47 === t) { for (var h = r - 1, v = void 0; h >= 0 && " " === (v = e.charAt(h)); h--); v && Tr.test(v) || (u = !0) } } else void 0 === i ? (d = r + 1, i = e.slice(0, r).trim()) : m(); function m() { (o || (o = [])).push(e.slice(d, r).trim()), d = r + 1 } if (void 0 === i ? i = e.slice(0, r).trim() : 0 !== d && m(), o) for (r = 0; r < o.length; r++)i = jr(i, o[r]); return i } function jr(e, t) { var n = t.indexOf("("); if (n < 0) return '_f("' + t + '")(' + e + ")"; var r = t.slice(0, n), i = t.slice(n + 1); return '_f("' + r + '")(' + e + (")" !== i ? "," + i : i) } function Ir(e, t) { console.error("[Vue compiler]: " + e) } function Fr(e, t) { return e ? e.map((function (e) { return e[t] })).filter((function (e) { return e })) : [] } function Mr(e, t, n, r, i) { (e.props || (e.props = [])).push(Ur({ name: t, value: n, dynamic: i }, r)), e.plain = !1 } function Rr(e, t, n, r, i) { (i ? e.dynamicAttrs || (e.dynamicAttrs = []) : e.attrs || (e.attrs = [])).push(Ur({ name: t, value: n, dynamic: i }, r)), e.plain = !1 } function Pr(e, t, n, r) { e.attrsMap[t] = n, e.attrsList.push(Ur({ name: t, value: n }, r)) } function Nr(e, t, n, r, i, o, a, s) { (e.directives || (e.directives = [])).push(Ur({ name: t, rawName: n, value: r, arg: i, isDynamicArg: o, modifiers: a }, s)), e.plain = !1 } function zr(e, t, n) { return n ? "_p(" + t + ',"' + e + '")' : e + t } function Dr(e, t, n, i, o, a, s, c) { var u; (i = i || r).right ? c ? t = "(" + t + ")==='click'?'contextmenu':(" + t + ")" : "click" === t && (t = "contextmenu", delete i.right) : i.middle && (c ? t = "(" + t + ")==='click'?'mouseup':(" + t + ")" : "click" === t && (t = "mouseup")), i.capture && (delete i.capture, t = zr("!", t, c)), i.once && (delete i.once, t = zr("~", t, c)), i.passive && (delete i.passive, t = zr("&", t, c)), i.native ? (delete i.native, u = e.nativeEvents || (e.nativeEvents = {})) : u = e.events || (e.events = {}); var l = Ur({ value: n.trim(), dynamic: c }, s); i !== r && (l.modifiers = i); var f = u[t]; Array.isArray(f) ? o ? f.unshift(l) : f.push(l) : u[t] = f ? o ? [l, f] : [f, l] : l, e.plain = !1 } function Hr(e, t, n) { var r = Br(e, ":" + t) || Br(e, "v-bind:" + t); if (null != r) return Lr(r); if (!1 !== n) { var i = Br(e, t); if (null != i) return JSON.stringify(i) } } function Br(e, t, n) { var r; if (null != (r = e.attrsMap[t])) for (var i = e.attrsList, o = 0, a = i.length; o < a; o++)if (i[o].name === t) { i.splice(o, 1); break } return n && delete e.attrsMap[t], r } function Gr(e, t) { for (var n = e.attrsList, r = 0, i = n.length; r < i; r++) { var o = n[r]; if (t.test(o.name)) return n.splice(r, 1), o } } function Ur(e, t) { return t && (null != t.start && (e.start = t.start), null != t.end && (e.end = t.end)), e } function Zr(e, t, n) { var r = n || {}, i = r.number, o = "$$v"; r.trim && (o = "(typeof $$v === 'string'? $$v.trim(): $$v)"), i && (o = "_n(" + o + ")"); var a = qr(t, o); e.model = { value: "(" + t + ")", expression: JSON.stringify(t), callback: "function ($$v) {" + a + "}" } } function qr(e, t) { var n = function (e) { if (e = e.trim(), xr = e.length, e.indexOf("[") < 0 || e.lastIndexOf("]") < xr - 1) return ($r = e.lastIndexOf(".")) > -1 ? { exp: e.slice(0, $r), key: '"' + e.slice($r + 1) + '"' } : { exp: e, key: null }; Cr = e, $r = Sr = Or = 0; for (; !Kr();)Jr(Ar = Vr()) ? Yr(Ar) : 91 === Ar && Wr(Ar); return { exp: e.slice(0, Sr), key: e.slice(Sr + 1, Or) } }(e); return null === n.key ? e + "=" + t : "$set(" + n.exp + ", " + n.key + ", " + t + ")" } function Vr() { return Cr.charCodeAt(++$r) } function Kr() { return $r >= xr } function Jr(e) { return 34 === e || 39 === e } function Wr(e) { var t = 1; for (Sr = $r; !Kr();)if (Jr(e = Vr())) Yr(e); else if (91 === e && t++ , 93 === e && t-- , 0 === t) { Or = $r; break } } function Yr(e) { for (var t = e; !Kr() && (e = Vr()) !== t;); } var Xr, Qr = "__r", ei = "__c"; function ti(e, t, n) { var r = Xr; return function i() { var o = t.apply(null, arguments); null !== o && ii(e, i, n, r) } } var ni = Je && !(ne && Number(ne[1]) <= 53); function ri(e, t, n, r) { if (ni) { var i = fn, o = t; t = o._wrapper = function (e) { if (e.target === e.currentTarget || e.timeStamp >= i || e.timeStamp <= 0 || e.target.ownerDocument !== document) return o.apply(this, arguments) } } Xr.addEventListener(e, t, ie ? { capture: n, passive: r } : n) } function ii(e, t, n, r) { (r || Xr).removeEventListener(e, t._wrapper || t, n) } function oi(e, t) { if (!i(e.data.on) || !i(t.data.on)) { var n = t.data.on || {}, r = e.data.on || {}; Xr = t.elm, function (e) { if (o(e[Qr])) { var t = X ? "change" : "input"; e[t] = [].concat(e[Qr], e[t] || []), delete e[Qr] } o(e[ei]) && (e.change = [].concat(e[ei], e.change || []), delete e[ei]) }(n), ct(n, r, ri, ii, ti, t.context), Xr = void 0 } } var ai, si = { create: oi, update: oi }; function ci(e, t) { if (!i(e.data.domProps) || !i(t.data.domProps)) { var n, r, a = t.elm, s = e.data.domProps || {}, c = t.data.domProps || {}; for (n in o(c.__ob__) && (c = t.data.domProps = T({}, c)), s) n in c || (a[n] = ""); for (n in c) { if (r = c[n], "textContent" === n || "innerHTML" === n) { if (t.children && (t.children.length = 0), r === s[n]) continue; 1 === a.childNodes.length && a.removeChild(a.childNodes[0]) } if ("value" === n && "PROGRESS" !== a.tagName) { a._value = r; var u = i(r) ? "" : String(r); ui(a, u) && (a.value = u) } else if ("innerHTML" === n && Xn(a.tagName) && i(a.innerHTML)) { (ai = ai || document.createElement("div")).innerHTML = "<svg>" + r + "</svg>"; for (var l = ai.firstChild; a.firstChild;)a.removeChild(a.firstChild); for (; l.firstChild;)a.appendChild(l.firstChild) } else if (r !== s[n]) try { a[n] = r } catch (hs) { } } } } function ui(e, t) { return !e.composing && ("OPTION" === e.tagName || function (e, t) { var n = !0; try { n = document.activeElement !== e } catch (hs) { } return n && e.value !== t }(e, t) || function (e, t) { var n = e.value, r = e._vModifiers; if (o(r)) { if (r.number) return v(n) !== v(t); if (r.trim) return n.trim() !== t.trim() } return n !== t }(e, t)) } var li = { create: ci, update: ci }, fi = w((function (e) { var t = {}, n = /:(.+)/; return e.split(/;(?![^(]*\))/g).forEach((function (e) { if (e) { var r = e.split(n); r.length > 1 && (t[r[0].trim()] = r[1].trim()) } })), t })); function pi(e) { var t = di(e.style); return e.staticStyle ? T(e.staticStyle, t) : t } function di(e) { return Array.isArray(e) ? L(e) : "string" === typeof e ? fi(e) : e } var hi, vi = /^--/, mi = /\s*!important$/, gi = function (e, t, n) { if (vi.test(t)) e.style.setProperty(t, n); else if (mi.test(n)) e.style.setProperty(S(t), n.replace(mi, ""), "important"); else { var r = bi(t); if (Array.isArray(n)) for (var i = 0, o = n.length; i < o; i++)e.style[r] = n[i]; else e.style[r] = n } }, yi = ["Webkit", "Moz", "ms"], bi = w((function (e) { if (hi = hi || document.createElement("div").style, "filter" !== (e = C(e)) && e in hi) return e; for (var t = e.charAt(0).toUpperCase() + e.slice(1), n = 0; n < yi.length; n++) { var r = yi[n] + t; if (r in hi) return r } })); function _i(e, t) { var n = t.data, r = e.data; if (!(i(n.staticStyle) && i(n.style) && i(r.staticStyle) && i(r.style))) { var a, s, c = t.elm, u = r.staticStyle, l = r.normalizedStyle || r.style || {}, f = u || l, p = di(t.data.style) || {}; t.data.normalizedStyle = o(p.__ob__) ? T({}, p) : p; var d = function (e, t) { var n, r = {}; if (t) for (var i = e; i.componentInstance;)(i = i.componentInstance._vnode) && i.data && (n = pi(i.data)) && T(r, n); (n = pi(e.data)) && T(r, n); for (var o = e; o = o.parent;)o.data && (n = pi(o.data)) && T(r, n); return r }(t, !0); for (s in f) i(d[s]) && gi(c, s, ""); for (s in d) (a = d[s]) !== f[s] && gi(c, s, null == a ? "" : a) } } var ki = { create: _i, update: _i }, wi = /\s+/; function xi(e, t) { if (t && (t = t.trim())) if (e.classList) t.indexOf(" ") > -1 ? t.split(wi).forEach((function (t) { return e.classList.add(t) })) : e.classList.add(t); else { var n = " " + (e.getAttribute("class") || "") + " "; n.indexOf(" " + t + " ") < 0 && e.setAttribute("class", (n + t).trim()) } } function Ci(e, t) { if (t && (t = t.trim())) if (e.classList) t.indexOf(" ") > -1 ? t.split(wi).forEach((function (t) { return e.classList.remove(t) })) : e.classList.remove(t), e.classList.length || e.removeAttribute("class"); else { for (var n = " " + (e.getAttribute("class") || "") + " ", r = " " + t + " "; n.indexOf(r) >= 0;)n = n.replace(r, " "); (n = n.trim()) ? e.setAttribute("class", n) : e.removeAttribute("class") } } function Ai(e) { if (e) { if ("object" === typeof e) { var t = {}; return !1 !== e.css && T(t, $i(e.name || "v")), T(t, e), t } return "string" === typeof e ? $i(e) : void 0 } } var $i = w((function (e) { return { enterClass: e + "-enter", enterToClass: e + "-enter-to", enterActiveClass: e + "-enter-active", leaveClass: e + "-leave", leaveToClass: e + "-leave-to", leaveActiveClass: e + "-leave-active" } })), Si = K && !Q, Oi = "transition", Ei = "animation", Ti = "transition", Li = "transitionend", ji = "animation", Ii = "animationend"; Si && (void 0 === window.ontransitionend && void 0 !== window.onwebkittransitionend && (Ti = "WebkitTransition", Li = "webkitTransitionEnd"), void 0 === window.onanimationend && void 0 !== window.onwebkitanimationend && (ji = "WebkitAnimation", Ii = "webkitAnimationEnd")); var Fi = K ? window.requestAnimationFrame ? window.requestAnimationFrame.bind(window) : setTimeout : function (e) { return e() }; function Mi(e) { Fi((function () { Fi(e) })) } function Ri(e, t) { var n = e._transitionClasses || (e._transitionClasses = []); n.indexOf(t) < 0 && (n.push(t), xi(e, t)) } function Pi(e, t) { e._transitionClasses && b(e._transitionClasses, t), Ci(e, t) } function Ni(e, t, n) { var r = Di(e, t), i = r.type, o = r.timeout, a = r.propCount; if (!i) return n(); var s = i === Oi ? Li : Ii, c = 0, u = function () { e.removeEventListener(s, l), n() }, l = function (t) { t.target === e && ++c >= a && u() }; setTimeout((function () { c < a && u() }), o + 1), e.addEventListener(s, l) } var zi = /\b(transform|all)(,|$)/; function Di(e, t) { var n, r = window.getComputedStyle(e), i = (r[Ti + "Delay"] || "").split(", "), o = (r[Ti + "Duration"] || "").split(", "), a = Hi(i, o), s = (r[ji + "Delay"] || "").split(", "), c = (r[ji + "Duration"] || "").split(", "), u = Hi(s, c), l = 0, f = 0; return t === Oi ? a > 0 && (n = Oi, l = a, f = o.length) : t === Ei ? u > 0 && (n = Ei, l = u, f = c.length) : f = (n = (l = Math.max(a, u)) > 0 ? a > u ? Oi : Ei : null) ? n === Oi ? o.length : c.length : 0, { type: n, timeout: l, propCount: f, hasTransform: n === Oi && zi.test(r[Ti + "Property"]) } } function Hi(e, t) { for (; e.length < t.length;)e = e.concat(e); return Math.max.apply(null, t.map((function (t, n) { return Bi(t) + Bi(e[n]) }))) } function Bi(e) { return 1e3 * Number(e.slice(0, -1).replace(",", ".")) } function Gi(e, t) { var n = e.elm; o(n._leaveCb) && (n._leaveCb.cancelled = !0, n._leaveCb()); var r = Ai(e.data.transition); if (!i(r) && !o(n._enterCb) && 1 === n.nodeType) { for (var a = r.css, s = r.type, u = r.enterClass, l = r.enterToClass, f = r.enterActiveClass, p = r.appearClass, d = r.appearToClass, h = r.appearActiveClass, m = r.beforeEnter, g = r.enter, y = r.afterEnter, b = r.enterCancelled, _ = r.beforeAppear, k = r.appear, w = r.afterAppear, x = r.appearCancelled, C = r.duration, A = Qt, $ = Qt.$vnode; $ && $.parent;)A = $.context, $ = $.parent; var S = !A._isMounted || !e.isRootInsert; if (!S || k || "" === k) { var O = S && p ? p : u, E = S && h ? h : f, T = S && d ? d : l, L = S && _ || m, j = S && "function" === typeof k ? k : g, I = S && w || y, F = S && x || b, M = v(c(C) ? C.enter : C); 0; var R = !1 !== a && !Q, N = qi(j), z = n._enterCb = P((function () { R && (Pi(n, T), Pi(n, E)), z.cancelled ? (R && Pi(n, O), F && F(n)) : I && I(n), n._enterCb = null })); e.data.show || ut(e, "insert", (function () { var t = n.parentNode, r = t && t._pending && t._pending[e.key]; r && r.tag === e.tag && r.elm._leaveCb && r.elm._leaveCb(), j && j(n, z) })), L && L(n), R && (Ri(n, O), Ri(n, E), Mi((function () { Pi(n, O), z.cancelled || (Ri(n, T), N || (Zi(M) ? setTimeout(z, M) : Ni(n, s, z))) }))), e.data.show && (t && t(), j && j(n, z)), R || N || z() } } } function Ui(e, t) { var n = e.elm; o(n._enterCb) && (n._enterCb.cancelled = !0, n._enterCb()); var r = Ai(e.data.transition); if (i(r) || 1 !== n.nodeType) return t(); if (!o(n._leaveCb)) { var a = r.css, s = r.type, u = r.leaveClass, l = r.leaveToClass, f = r.leaveActiveClass, p = r.beforeLeave, d = r.leave, h = r.afterLeave, m = r.leaveCancelled, g = r.delayLeave, y = r.duration, b = !1 !== a && !Q, _ = qi(d), k = v(c(y) ? y.leave : y); 0; var w = n._leaveCb = P((function () { n.parentNode && n.parentNode._pending && (n.parentNode._pending[e.key] = null), b && (Pi(n, l), Pi(n, f)), w.cancelled ? (b && Pi(n, u), m && m(n)) : (t(), h && h(n)), n._leaveCb = null })); g ? g(x) : x() } function x() { w.cancelled || (!e.data.show && n.parentNode && ((n.parentNode._pending || (n.parentNode._pending = {}))[e.key] = e), p && p(n), b && (Ri(n, u), Ri(n, f), Mi((function () { Pi(n, u), w.cancelled || (Ri(n, l), _ || (Zi(k) ? setTimeout(w, k) : Ni(n, s, w))) }))), d && d(n, w), b || _ || w()) } } function Zi(e) { return "number" === typeof e && !isNaN(e) } function qi(e) { if (i(e)) return !1; var t = e.fns; return o(t) ? qi(Array.isArray(t) ? t[0] : t) : (e._length || e.length) > 1 } function Vi(e, t) { !0 !== t.data.show && Gi(t) } var Ki = function (e) { var t, n, r = {}, c = e.modules, u = e.nodeOps; for (t = 0; t < cr.length; ++t)for (r[cr[t]] = [], n = 0; n < c.length; ++n)o(c[n][cr[t]]) && r[cr[t]].push(c[n][cr[t]]); function l(e) { var t = u.parentNode(e); o(t) && u.removeChild(t, e) } function f(e, t, n, i, s, c, l) { if (o(e.elm) && o(c) && (e = c[l] = ke(e)), e.isRootInsert = !s, !function (e, t, n, i) { var s = e.data; if (o(s)) { var c = o(e.componentInstance) && s.keepAlive; if (o(s = s.hook) && o(s = s.init) && s(e, !1), o(e.componentInstance)) return p(e, t), d(n, e.elm, i), a(c) && function (e, t, n, i) { var a, s = e; for (; s.componentInstance;)if (s = s.componentInstance._vnode, o(a = s.data) && o(a = a.transition)) { for (a = 0; a < r.activate.length; ++a)r.activate[a](sr, s); t.push(s); break } d(n, e.elm, i) }(e, t, n, i), !0 } }(e, t, n, i)) { var f = e.data, v = e.children, m = e.tag; o(m) ? (e.elm = e.ns ? u.createElementNS(e.ns, m) : u.createElement(m, e), y(e), h(e, v, t), o(f) && g(e, t), d(n, e.elm, i)) : a(e.isComment) ? (e.elm = u.createComment(e.text), d(n, e.elm, i)) : (e.elm = u.createTextNode(e.text), d(n, e.elm, i)) } } function p(e, t) { o(e.data.pendingInsert) && (t.push.apply(t, e.data.pendingInsert), e.data.pendingInsert = null), e.elm = e.componentInstance.$el, v(e) ? (g(e, t), y(e)) : (ar(e), t.push(e)) } function d(e, t, n) { o(e) && (o(n) ? u.parentNode(n) === e && u.insertBefore(e, t, n) : u.appendChild(e, t)) } function h(e, t, n) { if (Array.isArray(t)) { 0; for (var r = 0; r < t.length; ++r)f(t[r], n, e.elm, null, !0, t, r) } else s(e.text) && u.appendChild(e.elm, u.createTextNode(String(e.text))) } function v(e) { for (; e.componentInstance;)e = e.componentInstance._vnode; return o(e.tag) } function g(e, n) { for (var i = 0; i < r.create.length; ++i)r.create[i](sr, e); o(t = e.data.hook) && (o(t.create) && t.create(sr, e), o(t.insert) && n.push(e)) } function y(e) { var t; if (o(t = e.fnScopeId)) u.setStyleScope(e.elm, t); else for (var n = e; n;)o(t = n.context) && o(t = t.$options._scopeId) && u.setStyleScope(e.elm, t), n = n.parent; o(t = Qt) && t !== e.context && t !== e.fnContext && o(t = t.$options._scopeId) && u.setStyleScope(e.elm, t) } function b(e, t, n, r, i, o) { for (; r <= i; ++r)f(n[r], o, e, t, !1, n, r) } function _(e) { var t, n, i = e.data; if (o(i)) for (o(t = i.hook) && o(t = t.destroy) && t(e), t = 0; t < r.destroy.length; ++t)r.destroy[t](e); if (o(t = e.children)) for (n = 0; n < e.children.length; ++n)_(e.children[n]) } function k(e, t, n, r) { for (; n <= r; ++n) { var i = t[n]; o(i) && (o(i.tag) ? (w(i), _(i)) : l(i.elm)) } } function w(e, t) { if (o(t) || o(e.data)) { var n, i = r.remove.length + 1; for (o(t) ? t.listeners += i : t = function (e, t) { function n() { 0 === --n.listeners && l(e) } return n.listeners = t, n }(e.elm, i), o(n = e.componentInstance) && o(n = n._vnode) && o(n.data) && w(n, t), n = 0; n < r.remove.length; ++n)r.remove[n](e, t); o(n = e.data.hook) && o(n = n.remove) ? n(e, t) : t() } else l(e.elm) } function x(e, t, n, r) { for (var i = n; i < r; i++) { var a = t[i]; if (o(a) && ur(e, a)) return i } } function C(e, t, n, s, c, l) { if (e !== t) { o(t.elm) && o(s) && (t = s[c] = ke(t)); var p = t.elm = e.elm; if (a(e.isAsyncPlaceholder)) o(t.asyncFactory.resolved) ? S(e.elm, t, n) : t.isAsyncPlaceholder = !0; else if (a(t.isStatic) && a(e.isStatic) && t.key === e.key && (a(t.isCloned) || a(t.isOnce))) t.componentInstance = e.componentInstance; else { var d, h = t.data; o(h) && o(d = h.hook) && o(d = d.prepatch) && d(e, t); var m = e.children, g = t.children; if (o(h) && v(t)) { for (d = 0; d < r.update.length; ++d)r.update[d](e, t); o(d = h.hook) && o(d = d.update) && d(e, t) } i(t.text) ? o(m) && o(g) ? m !== g && function (e, t, n, r, a) { var s, c, l, p = 0, d = 0, h = t.length - 1, v = t[0], m = t[h], g = n.length - 1, y = n[0], _ = n[g], w = !a; for (0; p <= h && d <= g;)i(v) ? v = t[++p] : i(m) ? m = t[--h] : ur(v, y) ? (C(v, y, r, n, d), v = t[++p], y = n[++d]) : ur(m, _) ? (C(m, _, r, n, g), m = t[--h], _ = n[--g]) : ur(v, _) ? (C(v, _, r, n, g), w && u.insertBefore(e, v.elm, u.nextSibling(m.elm)), v = t[++p], _ = n[--g]) : ur(m, y) ? (C(m, y, r, n, d), w && u.insertBefore(e, m.elm, v.elm), m = t[--h], y = n[++d]) : (i(s) && (s = lr(t, p, h)), i(c = o(y.key) ? s[y.key] : x(y, t, p, h)) ? f(y, r, e, v.elm, !1, n, d) : ur(l = t[c], y) ? (C(l, y, r, n, d), t[c] = void 0, w && u.insertBefore(e, l.elm, v.elm)) : f(y, r, e, v.elm, !1, n, d), y = n[++d]); p > h ? b(e, i(n[g + 1]) ? null : n[g + 1].elm, n, d, g, r) : d > g && k(0, t, p, h) }(p, m, g, n, l) : o(g) ? (o(e.text) && u.setTextContent(p, ""), b(p, null, g, 0, g.length - 1, n)) : o(m) ? k(0, m, 0, m.length - 1) : o(e.text) && u.setTextContent(p, "") : e.text !== t.text && u.setTextContent(p, t.text), o(h) && o(d = h.hook) && o(d = d.postpatch) && d(e, t) } } } function A(e, t, n) { if (a(n) && o(e.parent)) e.parent.data.pendingInsert = t; else for (var r = 0; r < t.length; ++r)t[r].data.hook.insert(t[r]) } var $ = m("attrs,class,staticClass,staticStyle,key"); function S(e, t, n, r) { var i, s = t.tag, c = t.data, u = t.children; if (r = r || c && c.pre, t.elm = e, a(t.isComment) && o(t.asyncFactory)) return t.isAsyncPlaceholder = !0, !0; if (o(c) && (o(i = c.hook) && o(i = i.init) && i(t, !0), o(i = t.componentInstance))) return p(t, n), !0; if (o(s)) { if (o(u)) if (e.hasChildNodes()) if (o(i = c) && o(i = i.domProps) && o(i = i.innerHTML)) { if (i !== e.innerHTML) return !1 } else { for (var l = !0, f = e.firstChild, d = 0; d < u.length; d++) { if (!f || !S(f, u[d], n, r)) { l = !1; break } f = f.nextSibling } if (!l || f) return !1 } else h(t, u, n); if (o(c)) { var v = !1; for (var m in c) if (!$(m)) { v = !0, g(t, n); break } !v && c.class && ot(c.class) } } else e.data !== t.text && (e.data = t.text); return !0 } return function (e, t, n, s) { if (!i(t)) { var c, l = !1, p = []; if (i(e)) l = !0, f(t, p); else { var d = o(e.nodeType); if (!d && ur(e, t)) C(e, t, p, null, null, s); else { if (d) { if (1 === e.nodeType && e.hasAttribute(N) && (e.removeAttribute(N), n = !0), a(n) && S(e, t, p)) return A(t, p, !0), e; c = e, e = new ge(u.tagName(c).toLowerCase(), {}, [], void 0, c) } var h = e.elm, m = u.parentNode(h); if (f(t, p, h._leaveCb ? null : m, u.nextSibling(h)), o(t.parent)) for (var g = t.parent, y = v(t); g;) { for (var b = 0; b < r.destroy.length; ++b)r.destroy[b](g); if (g.elm = t.elm, y) { for (var w = 0; w < r.create.length; ++w)r.create[w](sr, g); var x = g.data.hook.insert; if (x.merged) for (var $ = 1; $ < x.fns.length; $++)x.fns[$]() } else ar(g); g = g.parent } o(m) ? k(0, [e], 0, 0) : o(e.tag) && _(e) } } return A(t, p, l), t.elm } o(e) && _(e) } }({ nodeOps: ir, modules: [kr, Er, si, li, ki, K ? { create: Vi, activate: Vi, remove: function (e, t) { !0 !== e.data.show ? Ui(e, t) : t() } } : {}].concat(gr) }); Q && document.addEventListener("selectionchange", (function () { var e = document.activeElement; e && e.vmodel && no(e, "input") })); var Ji = { inserted: function (e, t, n, r) { "select" === n.tag ? (r.elm && !r.elm._vOptions ? ut(n, "postpatch", (function () { Ji.componentUpdated(e, t, n) })) : Wi(e, t, n.context), e._vOptions = [].map.call(e.options, Qi)) : ("textarea" === n.tag || nr(e.type)) && (e._vModifiers = t.modifiers, t.modifiers.lazy || (e.addEventListener("compositionstart", eo), e.addEventListener("compositionend", to), e.addEventListener("change", to), Q && (e.vmodel = !0))) }, componentUpdated: function (e, t, n) { if ("select" === n.tag) { Wi(e, t, n.context); var r = e._vOptions, i = e._vOptions = [].map.call(e.options, Qi); if (i.some((function (e, t) { return !M(e, r[t]) }))) (e.multiple ? t.value.some((function (e) { return Xi(e, i) })) : t.value !== t.oldValue && Xi(t.value, i)) && no(e, "change") } } }; function Wi(e, t, n) { Yi(e, t, n), (X || ee) && setTimeout((function () { Yi(e, t, n) }), 0) } function Yi(e, t, n) { var r = t.value, i = e.multiple; if (!i || Array.isArray(r)) { for (var o, a, s = 0, c = e.options.length; s < c; s++)if (a = e.options[s], i) o = R(r, Qi(a)) > -1, a.selected !== o && (a.selected = o); else if (M(Qi(a), r)) return void (e.selectedIndex !== s && (e.selectedIndex = s)); i || (e.selectedIndex = -1) } } function Xi(e, t) { return t.every((function (t) { return !M(t, e) })) } function Qi(e) { return "_value" in e ? e._value : e.value } function eo(e) { e.target.composing = !0 } function to(e) { e.target.composing && (e.target.composing = !1, no(e.target, "input")) } function no(e, t) { var n = document.createEvent("HTMLEvents"); n.initEvent(t, !0, !0), e.dispatchEvent(n) } function ro(e) { return !e.componentInstance || e.data && e.data.transition ? e : ro(e.componentInstance._vnode) } var io = { model: Ji, show: { bind: function (e, t, n) { var r = t.value, i = (n = ro(n)).data && n.data.transition, o = e.__vOriginalDisplay = "none" === e.style.display ? "" : e.style.display; r && i ? (n.data.show = !0, Gi(n, (function () { e.style.display = o }))) : e.style.display = r ? o : "none" }, update: function (e, t, n) { var r = t.value; !r !== !t.oldValue && ((n = ro(n)).data && n.data.transition ? (n.data.show = !0, r ? Gi(n, (function () { e.style.display = e.__vOriginalDisplay })) : Ui(n, (function () { e.style.display = "none" }))) : e.style.display = r ? e.__vOriginalDisplay : "none") }, unbind: function (e, t, n, r, i) { i || (e.style.display = e.__vOriginalDisplay) } } }, oo = { name: String, appear: Boolean, css: Boolean, mode: String, type: String, enterClass: String, leaveClass: String, enterToClass: String, leaveToClass: String, enterActiveClass: String, leaveActiveClass: String, appearClass: String, appearActiveClass: String, appearToClass: String, duration: [Number, String, Object] }; function ao(e) { var t = e && e.componentOptions; return t && t.Ctor.options.abstract ? ao(Kt(t.children)) : e } function so(e) { var t = {}, n = e.$options; for (var r in n.propsData) t[r] = e[r]; var i = n._parentListeners; for (var o in i) t[C(o)] = i[o]; return t } function co(e, t) { if (/\d-keep-alive$/.test(t.tag)) return e("keep-alive", { props: t.componentOptions.propsData }) } var uo = function (e) { return e.tag || Vt(e) }, lo = function (e) { return "show" === e.name }, fo = { name: "transition", props: oo, abstract: !0, render: function (e) { var t = this, n = this.$slots.default; if (n && (n = n.filter(uo)).length) { 0; var r = this.mode; 0; var i = n[0]; if (function (e) { for (; e = e.parent;)if (e.data.transition) return !0 }(this.$vnode)) return i; var o = ao(i); if (!o) return i; if (this._leaving) return co(e, i); var a = "__transition-" + this._uid + "-"; o.key = null == o.key ? o.isComment ? a + "comment" : a + o.tag : s(o.key) ? 0 === String(o.key).indexOf(a) ? o.key : a + o.key : o.key; var c = (o.data || (o.data = {})).transition = so(this), u = this._vnode, l = ao(u); if (o.data.directives && o.data.directives.some(lo) && (o.data.show = !0), l && l.data && !function (e, t) { return t.key === e.key && t.tag === e.tag }(o, l) && !Vt(l) && (!l.componentInstance || !l.componentInstance._vnode.isComment)) { var f = l.data.transition = T({}, c); if ("out-in" === r) return this._leaving = !0, ut(f, "afterLeave", (function () { t._leaving = !1, t.$forceUpdate() })), co(e, i); if ("in-out" === r) { if (Vt(o)) return u; var p, d = function () { p() }; ut(c, "afterEnter", d), ut(c, "enterCancelled", d), ut(f, "delayLeave", (function (e) { p = e })) } } return i } } }, po = T({ tag: String, moveClass: String }, oo); function ho(e) { e.elm._moveCb && e.elm._moveCb(), e.elm._enterCb && e.elm._enterCb() } function vo(e) { e.data.newPos = e.elm.getBoundingClientRect() } function mo(e) { var t = e.data.pos, n = e.data.newPos, r = t.left - n.left, i = t.top - n.top; if (r || i) { e.data.moved = !0; var o = e.elm.style; o.transform = o.WebkitTransform = "translate(" + r + "px," + i + "px)", o.transitionDuration = "0s" } } delete po.mode; var go = { Transition: fo, TransitionGroup: { props: po, beforeMount: function () { var e = this, t = this._update; this._update = function (n, r) { var i = en(e); e.__patch__(e._vnode, e.kept, !1, !0), e._vnode = e.kept, i(), t.call(e, n, r) } }, render: function (e) { for (var t = this.tag || this.$vnode.data.tag || "span", n = Object.create(null), r = this.prevChildren = this.children, i = this.$slots.default || [], o = this.children = [], a = so(this), s = 0; s < i.length; s++) { var c = i[s]; if (c.tag) if (null != c.key && 0 !== String(c.key).indexOf("__vlist")) o.push(c), n[c.key] = c, (c.data || (c.data = {})).transition = a; else; } if (r) { for (var u = [], l = [], f = 0; f < r.length; f++) { var p = r[f]; p.data.transition = a, p.data.pos = p.elm.getBoundingClientRect(), n[p.key] ? u.push(p) : l.push(p) } this.kept = e(t, null, u), this.removed = l } return e(t, null, o) }, updated: function () { var e = this.prevChildren, t = this.moveClass || (this.name || "v") + "-move"; e.length && this.hasMove(e[0].elm, t) && (e.forEach(ho), e.forEach(vo), e.forEach(mo), this._reflow = document.body.offsetHeight, e.forEach((function (e) { if (e.data.moved) { var n = e.elm, r = n.style; Ri(n, t), r.transform = r.WebkitTransform = r.transitionDuration = "", n.addEventListener(Li, n._moveCb = function e(r) { r && r.target !== n || r && !/transform$/.test(r.propertyName) || (n.removeEventListener(Li, e), n._moveCb = null, Pi(n, t)) }) } }))) }, methods: { hasMove: function (e, t) { if (!Si) return !1; if (this._hasMove) return this._hasMove; var n = e.cloneNode(); e._transitionClasses && e._transitionClasses.forEach((function (e) { Ci(n, e) })), xi(n, t), n.style.display = "none", this.$el.appendChild(n); var r = Di(n); return this.$el.removeChild(n), this._hasMove = r.hasTransform } } } }; Sn.config.mustUseProp = Pn, Sn.config.isReservedTag = Qn, Sn.config.isReservedAttr = Mn, Sn.config.getTagNamespace = er, Sn.config.isUnknownElement = function (e) { if (!K) return !0; if (Qn(e)) return !1; if (e = e.toLowerCase(), null != tr[e]) return tr[e]; var t = document.createElement(e); return e.indexOf("-") > -1 ? tr[e] = t.constructor === window.HTMLUnknownElement || t.constructor === window.HTMLElement : tr[e] = /HTMLUnknownElement/.test(t.toString()) }, T(Sn.options.directives, io), T(Sn.options.components, go), Sn.prototype.__patch__ = K ? Ki : j, Sn.prototype.$mount = function (e, t) { return function (e, t, n) { var r; return e.$el = t, e.$options.render || (e.$options.render = be), rn(e, "beforeMount"), r = function () { e._update(e._render(), n) }, new mn(e, r, j, { before: function () { e._isMounted && !e._isDestroyed && rn(e, "beforeUpdate") } }, !0), n = !1, null == e.$vnode && (e._isMounted = !0, rn(e, "mounted")), e }(this, e = e && K ? rr(e) : void 0, t) }, K && setTimeout((function () { H.devtools && se && se.emit("init", Sn) }), 0); var yo = /\{\{((?:.|\r?\n)+?)\}\}/g, bo = /[-.*+?^${}()|[\]\/\\]/g, _o = w((function (e) { var t = e[0].replace(bo, "\\$&"), n = e[1].replace(bo, "\\$&"); return new RegExp(t + "((?:.|\\n)+?)" + n, "g") })); var ko = { staticKeys: ["staticClass"], transformNode: function (e, t) { t.warn; var n = Br(e, "class"); n && (e.staticClass = JSON.stringify(n)); var r = Hr(e, "class", !1); r && (e.classBinding = r) }, genData: function (e) { var t = ""; return e.staticClass && (t += "staticClass:" + e.staticClass + ","), e.classBinding && (t += "class:" + e.classBinding + ","), t } }; var wo, xo = { staticKeys: ["staticStyle"], transformNode: function (e, t) { t.warn; var n = Br(e, "style"); n && (e.staticStyle = JSON.stringify(fi(n))); var r = Hr(e, "style", !1); r && (e.styleBinding = r) }, genData: function (e) { var t = ""; return e.staticStyle && (t += "staticStyle:" + e.staticStyle + ","), e.styleBinding && (t += "style:(" + e.styleBinding + "),"), t } }, Co = function (e) { return (wo = wo || document.createElement("div")).innerHTML = e, wo.textContent }, Ao = m("area,base,br,col,embed,frame,hr,img,input,isindex,keygen,link,meta,param,source,track,wbr"), $o = m("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr,source"), So = m("address,article,aside,base,blockquote,body,caption,col,colgroup,dd,details,dialog,div,dl,dt,fieldset,figcaption,figure,footer,form,h1,h2,h3,h4,h5,h6,head,header,hgroup,hr,html,legend,li,menuitem,meta,optgroup,option,param,rp,rt,source,style,summary,tbody,td,tfoot,th,thead,title,tr,track"), Oo = /^\s*([^\s"'<>\/=]+)(?:\s*(=)\s*(?:"([^"]*)"+|'([^']*)'+|([^\s"'=<>`]+)))?/, Eo = /^\s*((?:v-[\w-]+:|@|:|#)\[[^=]+\][^\s"'<>\/=]*)(?:\s*(=)\s*(?:"([^"]*)"+|'([^']*)'+|([^\s"'=<>`]+)))?/, To = "[a-zA-Z_][\\-\\.0-9_a-zA-Z" + B.source + "]*", Lo = "((?:" + To + "\\:)?" + To + ")", jo = new RegExp("^<" + Lo), Io = /^\s*(\/?)>/, Fo = new RegExp("^<\\/" + Lo + "[^>]*>"), Mo = /^<!DOCTYPE [^>]+>/i, Ro = /^<!\--/, Po = /^<!\[/, No = m("script,style,textarea", !0), zo = {}, Do = { "&lt;": "<", "&gt;": ">", "&quot;": '"', "&amp;": "&", "&#10;": "\n", "&#9;": "\t", "&#39;": "'" }, Ho = /&(?:lt|gt|quot|amp|#39);/g, Bo = /&(?:lt|gt|quot|amp|#39|#10|#9);/g, Go = m("pre,textarea", !0), Uo = function (e, t) { return e && Go(e) && "\n" === t[0] }; function Zo(e, t) { var n = t ? Bo : Ho; return e.replace(n, (function (e) { return Do[e] })) } var qo, Vo, Ko, Jo, Wo, Yo, Xo, Qo, ea = /^@|^v-on:/, ta = /^v-|^@|^:/, na = /([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/, ra = /,([^,\}\]]*)(?:,([^,\}\]]*))?$/, ia = /^\(|\)$/g, oa = /^\[.*\]$/, aa = /:(.*)$/, sa = /^:|^\.|^v-bind:/, ca = /\.[^.\]]+(?=[^\]]*$)/g, ua = /^v-slot(:|$)|^#/, la = /[\r\n]/, fa = /\s+/g, pa = w(Co), da = "_empty_"; function ha(e, t, n) { return { type: 1, tag: e, attrsList: t, attrsMap: ka(t), rawAttrsMap: {}, parent: n, children: [] } } function va(e, t) { qo = t.warn || Ir, Yo = t.isPreTag || I, Xo = t.mustUseProp || I, Qo = t.getTagNamespace || I; var n = t.isReservedTag || I; (function (e) { return !!e.component || !n(e.tag) }), Ko = Fr(t.modules, "transformNode"), Jo = Fr(t.modules, "preTransformNode"), Wo = Fr(t.modules, "postTransformNode"), Vo = t.delimiters; var r, i, o = [], a = !1 !== t.preserveWhitespace, s = t.whitespace, c = !1, u = !1; function l(e) { if (f(e), c || e.processed || (e = ma(e, t)), o.length || e === r || r.if && (e.elseif || e.else) && ya(r, { exp: e.elseif, block: e }), i && !e.forbidden) if (e.elseif || e.else) !function (e, t) { var n = function (e) { var t = e.length; for (; t--;) { if (1 === e[t].type) return e[t]; e.pop() } }(t.children); n && n.if && ya(n, { exp: e.elseif, block: e }) }(e, i); else { if (e.slotScope) { var n = e.slotTarget || '"default"'; (i.scopedSlots || (i.scopedSlots = {}))[n] = e } i.children.push(e), e.parent = i } e.children = e.children.filter((function (e) { return !e.slotScope })), f(e), e.pre && (c = !1), Yo(e.tag) && (u = !1); for (var a = 0; a < Wo.length; a++)Wo[a](e, t) } function f(e) { if (!u) for (var t; (t = e.children[e.children.length - 1]) && 3 === t.type && " " === t.text;)e.children.pop() } return function (e, t) { for (var n, r, i = [], o = t.expectHTML, a = t.isUnaryTag || I, s = t.canBeLeftOpenTag || I, c = 0; e;) { if (n = e, r && No(r)) { var u = 0, l = r.toLowerCase(), f = zo[l] || (zo[l] = new RegExp("([\\s\\S]*?)(</" + l + "[^>]*>)", "i")), p = e.replace(f, (function (e, n, r) { return u = r.length, No(l) || "noscript" === l || (n = n.replace(/<!\--([\s\S]*?)-->/g, "$1").replace(/<!\[CDATA\[([\s\S]*?)]]>/g, "$1")), Uo(l, n) && (n = n.slice(1)), t.chars && t.chars(n), "" })); c += e.length - p.length, e = p, $(l, c - u, c) } else { var d = e.indexOf("<"); if (0 === d) { if (Ro.test(e)) { var h = e.indexOf("--\x3e"); if (h >= 0) { t.shouldKeepComment && t.comment(e.substring(4, h), c, c + h + 3), x(h + 3); continue } } if (Po.test(e)) { var v = e.indexOf("]>"); if (v >= 0) { x(v + 2); continue } } var m = e.match(Mo); if (m) { x(m[0].length); continue } var g = e.match(Fo); if (g) { var y = c; x(g[0].length), $(g[1], y, c); continue } var b = C(); if (b) { A(b), Uo(b.tagName, e) && x(1); continue } } var _ = void 0, k = void 0, w = void 0; if (d >= 0) { for (k = e.slice(d); !Fo.test(k) && !jo.test(k) && !Ro.test(k) && !Po.test(k) && !((w = k.indexOf("<", 1)) < 0);)d += w, k = e.slice(d); _ = e.substring(0, d) } d < 0 && (_ = e), _ && x(_.length), t.chars && _ && t.chars(_, c - _.length, c) } if (e === n) { t.chars && t.chars(e); break } } function x(t) { c += t, e = e.substring(t) } function C() { var t = e.match(jo); if (t) { var n, r, i = { tagName: t[1], attrs: [], start: c }; for (x(t[0].length); !(n = e.match(Io)) && (r = e.match(Eo) || e.match(Oo));)r.start = c, x(r[0].length), r.end = c, i.attrs.push(r); if (n) return i.unarySlash = n[1], x(n[0].length), i.end = c, i } } function A(e) { var n = e.tagName, c = e.unarySlash; o && ("p" === r && So(n) && $(r), s(n) && r === n && $(n)); for (var u = a(n) || !!c, l = e.attrs.length, f = new Array(l), p = 0; p < l; p++) { var d = e.attrs[p], h = d[3] || d[4] || d[5] || "", v = "a" === n && "href" === d[1] ? t.shouldDecodeNewlinesForHref : t.shouldDecodeNewlines; f[p] = { name: d[1], value: Zo(h, v) } } u || (i.push({ tag: n, lowerCasedTag: n.toLowerCase(), attrs: f, start: e.start, end: e.end }), r = n), t.start && t.start(n, f, u, e.start, e.end) } function $(e, n, o) { var a, s; if (null == n && (n = c), null == o && (o = c), e) for (s = e.toLowerCase(), a = i.length - 1; a >= 0 && i[a].lowerCasedTag !== s; a--); else a = 0; if (a >= 0) { for (var u = i.length - 1; u >= a; u--)t.end && t.end(i[u].tag, n, o); i.length = a, r = a && i[a - 1].tag } else "br" === s ? t.start && t.start(e, [], !0, n, o) : "p" === s && (t.start && t.start(e, [], !1, n, o), t.end && t.end(e, n, o)) } $() }(e, { warn: qo, expectHTML: t.expectHTML, isUnaryTag: t.isUnaryTag, canBeLeftOpenTag: t.canBeLeftOpenTag, shouldDecodeNewlines: t.shouldDecodeNewlines, shouldDecodeNewlinesForHref: t.shouldDecodeNewlinesForHref, shouldKeepComment: t.comments, outputSourceRange: t.outputSourceRange, start: function (e, n, a, s, f) { var p = i && i.ns || Qo(e); X && "svg" === p && (n = function (e) { for (var t = [], n = 0; n < e.length; n++) { var r = e[n]; wa.test(r.name) || (r.name = r.name.replace(xa, ""), t.push(r)) } return t }(n)); var d, h = ha(e, n, i); p && (h.ns = p), "style" !== (d = h).tag && ("script" !== d.tag || d.attrsMap.type && "text/javascript" !== d.attrsMap.type) || ae() || (h.forbidden = !0); for (var v = 0; v < Jo.length; v++)h = Jo[v](h, t) || h; c || (!function (e) { null != Br(e, "v-pre") && (e.pre = !0) }(h), h.pre && (c = !0)), Yo(h.tag) && (u = !0), c ? function (e) { var t = e.attrsList, n = t.length; if (n) for (var r = e.attrs = new Array(n), i = 0; i < n; i++)r[i] = { name: t[i].name, value: JSON.stringify(t[i].value) }, null != t[i].start && (r[i].start = t[i].start, r[i].end = t[i].end); else e.pre || (e.plain = !0) }(h) : h.processed || (ga(h), function (e) { var t = Br(e, "v-if"); if (t) e.if = t, ya(e, { exp: t, block: e }); else { null != Br(e, "v-else") && (e.else = !0); var n = Br(e, "v-else-if"); n && (e.elseif = n) } }(h), function (e) { null != Br(e, "v-once") && (e.once = !0) }(h)), r || (r = h), a ? l(h) : (i = h, o.push(h)) }, end: function (e, t, n) { var r = o[o.length - 1]; o.length -= 1, i = o[o.length - 1], l(r) }, chars: function (e, t, n) { if (i && (!X || "textarea" !== i.tag || i.attrsMap.placeholder !== e)) { var r, o, l, f = i.children; if (e = u || e.trim() ? "script" === (r = i).tag || "style" === r.tag ? e : pa(e) : f.length ? s ? "condense" === s && la.test(e) ? "" : " " : a ? " " : "" : "") u || "condense" !== s || (e = e.replace(fa, " ")), !c && " " !== e && (o = function (e, t) { var n = t ? _o(t) : yo; if (n.test(e)) { for (var r, i, o, a = [], s = [], c = n.lastIndex = 0; r = n.exec(e);) { (i = r.index) > c && (s.push(o = e.slice(c, i)), a.push(JSON.stringify(o))); var u = Lr(r[1].trim()); a.push("_s(" + u + ")"), s.push({ "@binding": u }), c = i + r[0].length } return c < e.length && (s.push(o = e.slice(c)), a.push(JSON.stringify(o))), { expression: a.join("+"), tokens: s } } }(e, Vo)) ? l = { type: 2, expression: o.expression, tokens: o.tokens, text: e } : " " === e && f.length && " " === f[f.length - 1].text || (l = { type: 3, text: e }), l && f.push(l) } }, comment: function (e, t, n) { if (i) { var r = { type: 3, text: e, isComment: !0 }; 0, i.children.push(r) } } }), r } function ma(e, t) { var n; !function (e) { var t = Hr(e, "key"); if (t) { e.key = t } }(e), e.plain = !e.key && !e.scopedSlots && !e.attrsList.length, function (e) { var t = Hr(e, "ref"); t && (e.ref = t, e.refInFor = function (e) { var t = e; for (; t;) { if (void 0 !== t.for) return !0; t = t.parent } return !1 }(e)) }(e), function (e) { var t; "template" === e.tag ? (t = Br(e, "scope"), e.slotScope = t || Br(e, "slot-scope")) : (t = Br(e, "slot-scope")) && (e.slotScope = t); var n = Hr(e, "slot"); n && (e.slotTarget = '""' === n ? '"default"' : n, e.slotTargetDynamic = !(!e.attrsMap[":slot"] && !e.attrsMap["v-bind:slot"]), "template" === e.tag || e.slotScope || Rr(e, "slot", n, function (e, t) { return e.rawAttrsMap[":" + t] || e.rawAttrsMap["v-bind:" + t] || e.rawAttrsMap[t] }(e, "slot"))); if ("template" === e.tag) { var r = Gr(e, ua); if (r) { 0; var i = ba(r), o = i.name, a = i.dynamic; e.slotTarget = o, e.slotTargetDynamic = a, e.slotScope = r.value || da } } else { var s = Gr(e, ua); if (s) { 0; var c = e.scopedSlots || (e.scopedSlots = {}), u = ba(s), l = u.name, f = u.dynamic, p = c[l] = ha("template", [], e); p.slotTarget = l, p.slotTargetDynamic = f, p.children = e.children.filter((function (e) { if (!e.slotScope) return e.parent = p, !0 })), p.slotScope = s.value || da, e.children = [], e.plain = !1 } } }(e), "slot" === (n = e).tag && (n.slotName = Hr(n, "name")), function (e) { var t; (t = Hr(e, "is")) && (e.component = t); null != Br(e, "inline-template") && (e.inlineTemplate = !0) }(e); for (var r = 0; r < Ko.length; r++)e = Ko[r](e, t) || e; return function (e) { var t, n, r, i, o, a, s, c, u = e.attrsList; for (t = 0, n = u.length; t < n; t++) { if (r = i = u[t].name, o = u[t].value, ta.test(r)) if (e.hasBindings = !0, (a = _a(r.replace(ta, ""))) && (r = r.replace(ca, "")), sa.test(r)) r = r.replace(sa, ""), o = Lr(o), (c = oa.test(r)) && (r = r.slice(1, -1)), a && (a.prop && !c && "innerHtml" === (r = C(r)) && (r = "innerHTML"), a.camel && !c && (r = C(r)), a.sync && (s = qr(o, "$event"), c ? Dr(e, '"update:"+(' + r + ")", s, null, !1, 0, u[t], !0) : (Dr(e, "update:" + C(r), s, null, !1, 0, u[t]), S(r) !== C(r) && Dr(e, "update:" + S(r), s, null, !1, 0, u[t])))), a && a.prop || !e.component && Xo(e.tag, e.attrsMap.type, r) ? Mr(e, r, o, u[t], c) : Rr(e, r, o, u[t], c); else if (ea.test(r)) r = r.replace(ea, ""), (c = oa.test(r)) && (r = r.slice(1, -1)), Dr(e, r, o, a, !1, 0, u[t], c); else { var l = (r = r.replace(ta, "")).match(aa), f = l && l[1]; c = !1, f && (r = r.slice(0, -(f.length + 1)), oa.test(f) && (f = f.slice(1, -1), c = !0)), Nr(e, r, i, o, f, c, a, u[t]) } else Rr(e, r, JSON.stringify(o), u[t]), !e.component && "muted" === r && Xo(e.tag, e.attrsMap.type, r) && Mr(e, r, "true", u[t]) } }(e), e } function ga(e) { var t; if (t = Br(e, "v-for")) { var n = function (e) { var t = e.match(na); if (!t) return; var n = {}; n.for = t[2].trim(); var r = t[1].trim().replace(ia, ""), i = r.match(ra); i ? (n.alias = r.replace(ra, "").trim(), n.iterator1 = i[1].trim(), i[2] && (n.iterator2 = i[2].trim())) : n.alias = r; return n }(t); n && T(e, n) } } function ya(e, t) { e.ifConditions || (e.ifConditions = []), e.ifConditions.push(t) } function ba(e) { var t = e.name.replace(ua, ""); return t || "#" !== e.name[0] && (t = "default"), oa.test(t) ? { name: t.slice(1, -1), dynamic: !0 } : { name: '"' + t + '"', dynamic: !1 } } function _a(e) { var t = e.match(ca); if (t) { var n = {}; return t.forEach((function (e) { n[e.slice(1)] = !0 })), n } } function ka(e) { for (var t = {}, n = 0, r = e.length; n < r; n++)t[e[n].name] = e[n].value; return t } var wa = /^xmlns:NS\d+/, xa = /^NS\d+:/; function Ca(e) { return ha(e.tag, e.attrsList.slice(), e.parent) } var Aa = [ko, xo, { preTransformNode: function (e, t) { if ("input" === e.tag) { var n, r = e.attrsMap; if (!r["v-model"]) return; if ((r[":type"] || r["v-bind:type"]) && (n = Hr(e, "type")), r.type || n || !r["v-bind"] || (n = "(" + r["v-bind"] + ").type"), n) { var i = Br(e, "v-if", !0), o = i ? "&&(" + i + ")" : "", a = null != Br(e, "v-else", !0), s = Br(e, "v-else-if", !0), c = Ca(e); ga(c), Pr(c, "type", "checkbox"), ma(c, t), c.processed = !0, c.if = "(" + n + ")==='checkbox'" + o, ya(c, { exp: c.if, block: c }); var u = Ca(e); Br(u, "v-for", !0), Pr(u, "type", "radio"), ma(u, t), ya(c, { exp: "(" + n + ")==='radio'" + o, block: u }); var l = Ca(e); return Br(l, "v-for", !0), Pr(l, ":type", n), ma(l, t), ya(c, { exp: i, block: l }), a ? c.else = !0 : s && (c.elseif = s), c } } } }]; var $a, Sa, Oa = { expectHTML: !0, modules: Aa, directives: { model: function (e, t, n) { n; var r = t.value, i = t.modifiers, o = e.tag, a = e.attrsMap.type; if (e.component) return Zr(e, r, i), !1; if ("select" === o) !function (e, t, n) { var r = 'var $$selectedVal = Array.prototype.filter.call($event.target.options,function(o){return o.selected}).map(function(o){var val = "_value" in o ? o._value : o.value;return ' + (n && n.number ? "_n(val)" : "val") + "});"; r = r + " " + qr(t, "$event.target.multiple ? $$selectedVal : $$selectedVal[0]"), Dr(e, "change", r, null, !0) }(e, r, i); else if ("input" === o && "checkbox" === a) !function (e, t, n) { var r = n && n.number, i = Hr(e, "value") || "null", o = Hr(e, "true-value") || "true", a = Hr(e, "false-value") || "false"; Mr(e, "checked", "Array.isArray(" + t + ")?_i(" + t + "," + i + ")>-1" + ("true" === o ? ":(" + t + ")" : ":_q(" + t + "," + o + ")")), Dr(e, "change", "var $$a=" + t + ",$$el=$event.target,$$c=$$el.checked?(" + o + "):(" + a + ");if(Array.isArray($$a)){var $$v=" + (r ? "_n(" + i + ")" : i) + ",$$i=_i($$a,$$v);if($$el.checked){$$i<0&&(" + qr(t, "$$a.concat([$$v])") + ")}else{$$i>-1&&(" + qr(t, "$$a.slice(0,$$i).concat($$a.slice($$i+1))") + ")}}else{" + qr(t, "$$c") + "}", null, !0) }(e, r, i); else if ("input" === o && "radio" === a) !function (e, t, n) { var r = n && n.number, i = Hr(e, "value") || "null"; Mr(e, "checked", "_q(" + t + "," + (i = r ? "_n(" + i + ")" : i) + ")"), Dr(e, "change", qr(t, i), null, !0) }(e, r, i); else if ("input" === o || "textarea" === o) !function (e, t, n) { var r = e.attrsMap.type; 0; var i = n || {}, o = i.lazy, a = i.number, s = i.trim, c = !o && "range" !== r, u = o ? "change" : "range" === r ? Qr : "input", l = "$event.target.value"; s && (l = "$event.target.value.trim()"); a && (l = "_n(" + l + ")"); var f = qr(t, l); c && (f = "if($event.target.composing)return;" + f); Mr(e, "value", "(" + t + ")"), Dr(e, u, f, null, !0), (s || a) && Dr(e, "blur", "$forceUpdate()") }(e, r, i); else { if (!H.isReservedTag(o)) return Zr(e, r, i), !1 } return !0 }, text: function (e, t) { t.value && Mr(e, "textContent", "_s(" + t.value + ")", t) }, html: function (e, t) { t.value && Mr(e, "innerHTML", "_s(" + t.value + ")", t) } }, isPreTag: function (e) { return "pre" === e }, isUnaryTag: Ao, mustUseProp: Pn, canBeLeftOpenTag: $o, isReservedTag: Qn, getTagNamespace: er, staticKeys: function (e) { return e.reduce((function (e, t) { return e.concat(t.staticKeys || []) }), []).join(",") }(Aa) }, Ea = w((function (e) { return m("type,tag,attrsList,attrsMap,plain,parent,children,attrs,start,end,rawAttrsMap" + (e ? "," + e : "")) })); function Ta(e, t) { e && ($a = Ea(t.staticKeys || ""), Sa = t.isReservedTag || I, function e(t) { t.static = function (e) { if (2 === e.type) return !1; if (3 === e.type) return !0; return !(!e.pre && (e.hasBindings || e.if || e.for || g(e.tag) || !Sa(e.tag) || function (e) { for (; e.parent;) { if ("template" !== (e = e.parent).tag) return !1; if (e.for) return !0 } return !1 }(e) || !Object.keys(e).every($a))) }(t); if (1 === t.type) { if (!Sa(t.tag) && "slot" !== t.tag && null == t.attrsMap["inline-template"]) return; for (var n = 0, r = t.children.length; n < r; n++) { var i = t.children[n]; e(i), i.static || (t.static = !1) } if (t.ifConditions) for (var o = 1, a = t.ifConditions.length; o < a; o++) { var s = t.ifConditions[o].block; e(s), s.static || (t.static = !1) } } }(e), function e(t, n) { if (1 === t.type) { if ((t.static || t.once) && (t.staticInFor = n), t.static && t.children.length && (1 !== t.children.length || 3 !== t.children[0].type)) return void (t.staticRoot = !0); if (t.staticRoot = !1, t.children) for (var r = 0, i = t.children.length; r < i; r++)e(t.children[r], n || !!t.for); if (t.ifConditions) for (var o = 1, a = t.ifConditions.length; o < a; o++)e(t.ifConditions[o].block, n) } }(e, !1)) } var La = /^([\w$_]+|\([^)]*?\))\s*=>|^function\s*(?:[\w$]+)?\s*\(/, ja = /\([^)]*?\);*$/, Ia = /^[A-Za-z_$][\w$]*(?:\.[A-Za-z_$][\w$]*|\['[^']*?']|\["[^"]*?"]|\[\d+]|\[[A-Za-z_$][\w$]*])*$/, Fa = { esc: 27, tab: 9, enter: 13, space: 32, up: 38, left: 37, right: 39, down: 40, delete: [8, 46] }, Ma = { esc: ["Esc", "Escape"], tab: "Tab", enter: "Enter", space: [" ", "Spacebar"], up: ["Up", "ArrowUp"], left: ["Left", "ArrowLeft"], right: ["Right", "ArrowRight"], down: ["Down", "ArrowDown"], delete: ["Backspace", "Delete", "Del"] }, Ra = function (e) { return "if(" + e + ")return null;" }, Pa = { stop: "$event.stopPropagation();", prevent: "$event.preventDefault();", self: Ra("$event.target !== $event.currentTarget"), ctrl: Ra("!$event.ctrlKey"), shift: Ra("!$event.shiftKey"), alt: Ra("!$event.altKey"), meta: Ra("!$event.metaKey"), left: Ra("'button' in $event && $event.button !== 0"), middle: Ra("'button' in $event && $event.button !== 1"), right: Ra("'button' in $event && $event.button !== 2") }; function Na(e, t) { var n = t ? "nativeOn:" : "on:", r = "", i = ""; for (var o in e) { var a = za(e[o]); e[o] && e[o].dynamic ? i += o + "," + a + "," : r += '"' + o + '":' + a + "," } return r = "{" + r.slice(0, -1) + "}", i ? n + "_d(" + r + ",[" + i.slice(0, -1) + "])" : n + r } function za(e) { if (!e) return "function(){}"; if (Array.isArray(e)) return "[" + e.map((function (e) { return za(e) })).join(",") + "]"; var t = Ia.test(e.value), n = La.test(e.value), r = Ia.test(e.value.replace(ja, "")); if (e.modifiers) { var i = "", o = "", a = []; for (var s in e.modifiers) if (Pa[s]) o += Pa[s], Fa[s] && a.push(s); else if ("exact" === s) { var c = e.modifiers; o += Ra(["ctrl", "shift", "alt", "meta"].filter((function (e) { return !c[e] })).map((function (e) { return "$event." + e + "Key" })).join("||")) } else a.push(s); return a.length && (i += function (e) { return "if(!$event.type.indexOf('key')&&" + e.map(Da).join("&&") + ")return null;" }(a)), o && (i += o), "function($event){" + i + (t ? "return " + e.value + "($event)" : n ? "return (" + e.value + ")($event)" : r ? "return " + e.value : e.value) + "}" } return t || n ? e.value : "function($event){" + (r ? "return " + e.value : e.value) + "}" } function Da(e) { var t = parseInt(e, 10); if (t) return "$event.keyCode!==" + t; var n = Fa[e], r = Ma[e]; return "_k($event.keyCode," + JSON.stringify(e) + "," + JSON.stringify(n) + ",$event.key," + JSON.stringify(r) + ")" } var Ha = { on: function (e, t) { e.wrapListeners = function (e) { return "_g(" + e + "," + t.value + ")" } }, bind: function (e, t) { e.wrapData = function (n) { return "_b(" + n + ",'" + e.tag + "'," + t.value + "," + (t.modifiers && t.modifiers.prop ? "true" : "false") + (t.modifiers && t.modifiers.sync ? ",true" : "") + ")" } }, cloak: j }, Ba = function (e) { this.options = e, this.warn = e.warn || Ir, this.transforms = Fr(e.modules, "transformCode"), this.dataGenFns = Fr(e.modules, "genData"), this.directives = T(T({}, Ha), e.directives); var t = e.isReservedTag || I; this.maybeComponent = function (e) { return !!e.component || !t(e.tag) }, this.onceId = 0, this.staticRenderFns = [], this.pre = !1 }; function Ga(e, t) { var n = new Ba(t); return { render: "with(this){return " + (e ? Ua(e, n) : '_c("div")') + "}", staticRenderFns: n.staticRenderFns } } function Ua(e, t) { if (e.parent && (e.pre = e.pre || e.parent.pre), e.staticRoot && !e.staticProcessed) return Za(e, t); if (e.once && !e.onceProcessed) return qa(e, t); if (e.for && !e.forProcessed) return Ka(e, t); if (e.if && !e.ifProcessed) return Va(e, t); if ("template" !== e.tag || e.slotTarget || t.pre) { if ("slot" === e.tag) return function (e, t) { var n = e.slotName || '"default"', r = Xa(e, t), i = "_t(" + n + (r ? "," + r : ""), o = e.attrs || e.dynamicAttrs ? ts((e.attrs || []).concat(e.dynamicAttrs || []).map((function (e) { return { name: C(e.name), value: e.value, dynamic: e.dynamic } }))) : null, a = e.attrsMap["v-bind"]; !o && !a || r || (i += ",null"); o && (i += "," + o); a && (i += (o ? "" : ",null") + "," + a); return i + ")" }(e, t); var n; if (e.component) n = function (e, t, n) { var r = t.inlineTemplate ? null : Xa(t, n, !0); return "_c(" + e + "," + Ja(t, n) + (r ? "," + r : "") + ")" }(e.component, e, t); else { var r; (!e.plain || e.pre && t.maybeComponent(e)) && (r = Ja(e, t)); var i = e.inlineTemplate ? null : Xa(e, t, !0); n = "_c('" + e.tag + "'" + (r ? "," + r : "") + (i ? "," + i : "") + ")" } for (var o = 0; o < t.transforms.length; o++)n = t.transforms[o](e, n); return n } return Xa(e, t) || "void 0" } function Za(e, t) { e.staticProcessed = !0; var n = t.pre; return e.pre && (t.pre = e.pre), t.staticRenderFns.push("with(this){return " + Ua(e, t) + "}"), t.pre = n, "_m(" + (t.staticRenderFns.length - 1) + (e.staticInFor ? ",true" : "") + ")" } function qa(e, t) { if (e.onceProcessed = !0, e.if && !e.ifProcessed) return Va(e, t); if (e.staticInFor) { for (var n = "", r = e.parent; r;) { if (r.for) { n = r.key; break } r = r.parent } return n ? "_o(" + Ua(e, t) + "," + t.onceId++ + "," + n + ")" : Ua(e, t) } return Za(e, t) } function Va(e, t, n, r) { return e.ifProcessed = !0, function e(t, n, r, i) { if (!t.length) return i || "_e()"; var o = t.shift(); return o.exp ? "(" + o.exp + ")?" + a(o.block) + ":" + e(t, n, r, i) : "" + a(o.block); function a(e) { return r ? r(e, n) : e.once ? qa(e, n) : Ua(e, n) } }(e.ifConditions.slice(), t, n, r) } function Ka(e, t, n, r) { var i = e.for, o = e.alias, a = e.iterator1 ? "," + e.iterator1 : "", s = e.iterator2 ? "," + e.iterator2 : ""; return e.forProcessed = !0, (r || "_l") + "((" + i + "),function(" + o + a + s + "){return " + (n || Ua)(e, t) + "})" } function Ja(e, t) { var n = "{", r = function (e, t) { var n = e.directives; if (!n) return; var r, i, o, a, s = "directives:[", c = !1; for (r = 0, i = n.length; r < i; r++) { o = n[r], a = !0; var u = t.directives[o.name]; u && (a = !!u(e, o, t.warn)), a && (c = !0, s += '{name:"' + o.name + '",rawName:"' + o.rawName + '"' + (o.value ? ",value:(" + o.value + "),expression:" + JSON.stringify(o.value) : "") + (o.arg ? ",arg:" + (o.isDynamicArg ? o.arg : '"' + o.arg + '"') : "") + (o.modifiers ? ",modifiers:" + JSON.stringify(o.modifiers) : "") + "},") } if (c) return s.slice(0, -1) + "]" }(e, t); r && (n += r + ","), e.key && (n += "key:" + e.key + ","), e.ref && (n += "ref:" + e.ref + ","), e.refInFor && (n += "refInFor:true,"), e.pre && (n += "pre:true,"), e.component && (n += 'tag:"' + e.tag + '",'); for (var i = 0; i < t.dataGenFns.length; i++)n += t.dataGenFns[i](e); if (e.attrs && (n += "attrs:" + ts(e.attrs) + ","), e.props && (n += "domProps:" + ts(e.props) + ","), e.events && (n += Na(e.events, !1) + ","), e.nativeEvents && (n += Na(e.nativeEvents, !0) + ","), e.slotTarget && !e.slotScope && (n += "slot:" + e.slotTarget + ","), e.scopedSlots && (n += function (e, t, n) { var r = e.for || Object.keys(t).some((function (e) { var n = t[e]; return n.slotTargetDynamic || n.if || n.for || Wa(n) })), i = !!e.if; if (!r) for (var o = e.parent; o;) { if (o.slotScope && o.slotScope !== da || o.for) { r = !0; break } o.if && (i = !0), o = o.parent } var a = Object.keys(t).map((function (e) { return Ya(t[e], n) })).join(","); return "scopedSlots:_u([" + a + "]" + (r ? ",null,true" : "") + (!r && i ? ",null,false," + function (e) { var t = 5381, n = e.length; for (; n;)t = 33 * t ^ e.charCodeAt(--n); return t >>> 0 }(a) : "") + ")" }(e, e.scopedSlots, t) + ","), e.model && (n += "model:{value:" + e.model.value + ",callback:" + e.model.callback + ",expression:" + e.model.expression + "},"), e.inlineTemplate) { var o = function (e, t) { var n = e.children[0]; 0; if (n && 1 === n.type) { var r = Ga(n, t.options); return "inlineTemplate:{render:function(){" + r.render + "},staticRenderFns:[" + r.staticRenderFns.map((function (e) { return "function(){" + e + "}" })).join(",") + "]}" } }(e, t); o && (n += o + ",") } return n = n.replace(/,$/, "") + "}", e.dynamicAttrs && (n = "_b(" + n + ',"' + e.tag + '",' + ts(e.dynamicAttrs) + ")"), e.wrapData && (n = e.wrapData(n)), e.wrapListeners && (n = e.wrapListeners(n)), n } function Wa(e) { return 1 === e.type && ("slot" === e.tag || e.children.some(Wa)) } function Ya(e, t) { var n = e.attrsMap["slot-scope"]; if (e.if && !e.ifProcessed && !n) return Va(e, t, Ya, "null"); if (e.for && !e.forProcessed) return Ka(e, t, Ya); var r = e.slotScope === da ? "" : String(e.slotScope), i = "function(" + r + "){return " + ("template" === e.tag ? e.if && n ? "(" + e.if + ")?" + (Xa(e, t) || "undefined") + ":undefined" : Xa(e, t) || "undefined" : Ua(e, t)) + "}", o = r ? "" : ",proxy:true"; return "{key:" + (e.slotTarget || '"default"') + ",fn:" + i + o + "}" } function Xa(e, t, n, r, i) { var o = e.children; if (o.length) { var a = o[0]; if (1 === o.length && a.for && "template" !== a.tag && "slot" !== a.tag) { var s = n ? t.maybeComponent(a) ? ",1" : ",0" : ""; return "" + (r || Ua)(a, t) + s } var c = n ? function (e, t) { for (var n = 0, r = 0; r < e.length; r++) { var i = e[r]; if (1 === i.type) { if (Qa(i) || i.ifConditions && i.ifConditions.some((function (e) { return Qa(e.block) }))) { n = 2; break } (t(i) || i.ifConditions && i.ifConditions.some((function (e) { return t(e.block) }))) && (n = 1) } } return n }(o, t.maybeComponent) : 0, u = i || es; return "[" + o.map((function (e) { return u(e, t) })).join(",") + "]" + (c ? "," + c : "") } } function Qa(e) { return void 0 !== e.for || "template" === e.tag || "slot" === e.tag } function es(e, t) { return 1 === e.type ? Ua(e, t) : 3 === e.type && e.isComment ? function (e) { return "_e(" + JSON.stringify(e.text) + ")" }(e) : function (e) { return "_v(" + (2 === e.type ? e.expression : ns(JSON.stringify(e.text))) + ")" }(e) } function ts(e) { for (var t = "", n = "", r = 0; r < e.length; r++) { var i = e[r], o = ns(i.value); i.dynamic ? n += i.name + "," + o + "," : t += '"' + i.name + '":' + o + "," } return t = "{" + t.slice(0, -1) + "}", n ? "_d(" + t + ",[" + n.slice(0, -1) + "])" : t } function ns(e) { return e.replace(/\u2028/g, "\\u2028").replace(/\u2029/g, "\\u2029") } new RegExp("\\b" + "do,if,for,let,new,try,var,case,else,with,await,break,catch,class,const,super,throw,while,yield,delete,export,import,return,switch,default,extends,finally,continue,debugger,function,arguments".split(",").join("\\b|\\b") + "\\b"), new RegExp("\\b" + "delete,typeof,void".split(",").join("\\s*\\([^\\)]*\\)|\\b") + "\\s*\\([^\\)]*\\)"); function rs(e, t) { try { return new Function(e) } catch (n) { return t.push({ err: n, code: e }), j } } function is(e) { var t = Object.create(null); return function (n, r, i) { (r = T({}, r)).warn; delete r.warn; var o = r.delimiters ? String(r.delimiters) + n : n; if (t[o]) return t[o]; var a = e(n, r); var s = {}, c = []; return s.render = rs(a.render, c), s.staticRenderFns = a.staticRenderFns.map((function (e) { return rs(e, c) })), t[o] = s } } var os, as, ss = (os = function (e, t) { var n = va(e.trim(), t); !1 !== t.optimize && Ta(n, t); var r = Ga(n, t); return { ast: n, render: r.render, staticRenderFns: r.staticRenderFns } }, function (e) { function t(t, n) { var r = Object.create(e), i = [], o = []; if (n) for (var a in n.modules && (r.modules = (e.modules || []).concat(n.modules)), n.directives && (r.directives = T(Object.create(e.directives || null), n.directives)), n) "modules" !== a && "directives" !== a && (r[a] = n[a]); r.warn = function (e, t, n) { (n ? o : i).push(e) }; var s = os(t.trim(), r); return s.errors = i, s.tips = o, s } return { compile: t, compileToFunctions: is(t) } })(Oa), cs = (ss.compile, ss.compileToFunctions); function us(e) { return (as = as || document.createElement("div")).innerHTML = e ? '<a href="\n"/>' : '<div a="\n"/>', as.innerHTML.indexOf("&#10;") > 0 } var ls = !!K && us(!1), fs = !!K && us(!0), ps = w((function (e) { var t = rr(e); return t && t.innerHTML })), ds = Sn.prototype.$mount; Sn.prototype.$mount = function (e, t) { if ((e = e && rr(e)) === document.body || e === document.documentElement) return this; var n = this.$options; if (!n.render) { var r = n.template; if (r) if ("string" === typeof r) "#" === r.charAt(0) && (r = ps(r)); else { if (!r.nodeType) return this; r = r.innerHTML } else e && (r = function (e) { if (e.outerHTML) return e.outerHTML; var t = document.createElement("div"); return t.appendChild(e.cloneNode(!0)), t.innerHTML }(e)); if (r) { 0; var i = cs(r, { outputSourceRange: !1, shouldDecodeNewlines: ls, shouldDecodeNewlinesForHref: fs, delimiters: n.delimiters, comments: n.comments }, this), o = i.render, a = i.staticRenderFns; n.render = o, n.staticRenderFns = a } } return ds.call(this, e, t) }, Sn.compile = cs, t.a = Sn }).call(this, n(1), n(33).setImmediate) }, function (e, t) { var n; n = function () { return this }(); try { n = n || new Function("return this")() } catch (r) { "object" === typeof window && (n = window) } e.exports = n }, function (e, t, n) { "use strict"; (function (e) { n.d(t, "b", (function () { return _ })); var r = ("undefined" !== typeof window ? window : "undefined" !== typeof e ? e : {}).__VUE_DEVTOOLS_GLOBAL_HOOK__; function i(e, t) { Object.keys(e).forEach((function (n) { return t(e[n], n) })) } var o = function (e, t) { this.runtime = t, this._children = Object.create(null), this._rawModule = e; var n = e.state; this.state = ("function" === typeof n ? n() : n) || {} }, a = { namespaced: { configurable: !0 } }; a.namespaced.get = function () { return !!this._rawModule.namespaced }, o.prototype.addChild = function (e, t) { this._children[e] = t }, o.prototype.removeChild = function (e) { delete this._children[e] }, o.prototype.getChild = function (e) { return this._children[e] }, o.prototype.update = function (e) { this._rawModule.namespaced = e.namespaced, e.actions && (this._rawModule.actions = e.actions), e.mutations && (this._rawModule.mutations = e.mutations), e.getters && (this._rawModule.getters = e.getters) }, o.prototype.forEachChild = function (e) { i(this._children, e) }, o.prototype.forEachGetter = function (e) { this._rawModule.getters && i(this._rawModule.getters, e) }, o.prototype.forEachAction = function (e) { this._rawModule.actions && i(this._rawModule.actions, e) }, o.prototype.forEachMutation = function (e) { this._rawModule.mutations && i(this._rawModule.mutations, e) }, Object.defineProperties(o.prototype, a); var s = function (e) { this.register([], e, !1) }; s.prototype.get = function (e) { return e.reduce((function (e, t) { return e.getChild(t) }), this.root) }, s.prototype.getNamespace = function (e) { var t = this.root; return e.reduce((function (e, n) { return e + ((t = t.getChild(n)).namespaced ? n + "/" : "") }), "") }, s.prototype.update = function (e) { !function e(t, n, r) { 0; n.update(r); if (r.modules) for (var i in r.modules) { if (!n.getChild(i)) return void 0; e(t.concat(i), n.getChild(i), r.modules[i]) } }([], this.root, e) }, s.prototype.register = function (e, t, n) { var r = this; void 0 === n && (n = !0); var a = new o(t, n); 0 === e.length ? this.root = a : this.get(e.slice(0, -1)).addChild(e[e.length - 1], a); t.modules && i(t.modules, (function (t, i) { r.register(e.concat(i), t, n) })) }, s.prototype.unregister = function (e) { var t = this.get(e.slice(0, -1)), n = e[e.length - 1]; t.getChild(n).runtime && t.removeChild(n) }; var c; var u = function (e) { var t = this; void 0 === e && (e = {}), !c && "undefined" !== typeof window && window.Vue && g(window.Vue); var n = e.plugins; void 0 === n && (n = []); var i = e.strict; void 0 === i && (i = !1), this._committing = !1, this._actions = Object.create(null), this._actionSubscribers = [], this._mutations = Object.create(null), this._wrappedGetters = Object.create(null), this._modules = new s(e), this._modulesNamespaceMap = Object.create(null), this._subscribers = [], this._watcherVM = new c; var o = this, a = this.dispatch, u = this.commit; this.dispatch = function (e, t) { return a.call(o, e, t) }, this.commit = function (e, t, n) { return u.call(o, e, t, n) }, this.strict = i; var l = this._modules.root.state; h(this, l, [], this._modules.root), d(this, l), n.forEach((function (e) { return e(t) })), (void 0 !== e.devtools ? e.devtools : c.config.devtools) && function (e) { r && (e._devtoolHook = r, r.emit("vuex:init", e), r.on("vuex:travel-to-state", (function (t) { e.replaceState(t) })), e.subscribe((function (e, t) { r.emit("vuex:mutation", e, t) }))) }(this) }, l = { state: { configurable: !0 } }; function f(e, t) { return t.indexOf(e) < 0 && t.push(e), function () { var n = t.indexOf(e); n > -1 && t.splice(n, 1) } } function p(e, t) { e._actions = Object.create(null), e._mutations = Object.create(null), e._wrappedGetters = Object.create(null), e._modulesNamespaceMap = Object.create(null); var n = e.state; h(e, n, [], e._modules.root, !0), d(e, n, t) } function d(e, t, n) { var r = e._vm; e.getters = {}; var o = e._wrappedGetters, a = {}; i(o, (function (t, n) { a[n] = function (e, t) { return function () { return e(t) } }(t, e), Object.defineProperty(e.getters, n, { get: function () { return e._vm[n] }, enumerable: !0 }) })); var s = c.config.silent; c.config.silent = !0, e._vm = new c({ data: { $$state: t }, computed: a }), c.config.silent = s, e.strict && function (e) { e._vm.$watch((function () { return this._data.$$state }), (function () { 0 }), { deep: !0, sync: !0 }) }(e), r && (n && e._withCommit((function () { r._data.$$state = null })), c.nextTick((function () { return r.$destroy() }))) } function h(e, t, n, r, i) { var o = !n.length, a = e._modules.getNamespace(n); if (r.namespaced && (e._modulesNamespaceMap[a] = r), !o && !i) { var s = v(t, n.slice(0, -1)), u = n[n.length - 1]; e._withCommit((function () { c.set(s, u, r.state) })) } var l = r.context = function (e, t, n) { var r = "" === t, i = { dispatch: r ? e.dispatch : function (n, r, i) { var o = m(n, r, i), a = o.payload, s = o.options, c = o.type; return s && s.root || (c = t + c), e.dispatch(c, a) }, commit: r ? e.commit : function (n, r, i) { var o = m(n, r, i), a = o.payload, s = o.options, c = o.type; s && s.root || (c = t + c), e.commit(c, a, s) } }; return Object.defineProperties(i, { getters: { get: r ? function () { return e.getters } : function () { return function (e, t) { var n = {}, r = t.length; return Object.keys(e.getters).forEach((function (i) { if (i.slice(0, r) === t) { var o = i.slice(r); Object.defineProperty(n, o, { get: function () { return e.getters[i] }, enumerable: !0 }) } })), n }(e, t) } }, state: { get: function () { return v(e.state, n) } } }), i }(e, a, n); r.forEachMutation((function (t, n) { !function (e, t, n, r) { (e._mutations[t] || (e._mutations[t] = [])).push((function (t) { n.call(e, r.state, t) })) }(e, a + n, t, l) })), r.forEachAction((function (t, n) { var r = t.root ? n : a + n, i = t.handler || t; !function (e, t, n, r) { (e._actions[t] || (e._actions[t] = [])).push((function (t, i) { var o, a = n.call(e, { dispatch: r.dispatch, commit: r.commit, getters: r.getters, state: r.state, rootGetters: e.getters, rootState: e.state }, t, i); return (o = a) && "function" === typeof o.then || (a = Promise.resolve(a)), e._devtoolHook ? a.catch((function (t) { throw e._devtoolHook.emit("vuex:error", t), t })) : a })) }(e, r, i, l) })), r.forEachGetter((function (t, n) { !function (e, t, n, r) { if (e._wrappedGetters[t]) return void 0; e._wrappedGetters[t] = function (e) { return n(r.state, r.getters, e.state, e.getters) } }(e, a + n, t, l) })), r.forEachChild((function (r, o) { h(e, t, n.concat(o), r, i) })) } function v(e, t) { return t.length ? t.reduce((function (e, t) { return e[t] }), e) : e } function m(e, t, n) { var r; return null !== (r = e) && "object" === typeof r && e.type && (n = t, t = e, e = e.type), { type: e, payload: t, options: n } } function g(e) { c && e === c || function (e) { if (Number(e.version.split(".")[0]) >= 2) e.mixin({ beforeCreate: n }); else { var t = e.prototype._init; e.prototype._init = function (e) { void 0 === e && (e = {}), e.init = e.init ? [n].concat(e.init) : n, t.call(this, e) } } function n() { var e = this.$options; e.store ? this.$store = "function" === typeof e.store ? e.store() : e.store : e.parent && e.parent.$store && (this.$store = e.parent.$store) } }(c = e) } l.state.get = function () { return this._vm._data.$$state }, l.state.set = function (e) { 0 }, u.prototype.commit = function (e, t, n) { var r = this, i = m(e, t, n), o = i.type, a = i.payload, s = (i.options, { type: o, payload: a }), c = this._mutations[o]; c && (this._withCommit((function () { c.forEach((function (e) { e(a) })) })), this._subscribers.forEach((function (e) { return e(s, r.state) }))) }, u.prototype.dispatch = function (e, t) { var n = this, r = m(e, t), i = r.type, o = r.payload, a = { type: i, payload: o }, s = this._actions[i]; if (s) { try { this._actionSubscribers.filter((function (e) { return e.before })).forEach((function (e) { return e.before(a, n.state) })) } catch (c) { 0 } return (s.length > 1 ? Promise.all(s.map((function (e) { return e(o) }))) : s[0](o)).then((function (e) { try { n._actionSubscribers.filter((function (e) { return e.after })).forEach((function (e) { return e.after(a, n.state) })) } catch (c) { 0 } return e })) } }, u.prototype.subscribe = function (e) { return f(e, this._subscribers) }, u.prototype.subscribeAction = function (e) { return f("function" === typeof e ? { before: e } : e, this._actionSubscribers) }, u.prototype.watch = function (e, t, n) { var r = this; return this._watcherVM.$watch((function () { return e(r.state, r.getters) }), t, n) }, u.prototype.replaceState = function (e) { var t = this; this._withCommit((function () { t._vm._data.$$state = e })) }, u.prototype.registerModule = function (e, t, n) { void 0 === n && (n = {}), "string" === typeof e && (e = [e]), this._modules.register(e, t), h(this, this.state, e, this._modules.get(e), n.preserveState), d(this, this.state) }, u.prototype.unregisterModule = function (e) { var t = this; "string" === typeof e && (e = [e]), this._modules.unregister(e), this._withCommit((function () { var n = v(t.state, e.slice(0, -1)); c.delete(n, e[e.length - 1]) })), p(this) }, u.prototype.hotUpdate = function (e) { this._modules.update(e), p(this, !0) }, u.prototype._withCommit = function (e) { var t = this._committing; this._committing = !0, e(), this._committing = t }, Object.defineProperties(u.prototype, l); var y = x((function (e, t) { var n = {}; return w(t).forEach((function (t) { var r = t.key, i = t.val; n[r] = function () { var t = this.$store.state, n = this.$store.getters; if (e) { var r = C(this.$store, "mapState", e); if (!r) return; t = r.context.state, n = r.context.getters } return "function" === typeof i ? i.call(this, t, n) : t[i] }, n[r].vuex = !0 })), n })), b = x((function (e, t) { var n = {}; return w(t).forEach((function (t) { var r = t.key, i = t.val; n[r] = function () { for (var t = [], n = arguments.length; n--;)t[n] = arguments[n]; var r = this.$store.commit; if (e) { var o = C(this.$store, "mapMutations", e); if (!o) return; r = o.context.commit } return "function" === typeof i ? i.apply(this, [r].concat(t)) : r.apply(this.$store, [i].concat(t)) } })), n })), _ = x((function (e, t) { var n = {}; return w(t).forEach((function (t) { var r = t.key, i = t.val; i = e + i, n[r] = function () { if (!e || C(this.$store, "mapGetters", e)) return this.$store.getters[i] }, n[r].vuex = !0 })), n })), k = x((function (e, t) { var n = {}; return w(t).forEach((function (t) { var r = t.key, i = t.val; n[r] = function () { for (var t = [], n = arguments.length; n--;)t[n] = arguments[n]; var r = this.$store.dispatch; if (e) { var o = C(this.$store, "mapActions", e); if (!o) return; r = o.context.dispatch } return "function" === typeof i ? i.apply(this, [r].concat(t)) : r.apply(this.$store, [i].concat(t)) } })), n })); function w(e) { return Array.isArray(e) ? e.map((function (e) { return { key: e, val: e } })) : Object.keys(e).map((function (t) { return { key: t, val: e[t] } })) } function x(e) { return function (t, n) { return "string" !== typeof t ? (n = t, t = "") : "/" !== t.charAt(t.length - 1) && (t += "/"), e(t, n) } } function C(e, t, n) { return e._modulesNamespaceMap[n] } var A = { Store: u, install: g, version: "3.1.1", mapState: y, mapMutations: b, mapGetters: _, mapActions: k, createNamespacedHelpers: function (e) { return { mapState: y.bind(null, e), mapGetters: _.bind(null, e), mapMutations: b.bind(null, e), mapActions: k.bind(null, e) } } }; t.a = A }).call(this, n(1)) }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { }, function (e, t, n) { (function (t) { var n = function (e) { var t = /\blang(?:uage)?-([\w-]+)\b/i, n = 0, r = { manual: e.Prism && e.Prism.manual, disableWorkerMessageHandler: e.Prism && e.Prism.disableWorkerMessageHandler, util: { encode: function (e) { return e instanceof i ? new i(e.type, r.util.encode(e.content), e.alias) : Array.isArray(e) ? e.map(r.util.encode) : e.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/\u00a0/g, " ") }, type: function (e) { return Object.prototype.toString.call(e).slice(8, -1) }, objId: function (e) { return e.__id || Object.defineProperty(e, "__id", { value: ++n }), e.__id }, clone: function e(t, n) { var i, o, a = r.util.type(t); switch (n = n || {}, a) { case "Object": if (o = r.util.objId(t), n[o]) return n[o]; for (var s in i = {}, n[o] = i, t) t.hasOwnProperty(s) && (i[s] = e(t[s], n)); return i; case "Array": return o = r.util.objId(t), n[o] ? n[o] : (i = [], n[o] = i, t.forEach((function (t, r) { i[r] = e(t, n) })), i); default: return t } } }, languages: { extend: function (e, t) { var n = r.util.clone(r.languages[e]); for (var i in t) n[i] = t[i]; return n }, insertBefore: function (e, t, n, i) { var o = (i = i || r.languages)[e], a = {}; for (var s in o) if (o.hasOwnProperty(s)) { if (s == t) for (var c in n) n.hasOwnProperty(c) && (a[c] = n[c]); n.hasOwnProperty(s) || (a[s] = o[s]) } var u = i[e]; return i[e] = a, r.languages.DFS(r.languages, (function (t, n) { n === u && t != e && (this[t] = a) })), a }, DFS: function e(t, n, i, o) { o = o || {}; var a = r.util.objId; for (var s in t) if (t.hasOwnProperty(s)) { n.call(t, s, t[s], i || s); var c = t[s], u = r.util.type(c); "Object" !== u || o[a(c)] ? "Array" !== u || o[a(c)] || (o[a(c)] = !0, e(c, n, s, o)) : (o[a(c)] = !0, e(c, n, null, o)) } } }, plugins: {}, highlightAll: function (e, t) { r.highlightAllUnder(document, e, t) }, highlightAllUnder: function (e, t, n) { var i = { callback: n, selector: 'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code' }; r.hooks.run("before-highlightall", i); for (var o, a = e.querySelectorAll(i.selector), s = 0; o = a[s++];)r.highlightElement(o, !0 === t, i.callback) }, highlightElement: function (n, i, o) { for (var a, s = "none", c = n; c && !t.test(c.className);)c = c.parentNode; c && (s = (c.className.match(t) || [, "none"])[1].toLowerCase(), a = r.languages[s]), n.className = n.className.replace(t, "").replace(/\s+/g, " ") + " language-" + s, n.parentNode && (c = n.parentNode, /pre/i.test(c.nodeName) && (c.className = c.className.replace(t, "").replace(/\s+/g, " ") + " language-" + s)); var u = { element: n, language: s, grammar: a, code: n.textContent }, l = function (e) { u.highlightedCode = e, r.hooks.run("before-insert", u), u.element.innerHTML = u.highlightedCode, r.hooks.run("after-highlight", u), r.hooks.run("complete", u), o && o.call(u.element) }; if (r.hooks.run("before-sanity-check", u), u.code) if (r.hooks.run("before-highlight", u), u.grammar) if (i && e.Worker) { var f = new Worker(r.filename); f.onmessage = function (e) { l(e.data) }, f.postMessage(JSON.stringify({ language: u.language, code: u.code, immediateClose: !0 })) } else l(r.highlight(u.code, u.grammar, u.language)); else l(r.util.encode(u.code)); else r.hooks.run("complete", u) }, highlight: function (e, t, n) { var o = { code: e, grammar: t, language: n }; return r.hooks.run("before-tokenize", o), o.tokens = r.tokenize(o.code, o.grammar), r.hooks.run("after-tokenize", o), i.stringify(r.util.encode(o.tokens), o.language) }, matchGrammar: function (e, t, n, o, a, s, c) { for (var u in n) if (n.hasOwnProperty(u) && n[u]) { if (u == c) return; var l = n[u]; l = "Array" === r.util.type(l) ? l : [l]; for (var f = 0; f < l.length; ++f) { var p = l[f], d = p.inside, h = !!p.lookbehind, v = !!p.greedy, m = 0, g = p.alias; if (v && !p.pattern.global) { var y = p.pattern.toString().match(/[imuy]*$/)[0]; p.pattern = RegExp(p.pattern.source, y + "g") } p = p.pattern || p; for (var b = o, _ = a; b < t.length; _ += t[b].length, ++b) { var k = t[b]; if (t.length > e.length) return; if (!(k instanceof i)) { if (v && b != t.length - 1) { if (p.lastIndex = _, !(S = p.exec(e))) break; for (var w = S.index + (h ? S[1].length : 0), x = S.index + S[0].length, C = b, A = _, $ = t.length; C < $ && (A < x || !t[C].type && !t[C - 1].greedy); ++C)w >= (A += t[C].length) && (++b, _ = A); if (t[b] instanceof i) continue; O = C - b, k = e.slice(_, A), S.index -= _ } else { p.lastIndex = 0; var S = p.exec(k), O = 1 } if (S) { h && (m = S[1] ? S[1].length : 0); x = (w = S.index + m) + (S = S[0].slice(m)).length; var E = k.slice(0, w), T = k.slice(x), L = [b, O]; E && (++b, _ += E.length, L.push(E)); var j = new i(u, d ? r.tokenize(S, d) : S, g, S, v); if (L.push(j), T && L.push(T), Array.prototype.splice.apply(t, L), 1 != O && r.matchGrammar(e, t, n, b, _, !0, u), s) break } else if (s) break } } } } }, tokenize: function (e, t) { var n = [e], i = t.rest; if (i) { for (var o in i) t[o] = i[o]; delete t.rest } return r.matchGrammar(e, n, t, 0, 0, !1), n }, hooks: { all: {}, add: function (e, t) { var n = r.hooks.all; n[e] = n[e] || [], n[e].push(t) }, run: function (e, t) { var n = r.hooks.all[e]; if (n && n.length) for (var i, o = 0; i = n[o++];)i(t) } }, Token: i }; function i(e, t, n, r, i) { this.type = e, this.content = t, this.alias = n, this.length = 0 | (r || "").length, this.greedy = !!i } if (e.Prism = r, i.stringify = function (e, t) { if ("string" == typeof e) return e; if (Array.isArray(e)) return e.map((function (e) { return i.stringify(e, t) })).join(""); var n = { type: e.type, content: i.stringify(e.content, t), tag: "span", classes: ["token", e.type], attributes: {}, language: t }; if (e.alias) { var o = Array.isArray(e.alias) ? e.alias : [e.alias]; Array.prototype.push.apply(n.classes, o) } r.hooks.run("wrap", n); var a = Object.keys(n.attributes).map((function (e) { return e + '="' + (n.attributes[e] || "").replace(/"/g, "&quot;") + '"' })).join(" "); return "<" + n.tag + ' class="' + n.classes.join(" ") + '"' + (a ? " " + a : "") + ">" + n.content + "</" + n.tag + ">" }, !e.document) return e.addEventListener ? (r.disableWorkerMessageHandler || e.addEventListener("message", (function (t) { var n = JSON.parse(t.data), i = n.language, o = n.code, a = n.immediateClose; e.postMessage(r.highlight(o, r.languages[i], i)), a && e.close() }), !1), r) : r; var o = document.currentScript || [].slice.call(document.getElementsByTagName("script")).pop(); return o && (r.filename = o.src, r.manual || o.hasAttribute("data-manual") || ("loading" !== document.readyState ? window.requestAnimationFrame ? window.requestAnimationFrame(r.highlightAll) : window.setTimeout(r.highlightAll, 16) : document.addEventListener("DOMContentLoaded", r.highlightAll))), r }("undefined" !== typeof window ? window : "undefined" !== typeof WorkerGlobalScope && self instanceof WorkerGlobalScope ? self : {}); e.exports && (e.exports = n), "undefined" !== typeof t && (t.Prism = n), n.languages.markup = { comment: /<!--[\s\S]*?-->/, prolog: /<\?[\s\S]+?\?>/, doctype: /<!DOCTYPE[\s\S]+?>/i, cdata: /<!\[CDATA\[[\s\S]*?]]>/i, tag: { pattern: /<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/i, greedy: !0, inside: { tag: { pattern: /^<\/?[^\s>\/]+/i, inside: { punctuation: /^<\/?/, namespace: /^[^\s>\/:]+:/ } }, "attr-value": { pattern: /=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/i, inside: { punctuation: [/^=/, { pattern: /^(\s*)["']|["']$/, lookbehind: !0 }] } }, punctuation: /\/?>/, "attr-name": { pattern: /[^\s>\/]+/, inside: { namespace: /^[^\s>\/:]+:/ } } } }, entity: /&#?[\da-z]{1,8};/i }, n.languages.markup.tag.inside["attr-value"].inside.entity = n.languages.markup.entity, n.hooks.add("wrap", (function (e) { "entity" === e.type && (e.attributes.title = e.content.replace(/&amp;/, "&")) })), Object.defineProperty(n.languages.markup.tag, "addInlined", { value: function (e, t) { var r = {}; r["language-" + t] = { pattern: /(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i, lookbehind: !0, inside: n.languages[t] }, r.cdata = /^<!\[CDATA\[|\]\]>$/i; var i = { "included-cdata": { pattern: /<!\[CDATA\[[\s\S]*?\]\]>/i, inside: r } }; i["language-" + t] = { pattern: /[\s\S]+/, inside: n.languages[t] }; var o = {}; o[e] = { pattern: RegExp(/(<__[\s\S]*?>)(?:<!\[CDATA\[[\s\S]*?\]\]>\s*|[\s\S])*?(?=<\/__>)/.source.replace(/__/g, e), "i"), lookbehind: !0, greedy: !0, inside: i }, n.languages.insertBefore("markup", "cdata", o) } }), n.languages.xml = n.languages.extend("markup", {}), n.languages.html = n.languages.markup, n.languages.mathml = n.languages.markup, n.languages.svg = n.languages.markup, function (e) { var t = /("|')(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/; e.languages.css = { comment: /\/\*[\s\S]*?\*\//, atrule: { pattern: /@[\w-]+[\s\S]*?(?:;|(?=\s*\{))/, inside: { rule: /@[\w-]+/ } }, url: { pattern: RegExp("url\\((?:" + t.source + "|[^\n\r()]*)\\)", "i"), inside: { function: /^url/i, punctuation: /^\(|\)$/ } }, selector: RegExp("[^{}\\s](?:[^{};\"']|" + t.source + ")*?(?=\\s*\\{)"), string: { pattern: t, greedy: !0 }, property: /[-_a-z\xA0-\uFFFF][-\w\xA0-\uFFFF]*(?=\s*:)/i, important: /!important\b/i, function: /[-a-z0-9]+(?=\()/i, punctuation: /[(){};:,]/ }, e.languages.css.atrule.inside.rest = e.languages.css; var n = e.languages.markup; n && (n.tag.addInlined("style", "css"), e.languages.insertBefore("inside", "attr-value", { "style-attr": { pattern: /\s*style=("|')(?:\\[\s\S]|(?!\1)[^\\])*\1/i, inside: { "attr-name": { pattern: /^\s*style/i, inside: n.tag.inside }, punctuation: /^\s*=\s*['"]|['"]\s*$/, "attr-value": { pattern: /.+/i, inside: e.languages.css } }, alias: "language-css" } }, n.tag)) }(n), n.languages.clike = { comment: [{ pattern: /(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/, lookbehind: !0 }, { pattern: /(^|[^\\:])\/\/.*/, lookbehind: !0, greedy: !0 }], string: { pattern: /(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/, greedy: !0 }, "class-name": { pattern: /((?:\b(?:class|interface|extends|implements|trait|instanceof|new)\s+)|(?:catch\s+\())[\w.\\]+/i, lookbehind: !0, inside: { punctuation: /[.\\]/ } }, keyword: /\b(?:if|else|while|do|for|return|in|instanceof|function|new|try|throw|catch|finally|null|break|continue)\b/, boolean: /\b(?:true|false)\b/, function: /\w+(?=\()/, number: /\b0x[\da-f]+\b|(?:\b\d+\.?\d*|\B\.\d+)(?:e[+-]?\d+)?/i, operator: /--?|\+\+?|!=?=?|<=?|>=?|==?=?|&&?|\|\|?|\?|\*|\/|~|\^|%/, punctuation: /[{}[\];(),.:]/ }, n.languages.javascript = n.languages.extend("clike", { "class-name": [n.languages.clike["class-name"], { pattern: /(^|[^$\w\xA0-\uFFFF])[_$A-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\.(?:prototype|constructor))/, lookbehind: !0 }], keyword: [{ pattern: /((?:^|})\s*)(?:catch|finally)\b/, lookbehind: !0 }, { pattern: /(^|[^.])\b(?:as|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/, lookbehind: !0 }], number: /\b(?:(?:0[xX](?:[\dA-Fa-f](?:_[\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\d(?:_\d)?)+n|NaN|Infinity)\b|(?:\b(?:\d(?:_\d)?)+\.?(?:\d(?:_\d)?)*|\B\.(?:\d(?:_\d)?)+)(?:[Ee][+-]?(?:\d(?:_\d)?)+)?/, function: /#?[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/, operator: /-[-=]?|\+[+=]?|!=?=?|<<?=?|>>?>?=?|=(?:==?|>)?|&[&=]?|\|[|=]?|\*\*?=?|\/=?|~|\^=?|%=?|\?|\.{3}/ }), n.languages.javascript["class-name"][0].pattern = /(\b(?:class|interface|extends|implements|instanceof|new)\s+)[\w.\\]+/, n.languages.insertBefore("javascript", "keyword", { regex: { pattern: /((?:^|[^$\w\xA0-\uFFFF."'\])\s])\s*)\/(\[(?:[^\]\\\r\n]|\\.)*]|\\.|[^/\\\[\r\n])+\/[gimyus]{0,6}(?=\s*($|[\r\n,.;})\]]))/, lookbehind: !0, greedy: !0 }, "function-variable": { pattern: /#?[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)\s*=>))/, alias: "function" }, parameter: [{ pattern: /(function(?:\s+[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)?\s*\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\))/, lookbehind: !0, inside: n.languages.javascript }, { pattern: /[_$a-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*=>)/i, inside: n.languages.javascript }, { pattern: /(\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*=>)/, lookbehind: !0, inside: n.languages.javascript }, { pattern: /((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*\s*)\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*\{)/, lookbehind: !0, inside: n.languages.javascript }], constant: /\b[A-Z](?:[A-Z_]|\dx?)*\b/ }), n.languages.insertBefore("javascript", "string", { "template-string": { pattern: /`(?:\\[\s\S]|\${(?:[^{}]|{(?:[^{}]|{[^}]*})*})+}|(?!\${)[^\\`])*`/, greedy: !0, inside: { "template-punctuation": { pattern: /^`|`$/, alias: "string" }, interpolation: { pattern: /((?:^|[^\\])(?:\\{2})*)\${(?:[^{}]|{(?:[^{}]|{[^}]*})*})+}/, lookbehind: !0, inside: { "interpolation-punctuation": { pattern: /^\${|}$/, alias: "punctuation" }, rest: n.languages.javascript } }, string: /[\s\S]+/ } } }), n.languages.markup && n.languages.markup.tag.addInlined("script", "javascript"), n.languages.js = n.languages.javascript, "undefined" !== typeof self && self.Prism && self.document && document.querySelector && (self.Prism.fileHighlight = function (e) { e = e || document; var t = { js: "javascript", py: "python", rb: "ruby", ps1: "powershell", psm1: "powershell", sh: "bash", bat: "batch", h: "c", tex: "latex" }; Array.prototype.slice.call(e.querySelectorAll("pre[data-src]")).forEach((function (e) { if (!e.hasAttribute("data-src-loaded")) { for (var r, i = e.getAttribute("data-src"), o = e, a = /\blang(?:uage)?-([\w-]+)\b/i; o && !a.test(o.className);)o = o.parentNode; if (o && (r = (e.className.match(a) || [, ""])[1]), !r) { var s = (i.match(/\.(\w+)$/) || [, ""])[1]; r = t[s] || s } var c = document.createElement("code"); c.className = "language-" + r, e.textContent = "", c.textContent = "Loading\u2026", e.appendChild(c); var u = new XMLHttpRequest; u.open("GET", i, !0), u.onreadystatechange = function () { 4 == u.readyState && (u.status < 400 && u.responseText ? (c.textContent = u.responseText, n.highlightElement(c), e.setAttribute("data-src-loaded", "")) : u.status >= 400 ? c.textContent = "\u2716 Error " + u.status + " while fetching file: " + u.statusText : c.textContent = "\u2716 Error: File does not exist or is empty") }, u.send(null) } })), n.plugins.toolbar && n.plugins.toolbar.registerButton("download-file", (function (e) { var t = e.element.parentNode; if (t && /pre/i.test(t.nodeName) && t.hasAttribute("data-src") && t.hasAttribute("data-download-link")) { var n = t.getAttribute("data-src"), r = document.createElement("a"); return r.textContent = t.getAttribute("data-download-link-label") || "Download", r.setAttribute("download", ""), r.href = n, r } })) }, document.addEventListener("DOMContentLoaded", (function () { self.Prism.fileHighlight() }))) }).call(this, n(1)) }, function (e) { e.exports = JSON.parse('{"javascript":"clike","actionscript":"javascript","arduino":"cpp","aspnet":["markup","csharp"],"bison":"c","c":"clike","csharp":"clike","cpp":"c","coffeescript":"javascript","crystal":"ruby","css-extras":"css","d":"clike","dart":"clike","django":"markup","erb":["ruby","markup-templating"],"fsharp":"clike","flow":"javascript","glsl":"clike","go":"clike","groovy":"clike","haml":"ruby","handlebars":"markup-templating","haxe":"clike","java":"clike","jolie":"clike","kotlin":"clike","less":"css","markdown":"markup","markup-templating":"markup","n4js":"javascript","nginx":"clike","objectivec":"c","opencl":"cpp","parser":"markup","php":["clike","markup-templating"],"php-extras":"php","plsql":"sql","processing":"clike","protobuf":"clike","pug":"javascript","qore":"clike","jsx":["markup","javascript"],"tsx":["jsx","typescript"],"reason":"clike","ruby":"clike","sass":"css","scss":"css","scala":"java","smarty":"markup-templating","soy":"markup-templating","swift":"clike","tap":"yaml","textile":"markup","tt2":["clike","markup-templating"],"twig":"markup","typescript":"javascript","vbnet":"basic","velocity":"markup","wiki":"markup","xeora":"markup","xquery":"markup","builtin":["markup","xml","html","mathml","svg","css","clike","javascript","js"]}') }, function (e, t, n) { var r, i, o; i = [], void 0 === (o = "function" === typeof (r = function () { var e = function () { }, t = {}, n = {}, r = {}; function i(e, t) { if (e) { var i = r[e]; if (n[e] = t, i) for (; i.length;)i[0](e, t), i.splice(0, 1) } } function o(t, n) { t.call && (t = { success: t }), n.length ? (t.error || e)(n) : (t.success || e)(t) } function a(t, n, r, i) { var o, s, c = document, u = r.async, l = (r.numRetries || 0) + 1, f = r.before || e, p = t.replace(/^(css|img)!/, ""); i = i || 0, /(^css!|\.css$)/.test(t) ? ((s = c.createElement("link")).rel = "stylesheet", s.href = p, (o = "hideFocus" in s) && s.relList && (o = 0, s.rel = "preload", s.as = "style")) : /(^img!|\.(png|gif|jpg|svg)$)/.test(t) ? (s = c.createElement("img")).src = p : ((s = c.createElement("script")).src = t, s.async = void 0 === u || u), s.onload = s.onerror = s.onbeforeload = function (e) { var c = e.type[0]; if (o) try { s.sheet.cssText.length || (c = "e") } catch (u) { 18 != u.code && (c = "e") } if ("e" == c) { if ((i += 1) < l) return a(t, n, r, i) } else if ("preload" == s.rel && "style" == s.as) return s.rel = "stylesheet"; n(t, c, e.defaultPrevented) }, !1 !== f(t, s) && c.head.appendChild(s) } function s(e, n, r) { var s, c; if (n && n.trim && (s = n), c = (s ? r : n) || {}, s) { if (s in t) throw "LoadJS"; t[s] = !0 } function u(t, n) { !function (e, t, n) { var r, i, o = (e = e.push ? e : [e]).length, s = o, c = []; for (r = function (e, n, r) { if ("e" == n && c.push(e), "b" == n) { if (!r) return; c.push(e) } --o || t(c) }, i = 0; i < s; i++)a(e[i], r, n) }(e, (function (e) { o(c, e), t && o({ success: t, error: n }, e), i(s, e) }), c) } if (c.returnPromise) return new Promise(u); u() } return s.ready = function (e, t) { return function (e, t) { e = e.push ? e : [e]; var i, o, a, s = [], c = e.length, u = c; for (i = function (e, n) { n.length && s.push(e), --u || t(s) }; c--;)o = e[c], (a = n[o]) ? i(o, a) : (r[o] = r[o] || []).push(i) }(e, (function (e) { o(t, e) })), s }, s.done = function (e) { i(e, []) }, s.reset = function () { t = {}, n = {}, r = {} }, s.isDefined = function (e) { return e in t }, s }) ? r.apply(t, i) : r) || (e.exports = o) }, function (e, t) { var n = /^(attrs|props|on|nativeOn|class|style|hook)$/; function r(e, t) { return function () { e && e.apply(this, arguments), t && t.apply(this, arguments) } } e.exports = function (e) { return e.reduce((function (e, t) { var i, o, a, s, c; for (a in t) if (i = e[a], o = t[a], i && n.test(a)) if ("class" === a && ("string" === typeof i && (c = i, e[a] = i = {}, i[c] = !0), "string" === typeof o && (c = o, t[a] = o = {}, o[c] = !0)), "on" === a || "nativeOn" === a || "hook" === a) for (s in o) i[s] = r(i[s], o[s]); else if (Array.isArray(i)) e[a] = i.concat(o); else if (Array.isArray(o)) e[a] = [i].concat(o); else for (s in o) i[s] = o[s]; else e[a] = t[a]; return e }), {}) } }, function (e, t) { function n(e, t) { var r = { name: e.name, path: e.path, hash: e.hash, query: e.query, params: e.params, fullPath: e.fullPath, meta: e.meta }; return t && (r.from = n(t)), Object.freeze(r) } t.sync = function (e, t, r) { var i = (r || {}).moduleName || "route"; e.registerModule(i, { namespaced: !0, state: n(t.currentRoute), mutations: { ROUTE_CHANGED: function (t, r) { e.state[i] = n(r.to, r.from) } } }); var o, a = !1, s = e.watch((function (e) { return e[i] }), (function (e) { var n = e.fullPath; n !== o && (null != o && (a = !0, t.push(e)), o = n) }), { sync: !0 }), c = t.afterEach((function (t, n) { a ? a = !1 : (o = t.fullPath, e.commit(i + "/ROUTE_CHANGED", { to: t, from: n })) })); return function () { null != c && c(), null != s && s(), e.unregisterModule(i) } } }, function (e, t, n) { e.exports = n(61) }, function (e, t, n) { (function (e) { var r = "undefined" !== typeof e && e || "undefined" !== typeof self && self || window, i = Function.prototype.apply; function o(e, t) { this._id = e, this._clearFn = t } t.setTimeout = function () { return new o(i.call(setTimeout, r, arguments), clearTimeout) }, t.setInterval = function () { return new o(i.call(setInterval, r, arguments), clearInterval) }, t.clearTimeout = t.clearInterval = function (e) { e && e.close() }, o.prototype.unref = o.prototype.ref = function () { }, o.prototype.close = function () { this._clearFn.call(r, this._id) }, t.enroll = function (e, t) { clearTimeout(e._idleTimeoutId), e._idleTimeout = t }, t.unenroll = function (e) { clearTimeout(e._idleTimeoutId), e._idleTimeout = -1 }, t._unrefActive = t.active = function (e) { clearTimeout(e._idleTimeoutId); var t = e._idleTimeout; t >= 0 && (e._idleTimeoutId = setTimeout((function () { e._onTimeout && e._onTimeout() }), t)) }, n(34), t.setImmediate = "undefined" !== typeof self && self.setImmediate || "undefined" !== typeof e && e.setImmediate || this && this.setImmediate, t.clearImmediate = "undefined" !== typeof self && self.clearImmediate || "undefined" !== typeof e && e.clearImmediate || this && this.clearImmediate }).call(this, n(1)) }, function (e, t, n) { (function (e, t) { !function (e, n) { "use strict"; if (!e.setImmediate) { var r, i = 1, o = {}, a = !1, s = e.document, c = Object.getPrototypeOf && Object.getPrototypeOf(e); c = c && c.setTimeout ? c : e, "[object process]" === {}.toString.call(e.process) ? r = function (e) { t.nextTick((function () { l(e) })) } : function () { if (e.postMessage && !e.importScripts) { var t = !0, n = e.onmessage; return e.onmessage = function () { t = !1 }, e.postMessage("", "*"), e.onmessage = n, t } }() ? function () { var t = "setImmediate$" + Math.random() + "$", n = function (n) { n.source === e && "string" === typeof n.data && 0 === n.data.indexOf(t) && l(+n.data.slice(t.length)) }; e.addEventListener ? e.addEventListener("message", n, !1) : e.attachEvent("onmessage", n), r = function (n) { e.postMessage(t + n, "*") } }() : e.MessageChannel ? function () { var e = new MessageChannel; e.port1.onmessage = function (e) { l(e.data) }, r = function (t) { e.port2.postMessage(t) } }() : s && "onreadystatechange" in s.createElement("script") ? function () { var e = s.documentElement; r = function (t) { var n = s.createElement("script"); n.onreadystatechange = function () { l(t), n.onreadystatechange = null, e.removeChild(n), n = null }, e.appendChild(n) } }() : r = function (e) { setTimeout(l, 0, e) }, c.setImmediate = function (e) { "function" !== typeof e && (e = new Function("" + e)); for (var t = new Array(arguments.length - 1), n = 0; n < t.length; n++)t[n] = arguments[n + 1]; var a = { callback: e, args: t }; return o[i] = a, r(i), i++ }, c.clearImmediate = u } function u(e) { delete o[e] } function l(e) { if (a) setTimeout(l, 0, e); else { var t = o[e]; if (t) { a = !0; try { !function (e) { var t = e.callback, r = e.args; switch (r.length) { case 0: t(); break; case 1: t(r[0]); break; case 2: t(r[0], r[1]); break; case 3: t(r[0], r[1], r[2]); break; default: t.apply(n, r) } }(t) } finally { u(e), a = !1 } } } } }("undefined" === typeof self ? "undefined" === typeof e ? this : e : self) }).call(this, n(1), n(35)) }, function (e, t) { var n, r, i = e.exports = {}; function o() { throw new Error("setTimeout has not been defined") } function a() { throw new Error("clearTimeout has not been defined") } function s(e) { if (n === setTimeout) return setTimeout(e, 0); if ((n === o || !n) && setTimeout) return n = setTimeout, setTimeout(e, 0); try { return n(e, 0) } catch (t) { try { return n.call(null, e, 0) } catch (t) { return n.call(this, e, 0) } } } !function () { try { n = "function" === typeof setTimeout ? setTimeout : o } catch (e) { n = o } try { r = "function" === typeof clearTimeout ? clearTimeout : a } catch (e) { r = a } }(); var c, u = [], l = !1, f = -1; function p() { l && c && (l = !1, c.length ? u = c.concat(u) : f = -1, u.length && d()) } function d() { if (!l) { var e = s(p); l = !0; for (var t = u.length; t;) { for (c = u, u = []; ++f < t;)c && c[f].run(); f = -1, t = u.length } c = null, l = !1, function (e) { if (r === clearTimeout) return clearTimeout(e); if ((r === a || !r) && clearTimeout) return r = clearTimeout, clearTimeout(e); try { r(e) } catch (t) { try { return r.call(null, e) } catch (t) { return r.call(this, e) } } }(e) } } function h(e, t) { this.fun = e, this.array = t } function v() { } i.nextTick = function (e) { var t = new Array(arguments.length - 1); if (arguments.length > 1) for (var n = 1; n < arguments.length; n++)t[n - 1] = arguments[n]; u.push(new h(e, t)), 1 !== u.length || l || s(d) }, h.prototype.run = function () { this.fun.apply(null, this.array) }, i.title = "browser", i.browser = !0, i.env = {}, i.argv = [], i.version = "", i.versions = {}, i.on = v, i.addListener = v, i.once = v, i.off = v, i.removeListener = v, i.removeAllListeners = v, i.emit = v, i.prependListener = v, i.prependOnceListener = v, i.listeners = function (e) { return [] }, i.binding = function (e) { throw new Error("process.binding is not supported") }, i.cwd = function () { return "/" }, i.chdir = function (e) { throw new Error("process.chdir is not supported") }, i.umask = function () { return 0 } }, function (e, t, n) { "use strict"; var r = n(3); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(4); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(5); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(6); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(7); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(8); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(9); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(10); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(11); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(12); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(13); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(14); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(15); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(16); n.n(r).a }, function (e, t, n) { "use strict"; n.r(t); var r = Object.assign || function (e) { for (var t, n = 1; n < arguments.length; n++)for (var r in t = arguments[n]) Object.prototype.hasOwnProperty.call(t, r) && (e[r] = t[r]); return e }, i = function (e) { return "IMG" === e.tagName }, o = function (e) { return e && 1 === e.nodeType }, a = function (e) { return ".svg" === (e.currentSrc || e.src).substr(-4).toLowerCase() }, s = function (e) { try { return Array.isArray(e) ? e.filter(i) : function (e) { return NodeList.prototype.isPrototypeOf(e) }(e) ? [].slice.call(e).filter(i) : o(e) ? [e].filter(i) : "string" == typeof e ? [].slice.call(document.querySelectorAll(e)).filter(i) : [] } catch (e) { throw new TypeError("The provided selector is invalid.\nExpects a CSS selector, a Node element, a NodeList or an array.\nSee: https://github.com/francoischalifour/medium-zoom") } }, c = function (e) { var t = document.createElement("div"); return t.classList.add("medium-zoom-overlay"), t.style.background = e, t }, u = function (e) { var t = e.getBoundingClientRect(), n = t.top, r = t.left, i = t.width, o = t.height, a = e.cloneNode(), s = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0, c = window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0; return a.removeAttribute("id"), a.style.position = "absolute", a.style.top = n + s + "px", a.style.left = r + c + "px", a.style.width = i + "px", a.style.height = o + "px", a.style.transform = "", a }, l = function (e, t) { var n = r({ bubbles: !1, cancelable: !1, detail: void 0 }, t); if ("function" == typeof window.CustomEvent) return new CustomEvent(e, n); var i = document.createEvent("CustomEvent"); return i.initCustomEvent(e, n.bubbles, n.cancelable, n.detail), i }; !function (e, t) { void 0 === t && (t = {}); var n = t.insertAt; if (e && "undefined" != typeof document) { var r = document.head || document.getElementsByTagName("head")[0], i = document.createElement("style"); i.type = "text/css", "top" === n && r.firstChild ? r.insertBefore(i, r.firstChild) : r.appendChild(i), i.styleSheet ? i.styleSheet.cssText = e : i.appendChild(document.createTextNode(e)) } }(".medium-zoom-overlay{position:fixed;top:0;right:0;bottom:0;left:0;opacity:0;transition:opacity .3s;will-change:opacity}.medium-zoom--opened .medium-zoom-overlay{cursor:pointer;cursor:zoom-out;opacity:1}.medium-zoom-image{cursor:pointer;cursor:zoom-in;transition:transform .3s cubic-bezier(.2,0,.2,1)}.medium-zoom-image--hidden{visibility:hidden}.medium-zoom-image--opened{position:relative;cursor:pointer;cursor:zoom-out;will-change:transform}"), t.default = function e(t) { var n = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : {}, i = window.Promise || function (e) { function t() { } e(t, t) }, f = function (e) { var t = e.target; return t === T ? void _() : void (-1 === C.indexOf(t) || k({ target: t })) }, p = function () { if (!$ && E.original) { var e = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0; Math.abs(S - e) > O.scrollOffset && setTimeout(_, 150) } }, d = function () { var e = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : {}, t = e; if (e.background && (T.style.background = e.background), e.container && e.container instanceof Object && (t.container = r({}, O.container, e.container)), e.template) { var n = o(e.template) ? e.template : document.querySelector(e.template); t.template = n } return O = r({}, O, t), C.forEach((function (e) { e.dispatchEvent(l("medium-zoom:update", { detail: { zoom: L } })) })), L }, h = function () { var t = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : {}; return e(r({}, O, t)) }, v = function () { for (var e = arguments.length, t = Array(e), n = 0; n < e; n++)t[n] = arguments[n]; var r = t.reduce((function (e, t) { return [].concat(e, s(t)) }), []); return r.filter((function (e) { return -1 === C.indexOf(e) })).forEach((function (e) { C.push(e), e.classList.add("medium-zoom-image") })), A.forEach((function (e) { var t = e.type, n = e.listener, i = e.options; r.forEach((function (e) { e.addEventListener(t, n, i) })) })), L }, m = function () { for (var e = arguments.length, t = Array(e), n = 0; n < e; n++)t[n] = arguments[n]; E.zoomed && _(); var r = 0 < t.length ? t.reduce((function (e, t) { return [].concat(e, s(t)) }), []) : C; return r.forEach((function (e) { e.classList.remove("medium-zoom-image"), e.dispatchEvent(l("medium-zoom:detach", { detail: { zoom: L } })) })), C = C.filter((function (e) { return -1 === r.indexOf(e) })), L }, g = function (e, t) { var n = 2 < arguments.length && void 0 !== arguments[2] ? arguments[2] : {}; return C.forEach((function (r) { r.addEventListener("medium-zoom:" + e, t, n) })), A.push({ type: "medium-zoom:" + e, listener: t, options: n }), L }, y = function (e, t) { var n = 2 < arguments.length && void 0 !== arguments[2] ? arguments[2] : {}; return C.forEach((function (r) { r.removeEventListener("medium-zoom:" + e, t, n) })), A = A.filter((function (n) { return n.type !== "medium-zoom:" + e || n.listener.toString() !== t.toString() })), L }, b = function () { var e = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : {}, t = e.target, n = function () { var e = Math.min, t = { width: document.documentElement.clientWidth, height: document.documentElement.clientHeight, left: 0, top: 0, right: 0, bottom: 0 }, n = void 0, i = void 0; if (O.container) if (O.container instanceof Object) n = (t = r({}, t, O.container)).width - t.left - t.right - 2 * O.margin, i = t.height - t.top - t.bottom - 2 * O.margin; else { var s = (o(O.container) ? O.container : document.querySelector(O.container)).getBoundingClientRect(), c = s.width, u = s.height, l = s.left, f = s.top; t = r({}, t, { width: c, height: u, left: l, top: f }) } n = n || t.width - 2 * O.margin, i = i || t.height - 2 * O.margin; var p = E.zoomedHd || E.original, d = a(p) ? n : p.naturalWidth || n, h = a(p) ? i : p.naturalHeight || i, v = p.getBoundingClientRect(), m = v.top, g = v.left, y = v.width, b = v.height, _ = e(d, n) / y, k = e(h, i) / b, w = e(_, k), x = "scale(" + w + ") translate3d(" + ((n - y) / 2 - g + O.margin + t.left) / w + "px, " + ((i - b) / 2 - m + O.margin + t.top) / w + "px, 0)"; E.zoomed.style.transform = x, E.zoomedHd && (E.zoomedHd.style.transform = x) }; return new i((function (e) { if (t && -1 === C.indexOf(t)) e(L); else if (E.zoomed) e(L); else { if (t) E.original = t; else { if (!(0 < C.length)) return void e(L); var r = C; E.original = r[0] } if (E.original.dispatchEvent(l("medium-zoom:open", { detail: { zoom: L } })), S = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0, $ = !0, E.zoomed = u(E.original), document.body.appendChild(T), O.template) { var i = o(O.template) ? O.template : document.querySelector(O.template); E.template = document.createElement("div"), E.template.appendChild(i.content.cloneNode(!0)), document.body.appendChild(E.template) } if (document.body.appendChild(E.zoomed), window.requestAnimationFrame((function () { document.body.classList.add("medium-zoom--opened") })), E.original.classList.add("medium-zoom-image--hidden"), E.zoomed.classList.add("medium-zoom-image--opened"), E.zoomed.addEventListener("click", _), E.zoomed.addEventListener("transitionend", (function t() { $ = !1, E.zoomed.removeEventListener("transitionend", t), E.original.dispatchEvent(l("medium-zoom:opened", { detail: { zoom: L } })), e(L) })), E.original.getAttribute("data-zoom-src")) { E.zoomedHd = E.zoomed.cloneNode(), E.zoomedHd.removeAttribute("srcset"), E.zoomedHd.removeAttribute("sizes"), E.zoomedHd.src = E.zoomed.getAttribute("data-zoom-src"), E.zoomedHd.onerror = function () { clearInterval(a), console.warn("Unable to reach the zoom image target " + E.zoomedHd.src), E.zoomedHd = null, n() }; var a = setInterval((function () { E.zoomedHd.complete && (clearInterval(a), E.zoomedHd.classList.add("medium-zoom-image--opened"), E.zoomedHd.addEventListener("click", _), document.body.appendChild(E.zoomedHd), n()) }), 10) } else if (E.original.hasAttribute("srcset")) { E.zoomedHd = E.zoomed.cloneNode(), E.zoomedHd.removeAttribute("sizes"); var s = E.zoomedHd.addEventListener("load", (function () { E.zoomedHd.removeEventListener("load", s), E.zoomedHd.classList.add("medium-zoom-image--opened"), E.zoomedHd.addEventListener("click", _), document.body.appendChild(E.zoomedHd), n() })) } else n() } })) }, _ = function () { return new i((function (e) { !$ && E.original ? ($ = !0, document.body.classList.remove("medium-zoom--opened"), E.zoomed.style.transform = "", E.zoomedHd && (E.zoomedHd.style.transform = ""), E.template && (E.template.style.transition = "opacity 150ms", E.template.style.opacity = 0), E.original.dispatchEvent(l("medium-zoom:close", { detail: { zoom: L } })), E.zoomed.addEventListener("transitionend", (function t() { E.original.classList.remove("medium-zoom-image--hidden"), document.body.removeChild(E.zoomed), E.zoomedHd && document.body.removeChild(E.zoomedHd), document.body.removeChild(T), E.zoomed.classList.remove("medium-zoom-image--opened"), E.template && document.body.removeChild(E.template), $ = !1, E.zoomed.removeEventListener("transitionend", t), E.original.dispatchEvent(l("medium-zoom:closed", { detail: { zoom: L } })), E.original = null, E.zoomed = null, E.zoomedHd = null, E.template = null, e(L) }))) : e(L) })) }, k = function () { var e = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : {}, t = e.target; return E.original ? _() : b({ target: t }) }, w = function () { return O }, x = function () { return C }, C = [], A = [], $ = !1, S = 0, O = n, E = { original: null, zoomed: null, zoomedHd: null, template: null }; "[object Object]" === Object.prototype.toString.call(t) ? O = t : (t || "string" == typeof t) && v(t), O = r({ margin: 0, background: "#fff", scrollOffset: 40, container: null, template: null }, O); var T = c(O.background); document.addEventListener("click", f), document.addEventListener("keyup", (function (e) { 27 === (e.keyCode || e.which) && _() })), document.addEventListener("scroll", p), window.addEventListener("resize", _); var L = { open: b, close: _, toggle: k, update: d, clone: h, attach: v, detach: m, on: g, off: y, getOptions: w, getImages: x, getZoomedImage: function () { return E.original } }; return L } }, function (e, t, n) { "use strict"; var r = n(17); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(18); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(19); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(20); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(21); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(22); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(23); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(24); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(25); n.n(r).a }, function (e, t, n) { "use strict"; var r = n(26); n.n(r).a }, function (e, t, n) { "use strict"; n.r(t); var r = n(0), i = n(31), o = { name: "InjectedComponents", functional: !0, props: { position: { type: String, required: !0 } }, render: function (e, t) { var n = t.props, r = t.parent.$pluginApi.getComponents(n.position); if (0 !== r.length) return e("div", { class: "InjectedComponents", attrs: { "data-position": n.position } }, r.map((function (t) { var n = t.component, r = t.props; return e(n, { props: r }) }))) } }; var a = function () { function e() { this.hooks = {} } var t = e.prototype; return t.add = function (e, t) { return this.hooks[e] = this.hooks[e] || [], this.hooks[e].push(t), this }, t.invoke = function (e) { for (var t = this.hooks[e] || [], n = arguments.length, r = new Array(n > 1 ? n - 1 : 0), i = 1; i < n; i++)r[i - 1] = arguments[i]; var o = t, a = Array.isArray(o), s = 0; for (o = a ? o : o[Symbol.iterator](); ;) { var c; if (a) { if (s >= o.length) break; c = o[s++] } else { if ((s = o.next()).done) break; c = s.value } var u = c; u.apply(void 0, r) } return this }, t.process = function (e, t) { var n = this.hooks[e] || [], r = Array.isArray(n), i = 0; for (n = r ? n : n[Symbol.iterator](); ;) { var o; if (r) { if (i >= n.length) break; o = n[i++] } else { if ((i = n.next()).done) break; o = i.value } t = o(t) || t } return t }, t.processPromise = function (e, t) { try { return function (e, t) { return e && e.then ? e.then(t) : t(e) }(function (e, t, n) { if ("function" === typeof e[s]) { var r, i, o, a = e[s](); if (function e(s) { try { for (; !(r = a.next()).done && (!n || !n());)if ((s = t(r.value)) && s.then) { if (!l(s)) return void s.then(e, o || (o = c.bind(null, i = new u, 2))); s = s.v } i ? c(i, 1, s) : i = s } catch (f) { c(i || (i = new u), 2, f) } }(), a.return) { var f = function (e) { try { r.done || a.return() } catch (t) { } return e }; if (i && i.then) return i.then(f, (function (e) { throw f(e) })); f() } return i } if (!("length" in e)) throw new TypeError("Object is not iterable"); for (var p = [], d = 0; d < e.length; d++)p.push(e[d]); return function (e, t, n) { var r, i, o = -1; return function a(s) { try { for (; ++o < e.length && (!n || !n());)if ((s = t(o)) && s.then) { if (!l(s)) return void s.then(a, i || (i = c.bind(null, r = new u, 2))); s = s.v } r ? c(r, 1, s) : r = s } catch (f) { c(r || (r = new u), 2, f) } }(), r }(p, (function (e) { return t(p[e]) }), n) }(this.hooks[e] || [], (function (e) { return n = e(t), r = function (e) { t = e || t }, i ? r ? r(n) : n : (n && n.then || (n = Promise.resolve(n)), r ? n.then(r) : n); var n, r, i })), (function () { return t })) } catch (n) { return Promise.reject(n) } }, e }(), s = "undefined" !== typeof Symbol ? Symbol.iterator || (Symbol.iterator = Symbol("Symbol.iterator")) : "@@iterator"; function c(e, t, n) { if (!e.s) { if (n instanceof u) { if (!n.s) return void (n.o = c.bind(null, e, t)); 1 & t && (t = n.s), n = n.v } if (n && n.then) return void n.then(c.bind(null, e, t), c.bind(null, e, 2)); e.s = t, e.v = n; var r = e.o; r && r(e) } } var u = function () { function e() { } return e.prototype.then = function (t, n) { var r = new e, i = this.s; if (i) { var o = 1 & i ? t : n; if (o) { try { c(r, 1, o(this.v)) } catch (a) { c(r, 2, a) } return r } return this } return this.o = function (e) { try { var i = e.v; 1 & e.s ? c(r, 1, t ? t(i) : i) : n ? c(r, 1, n(i)) : c(r, 2, i) } catch (a) { c(r, 2, a) } }, r }, e }(); function l(e) { return e instanceof u && 1 & e.s } var f = new a, p = function () { function e(e) { var t = e.plugins, n = e.store, i = e.router; this.plugins = t, this.store = n, this.router = i, this.components = {}, this.hooks = f, this.search = {}, r.a.component(o.name, o) } var t = e.prototype; return t.hasPlugin = function (e) { return this.plugins.filter((function (t) { return t.name === e })).length > 0 }, t.registerComponent = function (e, t, n) { return this.components[e] = this.components[e] || [], this.components[e].push({ component: t, props: n }), this }, t.getComponents = function (e) { return this.components[e] || [] }, t.processMarkdown = function (e) { return this.hooks.add("processMarkdown", e), this }, t.processHTML = function (e) { return this.hooks.add("processHTML", e), this }, t.extendMarkedRenderer = function (e) { return this.hooks.add("extendMarkedRenderer", e), this }, t.onContentUpdated = function (e) { return this.hooks.add("onContentUpdated", e), this }, t.extendMarkdownComponent = function (e) { return this.hooks.add("extendMarkdownComponent", e), this }, t.enableSearch = function (e) { return void 0 === e && (e = {}), this.search = e, this.search.enabled = !0, this }, e }(), d = { name: "DocuteRoot", render: function (e) { return e("div", { attrs: { id: this.$store.getters.target, class: "Root" } }, [e("router-view")]) }, created: function () { this.insertStyle() }, computed: { css: function () { var e = this.$store.getters.cssVariables; return ":root{" + Object.keys(e).reduce((function (t, n) { return t += "--" + n.replace(/[A-Z]/g, (function (e) { return "-" + e.toLowerCase() })) + ":" + e[n] + ";" }), "") + "}" } }, watch: { css: function () { this.insertStyle() } }, methods: { insertStyle: function () { if (this.$ssrContext) this.$ssrContext.insertedStyle = this.css; else { var e = "docute-inserted-style", t = document.getElementById(e); t ? t.innerHTML = this.css : ((t = document.createElement("style")).id = e, t.innerHTML = this.css, document.head.insertBefore(t, document.head.firstChild)) } } } }; n(36); function h(e, t, n, r, i, o, a, s) { var c, u = "function" === typeof e ? e.options : e; if (t && (u.render = t, u.staticRenderFns = n, u._compiled = !0), r && (u.functional = !0), o && (u._scopeId = "data-v-" + o), a ? (c = function (e) { (e = e || this.$vnode && this.$vnode.ssrContext || this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) || "undefined" === typeof __VUE_SSR_CONTEXT__ || (e = __VUE_SSR_CONTEXT__), i && i.call(this, e), e && e._registeredComponents && e._registeredComponents.add(a) }, u._ssrRegister = c) : i && (c = s ? function () { i.call(this, this.$root.$options.shadowRoot) } : i), c) if (u.functional) { u._injectStyles = c; var l = u.render; u.render = function (e, t) { return c.call(t), l(e, t) } } else { var f = u.beforeCreate; u.beforeCreate = f ? [].concat(f, c) : [c] } return { exports: e, options: u } } var v = h(d, void 0, void 0, !1, null, null, null).exports, m = n(2), g = function (e) { if (e) try { e = new Function("return " + e)() } catch (t) { console.error("You're using invalid options for code fences, it must be JSON or JS object!\n" + t.message) } return e || {} }, y = /\.md$/, b = { newline: /^\n+/, code: /^( {4}[^\n]+\n*)+/, fences: j, hr: /^ {0,3}((?:- *){3,}|(?:_ *){3,}|(?:\* *){3,})(?:\n+|$)/, heading: /^ *(#{1,6}) *([^\n]+?) *(?:#+ *)?(?:\n+|$)/, nptable: j, blockquote: /^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/, list: /^( *)(bull) [\s\S]+?(?:hr|def|\n{2,}(?! )(?!\1bull )\n*|\s*$)/, html: "^ {0,3}(?:<(script|pre|style)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?\\?>\\n*|<![A-Z][\\s\\S]*?>\\n*|<!\\[CDATA\\[[\\s\\S]*?\\]\\]>\\n*|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:\\n{2,}|$)|<(?!script|pre|style)([a-z][\\w-]*)(?:attribute)*? */?>(?=\\h*\\n)[\\s\\S]*?(?:\\n{2,}|$)|</(?!script|pre|style)[a-z][\\w-]*\\s*>(?=\\h*\\n)[\\s\\S]*?(?:\\n{2,}|$))", def: /^ {0,3}\[(label)\]: *\n? *<?([^\s>]+)>?(?:(?: +\n? *| *\n *)(title))? *(?:\n+|$)/, table: j, lheading: /^([^\n]+)\n *(=|-){2,} *(?:\n+|$)/, paragraph: /^([^\n]+(?:\n(?!hr|heading|lheading| {0,3}>|<\/?(?:tag)(?: +|\n|\/?>)|<(?:script|pre|style|!--))[^\n]+)*)/, text: /^[^\n]+/ }; function _(e) { this.tokens = [], this.tokens.links = Object.create(null), this.options = e || R.defaults, this.rules = b.normal, this.options.pedantic ? this.rules = b.pedantic : this.options.gfm && (this.options.tables ? this.rules = b.tables : this.rules = b.gfm) } b._label = /(?!\s*\])(?:\\[\[\]]|[^\[\]])+/, b._title = /(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/, b.def = O(b.def).replace("label", b._label).replace("title", b._title).getRegex(), b.bullet = /(?:[*+-]|\d+\.)/, b.item = /^( *)(bull) [^\n]*(?:\n(?!\1bull )[^\n]*)*/, b.item = O(b.item, "gm").replace(/bull/g, b.bullet).getRegex(), b.list = O(b.list).replace(/bull/g, b.bullet).replace("hr", "\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def", "\\n+(?=" + b.def.source + ")").getRegex(), b._tag = "address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul", b._comment = /<!--(?!-?>)[\s\S]*?-->/, b.html = O(b.html, "i").replace("comment", b._comment).replace("tag", b._tag).replace("attribute", / +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(), b.paragraph = O(b.paragraph).replace("hr", b.hr).replace("heading", b.heading).replace("lheading", b.lheading).replace("tag", b._tag).getRegex(), b.blockquote = O(b.blockquote).replace("paragraph", b.paragraph).getRegex(), b.normal = I({}, b), b.gfm = I({}, b.normal, { fences: /^ *(`{3,}|~{3,})[ \.]*(\S+)? *({.+})?\n([\s\S]*?)\n? *\1 *(?:\n+|$)/, paragraph: /^/, heading: /^ *(#{1,6}) +([^\n]+?) *#* *(?:\n+|$)/ }), b.gfm.paragraph = O(b.paragraph).replace("(?!", "(?!" + b.gfm.fences.source.replace("\\1", "\\2") + "|" + b.list.source.replace("\\1", "\\3") + "|").getRegex(), b.tables = I({}, b.gfm, { nptable: /^ *([^|\n ].*\|.*)\n *([-:]+ *\|[-| :]*)(?:\n((?:.*[^>\n ].*(?:\n|$))*)\n*|$)/, table: /^ *\|(.+)\n *\|?( *[-:]+[-| :]*)(?:\n((?: *[^>\n ].*(?:\n|$))*)\n*|$)/ }), b.pedantic = I({}, b.normal, { html: O("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment", b._comment).replace(/tag/g, "(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(), def: /^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/ }), _.rules = b, _.lex = function (e, t) { return new _(t).lex(e) }, _.prototype.lex = function (e) { return e = e.replace(/\r\n|\r/g, "\n").replace(/\t/g, "    ").replace(/\u00a0/g, " ").replace(/\u2424/g, "\n"), this.token(e, !0) }, _.prototype.token = function (e, t) { var n, r, i, o, a, s, c, u, l, f, p, d, h, v, m, y; for (e = e.replace(/^ +$/gm, ""); e;)if ((i = this.rules.newline.exec(e)) && (e = e.substring(i[0].length), i[0].length > 1 && this.tokens.push({ type: "space" })), i = this.rules.code.exec(e)) e = e.substring(i[0].length), i = i[0].replace(/^ {4}/gm, ""), this.tokens.push({ type: "code", text: this.options.pedantic ? i : M(i, "\n") }); else if (i = this.rules.fences.exec(e)) e = e.substring(i[0].length), this.tokens.push({ type: "code", lang: i[2], text: i[4] || "", opts: g(i[3]) }); else if (i = this.rules.heading.exec(e)) e = e.substring(i[0].length), this.tokens.push({ type: "heading", depth: i[1].length, text: i[2] }); else if (t && (i = this.rules.nptable.exec(e)) && (s = { type: "table", header: F(i[1].replace(/^ *| *\| *$/g, "")), align: i[2].replace(/^ *|\| *$/g, "").split(/ *\| */), cells: i[3] ? i[3].replace(/\n$/, "").split("\n") : [] }).header.length === s.align.length) { for (e = e.substring(i[0].length), p = 0; p < s.align.length; p++)/^ *-+: *$/.test(s.align[p]) ? s.align[p] = "right" : /^ *:-+: *$/.test(s.align[p]) ? s.align[p] = "center" : /^ *:-+ *$/.test(s.align[p]) ? s.align[p] = "left" : s.align[p] = null; for (p = 0; p < s.cells.length; p++)s.cells[p] = F(s.cells[p], s.header.length); this.tokens.push(s) } else if (i = this.rules.hr.exec(e)) e = e.substring(i[0].length), this.tokens.push({ type: "hr" }); else if (i = this.rules.blockquote.exec(e)) e = e.substring(i[0].length), this.tokens.push({ type: "blockquote_start" }), i = i[0].replace(/^ *> ?/gm, ""), this.token(i, t), this.tokens.push({ type: "blockquote_end" }); else if (i = this.rules.list.exec(e)) { for (e = e.substring(i[0].length), c = { type: "list_start", ordered: v = (o = i[2]).length > 1, start: v ? +o : "", loose: !1 }, this.tokens.push(c), u = [], n = !1, h = (i = i[0].match(this.rules.item)).length, p = 0; p < h; p++)f = (s = i[p]).length, ~(s = s.replace(/^ *([*+-]|\d+\.) +/, "")).indexOf("\n ") && (f -= s.length, s = this.options.pedantic ? s.replace(/^ {1,4}/gm, "") : s.replace(new RegExp("^ {1," + f + "}", "gm"), "")), this.options.smartLists && p !== h - 1 && (o === (a = b.bullet.exec(i[p + 1])[0]) || o.length > 1 && a.length > 1 || (e = i.slice(p + 1).join("\n") + e, p = h - 1)), r = n || /\n\n(?!\s*$)/.test(s), p !== h - 1 && (n = "\n" === s.charAt(s.length - 1), r || (r = n)), r && (c.loose = !0), y = void 0, (m = /^\[[ xX]\] /.test(s)) && (y = " " !== s[1], s = s.replace(/^\[[ xX]\] +/, "")), l = { type: "list_item_start", task: m, checked: y, loose: r }, u.push(l), this.tokens.push(l), this.token(s, !1), this.tokens.push({ type: "list_item_end" }); if (c.loose) for (h = u.length, p = 0; p < h; p++)u[p].loose = !0; this.tokens.push({ type: "list_end" }) } else if (i = this.rules.html.exec(e)) e = e.substring(i[0].length), this.tokens.push({ type: this.options.sanitize ? "paragraph" : "html", pre: !this.options.sanitizer && ("pre" === i[1] || "script" === i[1] || "style" === i[1]), text: i[0] }); else if (t && (i = this.rules.def.exec(e))) e = e.substring(i[0].length), i[3] && (i[3] = i[3].substring(1, i[3].length - 1)), d = i[1].toLowerCase().replace(/\s+/g, " "), this.tokens.links[d] || (this.tokens.links[d] = { href: i[2], title: i[3] }); else if (t && (i = this.rules.table.exec(e)) && (s = { type: "table", header: F(i[1].replace(/^ *| *\| *$/g, "")), align: i[2].replace(/^ *|\| *$/g, "").split(/ *\| */), cells: i[3] ? i[3].replace(/(?: *\| *)?\n$/, "").split("\n") : [] }).header.length === s.align.length) { for (e = e.substring(i[0].length), p = 0; p < s.align.length; p++)/^ *-+: *$/.test(s.align[p]) ? s.align[p] = "right" : /^ *:-+: *$/.test(s.align[p]) ? s.align[p] = "center" : /^ *:-+ *$/.test(s.align[p]) ? s.align[p] = "left" : s.align[p] = null; for (p = 0; p < s.cells.length; p++)s.cells[p] = F(s.cells[p].replace(/^ *\| *| *\| *$/g, ""), s.header.length); this.tokens.push(s) } else if (i = this.rules.lheading.exec(e)) e = e.substring(i[0].length), this.tokens.push({ type: "heading", depth: "=" === i[2] ? 1 : 2, text: i[1] }); else if (t && (i = this.rules.paragraph.exec(e))) e = e.substring(i[0].length), this.tokens.push({ type: "paragraph", text: "\n" === i[1].charAt(i[1].length - 1) ? i[1].slice(0, -1) : i[1] }); else if (i = this.rules.text.exec(e)) e = e.substring(i[0].length), this.tokens.push({ type: "text", text: i[0] }); else if (e) throw new Error("Infinite loop on byte: " + e.charCodeAt(0)); return this.tokens }; var k = { escape: /^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/, autolink: /^<(scheme:[^\s\x00-\x1f<>]*|email)>/, url: j, tag: "^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>", link: /^!?\[(label)\]\(href(?:\s+(title))?\s*\)/, reflink: /^!?\[(label)\]\[(?!\s*\])((?:\\[\[\]]?|[^\[\]\\])+)\]/, nolink: /^!?\[(?!\s*\])((?:\[[^\[\]]*\]|\\[\[\]]|[^\[\]])*)\](?:\[\])?/, strong: /^__([^\s])__(?!_)|^\*\*([^\s])\*\*(?!\*)|^__([^\s][\s\S]*?[^\s])__(?!_)|^\*\*([^\s][\s\S]*?[^\s])\*\*(?!\*)/, em: /^_([^\s_])_(?!_)|^\*([^\s*"<\[])\*(?!\*)|^_([^\s][\s\S]*?[^\s_])_(?!_)|^_([^\s_][\s\S]*?[^\s])_(?!_)|^\*([^\s"<\[][\s\S]*?[^\s*])\*(?!\*)|^\*([^\s*"<\[][\s\S]*?[^\s])\*(?!\*)/, code: /^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/, br: /^( {2,}|\\)\n(?!\s*$)/, del: j, text: /^(`+|[^`])[\s\S]*?(?=[\\<!\[`*]|\b_| {2,}\n|$)/ }; function w(e, t) { if (this.options = t || R.defaults, this.links = e, this.rules = k.normal, this.renderer = this.options.renderer || new x, this.renderer.options = this.options, !this.links) throw new Error("Tokens array requires a `links` property."); this.options.pedantic ? this.rules = k.pedantic : this.options.gfm && (this.options.breaks ? this.rules = k.breaks : this.rules = k.gfm) } function x(e) { this.options = e || R.defaults } function C() { } function A(e) { this.tokens = [], this.token = null, this.options = e || R.defaults, this.options.renderer = this.options.renderer || new x, this.renderer = this.options.renderer, this.renderer.options = this.options } function $(e, t) { if (t) { if ($.escapeTest.test(e)) return e.replace($.escapeReplace, (function (e) { return $.replacements[e] })) } else if ($.escapeTestNoEncode.test(e)) return e.replace($.escapeReplaceNoEncode, (function (e) { return $.replacements[e] })); return e } function S(e) { return e.replace(/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi, (function (e, t) { return "colon" === (t = t.toLowerCase()) ? ":" : "#" === t.charAt(0) ? "x" === t.charAt(1) ? String.fromCharCode(parseInt(t.substring(2), 16)) : String.fromCharCode(+t.substring(1)) : "" })) } function O(e, t) { return e = e.source || e, t = t || "", { replace: function (t, n) { return n = (n = n.source || n).replace(/(^|[^\[])\^/g, "$1"), e = e.replace(t, n), this }, getRegex: function () { return new RegExp(e, t) } } } function E(e, t) { return T[" " + e] || (/^[^:]+:\/*[^/]*$/.test(e) ? T[" " + e] = e + "/" : T[" " + e] = M(e, "/", !0)), e = T[" " + e], "//" === t.slice(0, 2) ? e.replace(/:[\s\S]*/, ":") + t : "/" === t.charAt(0) ? e.replace(/(:\/*[^/]*)[\s\S]*/, "$1") + t : e + t } k._escapes = /\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g, k._scheme = /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/, k._email = /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/, k.autolink = O(k.autolink).replace("scheme", k._scheme).replace("email", k._email).getRegex(), k._attribute = /\s+[a-zA-Z@:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/, k.tag = O(k.tag).replace("comment", b._comment).replace("attribute", k._attribute).getRegex(), k._label = /(?:\[[^\[\]]*\]|\\[\[\]]?|`[^`]*`|[^\[\]\\])*?/, k._href = /\s*(<(?:\\[<>]?|[^\s<>\\])*>|(?:\\[()]?|\([^\s\x00-\x1f\\]*\)|[^\s\x00-\x1f()\\])*?)/, k._title = /"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/, k.link = O(k.link).replace("label", k._label).replace("href", k._href).replace("title", k._title).getRegex(), k.reflink = O(k.reflink).replace("label", k._label).getRegex(), k.normal = I({}, k), k.pedantic = I({}, k.normal, { strong: /^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/, em: /^_(?=\S)([\s\S]*?\S)_(?!_)|^\*(?=\S)([\s\S]*?\S)\*(?!\*)/, link: O(/^!?\[(label)\]\((.*?)\)/).replace("label", k._label).getRegex(), reflink: O(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label", k._label).getRegex() }), k.gfm = I({}, k.normal, { escape: O(k.escape).replace("])", "~|])").getRegex(), _extended_email: /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/, url: /^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/, _backpedal: /(?:[^?!.,:;*_~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_~)]+(?!$))+/, del: /^~+(?=\S)([\s\S]*?\S)~+/, text: O(k.text).replace("]|", "~]|").replace("|$", "|https?://|ftp://|www\\.|[a-zA-Z0-9.!#$%&'*+/=?^_`{\\|}~-]+@|$").getRegex() }), k.gfm.url = O(k.gfm.url).replace("email", k.gfm._extended_email).getRegex(), k.breaks = I({}, k.gfm, { br: O(k.br).replace("{2,}", "*").getRegex(), text: O(k.gfm.text).replace("{2,}", "*").getRegex() }), w.rules = k, w.output = function (e, t, n) { return new w(t, n).output(e) }, w.prototype.output = function (e) { for (var t, n, r, i, o, a, s = ""; e;)if (o = this.rules.escape.exec(e)) e = e.substring(o[0].length), s += o[1]; else if (o = this.rules.autolink.exec(e)) e = e.substring(o[0].length), r = "@" === o[2] ? "mailto:" + (n = $(this.mangle(o[1]))) : n = $(o[1]), s += this.renderer.link(r, null, n); else if (this.inLink || !(o = this.rules.url.exec(e))) { if (o = this.rules.tag.exec(e)) !this.inLink && /^<a /i.test(o[0]) ? this.inLink = !0 : this.inLink && /^<\/a>/i.test(o[0]) && (this.inLink = !1), !this.inRawBlock && /^<(pre|code|kbd|script)(\s|>)/i.test(o[0]) ? this.inRawBlock = !0 : this.inRawBlock && /^<\/(pre|code|kbd|script)(\s|>)/i.test(o[0]) && (this.inRawBlock = !1), e = e.substring(o[0].length), s += this.options.sanitize ? this.options.sanitizer ? this.options.sanitizer(o[0]) : $(o[0]) : o[0]; else if (o = this.rules.link.exec(e)) e = e.substring(o[0].length), this.inLink = !0, r = o[2], this.options.pedantic ? (t = /^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(r)) ? (r = t[1], i = t[3]) : i = "" : i = o[3] ? o[3].slice(1, -1) : "", r = r.trim().replace(/^<([\s\S]*)>$/, "$1"), s += this.outputLink(o, { href: w.escapes(r), title: w.escapes(i) }), this.inLink = !1; else if ((o = this.rules.reflink.exec(e)) || (o = this.rules.nolink.exec(e))) { if (e = e.substring(o[0].length), t = (o[2] || o[1]).replace(/\s+/g, " "), !(t = this.links[t.toLowerCase()]) || !t.href) { s += o[0].charAt(0), e = o[0].substring(1) + e; continue } this.inLink = !0, s += this.outputLink(o, t), this.inLink = !1 } else if (o = this.rules.strong.exec(e)) e = e.substring(o[0].length), s += this.renderer.strong(this.output(o[4] || o[3] || o[2] || o[1])); else if (o = this.rules.em.exec(e)) e = e.substring(o[0].length), s += this.renderer.em(this.output(o[6] || o[5] || o[4] || o[3] || o[2] || o[1])); else if (o = this.rules.code.exec(e)) e = e.substring(o[0].length), s += this.renderer.codespan($(o[2].trim(), !0)); else if (o = this.rules.br.exec(e)) e = e.substring(o[0].length), s += this.renderer.br(); else if (o = this.rules.del.exec(e)) e = e.substring(o[0].length), s += this.renderer.del(this.output(o[1])); else if (o = this.rules.text.exec(e)) e = e.substring(o[0].length), this.inRawBlock ? s += this.renderer.text(o[0]) : s += this.renderer.text($(this.smartypants(o[0]))); else if (e) throw new Error("Infinite loop on byte: " + e.charCodeAt(0)) } else { if ("@" === o[2]) r = "mailto:" + (n = $(o[0])); else { do { a = o[0], o[0] = this.rules._backpedal.exec(o[0])[0] } while (a !== o[0]); n = $(o[0]), r = "www." === o[1] ? "http://" + n : n } e = e.substring(o[0].length), s += this.renderer.link(r, null, n) } return s }, w.escapes = function (e) { return e ? e.replace(w.rules._escapes, "$1") : e }, w.prototype.outputLink = function (e, t) { var n = t.href, r = t.title ? $(t.title) : null; return "!" !== e[0].charAt(0) ? this.renderer.link(n, r, this.output(e[1])) : this.renderer.image(n, r, $(e[1])) }, w.prototype.smartypants = function (e) { return this.options.smartypants ? e.replace(/---/g, "\u2014").replace(/--/g, "\u2013").replace(/(^|[-\u2014/(\[{"\s])'/g, "$1\u2018").replace(/'/g, "\u2019").replace(/(^|[-\u2014/(\[{\u2018\s])"/g, "$1\u201c").replace(/"/g, "\u201d").replace(/\.{3}/g, "\u2026") : e }, w.prototype.mangle = function (e) { if (!this.options.mangle) return e; for (var t, n = "", r = e.length, i = 0; i < r; i++)t = e.charCodeAt(i), Math.random() > .5 && (t = "x" + t.toString(16)), n += "&#" + t + ";"; return n }, x.prototype.code = function (e, t, n) { if (this.options.highlight) { var r = this.options.highlight(e, t); null != r && r !== e && (n = !0, e = r) } return t ? '<pre><code class="' + this.options.langPrefix + $(t, !0) + '">' + (n ? e : $(e, !0)) + "</code></pre>\n" : "<pre><code>" + (n ? e : $(e, !0)) + "</code></pre>" }, x.prototype.blockquote = function (e) { return "<blockquote>\n" + e + "</blockquote>\n" }, x.prototype.html = function (e) { return e }, x.prototype.heading = function (e, t, n) { return this.options.headerIds ? "<h" + t + ' id="' + this.options.headerPrefix + n.toLowerCase().replace(/[^\w]+/g, "-") + '">' + e + "</h" + t + ">\n" : "<h" + t + ">" + e + "</h" + t + ">\n" }, x.prototype.hr = function () { return this.options.xhtml ? "<hr/>\n" : "<hr>\n" }, x.prototype.list = function (e, t, n, r) { var i = t ? "ol" : "ul"; return "<" + i + (t && 1 !== n ? ' start="' + n + '"' : "") + (r ? ' class="contains-task-list"' : "") + ">\n" + e + "</" + i + ">\n" }, x.prototype.listitem = function (e, t) { return "<li" + (t ? ' class="task-list-item"' : "") + ">" + e + "</li>\n" }, x.prototype.checkbox = function (e) { return "<input " + (e ? 'checked="" ' : "") + 'disabled="" type="checkbox"' + (this.options.xhtml ? " /" : "") + "> " }, x.prototype.paragraph = function (e) { return "<p>" + e + "</p>\n" }, x.prototype.table = function (e, t) { return t && (t = "<tbody>" + t + "</tbody>"), "<table>\n<thead>\n" + e + "</thead>\n" + t + "</table>\n" }, x.prototype.tablerow = function (e) { return "<tr>\n" + e + "</tr>\n" }, x.prototype.tablecell = function (e, t) { var n = t.header ? "th" : "td"; return (t.align ? "<" + n + ' align="' + t.align + '">' : "<" + n + ">") + e + "</" + n + ">\n" }, x.prototype.strong = function (e) { return "<strong>" + e + "</strong>" }, x.prototype.em = function (e) { return "<em>" + e + "</em>" }, x.prototype.codespan = function (e) { return "<code>" + e + "</code>" }, x.prototype.br = function () { return this.options.xhtml ? "<br/>" : "<br>" }, x.prototype.del = function (e) { return "<del>" + e + "</del>" }, x.prototype.link = function (e, t, n) { if (this.options.sanitize) { try { var r = decodeURIComponent(S(e)).replace(/[^\w:]/g, "").toLowerCase() } catch (c) { return n } if (0 === r.indexOf("javascript:") || 0 === r.indexOf("vbscript:") || 0 === r.indexOf("data:")) return n } this.options.baseUrl && !L.test(e) && (e = E(this.options.baseUrl, e)); try { e = encodeURI(e).replace(/%25/g, "%") } catch (c) { return n } var i = /^https?:\/\//.test(e), o = /^mailto:/.test(e), a = i || o ? "a" : "router-link", s = "<" + a + " " + ("a" === a ? "href" : "to") + '="' + $(i ? e : function (e) { var t = e.split("#"), n = t[0], r = t[1]; return y.test(n) && (n = n.replace(y, "")), n + (r ? "#" + r : "") }(S(e))) + '"'; return t && (s += ' title="' + t + '"'), i && (s += ' target="_blank" rel="noopener noreferrer"', n += "\n<external-link-icon />"), s += ">" + n + "</" + a + ">" }, x.prototype.image = function (e, t, n) { this.options.baseUrl && !L.test(e) && (e = E(this.options.baseUrl, e)); var r = this.options.env.config.imageZoom, i = "<" + (r ? "image-zoom" : "img") + ' src="' + e + '" alt="' + n + '"'; return t && (i += ' title="' + t + '"'), r && (i += ' v-bind:border="false"'), i += this.options.xhtml ? "/>" : ">" }, x.prototype.text = function (e) { return e }, C.prototype.strong = C.prototype.em = C.prototype.codespan = C.prototype.del = C.prototype.text = function (e) { return e }, C.prototype.link = C.prototype.image = function (e, t, n) { return "" + n }, C.prototype.br = function () { return "" }, A.parse = function (e, t) { return new A(t).parse(e) }, A.prototype.parse = function (e) { this.inline = new w(e.links, this.options), this.inlineText = new w(e.links, I({}, this.options, { renderer: new C })), this.tokens = e.reverse(); for (var t = ""; this.next();)t += this.tok(); return t }, A.prototype.next = function () { return this.token = this.tokens.pop() }, A.prototype.peek = function () { return this.tokens[this.tokens.length - 1] || 0 }, A.prototype.parseText = function () { for (var e = this.token.text; "text" === this.peek().type;)e += "\n" + this.next().text; return this.inline.output(e) }, A.prototype.tok = function () { switch (this.token.type) { case "space": return ""; case "hr": return this.renderer.hr(); case "heading": return this.renderer.heading(this.inline.output(this.token.text), this.token.depth, S(this.inlineText.output(this.token.text))); case "code": return this.renderer.code(this.token.text, this.token.lang, this.token.escaped, this.token.opts); case "table": var e, t, n, r, i = "", o = ""; for (n = "", e = 0; e < this.token.header.length; e++)n += this.renderer.tablecell(this.inline.output(this.token.header[e]), { header: !0, align: this.token.align[e] }); for (i += this.renderer.tablerow(n), e = 0; e < this.token.cells.length; e++) { for (t = this.token.cells[e], n = "", r = 0; r < t.length; r++)n += this.renderer.tablecell(this.inline.output(t[r]), { header: !1, align: this.token.align[r] }); o += this.renderer.tablerow(n) } return this.renderer.table(i, o); case "blockquote_start": for (o = ""; "blockquote_end" !== this.next().type;)o += this.tok(); return this.renderer.blockquote(o); case "list_start": o = ""; for (var a = this.token.ordered, s = this.token.start, c = !1; "list_end" !== this.next().type;)this.token.task && (c = !0), o += this.tok(); return this.renderer.list(o, a, s, c); case "list_item_start": o = ""; var u = this.token.loose, l = this.token.task; for (l && (o += this.renderer.checkbox(this.token.checked)); "list_item_end" !== this.next().type;)o += u || "text" !== this.token.type ? this.tok() : this.parseText(); return this.renderer.listitem(o, l); case "html": return this.renderer.html(this.token.text); case "paragraph": return this.renderer.paragraph(this.inline.output(this.token.text)); case "text": return this.renderer.paragraph(this.parseText()) } }, $.escapeTest = /[&<>"']/, $.escapeReplace = /[&<>"']/g, $.replacements = { "&": "&amp;", "<": "&lt;", ">": "&gt;", '"': "&quot;", "'": "&#39;" }, $.escapeTestNoEncode = /[<>"']|&(?!#?\w+;)/, $.escapeReplaceNoEncode = /[<>"']|&(?!#?\w+;)/g; var T = {}, L = /^$|^[a-z][a-z0-9+.-]*:|^[?#]/i; function j() { } function I(e) { for (var t, n, r = 1; r < arguments.length; r++)for (n in t = arguments[r]) Object.prototype.hasOwnProperty.call(t, n) && (e[n] = t[n]); return e } function F(e, t) { var n = e.replace(/\|/g, (function (e, t, n) { for (var r = !1, i = t; --i >= 0 && "\\" === n[i];)r = !r; return r ? "|" : " |" })).split(/ \|/), r = 0; if (n.length > t) n.splice(t); else for (; n.length < t;)n.push(""); for (; r < n.length; r++)n[r] = n[r].trim().replace(/\\\|/g, "|"); return n } function M(e, t, n) { if (0 === e.length) return ""; for (var r = 0; r < e.length;) { var i = e.charAt(e.length - r - 1); if (i !== t || n) { if (i === t || !n) break; r++ } else r++ } return e.substr(0, e.length - r) } function R(e, t, n) { if ("undefined" === typeof e || null === e) throw new Error("marked(): input parameter is undefined or null"); if ("string" !== typeof e) throw new Error("marked(): input parameter is of type " + Object.prototype.toString.call(e) + ", string expected"); if (n || "function" === typeof t) { n || (n = t, t = null); var r, i, o = (t = I({}, R.defaults, t || {})).highlight, a = 0; try { r = _.lex(e, t) } catch (c) { return n(c) } i = r.length; var s = function (e) { if (e) return t.highlight = o, n(e); var i; try { i = A.parse(r, t) } catch (c) { e = c } return t.highlight = o, e ? n(e) : n(null, i) }; if (!o || o.length < 3) return s(); if (delete t.highlight, !i) return s(); for (; a < r.length; a++)!function (e) { "code" !== e.type ? --i || s() : o(e.text, e.lang, (function (t, n) { return t ? s(t) : null == n || n === e.text ? --i || s() : (e.text = n, e.escaped = !0, void (--i || s())) })) }(r[a]) } else try { return t && (t = I({}, R.defaults, t)), A.parse(_.lex(e, t), t) } catch (c) { if (c.message += "\nPlease report this to https://github.com/markedjs/marked.", (t || R.defaults).silent) return "<p>An error occurred:</p><pre>" + $(c.message + "", !0) + "</pre>"; throw c } } j.exec = j, R.options = R.setOptions = function (e) { return I(R.defaults, e), R }, R.getDefaults = function () { return { baseUrl: null, breaks: !1, gfm: !0, headerIds: !0, headerPrefix: "", highlight: null, langPrefix: "language-", mangle: !0, pedantic: !1, renderer: new x, sanitize: !1, sanitizer: null, silent: !1, smartLists: !1, smartypants: !1, tables: !0, xhtml: !1 } }, R.defaults = R.getDefaults(), R.Parser = A, R.parser = A.parse, R.Renderer = x, R.TextRenderer = C, R.Lexer = _, R.lexer = _.lex, R.InlineLexer = w, R.inlineLexer = w.output, R.parse = R, R.escape = $, R.unescape = S; var P = R, N = n(27), z = n.n(N); function D(e, t) { if (!t) return e; var n = t && z.a.languages[t]; return n || (t = "markup", n = z.a.languages.markup), z.a.highlight(e, n, t) } var H = function (e) { return /^https?:\/\//.test(e) }, B = function (e, t) { return ((e = (e = e || ".").replace(/\/$/, "")) + t).replace(/^\.\//, "") }, G = function (e) { return e = e.replace(/^\/?/, "/"), /\.md$/.test(e) || (e = /\/$/.test(e) ? e + "README.md" : e + ".md"), e }, U = "undefined" !== typeof window, Z = function (e) { var t = new P.Renderer, n = []; t.heading = function (e, t, r) { var i = this.options.env, o = r.trim().replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g, "").replace(/\s/g, "-").toLowerCase(); n.push(o); var a = n.filter((function (e) { return e === o })).length; if (a > 1 && (o += "-" + a), 1 === t) return i.title = e, ""; 2 === t && i.headings.push({ level: t, raw: r, text: r.replace(/<.*>\s*$/g, ""), slug: o }); var s = "h" + t; return "<" + s + ' class="markdown-header" id="' + o + '">\n    <router-link class="header-anchor" :to="{hash:\'' + o + '\'}">\n      <svg class="anchor-icon" viewBox="0 0 16 16" version="1.1" width="16" height="16"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg>\n    </router-link>\n    ' + e + "</" + s + ">" }, t.codespan = function (e) { return "<code v-pre>" + e + "</code>" }; var r = t.code; return t.code = function (e, t, n, i) { i = i || {}; var o = this.options.env; if (i.mixin) return o.mixins.push(e), ""; var a = r.call(this, e, t, n); if (i.interpolate || (a = a.replace(/^<pre>/, "<pre v-pre>")), i.highlight) { var s = e.split("\n").map((function (e, t) { t += 1; var n = i.highlight.some((function (e) { if ("number" === typeof e) return e === t; if ("string" === typeof e) { var n = e.split("-").map(Number), r = n[0], i = n[1]; return t >= r && (!i || t <= i) } return !1 })), r = e ? P.escape(e) : "&#8203;"; return n ? '<span class="code-line highlighted">' + r + "</span>" : '<span class="code-line">' + r + "</span>" })).join(""); a += "<div" + (i.interpolate ? "" : " v-pre") + ' class="code-mask">' + s + "</div>" } return '<div data-lang="' + (t || "") + '" class="pre-wrapper">' + a + "</div>" }, e.process("extendMarkedRenderer", t) }, q = n(29), V = n.n(q), K = n(28), J = { accentColor: "#009688", pageBackground: "#fff", headerBackground: "#fff", headerTextColor: "var(--text-color)", textColor: "#000", linkColor: "var(--accent-color)", sidebarWidth: "280px", sidebarBackground: "var(--page-background)", sidebarLinkColor: "#444", sidebarLinkActiveColor: "#000", sidebarLinkArrowColor: "#999", mainBackground: "var(--page-background)", borderColor: "#eaeaea", headerHeight: "55px", codeFont: "SFMono-Regular,Consolas,Liberation Mono,Menlo,Courier,monospace", tipColor: "rgb(6, 125, 247)", successColor: "#42b983", warningColor: "#ff9800", dangerColor: "rgb(255, 0, 31)", navLinkColor: "#2c3e50", navLinkBorderColor: "var(--accent-color)", codeBlockBackground: "#011627", codeBlockTextColor: "white", codeBlockShadowColor: "#333", codeBlockShadowWidth: "0px", highlightedLineBackground: "#022a4b", highlightedLineBorderColor: "#ffa7c4", inlineCodeColor: "rgb(116, 66, 16)", inlineCodeBackground: "rgb(254, 252, 191)", loaderPrimaryColor: "#f3f3f3", loaderSecondaryColor: "#ecebeb", tableHeaderBackground: "#fafafa", tableHeaderColor: "#666", docuteSelectHeight: "38px", searchIconColor: "#999", searchFocusBorderColor: "#ccc", searchFocusIconColor: "#333", searchResultHoverBackground: "#f9f9f9" }, W = Object.assign({}, J, { headerBackground: "var(--page-background)", sidebarLinkColor: "var(--text-color)", sidebarLinkActiveColor: "var(--text-color)", textColor: "hsla(0,0%,100%,0.88)", pageBackground: "#2f3136", navLinkColor: "var(--text-color)", borderColor: "#3e4147", highlightedLineBackground: "#022a4b", highlightedLineBorderColor: "#ffa7c4", inlineCodeColor: "#e6e6e6", inlineCodeBackground: "#373c49", loaderPrimaryColor: "hsla(0, 0%, 100%, 0.08)", loaderSecondaryColor: "hsla(0, 0%, 100%, 0.18)", contentLinkBorder: "2px solid hsla(0, 0%, 100%, 0.28)", contentLinkHoverBorderColor: "currentColor", tableHeaderBackground: "var(--border-color)", tableHeaderColor: "#868686", searchIconColor: "#999", searchFocusBorderColor: "#999", searchFocusIconColor: "#ccc", searchResultBackground: "#27292f", searchResultHoverBackground: "#1e2025" }); function Y(e, t, n) { return n ? t ? t(e) : e : (e && e.then || (e = Promise.resolve(e)), t ? e.then(t) : e) } r.a.use(m.a); var X = U && window.__DOCUTE_INITIAL_STATE__, Q = new m.a.Store({ state: Object.assign({ originalConfig: {}, page: { title: null, headings: null, html: "" }, env: {}, showSidebar: !1, fetchingFile: !0 }, X), mutations: { SET_CONFIG: function (e, t) { void 0 === t && (t = {}), t.layout = t.layout || "narrow", t.centerContent && (t.layout = "narrow"), t.theme = function (e, t) { var n = t.theme, r = t.detectSystemDarkTheme; if (!U || !r) return n || "default"; var i = window.matchMedia("(prefers-color-scheme: dark)"); return i.addListener((function () { e.commit("SET_THEME", i.matches ? "dark" : "default") })), n || (i.matches ? "dark" : "default") }(Q, t), e.originalConfig = t }, SET_PAGE: function (e, t) { e.page = t }, TOGGLE_SIDEBAR: function (e, t) { e.showSidebar = "boolean" === typeof t ? t : !e.showSidebar }, SET_FETCHING: function (e, t) { e.fetchingFile = t }, SET_ENV: function (e, t) { e.env = t }, SET_THEME: function (e, t) { e.originalConfig.theme = t } }, actions: { fetchFile: function (e, t) { var n = e.commit, r = e.getters, i = e.dispatch; try { n("TOGGLE_SIDEBAR", !1), n("SET_FETCHING", !0); var o = Object.assign({ markdown: !0 }, r.config.routes && r.config.routes[t]); if (!o.content && !o.file) { var a = G(t); o.file = B(r.config.sourcePath, a), o.editLink = r.config.editLinkBase && B(r.config.editLinkBase, a) } return Y(Promise.all([!o.content && fetch(o.file, r.config.fetchOptions).then((function (e) { return e.text() })).then((function (e) { o.content = e })), i("fetchPrismLanguages")]), (function () { return Y(f.processPromise("processMarkdown", o.content), (function (e) { return o.content = e, Y(f.processPromise("processPage", o), (function (e) { o = e; var t = { headings: [], mixins: [], config: r.config }; return o.markdown && (o.content = P(o.content, { renderer: Z(f), highlight: D, env: t })), Y(f.processPromise("processHTML", o.content), (function (e) { o.content = e, o.headings = t.headings, o.title || (o.title = t.title), n("SET_PAGE", o), n("SET_ENV", t), n("SET_FETCHING", !1) })) })) })) })) } catch (s) { return Promise.reject(s) } }, fetchPrismLanguages: function (e) { var t, n, r = e.getters.config.highlight; return r && 0 !== r.length ? (t = r.reduce((function (e, t) { return K[t] && (e = e.concat(K[t])), e.push(t), e }), []).filter((function (e, t, n) { return n.indexOf(e) === t && -1 === K.builtin.indexOf(e) })).map((function (e) { return "https://unpkg.com/prismjs@1.17.1/components/prism-" + e + ".js" })), n = "prism-languages", new Promise((function (e) { if (V.a.isDefined(n)) return e(); V()(t, n, { success: e, error: function (t) { console.error("Deps not found:", t), e() } }) }))) : Promise.resolve() } }, getters: { target: function (e) { var t = e.originalConfig.target; return t ? "#" === t[0] ? t.slice(1) : t : "docute" }, languageOverrides: function (e) { var t = e.originalConfig, n = t.overrides || t.locales; return n && Object.keys(n).reduce((function (e, t) { return n[t].language && (e[t] = n[t]), e }), {}) }, currentLocalePath: function (e, t) { var n = e.route, r = t.languageOverrides; if (r) for (var i = 0, o = Object.keys(r); i < o.length; i++) { var a = o[i]; if ("/" !== a) if (new RegExp("^" + a).test(n.path)) return a } return "/" }, config: function (e, t) { var n = e.originalConfig, r = t.currentLocalePath, i = t.languageOverrides; return i ? Object.assign({}, n, {}, i[r]) : n }, homePaths: function (e, t) { var n = t.languageOverrides, r = n ? Object.keys(n) : []; return [].concat(r, ["/"]) }, sidebarLinks: function (e, t) { var n = t.sidebar; return n ? n.reduce((function (e, t) { var n = t.children || t.links || []; return [].concat(e, n) }), []).filter((function (e) { return !H(e.link) })) : [] }, sidebar: function (e, t) { var n = t.config.sidebar || []; return "function" === typeof n ? n(Q) : n }, cssVariables: function (e, t) { var n = t.config; return Object.assign({}, "dark" === n.theme ? W : J, {}, "function" === typeof n.cssVariables ? n.cssVariables(n.theme) : n.cssVariables) } } }); var ee = Q; function te(e) { return Object.prototype.toString.call(e).indexOf("Error") > -1 } function ne(e, t) { return t instanceof e || t && (t.name === e.name || t._name === e._name) } function re(e, t) { for (var n in t) e[n] = t[n]; return e } var ie = { name: "RouterView", functional: !0, props: { name: { type: String, default: "default" } }, render: function (e, t) { var n = t.props, r = t.children, i = t.parent, o = t.data; o.routerView = !0; for (var a = i.$createElement, s = n.name, c = i.$route, u = i._routerViewCache || (i._routerViewCache = {}), l = 0, f = !1; i && i._routerRoot !== i;) { var p = i.$vnode && i.$vnode.data; p && (p.routerView && l++ , p.keepAlive && i._inactive && (f = !0)), i = i.$parent } if (o.routerViewDepth = l, f) return a(u[s], o, r); var d = c.matched[l]; if (!d) return u[s] = null, a(); var h = u[s] = d.components[s]; o.registerRouteInstance = function (e, t) { var n = d.instances[s]; (t && n !== e || !t && n === e) && (d.instances[s] = t) }, (o.hook || (o.hook = {})).prepatch = function (e, t) { d.instances[s] = t.componentInstance }, o.hook.init = function (e) { e.data.keepAlive && e.componentInstance && e.componentInstance !== d.instances[s] && (d.instances[s] = e.componentInstance) }; var v = o.props = function (e, t) { switch (typeof t) { case "undefined": return; case "object": return t; case "function": return t(e); case "boolean": return t ? e.params : void 0; default: 0 } }(c, d.props && d.props[s]); if (v) { v = o.props = re({}, v); var m = o.attrs = o.attrs || {}; for (var g in v) h.props && g in h.props || (m[g] = v[g], delete v[g]) } return a(h, o, r) } }; var oe = /[!'()*]/g, ae = function (e) { return "%" + e.charCodeAt(0).toString(16) }, se = /%2C/g, ce = function (e) { return encodeURIComponent(e).replace(oe, ae).replace(se, ",") }, ue = decodeURIComponent; function le(e) { var t = {}; return (e = e.trim().replace(/^(\?|#|&)/, "")) ? (e.split("&").forEach((function (e) { var n = e.replace(/\+/g, " ").split("="), r = ue(n.shift()), i = n.length > 0 ? ue(n.join("=")) : null; void 0 === t[r] ? t[r] = i : Array.isArray(t[r]) ? t[r].push(i) : t[r] = [t[r], i] })), t) : t } function fe(e) { var t = e ? Object.keys(e).map((function (t) { var n = e[t]; if (void 0 === n) return ""; if (null === n) return ce(t); if (Array.isArray(n)) { var r = []; return n.forEach((function (e) { void 0 !== e && (null === e ? r.push(ce(t)) : r.push(ce(t) + "=" + ce(e))) })), r.join("&") } return ce(t) + "=" + ce(n) })).filter((function (e) { return e.length > 0 })).join("&") : null; return t ? "?" + t : "" } var pe = /\/?$/; function de(e, t, n, r) { var i = r && r.options.stringifyQuery, o = t.query || {}; try { o = he(o) } catch (s) { } var a = { name: t.name || e && e.name, meta: e && e.meta || {}, path: t.path || "/", hash: t.hash || "", query: o, params: t.params || {}, fullPath: ge(t, i), matched: e ? me(e) : [] }; return n && (a.redirectedFrom = ge(n, i)), Object.freeze(a) } function he(e) { if (Array.isArray(e)) return e.map(he); if (e && "object" === typeof e) { var t = {}; for (var n in e) t[n] = he(e[n]); return t } return e } var ve = de(null, { path: "/" }); function me(e) { for (var t = []; e;)t.unshift(e), e = e.parent; return t } function ge(e, t) { var n = e.path, r = e.query; void 0 === r && (r = {}); var i = e.hash; return void 0 === i && (i = ""), (n || "/") + (t || fe)(r) + i } function ye(e, t) { return t === ve ? e === t : !!t && (e.path && t.path ? e.path.replace(pe, "") === t.path.replace(pe, "") && e.hash === t.hash && be(e.query, t.query) : !(!e.name || !t.name) && (e.name === t.name && e.hash === t.hash && be(e.query, t.query) && be(e.params, t.params))) } function be(e, t) { if (void 0 === e && (e = {}), void 0 === t && (t = {}), !e || !t) return e === t; var n = Object.keys(e), r = Object.keys(t); return n.length === r.length && n.every((function (n) { var r = e[n], i = t[n]; return "object" === typeof r && "object" === typeof i ? be(r, i) : String(r) === String(i) })) } function _e(e, t, n) { var r = e.charAt(0); if ("/" === r) return e; if ("?" === r || "#" === r) return t + e; var i = t.split("/"); n && i[i.length - 1] || i.pop(); for (var o = e.replace(/^\//, "").split("/"), a = 0; a < o.length; a++) { var s = o[a]; ".." === s ? i.pop() : "." !== s && i.push(s) } return "" !== i[0] && i.unshift(""), i.join("/") } function ke(e) { return e.replace(/\/\//g, "/") } var we = Array.isArray || function (e) { return "[object Array]" == Object.prototype.toString.call(e) }, xe = Pe, Ce = Ee, Ae = function (e, t) { return Le(Ee(e, t)) }, $e = Le, Se = Re, Oe = new RegExp(["(\\\\.)", "([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"), "g"); function Ee(e, t) { for (var n, r = [], i = 0, o = 0, a = "", s = t && t.delimiter || "/"; null != (n = Oe.exec(e));) { var c = n[0], u = n[1], l = n.index; if (a += e.slice(o, l), o = l + c.length, u) a += u[1]; else { var f = e[o], p = n[2], d = n[3], h = n[4], v = n[5], m = n[6], g = n[7]; a && (r.push(a), a = ""); var y = null != p && null != f && f !== p, b = "+" === m || "*" === m, _ = "?" === m || "*" === m, k = n[2] || s, w = h || v; r.push({ name: d || i++, prefix: p || "", delimiter: k, optional: _, repeat: b, partial: y, asterisk: !!g, pattern: w ? Ie(w) : g ? ".*" : "[^" + je(k) + "]+?" }) } } return o < e.length && (a += e.substr(o)), a && r.push(a), r } function Te(e) { return encodeURI(e).replace(/[\/?#]/g, (function (e) { return "%" + e.charCodeAt(0).toString(16).toUpperCase() })) } function Le(e) { for (var t = new Array(e.length), n = 0; n < e.length; n++)"object" === typeof e[n] && (t[n] = new RegExp("^(?:" + e[n].pattern + ")$")); return function (n, r) { for (var i = "", o = n || {}, a = (r || {}).pretty ? Te : encodeURIComponent, s = 0; s < e.length; s++) { var c = e[s]; if ("string" !== typeof c) { var u, l = o[c.name]; if (null == l) { if (c.optional) { c.partial && (i += c.prefix); continue } throw new TypeError('Expected "' + c.name + '" to be defined') } if (we(l)) { if (!c.repeat) throw new TypeError('Expected "' + c.name + '" to not repeat, but received `' + JSON.stringify(l) + "`"); if (0 === l.length) { if (c.optional) continue; throw new TypeError('Expected "' + c.name + '" to not be empty') } for (var f = 0; f < l.length; f++) { if (u = a(l[f]), !t[s].test(u)) throw new TypeError('Expected all "' + c.name + '" to match "' + c.pattern + '", but received `' + JSON.stringify(u) + "`"); i += (0 === f ? c.prefix : c.delimiter) + u } } else { if (u = c.asterisk ? encodeURI(l).replace(/[?#]/g, (function (e) { return "%" + e.charCodeAt(0).toString(16).toUpperCase() })) : a(l), !t[s].test(u)) throw new TypeError('Expected "' + c.name + '" to match "' + c.pattern + '", but received "' + u + '"'); i += c.prefix + u } } else i += c } return i } } function je(e) { return e.replace(/([.+*?=^!:${}()[\]|\/\\])/g, "\\$1") } function Ie(e) { return e.replace(/([=!:$\/()])/g, "\\$1") } function Fe(e, t) { return e.keys = t, e } function Me(e) { return e.sensitive ? "" : "i" } function Re(e, t, n) { we(t) || (n = t || n, t = []); for (var r = (n = n || {}).strict, i = !1 !== n.end, o = "", a = 0; a < e.length; a++) { var s = e[a]; if ("string" === typeof s) o += je(s); else { var c = je(s.prefix), u = "(?:" + s.pattern + ")"; t.push(s), s.repeat && (u += "(?:" + c + u + ")*"), o += u = s.optional ? s.partial ? c + "(" + u + ")?" : "(?:" + c + "(" + u + "))?" : c + "(" + u + ")" } } var l = je(n.delimiter || "/"), f = o.slice(-l.length) === l; return r || (o = (f ? o.slice(0, -l.length) : o) + "(?:" + l + "(?=$))?"), o += i ? "$" : r && f ? "" : "(?=" + l + "|$)", Fe(new RegExp("^" + o, Me(n)), t) } function Pe(e, t, n) { return we(t) || (n = t || n, t = []), n = n || {}, e instanceof RegExp ? function (e, t) { var n = e.source.match(/\((?!\?)/g); if (n) for (var r = 0; r < n.length; r++)t.push({ name: r, prefix: null, delimiter: null, optional: !1, repeat: !1, partial: !1, asterisk: !1, pattern: null }); return Fe(e, t) }(e, t) : we(e) ? function (e, t, n) { for (var r = [], i = 0; i < e.length; i++)r.push(Pe(e[i], t, n).source); return Fe(new RegExp("(?:" + r.join("|") + ")", Me(n)), t) }(e, t, n) : function (e, t, n) { return Re(Ee(e, n), t, n) }(e, t, n) } xe.parse = Ce, xe.compile = Ae, xe.tokensToFunction = $e, xe.tokensToRegExp = Se; var Ne = Object.create(null); function ze(e, t, n) { t = t || {}; try { var r = Ne[e] || (Ne[e] = xe.compile(e)); return t.pathMatch && (t[0] = t.pathMatch), r(t, { pretty: !0 }) } catch (i) { return "" } finally { delete t[0] } } function De(e, t, n, r) { var i = "string" === typeof e ? { path: e } : e; if (i._normalized) return i; if (i.name) return re({}, e); if (!i.path && i.params && t) { (i = re({}, i))._normalized = !0; var o = re(re({}, t.params), i.params); if (t.name) i.name = t.name, i.params = o; else if (t.matched.length) { var a = t.matched[t.matched.length - 1].path; i.path = ze(a, o, t.path) } else 0; return i } var s = function (e) { var t = "", n = "", r = e.indexOf("#"); r >= 0 && (t = e.slice(r), e = e.slice(0, r)); var i = e.indexOf("?"); return i >= 0 && (n = e.slice(i + 1), e = e.slice(0, i)), { path: e, query: n, hash: t } }(i.path || ""), c = t && t.path || "/", u = s.path ? _e(s.path, c, n || i.append) : c, l = function (e, t, n) { void 0 === t && (t = {}); var r, i = n || le; try { r = i(e || "") } catch (a) { r = {} } for (var o in t) r[o] = t[o]; return r }(s.query, i.query, r && r.options.parseQuery), f = i.hash || s.hash; return f && "#" !== f.charAt(0) && (f = "#" + f), { _normalized: !0, path: u, query: l, hash: f } } var He, Be = [String, Object], Ge = [String, Array], Ue = function () { }, Ze = { name: "RouterLink", props: { to: { type: Be, required: !0 }, tag: { type: String, default: "a" }, exact: Boolean, append: Boolean, replace: Boolean, activeClass: String, exactActiveClass: String, event: { type: Ge, default: "click" } }, render: function (e) { var t = this, n = this.$router, r = this.$route, i = n.resolve(this.to, r, this.append), o = i.location, a = i.route, s = i.href, c = {}, u = n.options.linkActiveClass, l = n.options.linkExactActiveClass, f = null == u ? "router-link-active" : u, p = null == l ? "router-link-exact-active" : l, d = null == this.activeClass ? f : this.activeClass, h = null == this.exactActiveClass ? p : this.exactActiveClass, v = a.redirectedFrom ? de(null, De(a.redirectedFrom), null, n) : a; c[h] = ye(r, v), c[d] = this.exact ? c[h] : function (e, t) { return 0 === e.path.replace(pe, "/").indexOf(t.path.replace(pe, "/")) && (!t.hash || e.hash === t.hash) && function (e, t) { for (var n in t) if (!(n in e)) return !1; return !0 }(e.query, t.query) }(r, v); var m = function (e) { qe(e) && (t.replace ? n.replace(o, Ue) : n.push(o, Ue)) }, g = { click: qe }; Array.isArray(this.event) ? this.event.forEach((function (e) { g[e] = m })) : g[this.event] = m; var y = { class: c }, b = !this.$scopedSlots.$hasNormal && this.$scopedSlots.default && this.$scopedSlots.default({ href: s, route: a, navigate: m, isActive: c[d], isExactActive: c[h] }); if (b) { if (1 === b.length) return b[0]; if (b.length > 1 || !b.length) return 0 === b.length ? e() : e("span", {}, b) } if ("a" === this.tag) y.on = g, y.attrs = { href: s }; else { var _ = function e(t) { if (t) for (var n, r = 0; r < t.length; r++) { if ("a" === (n = t[r]).tag) return n; if (n.children && (n = e(n.children))) return n } }(this.$slots.default); if (_) { _.isStatic = !1; var k = _.data = re({}, _.data); for (var w in k.on = k.on || {}, k.on) { var x = k.on[w]; w in g && (k.on[w] = Array.isArray(x) ? x : [x]) } for (var C in g) C in k.on ? k.on[C].push(g[C]) : k.on[C] = m; (_.data.attrs = re({}, _.data.attrs)).href = s } else y.on = g } return e(this.tag, y, this.$slots.default) } }; function qe(e) { if (!(e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) && !e.defaultPrevented && (void 0 === e.button || 0 === e.button)) { if (e.currentTarget && e.currentTarget.getAttribute) { var t = e.currentTarget.getAttribute("target"); if (/\b_blank\b/i.test(t)) return } return e.preventDefault && e.preventDefault(), !0 } } var Ve = "undefined" !== typeof window; function Ke(e, t, n, r) { var i = t || [], o = n || Object.create(null), a = r || Object.create(null); e.forEach((function (e) { !function e(t, n, r, i, o, a) { var s = i.path; var c = i.name; 0; var u = i.pathToRegexpOptions || {}; var l = function (e, t, n) { n || (e = e.replace(/\/$/, "")); if ("/" === e[0]) return e; if (null == t) return e; return ke(t.path + "/" + e) }(s, o, u.strict); "boolean" === typeof i.caseSensitive && (u.sensitive = i.caseSensitive); var f = { path: l, regex: Je(l, u), components: i.components || { default: i.component }, instances: {}, name: c, parent: o, matchAs: a, redirect: i.redirect, beforeEnter: i.beforeEnter, meta: i.meta || {}, props: null == i.props ? {} : i.components ? i.props : { default: i.props } }; i.children && i.children.forEach((function (i) { var o = a ? ke(a + "/" + i.path) : void 0; e(t, n, r, i, f, o) })); n[f.path] || (t.push(f.path), n[f.path] = f); if (void 0 !== i.alias) for (var p = Array.isArray(i.alias) ? i.alias : [i.alias], d = 0; d < p.length; ++d) { 0; var h = { path: p[d], children: i.children }; e(t, n, r, h, o, f.path || "/") } c && (r[c] || (r[c] = f)) }(i, o, a, e) })); for (var s = 0, c = i.length; s < c; s++)"*" === i[s] && (i.push(i.splice(s, 1)[0]), c-- , s--); return { pathList: i, pathMap: o, nameMap: a } } function Je(e, t) { return xe(e, [], t) } function We(e, t) { var n = Ke(e), r = n.pathList, i = n.pathMap, o = n.nameMap; function a(e, n, a) { var s = De(e, n, !1, t), u = s.name; if (u) { var l = o[u]; if (!l) return c(null, s); var f = l.regex.keys.filter((function (e) { return !e.optional })).map((function (e) { return e.name })); if ("object" !== typeof s.params && (s.params = {}), n && "object" === typeof n.params) for (var p in n.params) !(p in s.params) && f.indexOf(p) > -1 && (s.params[p] = n.params[p]); return s.path = ze(l.path, s.params), c(l, s, a) } if (s.path) { s.params = {}; for (var d = 0; d < r.length; d++) { var h = r[d], v = i[h]; if (Ye(v.regex, s.path, s.params)) return c(v, s, a) } } return c(null, s) } function s(e, n) { var r = e.redirect, i = "function" === typeof r ? r(de(e, n, null, t)) : r; if ("string" === typeof i && (i = { path: i }), !i || "object" !== typeof i) return c(null, n); var s = i, u = s.name, l = s.path, f = n.query, p = n.hash, d = n.params; if (f = s.hasOwnProperty("query") ? s.query : f, p = s.hasOwnProperty("hash") ? s.hash : p, d = s.hasOwnProperty("params") ? s.params : d, u) { o[u]; return a({ _normalized: !0, name: u, query: f, hash: p, params: d }, void 0, n) } if (l) { var h = function (e, t) { return _e(e, t.parent ? t.parent.path : "/", !0) }(l, e); return a({ _normalized: !0, path: ze(h, d), query: f, hash: p }, void 0, n) } return c(null, n) } function c(e, n, r) { return e && e.redirect ? s(e, r || n) : e && e.matchAs ? function (e, t, n) { var r = a({ _normalized: !0, path: ze(n, t.params) }); if (r) { var i = r.matched, o = i[i.length - 1]; return t.params = r.params, c(o, t) } return c(null, t) }(0, n, e.matchAs) : de(e, n, r, t) } return { match: a, addRoutes: function (e) { Ke(e, r, i, o) } } } function Ye(e, t, n) { var r = t.match(e); if (!r) return !1; if (!n) return !0; for (var i = 1, o = r.length; i < o; ++i) { var a = e.keys[i - 1], s = "string" === typeof r[i] ? decodeURIComponent(r[i]) : r[i]; a && (n[a.name || "pathMatch"] = s) } return !0 } var Xe = Ve && window.performance && window.performance.now ? window.performance : Date; function Qe() { return Xe.now().toFixed(3) } var et = Qe(); function tt() { return et } function nt(e) { return et = e } var rt = Object.create(null); function it() { var e = window.location.protocol + "//" + window.location.host, t = window.location.href.replace(e, ""); window.history.replaceState({ key: tt() }, "", t), window.addEventListener("popstate", (function (e) { at(), e.state && e.state.key && nt(e.state.key) })) } function ot(e, t, n, r) { if (e.app) { var i = e.options.scrollBehavior; i && e.app.$nextTick((function () { var o = function () { var e = tt(); if (e) return rt[e] }(), a = i.call(e, t, n, r ? o : null); a && ("function" === typeof a.then ? a.then((function (e) { ft(e, o) })).catch((function (e) { 0 })) : ft(a, o)) })) } } function at() { var e = tt(); e && (rt[e] = { x: window.pageXOffset, y: window.pageYOffset }) } function st(e) { return ut(e.x) || ut(e.y) } function ct(e) { return { x: ut(e.x) ? e.x : window.pageXOffset, y: ut(e.y) ? e.y : window.pageYOffset } } function ut(e) { return "number" === typeof e } var lt = /^#\d/; function ft(e, t) { var n, r = "object" === typeof e; if (r && "string" === typeof e.selector) { var i = lt.test(e.selector) ? document.getElementById(e.selector.slice(1)) : document.querySelector(e.selector); if (i) { var o = e.offset && "object" === typeof e.offset ? e.offset : {}; t = function (e, t) { var n = document.documentElement.getBoundingClientRect(), r = e.getBoundingClientRect(); return { x: r.left - n.left - t.x, y: r.top - n.top - t.y } }(i, o = { x: ut((n = o).x) ? n.x : 0, y: ut(n.y) ? n.y : 0 }) } else st(e) && (t = ct(e)) } else r && st(e) && (t = ct(e)); t && window.scrollTo(t.x, t.y) } var pt = Ve && function () { var e = window.navigator.userAgent; return (-1 === e.indexOf("Android 2.") && -1 === e.indexOf("Android 4.0") || -1 === e.indexOf("Mobile Safari") || -1 !== e.indexOf("Chrome") || -1 !== e.indexOf("Windows Phone")) && (window.history && "pushState" in window.history) }(); function dt(e, t) { at(); var n = window.history; try { t ? n.replaceState({ key: tt() }, "", e) : n.pushState({ key: nt(Qe()) }, "", e) } catch (r) { window.location[t ? "replace" : "assign"](e) } } function ht(e) { dt(e, !0) } function vt(e, t, n) { var r = function (i) { i >= e.length ? n() : e[i] ? t(e[i], (function () { r(i + 1) })) : r(i + 1) }; r(0) } function mt(e) { return function (t, n, r) { var i = !1, o = 0, a = null; gt(e, (function (e, t, n, s) { if ("function" === typeof e && void 0 === e.cid) { i = !0, o++; var c, u = _t((function (t) { var i; ((i = t).__esModule || bt && "Module" === i[Symbol.toStringTag]) && (t = t.default), e.resolved = "function" === typeof t ? t : He.extend(t), n.components[s] = t, --o <= 0 && r() })), l = _t((function (e) { var t = "Failed to resolve async component " + s + ": " + e; a || (a = te(e) ? e : new Error(t), r(a)) })); try { c = e(u, l) } catch (p) { l(p) } if (c) if ("function" === typeof c.then) c.then(u, l); else { var f = c.component; f && "function" === typeof f.then && f.then(u, l) } } })), i || r() } } function gt(e, t) { return yt(e.map((function (e) { return Object.keys(e.components).map((function (n) { return t(e.components[n], e.instances[n], e, n) })) }))) } function yt(e) { return Array.prototype.concat.apply([], e) } var bt = "function" === typeof Symbol && "symbol" === typeof Symbol.toStringTag; function _t(e) { var t = !1; return function () { for (var n = [], r = arguments.length; r--;)n[r] = arguments[r]; if (!t) return t = !0, e.apply(this, n) } } var kt = function (e) { function t(t) { e.call(this), this.name = this._name = "NavigationDuplicated", this.message = 'Navigating to current location ("' + t.fullPath + '") is not allowed', Object.defineProperty(this, "stack", { value: (new e).stack, writable: !0, configurable: !0 }) } return e && (t.__proto__ = e), t.prototype = Object.create(e && e.prototype), t.prototype.constructor = t, t }(Error); kt._name = "NavigationDuplicated"; var wt = function (e, t) { this.router = e, this.base = function (e) { if (!e) if (Ve) { var t = document.querySelector("base"); e = (e = t && t.getAttribute("href") || "/").replace(/^https?:\/\/[^\/]+/, "") } else e = "/"; "/" !== e.charAt(0) && (e = "/" + e); return e.replace(/\/$/, "") }(t), this.current = ve, this.pending = null, this.ready = !1, this.readyCbs = [], this.readyErrorCbs = [], this.errorCbs = [] }; function xt(e, t, n, r) { var i = gt(e, (function (e, r, i, o) { var a = function (e, t) { "function" !== typeof e && (e = He.extend(e)); return e.options[t] }(e, t); if (a) return Array.isArray(a) ? a.map((function (e) { return n(e, r, i, o) })) : n(a, r, i, o) })); return yt(r ? i.reverse() : i) } function Ct(e, t) { if (t) return function () { return e.apply(t, arguments) } } wt.prototype.listen = function (e) { this.cb = e }, wt.prototype.onReady = function (e, t) { this.ready ? e() : (this.readyCbs.push(e), t && this.readyErrorCbs.push(t)) }, wt.prototype.onError = function (e) { this.errorCbs.push(e) }, wt.prototype.transitionTo = function (e, t, n) { var r = this, i = this.router.match(e, this.current); this.confirmTransition(i, (function () { r.updateRoute(i), t && t(i), r.ensureURL(), r.ready || (r.ready = !0, r.readyCbs.forEach((function (e) { e(i) }))) }), (function (e) { n && n(e), e && !r.ready && (r.ready = !0, r.readyErrorCbs.forEach((function (t) { t(e) }))) })) }, wt.prototype.confirmTransition = function (e, t, n) { var r = this, i = this.current, o = function (e) { !ne(kt, e) && te(e) && (r.errorCbs.length ? r.errorCbs.forEach((function (t) { t(e) })) : console.error(e)), n && n(e) }; if (ye(e, i) && e.matched.length === i.matched.length) return this.ensureURL(), o(new kt(e)); var a = function (e, t) { var n, r = Math.max(e.length, t.length); for (n = 0; n < r && e[n] === t[n]; n++); return { updated: t.slice(0, n), activated: t.slice(n), deactivated: e.slice(n) } }(this.current.matched, e.matched), s = a.updated, c = a.deactivated, u = a.activated, l = [].concat(function (e) { return xt(e, "beforeRouteLeave", Ct, !0) }(c), this.router.beforeHooks, function (e) { return xt(e, "beforeRouteUpdate", Ct) }(s), u.map((function (e) { return e.beforeEnter })), mt(u)); this.pending = e; var f = function (t, n) { if (r.pending !== e) return o(); try { t(e, i, (function (e) { !1 === e || te(e) ? (r.ensureURL(!0), o(e)) : "string" === typeof e || "object" === typeof e && ("string" === typeof e.path || "string" === typeof e.name) ? (o(), "object" === typeof e && e.replace ? r.replace(e) : r.push(e)) : n(e) })) } catch (a) { o(a) } }; vt(l, f, (function () { var n = []; vt(function (e, t, n) { return xt(e, "beforeRouteEnter", (function (e, r, i, o) { return function (e, t, n, r, i) { return function (o, a, s) { return e(o, a, (function (e) { "function" === typeof e && r.push((function () { !function e(t, n, r, i) { n[r] && !n[r]._isBeingDestroyed ? t(n[r]) : i() && setTimeout((function () { e(t, n, r, i) }), 16) }(e, t.instances, n, i) })), s(e) })) } }(e, i, o, t, n) })) }(u, n, (function () { return r.current === e })).concat(r.router.resolveHooks), f, (function () { if (r.pending !== e) return o(); r.pending = null, t(e), r.router.app && r.router.app.$nextTick((function () { n.forEach((function (e) { e() })) })) })) })) }, wt.prototype.updateRoute = function (e) { var t = this.current; this.current = e, this.cb && this.cb(e), this.router.afterHooks.forEach((function (n) { n && n(e, t) })) }; var At = function (e) { function t(t, n) { var r = this; e.call(this, t, n); var i = t.options.scrollBehavior, o = pt && i; o && it(); var a = $t(this.base); window.addEventListener("popstate", (function (e) { var n = r.current, i = $t(r.base); r.current === ve && i === a || r.transitionTo(i, (function (e) { o && ot(t, e, n, !0) })) })) } return e && (t.__proto__ = e), t.prototype = Object.create(e && e.prototype), t.prototype.constructor = t, t.prototype.go = function (e) { window.history.go(e) }, t.prototype.push = function (e, t, n) { var r = this, i = this.current; this.transitionTo(e, (function (e) { dt(ke(r.base + e.fullPath)), ot(r.router, e, i, !1), t && t(e) }), n) }, t.prototype.replace = function (e, t, n) { var r = this, i = this.current; this.transitionTo(e, (function (e) { ht(ke(r.base + e.fullPath)), ot(r.router, e, i, !1), t && t(e) }), n) }, t.prototype.ensureURL = function (e) { if ($t(this.base) !== this.current.fullPath) { var t = ke(this.base + this.current.fullPath); e ? dt(t) : ht(t) } }, t.prototype.getCurrentLocation = function () { return $t(this.base) }, t }(wt); function $t(e) { var t = decodeURI(window.location.pathname); return e && 0 === t.indexOf(e) && (t = t.slice(e.length)), (t || "/") + window.location.search + window.location.hash } var St = function (e) { function t(t, n, r) { e.call(this, t, n), r && function (e) { var t = $t(e); if (!/^\/#/.test(t)) return window.location.replace(ke(e + "/#" + t)), !0 }(this.base) || Ot() } return e && (t.__proto__ = e), t.prototype = Object.create(e && e.prototype), t.prototype.constructor = t, t.prototype.setupListeners = function () { var e = this, t = this.router.options.scrollBehavior, n = pt && t; n && it(), window.addEventListener(pt ? "popstate" : "hashchange", (function () { var t = e.current; Ot() && e.transitionTo(Et(), (function (r) { n && ot(e.router, r, t, !0), pt || jt(r.fullPath) })) })) }, t.prototype.push = function (e, t, n) { var r = this, i = this.current; this.transitionTo(e, (function (e) { Lt(e.fullPath), ot(r.router, e, i, !1), t && t(e) }), n) }, t.prototype.replace = function (e, t, n) { var r = this, i = this.current; this.transitionTo(e, (function (e) { jt(e.fullPath), ot(r.router, e, i, !1), t && t(e) }), n) }, t.prototype.go = function (e) { window.history.go(e) }, t.prototype.ensureURL = function (e) { var t = this.current.fullPath; Et() !== t && (e ? Lt(t) : jt(t)) }, t.prototype.getCurrentLocation = function () { return Et() }, t }(wt); function Ot() { var e = Et(); return "/" === e.charAt(0) || (jt("/" + e), !1) } function Et() { var e = window.location.href, t = e.indexOf("#"); if (t < 0) return ""; var n = (e = e.slice(t + 1)).indexOf("?"); if (n < 0) { var r = e.indexOf("#"); e = r > -1 ? decodeURI(e.slice(0, r)) + e.slice(r) : decodeURI(e) } else n > -1 && (e = decodeURI(e.slice(0, n)) + e.slice(n)); return e } function Tt(e) { var t = window.location.href, n = t.indexOf("#"); return (n >= 0 ? t.slice(0, n) : t) + "#" + e } function Lt(e) { pt ? dt(Tt(e)) : window.location.hash = e } function jt(e) { pt ? ht(Tt(e)) : window.location.replace(Tt(e)) } var It = function (e) { function t(t, n) { e.call(this, t, n), this.stack = [], this.index = -1 } return e && (t.__proto__ = e), t.prototype = Object.create(e && e.prototype), t.prototype.constructor = t, t.prototype.push = function (e, t, n) { var r = this; this.transitionTo(e, (function (e) { r.stack = r.stack.slice(0, r.index + 1).concat(e), r.index++ , t && t(e) }), n) }, t.prototype.replace = function (e, t, n) { var r = this; this.transitionTo(e, (function (e) { r.stack = r.stack.slice(0, r.index).concat(e), t && t(e) }), n) }, t.prototype.go = function (e) { var t = this, n = this.index + e; if (!(n < 0 || n >= this.stack.length)) { var r = this.stack[n]; this.confirmTransition(r, (function () { t.index = n, t.updateRoute(r) }), (function (e) { ne(kt, e) && (t.index = n) })) } }, t.prototype.getCurrentLocation = function () { var e = this.stack[this.stack.length - 1]; return e ? e.fullPath : "/" }, t.prototype.ensureURL = function () { }, t }(wt), Ft = function (e) { void 0 === e && (e = {}), this.app = null, this.apps = [], this.options = e, this.beforeHooks = [], this.resolveHooks = [], this.afterHooks = [], this.matcher = We(e.routes || [], this); var t = e.mode || "hash"; switch (this.fallback = "history" === t && !pt && !1 !== e.fallback, this.fallback && (t = "hash"), Ve || (t = "abstract"), this.mode = t, t) { case "history": this.history = new At(this, e.base); break; case "hash": this.history = new St(this, e.base, this.fallback); break; case "abstract": this.history = new It(this, e.base); break; default: 0 } }, Mt = { currentRoute: { configurable: !0 } }; function Rt(e, t) { return e.push(t), function () { var n = e.indexOf(t); n > -1 && e.splice(n, 1) } } Ft.prototype.match = function (e, t, n) { return this.matcher.match(e, t, n) }, Mt.currentRoute.get = function () { return this.history && this.history.current }, Ft.prototype.init = function (e) { var t = this; if (this.apps.push(e), e.$once("hook:destroyed", (function () { var n = t.apps.indexOf(e); n > -1 && t.apps.splice(n, 1), t.app === e && (t.app = t.apps[0] || null) })), !this.app) { this.app = e; var n = this.history; if (n instanceof At) n.transitionTo(n.getCurrentLocation()); else if (n instanceof St) { var r = function () { n.setupListeners() }; n.transitionTo(n.getCurrentLocation(), r, r) } n.listen((function (e) { t.apps.forEach((function (t) { t._route = e })) })) } }, Ft.prototype.beforeEach = function (e) { return Rt(this.beforeHooks, e) }, Ft.prototype.beforeResolve = function (e) { return Rt(this.resolveHooks, e) }, Ft.prototype.afterEach = function (e) { return Rt(this.afterHooks, e) }, Ft.prototype.onReady = function (e, t) { this.history.onReady(e, t) }, Ft.prototype.onError = function (e) { this.history.onError(e) }, Ft.prototype.push = function (e, t, n) { var r = this; if (!t && !n && "undefined" !== typeof Promise) return new Promise((function (t, n) { r.history.push(e, t, n) })); this.history.push(e, t, n) }, Ft.prototype.replace = function (e, t, n) { var r = this; if (!t && !n && "undefined" !== typeof Promise) return new Promise((function (t, n) { r.history.replace(e, t, n) })); this.history.replace(e, t, n) }, Ft.prototype.go = function (e) { this.history.go(e) }, Ft.prototype.back = function () { this.go(-1) }, Ft.prototype.forward = function () { this.go(1) }, Ft.prototype.getMatchedComponents = function (e) { var t = e ? e.matched ? e : this.resolve(e).route : this.currentRoute; return t ? [].concat.apply([], t.matched.map((function (e) { return Object.keys(e.components).map((function (t) { return e.components[t] })) }))) : [] }, Ft.prototype.resolve = function (e, t, n) { var r = De(e, t = t || this.history.current, n, this), i = this.match(r, t), o = i.redirectedFrom || i.fullPath; return { location: r, route: i, href: function (e, t, n) { var r = "hash" === n ? "#" + t : t; return e ? ke(e + "/" + r) : r }(this.history.base, o, this.mode), normalizedTo: r, resolved: i } }, Ft.prototype.addRoutes = function (e) { this.matcher.addRoutes(e), this.history.current !== ve && this.history.transitionTo(this.history.getCurrentLocation()) }, Object.defineProperties(Ft.prototype, Mt), Ft.install = function e(t) { if (!e.installed || He !== t) { e.installed = !0, He = t; var n = function (e) { return void 0 !== e }, r = function (e, t) { var r = e.$options._parentVnode; n(r) && n(r = r.data) && n(r = r.registerRouteInstance) && r(e, t) }; t.mixin({ beforeCreate: function () { n(this.$options.router) ? (this._routerRoot = this, this._router = this.$options.router, this._router.init(this), t.util.defineReactive(this, "_route", this._router.history.current)) : this._routerRoot = this.$parent && this.$parent._routerRoot || this, r(this, this) }, destroyed: function () { r(this) } }), Object.defineProperty(t.prototype, "$router", { get: function () { return this._routerRoot._router } }), Object.defineProperty(t.prototype, "$route", { get: function () { return this._routerRoot._route } }), t.component("RouterView", ie), t.component("RouterLink", Ze); var i = t.config.optionMergeStrategies; i.beforeRouteEnter = i.beforeRouteLeave = i.beforeRouteUpdate = i.created } }, Ft.version = "3.1.3", Ve && window.Vue && window.Vue.use(Ft); var Pt = Ft, Nt = "undefined" !== typeof window, zt = Nt && navigator.connection, Dt = Nt && (!zt || -1 === (zt.effectiveType || "").indexOf("2g") && !zt.saveData), Ht = Nt && window.IntersectionObserver, Bt = {}; function Gt(e) { return new Promise((function (t, n) { var r = new XMLHttpRequest; r.open("GET", e, r.withCredentials = !0), r.addEventListener("load", (function () { 200 === r.status ? t() : n() })), r.send() })) } function Ut(e) { return self.fetch ? fetch(e, { credentials: "include" }) : Gt(e) } var Zt = function (e) { if (Nt) { var t = document.createElement("link"); return t.relList && t.relList.supports && t.relList.supports(e) } }("prefetch") ? function (e) { return new Promise((function (t, n) { var r = document.createElement("link"); r.rel = "prefetch", r.href = e, r.addEventListener("load", t), r.addEventListener("error", n), document.head.appendChild(r) })) } : Gt; function qt(e, t) { if (Dt && !Bt[e]) return (t ? Ut : Zt)(e).then((function () { Bt[e] = !0 })) } var Vt = function (e, t) { void 0 === t && (t = {}); var n = t.componentName; void 0 === n && (n = "RouterLink"); var r = t.prefetch; void 0 === r && (r = !0); var i = Ht && new window.IntersectionObserver((function (e) { e.forEach((function (e) { e.isIntersecting && e.target._linkPrefetch() })) })), o = Nt && window.requestIdleCallback || function (e) { var t = Date.now(); return setTimeout((function () { e({ didTimeout: !1, timeRemaining: function () { return Math.max(0, 50 - (Date.now() - t)) } }) }), 1) }, a = { name: n, extends: e.component("RouterLink") || e.component("router-link"), props: { prefetch: { type: Boolean, default: r }, prefetchFiles: { type: Array }, timeout: { type: Number, default: 2e3 } }, mounted: function () { this.prefetch && i && Dt && o(this.observe, { timeout: this.timeout }) }, beforeDestory: function () { this.unobserve() }, methods: { observe: function () { i.observe(this.$el), this.$el._linkPrefetch = this.linkPrefetch, this._linkObserved = !0 }, unobserve: function () { this._linkObserved && i.unobserve(this.$el) }, getComponents: function () { return this.$router.getMatchedComponents(this.to).filter((function (e) { return void 0 === e.cid && "function" === typeof e })) }, linkPrefetch: function () { var e = this.$router.resolve(this.to).route; if (!e.meta.__prefetched) { if (e.meta.__prefetched = !0, !1 !== e.meta.prefetch) for (var t = 0, n = this.getComponents(); t < n.length; t += 1) { var r = n[t]; this.$emit("prefetch", this.to), r() } "function" === typeof e.meta.prefetch && e.meta.prefetch(e); var i = (this.prefetchFiles || []).concat(e.meta.prefetchFiles || []); if (i.length > 0) for (var o = 0, a = i; o < a.length; o += 1) { qt(a[o]) } this.unobserve() } } } }; e.component(a.name, a) }, Kt = function (e, t, n, r) { return (e /= r / 2) < 1 ? n / 2 * e * e + t : -n / 2 * (--e * (e - 2) - 1) + t }, Jt = "function" === typeof Symbol && "symbol" === typeof Symbol.iterator ? function (e) { return typeof e } : function (e) { return e && "function" === typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e }, Wt = function () { var e = void 0, t = void 0, n = void 0, r = void 0, i = void 0, o = void 0, a = void 0, s = void 0, c = void 0, u = void 0, l = void 0, f = void 0; function p(e) { return e.getBoundingClientRect().top + t } function d(n) { c || (c = n), l = i(u = n - c, t, a, s), window.scrollTo(0, l), u < s ? window.requestAnimationFrame(d) : function () { window.scrollTo(0, t + a), e && o && (e.setAttribute("tabindex", "-1"), e.focus()); "function" === typeof f && f(); c = !1 }() } return function (c) { var u = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}; switch (s = u.duration || 1e3, r = u.offset || 0, f = u.callback, i = u.easing || Kt, o = u.a11y || !1, t = window.scrollY || window.pageYOffset, "undefined" === typeof c ? "undefined" : Jt(c)) { case "number": e = void 0, o = !1, n = t + c; break; case "object": n = p(e = c); break; case "string": e = document.querySelector(c), n = p(e) }switch (a = n - t + r, Jt(u.duration)) { case "number": s = u.duration; break; case "function": s = u.duration(a) }window.requestAnimationFrame(d) } }(), Yt = n(30), Xt = n.n(Yt), Qt = function () { return Math.random().toString(36).substring(2) }, en = { name: "ContentLoader", functional: !0, props: { width: { type: Number, default: 400 }, height: { type: Number, default: 130 }, speed: { type: Number, default: 2 }, preserveAspectRatio: { type: String, default: "xMidYMid meet" }, baseUrl: { type: String, default: "" }, primaryColor: { type: String, default: "#f9f9f9" }, secondaryColor: { type: String, default: "#ecebeb" }, primaryOpacity: { type: Number, default: 1 }, secondaryOpacity: { type: Number, default: 1 }, uniqueKey: { type: String }, animate: { type: Boolean, default: !0 } }, render: function (e, t) { var n = t.props, r = t.data, i = t.children, o = n.uniqueKey ? n.uniqueKey + "-idClip" : Qt(), a = n.uniqueKey ? n.uniqueKey + "-idGradient" : Qt(); return e("svg", Xt()([r, { attrs: { viewBox: "0 0 " + n.width + " " + n.height, version: "1.1", preserveAspectRatio: n.preserveAspectRatio } }]), [e("rect", { style: { fill: "url(" + n.baseUrl + "#" + a + ")" }, attrs: { "clip-path": "url(" + n.baseUrl + "#" + o + ")", x: "0", y: "0", width: n.width, height: n.height } }), e("defs", [e("clipPath", { attrs: { id: o } }, [i || e("rect", { attrs: { x: "0", y: "0", rx: "5", ry: "5", width: n.width, height: n.height } })]), e("linearGradient", { attrs: { id: a } }, [e("stop", { attrs: { offset: "0%", "stop-color": n.primaryColor, "stop-opacity": n.primaryOpacity } }, [n.animate ? e("animate", { attrs: { attributeName: "offset", values: "-2; 1", dur: n.speed + "s", repeatCount: "indefinite" } }) : null]), e("stop", { attrs: { offset: "50%", "stop-color": n.secondaryColor, "stop-opacity": n.secondaryOpacity } }, [n.animate ? e("animate", { attrs: { attributeName: "offset", values: "-1.5; 1.5", dur: n.speed + "s", repeatCount: "indefinite" } }) : null]), e("stop", { attrs: { offset: "100%", "stop-color": n.primaryColor, "stop-opacity": n.primaryOpacity } }, [n.animate ? e("animate", { attrs: { attributeName: "offset", values: "-1; 2", dur: n.speed + "s", repeatCount: "indefinite" } }) : null])])])]) } }, tn = { functional: !0, props: ["openInNewTab", "externalLinkIcon"], render: function (e, t) { var n = t.data, r = t.children, i = t.props, o = i.openInNewTab, a = i.externalLinkIcon, s = Object.assign({}, n.attrs), c = s.to; return H(c) ? (delete s.to, delete s.prefetchFiles, e("a", Object.assign({}, n, { class: [n.class, "is-external-link"], attrs: Object.assign({}, s, { href: c, target: !1 === o ? "_self" : "_blank" }) }), [].concat(r, [!1 === o || !1 === a ? null : e("external-link-icon", { class: "external-link-icon" })]))) : e("router-link", n, r) } }, nn = (n(37), h(tn, void 0, void 0, !1, null, null, null).exports), rn = { components: { UniLink: nn }, props: { nav: { type: Array, required: !0 } }, methods: { isExternalLink: H } }, on = (n(38), h(rn, (function () { var e = this, t = e.$createElement, n = e._self._c || t; return n("div", { staticClass: "header-nav" }, e._l(e.nav, (function (t, r) { return n("div", { key: r, staticClass: "header-nav-item" }, [t.children ? n("div", { staticClass: "dropdown-wrapper" }, [n("span", { staticClass: "dropdown-trigger" }, [e._v("\n        " + e._s(t.title) + "\n        "), n("span", { staticClass: "arrow" })]), e._v(" "), t.children ? n("ul", { staticClass: "dropdown-list" }, e._l(t.children, (function (t, r) { return n("li", { key: r, staticClass: "dropdown-item" }, [n("uni-link", { attrs: { to: t.link, openInNewTab: t.openInNewTab, externalLinkIcon: !1 } }, [e._v(e._s(t.title))])], 1) })), 0) : e._e()]) : e._e(), e._v(" "), t.children ? e._e() : n("uni-link", { attrs: { to: t.link, openInNewTab: t.openInNewTab, externalLinkIcon: !1 } }, [e._v(e._s(t.title))])], 1) })), 0) }), [], !1, null, "93d08526", null).exports), an = { props: { link: { type: Object, required: !0 } } }, sn = (n(39), { components: { UniLink: nn, PageToc: h(an, (function () { var e = this, t = e.$createElement, n = e._self._c || t; return !e.$store.state.fetchingFile && !1 !== e.link.toc && e.link.link === e.$route.path && e.$store.state.page.headings && e.$store.state.page.headings.length > 0 ? n("div", { staticClass: "PageToc" }, e._l(e.$store.state.page.headings, (function (t) { return n("router-link", { key: t.slug, staticClass: "PageTocHeading", attrs: { to: { hash: t.slug }, "data-level": t.level }, domProps: { innerHTML: e._s(t.text) } }) })), 1) : e._e() }), [], !1, null, "b4f38364", null).exports }, props: { item: { type: Object, required: !0, default: function () { return {} } }, open: { type: Boolean, required: !1, default: function () { return !0 } } }, computed: { children: function () { return this.item.children || this.item.links } }, methods: { isExternalLink: H, getPrefetchFiles: function (e) { var t = this.$store.getters.config, n = t.sourcePath, r = t.routes; if (r && r[e]) { var i = r[e].file; return i ? [i] : [] } var o = G(e), a = B(n, o); return a ? [a] : [] }, getLinkTarget: function (e) { return H(e) && !1 !== e.openInNewTab ? "_blank" : "_self" } } }), cn = (n(40), { components: { HeaderNav: on, SidebarItem: h(sn, (function () { var e = this, t = e.$createElement, n = e._self._c || t; return n("div", { class: ["SidebarItem", e.item.title && "hasTitle"] }, [e.item.title && e.children ? n("div", { staticClass: "ItemTitle", class: { collapsable: !1 !== e.item.collapsable }, on: { click: function (t) { return e.$emit("toggle") } } }, [!1 !== e.item.collapsable ? n("span", { staticClass: "arrow", class: { open: e.open } }, [n("svg", { attrs: { width: "6", height: "10", viewBox: "0 0 6 10", fill: "none", xmlns: "http://www.w3.org/2000/svg" } }, [n("path", { attrs: { d: "M1.4 8.56L4.67 5M1.4 1.23L4.66 4.7", stroke: "currentColor", "stroke-linecap": "square" } })])]) : e._e(), e._v(" "), n("span", [e._v(e._s(e.item.title))])]) : e._e(), e._v(" "), e.item.title && e.item.link ? n("uni-link", { staticClass: "ItemLink", class: { active: e.$route.path === e.item.link }, attrs: { to: e.item.link } }, [e._v(e._s(e.item.title))]) : e._e(), e._v(" "), e.item.title && e.item.link ? n("div", { staticClass: "ItemLinkToc" }, [n("PageToc", { attrs: { link: e.item } })], 1) : e._e(), e._v(" "), e.children && (e.open || !1 === e.item.collapsable) ? n("div", { staticClass: "ItemChildren" }, e._l(e.children, (function (t, r) { return n("div", { key: r, staticClass: "ItemChild" }, [n("uni-link", { staticClass: "ItemChildLink", class: { active: e.$route.path === t.link }, attrs: { to: t.link, openInNewTab: t.openInNewTab, prefetchFiles: e.getPrefetchFiles(t.link) } }, [e._v(e._s(t.title))]), e._v(" "), n("PageToc", { attrs: { link: t } })], 1) })), 0) : e._e()], 1) }), [], !1, null, "794d5154", null).exports }, data: function () { return { closedItems: [] } }, watch: { "$route.path": { handler: function () { var e = this.getCurrentIndex(this.$route.path, this.$store.getters.sidebar); this.openItem(e) }, immediate: !0 } }, methods: { openItem: function (e) { this.closedItems.indexOf(e) > -1 && (this.closedItems = this.closedItems.filter((function (t) { return t !== e }))) }, toggleItem: function (e) { -1 === this.closedItems.indexOf(e) ? this.closedItems.push(e) : this.closedItems = this.closedItems.filter((function (t) { return t !== e })) }, getCurrentIndex: function (e, t) { for (var n = 0; n < t.length; n++)if (this.getChildren(t[n]).some((function (t) { return t.link === e }))) return n; return 0 }, getChildren: function (e) { return e.children || e.links || [] } } }), un = (n(41), h(cn, (function () { var e = this, t = e.$createElement, n = e._self._c || t; return n("div", { staticClass: "Sidebar", class: { isShown: e.$store.state.showSidebar } }, [n("InjectedComponents", { attrs: { position: "sidebar:start" } }), e._v(" "), n("InjectedComponents", { attrs: { position: "mobile-sidebar:start" } }), e._v(" "), e.$store.getters.config.nav ? n("HeaderNav", { staticClass: "mobile-header-nav", attrs: { nav: e.$store.getters.config.nav } }) : e._e(), e._v(" "), n("div", { staticClass: "SidebarItems" }, e._l(e.$store.getters.sidebar, (function (t, r) { return n("sidebar-item", { key: r, attrs: { item: t, open: -1 === e.closedItems.indexOf(r) }, on: { toggle: function (t) { return e.toggleItem(r) } } }) })), 1), e._v(" "), n("InjectedComponents", { attrs: { position: "sidebar:end" } }), e._v(" "), n("InjectedComponents", { attrs: { position: "sidebar:post-end" } })], 1) }), [], !1, null, "578c0cb2", null).exports), ln = (n(42), h({}, (function () { var e = this, t = e.$createElement, n = e._self._c || t; return e.$store.state.showSidebar ? n("div", { staticClass: "SidebarMask", on: { click: function (t) { return e.$store.commit("TOGGLE_SIDEBAR", !1) } } }) : e._e() }), [], !1, null, "69b97706", null).exports), fn = { methods: { toggleSidebar: function () { this.$store.commit("TOGGLE_SIDEBAR") } }, watch: { "$store.state.showSidebar": function (e) { document.body.style.overflow = e ? "hidden" : "initial" } } }, pn = (n(43), { components: { HeaderNav: on, SidebarToggle: h(fn, (function () { var e = this.$createElement, t = this._self._c || e; return t("span", { staticClass: "sidebar-toggle", on: { click: this.toggleSidebar } }, [t("svg", { attrs: { "aria-hidden": "true", role: "img", viewBox: "0 0 448 512" } }, [t("path", { attrs: { fill: "currentColor", d: "M436 124H12c-6.627 0-12-5.373-12-12V80c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12z" } })])]) }), [], !1, null, "bc0f6e38", null).exports }, computed: { leftNav: function () { var e = this.$store.getters.config.nav; return e && e.filter((function (e) { return "left" === e.position })) }, rightNav: function () { var e = this.$store.getters.config.nav; return e && e.filter((function (e) { return "right" === e.position || !e.position })) }, Logo: function () { var e = this.$store.getters.config, t = e.title, n = e.logo; return "string" === typeof n ? { template: n } : "object" === typeof n ? n : { render: function (e) { return e("span", null, [t]) } } } } }), dn = (n(44), h(pn, (function () { var e = this, t = e.$createElement, n = e._self._c || t; return n("header", { staticClass: "Header" }, [n("div", { staticClass: "Wrap" }, [n("div", { staticClass: "header-inner" }, [n("div", { staticClass: "header-left" }, [n("h1", { staticClass: "site-title" }, [n("SidebarToggle"), e._v(" "), n("router-link", { attrs: { to: "/" } }, [n(e.Logo, { tag: "component" })], 1)], 1), e._v(" "), e.leftNav ? n("HeaderNav", { attrs: { nav: e.leftNav } }) : e._e()], 1), e._v(" "), n("div", { staticClass: "header-right" }, [n("InjectedComponents", { attrs: { position: "header-right:start" } }), e._v(" "), e.rightNav ? n("HeaderNav", { attrs: { nav: e.rightNav } }) : e._e(), e._v(" "), n("InjectedComponents", { attrs: { position: "header-right:end" } })], 1)])])]) }), [], !1, null, "07005330", null).exports), hn = { computed: Object.assign({}, Object(m.b)(["sidebarLinks"]), { currentLink: function () { return this.$route.path }, currentLinkIndex: function () { for (var e = this.sidebarLinks, t = 0; t < e.length; t++) { if (e[t].link === this.currentLink) return t } return !1 }, prevLinkItem: function () { return "number" === typeof this.currentLinkIndex ? this.sidebarLinks[this.currentLinkIndex - 1] : null }, nextLinkItem: function () { return "number" === typeof this.currentLinkIndex ? this.sidebarLinks[this.currentLinkIndex + 1] : null } }) }, vn = (n(45), h(hn, (function () { var e = this, t = e.$createElement, n = e._self._c || t; return e.prevLinkItem || e.nextLinkItem ? n("div", { staticClass: "prev-next-links" }, [e.prevLinkItem ? n("div", { staticClass: "prev-link" }, [n("router-link", { attrs: { to: e.prevLinkItem.link } }, [e._v("\n      \u2190 " + e._s(e.prevLinkItem.title) + "\n    ")])], 1) : e._e(), e._v(" "), e.nextLinkItem ? n("div", { staticClass: "next-link" }, [n("router-link", { attrs: { to: e.nextLinkItem.link } }, [e._v("\n      " + e._s(e.nextLinkItem.title) + " \u2192\n    ")])], 1) : e._e()]) : e._e() }), [], !1, null, "28184c9e", null).exports), mn = { computed: { editLink: function () { return this.$store.state.page.editLink }, editLinkText: function () { return this.$store.getters.config.editLinkText || "Edit this page" } } }, gn = (n(46), h(mn, (function () { var e = this.$createElement, t = this._self._c || e; return this.editLink ? t("div", { staticClass: "EditLink" }, [t("a", { attrs: { target: "_blank", href: "" + this.editLink } }, [t("svg", { staticClass: "icon", attrs: { id: "i-compose", viewBox: "0 0 32 32", width: "32", height: "32", fill: "none", stroke: "currentcolor", "stroke-linecap": "round", "stroke-linejoin": "round", "stroke-width": "2" } }, [t("path", { attrs: { d: "M27 15 L27 30 2 30 2 5 17 5 M30 6 L26 2 9 19 7 25 13 23 Z M22 6 L26 10 Z M9 19 L13 23 Z" } })]), this._v("\n    " + this._s(this.editLinkText) + "\n  ")])]) : this._e() }), [], !1, null, "49573232", null).exports); function yn(e, t, n) { return n ? t ? t(e) : e : (e && e.then || (e = Promise.resolve(e)), t ? e.then(t) : e) } var bn = { name: "PageHome", components: { ContentLoader: en, Sidebar: un, SidebarMask: ln, SiteHeader: dn, PrevNextLinks: vn, EditLink: gn }, serverPrefetch: function () { try { var e = this; return yn(e.fetchFile(e.$route.path), (function () { e.setTitle() })) } catch (t) { return Promise.reject(t) } }, mounted: function () { window.__DOCUTE_INITIAL_STATE__ || this.fetchFile(this.$route.path).then(this.setInitialState) }, beforeRouteUpdate: function (e, t, n) { n(), e.path !== t.path && this.fetchFile(e.path) }, watch: { "$route.hash": function () { var e = this; this.$nextTick((function () { e.jumpToHash() })) }, pageTitle: function () { this.setTitle() } }, computed: { pageTitle: function () { return this.$store.state.page.title }, MarkdownTitle: function () { return { name: "MarkdownTitle", template: "<h1>" + this.pageTitle + "</h1>" } }, PageContent: function () { var e = this.$store.state.env, t = this.$store.getters.config.componentMixins, n = { mixins: [].concat(void 0 === t ? [] : t, e.mixins.map((function (e) { return new Function("Vue", "return " + e.trim())(r.a) }))), name: "PageContent", template: '<div class="page-content">' + this.$store.state.page.content + "</div>" }; return f.process("extendMarkdownComponent", n), n } }, methods: { fetchFile: function (e) { try { var t = this; return yn(t.$store.dispatch("fetchFile", e), (function () { return f.invoke("onContentWillUpdate", t), yn(t.$nextTick(), (function () { f.invoke("onContentUpdated", t), t.jumpToHash() })) })) } catch (n) { return Promise.reject(n) } }, jumpToHash: function () { var e = decodeURI(this.$route.hash); if (e) { var t = document.querySelector(e); if (t) { var n = document.querySelector(".Header"); Wt(t, { a11y: !0, duration: 0, offset: -(n.clientHeight + 30) }) } } }, setInitialState: function () { if (/(Prerender|jsdom|PhantomJS)/i.test(navigator.userAgent)) { var e = document.createElement("script"); e.textContent = "window.__DOCUTE_INITIAL_STATE__ = " + JSON.stringify({ page: this.$store.state.page, env: this.$store.state.env, fetchingFile: !1 }), document.head.appendChild(e) } }, setTitle: function () { var e = this.$route.path, t = this.$store.getters, n = t.config, r = t.homePaths.indexOf(e) > -1 ? n.title : this.pageTitle + " - " + n.title; r = r.replace(/<(?:.|\n)*?>/gm, ""), this.$ssrContext ? this.$ssrContext.title = r : document.title = r } } }, _n = (n(47), n(48), n(49), h(bn, (function () { var e, t = this, n = t.$createElement, r = t._self._c || n; return r("div", { staticClass: "Page", class: (e = {}, e["layout-" + t.$store.getters.config.layout] = !0, e) }, [r("SiteHeader"), t._v(" "), r("div", { staticClass: "Wrap" }, [r("Sidebar"), t._v(" "), r("SidebarMask"), t._v(" "), r("div", { staticClass: "Main" }, [t.$store.state.fetchingFile ? r("div", { staticClass: "Content" }, [r("content-loader", { attrs: { height: 160, width: 400, speed: 2, primaryColor: t.$store.getters.cssVariables.loaderPrimaryColor, secondaryColor: t.$store.getters.cssVariables.loaderSecondaryColor } }, [r("rect", { attrs: { x: "0", y: "5", rx: "4", ry: "4", width: "117", height: "6.4" } }), t._v(" "), r("rect", { attrs: { x: "0", y: "25", rx: "3", ry: "3", width: "85", height: "6.4" } }), t._v(" "), r("rect", { attrs: { x: "0", y: "60", rx: "3", ry: "3", width: "350", height: "6.4" } }), t._v(" "), r("rect", { attrs: { x: "0", y: "80", rx: "3", ry: "3", width: "380", height: "6.4" } }), t._v(" "), r("rect", { attrs: { x: "0", y: "100", rx: "3", ry: "3", width: "201", height: "6.4" } })])], 1) : r("div", { staticClass: "Content" }, [r("InjectedComponents", { attrs: { position: "content:start" } }), t._v(" "), t.pageTitle ? r(t.MarkdownTitle, { tag: "component", staticClass: "page-title" }) : t._e(), t._v(" "), r(t.PageContent, { tag: "component", class: { "has-page-title": t.pageTitle } }), t._v(" "), r("EditLink"), t._v(" "), r("PrevNextLinks"), t._v(" "), r("InjectedComponents", { attrs: { position: "content:end" } })], 1)])], 1)], 1) }), [], !1, null, "2f23fdf8", null).exports); r.a.use(Pt), r.a.use(Vt); var kn = function (e) { return new Pt(Object.assign({ scrollBehavior: function (e, t, n) { return n || { x: 0, y: 0 } } }, e, { routes: [{ path: "*", component: _n }] })) }, wn = function (e) { return { functional: !0, render: function (t, n) { return t(e, n.data, n.children) } } }, xn = { name: "ImageZoom", props: { url: { type: String }, src: { type: String }, alt: { type: String }, border: { type: Boolean, default: !0 }, width: { type: [String, Number] }, title: { type: String } }, computed: { imageURL: function () { return this.src || this.url } }, mounted: function () { (0, n(50).default)(this.$refs.img, {}) } }, Cn = (n(51), h(xn, (function () { var e = this.$createElement, t = this._self._c || e; return t("div", { staticClass: "ImageZoom", class: { "with-border": this.border } }, [t("img", { ref: "img", attrs: { src: this.imageURL, alt: this.alt, width: this.width, title: this.title } })]) }), [], !1, null, "f17c6258", null).exports), An = { name: "Badge", props: { type: { type: String }, color: { type: String } } }, $n = (n(52), h(An, (function (e, t) { return (0, t._c)("span", { class: ["badge", "is-" + t.props.type], style: { backgroundColor: t.props.color } }, [t._t("default")], 2) }), [], !0, null, null, null).exports), Sn = { name: "DocuteSelect", model: { event: "change" }, props: ["value"], methods: { handleChange: function (e) { this.$emit("change", e.target.value) } } }, On = (n(53), h(Sn, (function () { var e = this.$createElement, t = this._self._c || e; return t("div", { staticClass: "DocuteSelect" }, [t("select", { staticClass: "select", domProps: { value: this.value }, on: { change: this.handleChange } }, [this._t("default", null, null, { value: this.value })], 2), this._v(" "), t("div", { staticClass: "arrow" }, [t("svg", { attrs: { width: "13", height: "6", xmlns: "http://www.w3.org/2000/svg", fill: "none", stroke: "#979797", "fill-rule": "evenodd", "stroke-linecap": "square" } }, [t("path", { attrs: { d: "M1.367.375l5.185 5.303M11.685.375L6.5 5.678" } })])])]) }), [], !1, null, "091fbc3d", null).exports), En = { name: "Note", props: { type: { type: String, default: "note" }, label: { type: [String, Boolean], default: !0 } } }, Tn = (n(54), h(En, (function (e, t) { var n = t._c; return n("div", { class: ["note", "is-" + t.props.type] }, [t.props.label ? n("span", { staticClass: "note-label" }, [t._v(t._s(!0 === t.props.label ? t.props.type : t.props.label) + ":")]) : t._e(), t._v(" "), t._t("default")], 2) }), [], !0, null, null, null).exports), Ln = { name: "Gist", props: { id: { type: String, required: !0 } }, data: function () { return { html: "" } }, mounted: function () { var e = this; window["gist_callback_" + this.id] = function (t) { var n = document.createElement("link"); n.href = t.stylesheet, n.rel = "stylesheet", document.head.appendChild(n), e.html = t.div }; var t = document.createElement("script"); t.src = "https://gist.github.com/egoist/" + this.id + ".json?callback=gist_callback_" + this.id, document.head.appendChild(t) } }, jn = (n(55), h(Ln, (function () { var e = this.$createElement, t = this._self._c || e; return this.html ? t("div", { domProps: { innerHTML: this._s(this.html) } }) : t("Loading") }), [], !1, null, null, null).exports), In = { name: "Loading" }, Fn = (n(56), h(In, (function (e, t) { t._c; return t._m(0) }), [function (e, t) { var n = t._c; return n("div", { staticClass: "loading" }, [n("div", { staticClass: "dots" }, [n("span"), t._v(" "), n("span"), t._v(" "), n("span")]), t._v(" "), n("span", { staticClass: "loading-text" }, [t._v("Loading Gist")])]) }], !0, null, "4f620c69", null).exports), Mn = h({ name: "ExternalLinkIcon" }, (function (e, t) { var n = t._c; return n("svg", t._g(t._b({ class: [t.data.staticClass, t.data.class], attrs: { xmlns: "http://www.w3.org/2000/svg", "aria-hidden": "true", viewBox: "0 0 100 100", width: "15", height: "15" } }, "svg", t.data.attrs, !1), t.data.on), [n("path", { attrs: { fill: "currentColor", d: "M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z" } }), t._v(" "), n("polygon", { attrs: { fill: "currentColor", points: "45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9" } })]) }), [], !0, null, null, null).exports, Rn = { computed: { languages: function () { var e = this.$store.getters.languageOverrides; return Object.keys(e).map((function (t) { return { path: t, language: e[t].language } })) } }, methods: { handleChange: function (e) { var t = this.$route.path.replace(new RegExp("^" + this.$store.getters.currentLocalePath), e); this.$router.push(t) } } }, Pn = (n(57), h(Rn, (function () { var e = this, t = e.$createElement, n = e._self._c || t; return n("div", { staticClass: "LanguageSelector" }, [n("DocuteSelect", { attrs: { value: e.$store.getters.currentLocalePath }, on: { change: e.handleChange }, scopedSlots: e._u([{ key: "default", fn: function (t) { var r = t.value; return [n("option", { attrs: { disabled: "" } }, [e._v("Choose Language")]), e._v(" "), e._l(e.languages, (function (t) { return n("option", { key: t.path, domProps: { value: t.path, selected: r === t.path } }, [e._v(e._s(t.language))]) }))] } }]) })], 1) }), [], !1, null, "d5ba05fe", null).exports), Nn = { name: "i18n", extend: function (e) { e.store.getters.languageOverrides && e.registerComponent("sidebar:start", Pn) } }, zn = { name: "hoistTags", extend: function (e) { e.extendMarkedRenderer((function (e) { var t = /^<(script|style)(?=(\s|>|$))/i; e.html = function (e) { return e = e.trim(), t.test(e) ? e.replace(/^<(script|style)/, "<v-$1").replace(/<\/(script|style)>$/, "</v-$1>") : e } })) } }, Dn = { methods: { handleChange: function (e) { H(e) ? location.href = e : this.$router.push(e) } }, computed: { currentVersionLink: function () { for (var e = this.$store.getters.config.versions, t = 0, n = Object.keys(e); t < n.length; t++) { var r = e[n[t]].link; if ("/" !== r && this.$route.path.startsWith(r)) return r } return "/" } } }, Hn = (n(58), h(Dn, (function () { var e = this, t = e.$createElement, n = e._self._c || t; return n("div", { staticClass: "VersionsSelector" }, [n("DocuteSelect", { attrs: { value: e.currentVersionLink }, on: { change: e.handleChange }, scopedSlots: e._u([{ key: "default", fn: function (t) { var r = t.value; return [n("option", { attrs: { disabled: "" } }, [e._v("Choose Version")]), e._v(" "), e._l(e.$store.getters.config.versions, (function (t, i) { return n("option", { key: i, domProps: { value: t.link, selected: r === t.link } }, [e._v("\n      " + e._s(i) + "\n    ")]) }))] } }]) })], 1) }), [], !1, null, "7fba7fb5", null).exports), Bn = { name: "versions", extend: function (e) { e.store.getters.config.versions && e.registerComponent("sidebar:start", Hn) } }, Gn = function (e, t) { return "string" === typeof e ? { template: '<div class="' + t + '">' + e + "</div>" } : e }, Un = { name: "banner-footer", extend: function (e) { var t = e.store.getters.config, n = t.banner, r = t.footer; n && e.registerComponent("content:start", Gn(n, "docute-banner")), r && e.registerComponent("content:end", Gn(r, "docute-footer")) } }, Zn = { data: function () { var e = localStorage.getItem("docute:theme"); return { dark: "dark" in this.$route.query || ("dark" === e || "default" !== e && "dark" === this.$store.getters.config.theme) } }, created: function () { this.$store.commit("SET_THEME", this.dark ? "dark" : "default") }, methods: { handleChange: function () { var e = this.$store.getters.config.theme; this.dark = !this.dark, this.$store.commit("SET_THEME", this.dark ? "dark" : "dark" === e ? "default" : e), localStorage.setItem("docute:theme", this.dark ? "dark" : "default") } } }, qn = (n(59), h(Zn, (function () { var e = this.$createElement, t = this._self._c || e; return t("div", { staticClass: "dark-theme-toggler" }, [t("div", { staticClass: "toggle", class: { checked: this.dark }, on: { click: this.handleChange } }, [this._m(0), this._v(" "), t("div", { staticClass: "toggle-thumb" })]), this._v(" "), t("input", { staticClass: "toggler-screen-reader-only", attrs: { type: "checkbox", "aria-label": "Switch between Dark and Default theme" }, domProps: { checked: this.dark } })]) }), [function () { var e = this.$createElement, t = this._self._c || e; return t("div", { staticClass: "toggle-track" }, [t("div", { staticClass: "toggle-track-check" }, [t("img", { staticStyle: { "pointer-events": "none" }, attrs: { src: "data:image/png;base64,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", width: "16", height: "16", role: "presentation" } })]), this._v(" "), t("div", { staticClass: "toggle-track-x" }, [t("img", { staticStyle: { "pointer-events": "none" }, attrs: { src: "data:image/png;base64,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", width: "16", height: "16", role: "presentation" } })])]) }], !1, null, "d69f2884", null).exports), Vn = { name: "dark-theme-toggler", extend: function (e) { var t = e.store.getters.config.darkThemeToggler; !0 === t ? e.registerComponent("sidebar:post-end", qn) : "sidebar" === t && (e.registerComponent("header-right:start", qn), e.registerComponent("mobile-sidebar:start", qn)) } }; function Kn(e, t, n, r) { var i, o = !1, a = 0; function s() { i && clearTimeout(i) } function c() { var c = this, u = Date.now() - a, l = arguments; function f() { a = Date.now(), n.apply(c, l) } o || (r && !i && f(), s(), void 0 === r && u > e ? f() : !0 !== t && (i = setTimeout(r ? function () { i = void 0 } : f, void 0 === r ? e - u : e))) } return "boolean" !== typeof t && (r = n, n = t, t = void 0), c.cancel = function () { s(), o = !0 }, c } var Jn, Wn, Yn, Xn, Qn = { data: function () { return { result: [], focused: !1 } }, watch: { "$route.fullPath": function () { this.focused = !1 } }, mounted: function () { document.addEventListener("click", this.handleClick) }, beforeDestroy: function () { document.removeEventListener("click", this.handleClick) }, computed: { enabled: function () { return this.$pluginApi.search.enabled } }, methods: { handleClick: function (e) { this.$el.contains(e.target) && !this.$refs.result.contains(e.target) || (this.focused = !1) }, handleSearch: (Jn = 300, Xn = function (e) { var t, n, r, i = this, o = i.$pluginApi.search.handler; return t = o(e.target.value), n = function (e) { i.result = e }, r ? n ? n(t) : t : (t && t.then || (t = Promise.resolve(t)), n ? t.then(n) : t) }, Wn = function () { for (var e = [], t = 0; t < arguments.length; t++)e[t] = arguments[t]; try { return Promise.resolve(Xn.apply(this, e)) } catch (n) { return Promise.reject(n) } }, void 0 === Yn ? Kn(Jn, Wn, !1) : Kn(Jn, Yn, !1 !== Wn)), toggleFocus: function (e) { this.focused = e } } }, er = (n(60), h(Qn, (function () { var e = this, t = e.$createElement, n = e._self._c || t; return e.enabled ? n("div", { staticClass: "search", class: { "is-focused": e.focused } }, [n("div", { staticClass: "search-input-wrapper" }, [n("span", { staticClass: "search-icon" }, [n("svg", { attrs: { width: "13", height: "13", viewBox: "0 0 13 13", xmlns: "http://www.w3.org/2000/svg", fill: "currentColor" } }, [n("path", { attrs: { d: "M8.87 8.16l3.25 3.25-.7.71-3.26-3.25a5 5 0 1 1 .7-.7zM5 9a4 4 0 1 0 0-8 4 4 0 0 0 0 8z" } })])]), e._v(" "), n("input", { staticClass: "search-input", attrs: { type: "text" }, on: { input: e.handleSearch, focus: function (t) { return e.toggleFocus(!0) } } }), e._v(" "), n("div", { directives: [{ name: "show", rawName: "v-show", value: e.result.length > 0, expression: "result.length > 0" }], ref: "result", staticClass: "search-result" }, e._l(e.result, (function (t, r) { return n("router-link", { key: r, staticClass: "search-result-item", attrs: { to: t.link } }, [n("div", { staticClass: "item-header" }, [n("div", { staticClass: "item-title", domProps: { innerHTML: e._s(t.title) } }), e._v(" "), t.label ? n("span", { staticClass: "item-label" }, [e._v(e._s(t.label))]) : e._e()]), e._v(" "), n("div", { staticClass: "item-desc", domProps: { innerHTML: e._s(t.description) } })]) })), 1)])]) : e._e() }), [], !1, null, "026453b4", null).exports), tr = { name: "search", extend: function (e) { e.registerComponent("header-right:start", er) } }; r.a.component(Cn.name, Cn), r.a.component($n.name, $n), r.a.component(On.name, On), r.a.component(Tn.name, Tn), r.a.component(Mn.name, Mn), r.a.component(jn.name, jn), r.a.component(Fn.name, Fn), r.a.use((function (e) { e.component("v-style", wn("style")), e.component("v-script", wn("script")) })), r.a.mixin({ created: function () { var e = this.$options.pluginApi || this.$root.$pluginApi; e && (this.$pluginApi = e) } }); var nr = function () { function e(e) { void 0 === e && (e = {}); var t = kn(e.router); Object(i.sync)(ee, t), this.router = t, this.store = ee, ee.commit("SET_CONFIG", Object.assign({ title: U && document.title }, e)); var n = [Nn, zn, Bn, Un, Vn, tr].concat(ee.state.originalConfig.plugins || []); this.pluginApi = new p({ plugins: n, store: ee, router: t }), this.applyPlugins(), this.app = new r.a({ router: t, store: ee, pluginApi: this.pluginApi, render: function (e) { return e(v) } }), !1 !== e.mount && this.mount() } var t = e.prototype; return t.mount = function () { var e = ee.getters.target; return window.__DOCUTE_INITIAL_STATE__ ? this.app.$mount("#" + e, !0) : this.app.$mount("#" + e), this }, t.applyPlugins = function () { var e = this.pluginApi.plugins, t = Array.isArray(e), n = 0; for (e = t ? e : e[Symbol.iterator](); ;) { var r; if (t) { if (n >= e.length) break; r = e[n++] } else { if ((n = e.next()).done) break; r = n.value } r.extend(this.pluginApi) } }, e }(); nr.version = "4.23.3"; t.default = nr; "undefined" !== typeof window && (window.Vue = r.a, window.__DOCUTE_VERSION__ = "4.23.3") }]).default }));