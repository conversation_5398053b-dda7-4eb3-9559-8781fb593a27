# 备份系统THE VRTS安装

##  解压THEVRTSInstall.tar

通过“tar –vxf”解压THE VRTS安装包

    tar -vxf THEVRTSInstall.tar
    
## 安装THXH VRTS备份服务
-	通过执行./install.sh脚本，安装THXH VRTS备份MASTER Service服务；

-	执行完./install.sh后，选择“1” 配置服务需要的数据库；

-	下一步，填写数据库的账号和密码；

    ![ 安装THXH VRTS备份服务](/dist/img/2-2-2-01.png)

## 	安装THXH VRTS 介质服务

- 通过./install.sh安装HTXH VRTS Media Service服务

- 下一步，选择3，填写master server IP地址；

    ![ 安装THXH VRTS备份服务](/dist/img/2-2-3.png)

## 安装THXH VRTS 客户端

-	执行./install.sh脚本，安装THXH VRTS客户端；

-   下一步，选择4，并填写填写master server IP地址；

    ![ 安装THXH VRTS备份服务](/dist/img/2-2-4.png)

## 	启动安装好的服务

-	通过ls –l /etc/init.d/the*查看服务安装好的启动脚本

    ![ 安装THXH VRTS备份服务](/dist/img/2-2-5-01.png)

- 	进入THE VRTS安装目录，执行./VRTSStart.sh脚本，启动服务；

    ![ 安装THXH VRTS备份服务](/dist/img/2-2-5-02.png)

## 	卸载THE VRTS

- 进入THE VRTS安装目录，执行./uninstall.sh脚本，卸载THE VRTS；

    ![ 安装THXH VRTS备份服务](/dist/img/2-2-6.png)
