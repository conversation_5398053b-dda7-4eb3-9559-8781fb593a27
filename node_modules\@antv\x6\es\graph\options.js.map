{"version": 3, "file": "options.js", "sourceRoot": "", "sources": ["../../src/graph/options.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAE,SAAS,EAAgB,MAAM,iBAAiB,CAAA;AAEzD,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAA;AAMlC,OAAO,EAAE,IAAI,IAAI,YAAY,EAAE,MAAM,UAAU,CAAA;AAuT/C,MAAM,KAAW,OAAO,CAwDvB;AAxDD,WAAiB,OAAO;IACtB,SAAgB,GAAG,CAAC,OAAwB;QAC1C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,KAAgB,OAAO,EAAlB,MAAM,UAAK,OAAO;QAEnE,OAAO;QACP,OAAO;UAHD,8CAAmD,CAAU,CAAA;QAEnE,OAAO;QACP,OAAO;QACP,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;QACnC,IAAI,SAAS,IAAI,IAAI,EAAE;YACrB,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,EAAE;gBACxB,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC,WAAW,CAAA;aACrC;YAED,IAAI,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE;gBACzB,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC,YAAY,CAAA;aACvC;SACF;aAAM;YACL,MAAM,IAAI,KAAK,CACb,0DAA0D,CAC3D,CAAA;SACF;QAED,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,QAAA,QAAQ,EAAE,MAAM,CAAuB,CAAA;QAE1E,OAAO;QACP,OAAO;QACP,MAAM,WAAW,GAA8B,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAA;QAC3E,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,MAAM,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAA;SAC7C;aAAM,IAAI,OAAO,IAAI,KAAK,SAAS,EAAE;YACpC,MAAM,CAAC,IAAI,mCAAQ,WAAW,KAAE,OAAO,EAAE,IAAI,GAAE,CAAA;SAChD;aAAM;YACL,MAAM,CAAC,IAAI,mCAAQ,WAAW,GAAK,IAAI,CAAE,CAAA;SAC1C;QAED,UAAU;QACV,UAAU;QACV,MAAM,OAAO,GAAqC;YAChD,SAAS;YACT,YAAY;YACZ,WAAW;SACZ,CAAA;QAED,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YACtB,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAA;YACxB,IAAI,OAAO,GAAG,KAAK,SAAS,EAAE;gBAC5B,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,GAAG,GAAG,CAAA;aAC1B;iBAAM;gBACL,MAAM,CAAC,GAAG,CAAC,mCACN,MAAM,CAAC,GAAG,CAAC,GACV,GAAW,CAChB,CAAA;aACF;QACH,CAAC,CAAC,CAAA;QAEF,OAAO,MAAM,CAAA;IACf,CAAC;IAtDe,WAAG,MAsDlB,CAAA;AACH,CAAC,EAxDgB,OAAO,KAAP,OAAO,QAwDvB;AAsBD,WAAiB,OAAO;IACT,gBAAQ,GAAwB;QAC3C,CAAC,EAAE,CAAC;QACJ,CAAC,EAAE,CAAC;QACJ,OAAO,EAAE;YACP,GAAG,EAAE,IAAI;YACT,GAAG,EAAE,EAAE;SACR;QACD,IAAI,EAAE;YACJ,IAAI,EAAE,EAAE;YACR,OAAO,EAAE,KAAK;SACf;QACD,UAAU,EAAE,KAAK;QAEjB,OAAO,EAAE;YACP,OAAO,EAAE,KAAK;YACd,UAAU,EAAE,CAAC,eAAe,CAAC;SAC9B;QACD,UAAU,EAAE;YACV,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,GAAG;YACX,mBAAmB,EAAE,IAAI;SAC1B;QAED,YAAY,EAAE;YACZ,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE;oBACJ,OAAO,EAAE,CAAC;iBACX;aACF;YACD,aAAa,EAAE;gBACb,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE;oBACJ,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;iBAC3C;aACF;YACD,eAAe,EAAE;gBACf,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE;oBACJ,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC;iBAC7C;aACF;SACF;QACD,UAAU,EAAE;YACV,IAAI,EAAE,KAAK;YACX,SAAS,EAAE,IAAI;YACf,SAAS,EAAE,IAAI;YACf,SAAS,EAAE,KAAK;YAChB,SAAS,EAAE,IAAI;YACf,UAAU,EAAE,IAAI;YAChB,UAAU,EAAE,IAAI;YAChB,SAAS,EAAE,KAAK;YAEhB,MAAM,EAAE,QAAQ;YAChB,UAAU,EAAE,OAAO;YACnB,eAAe,EAAE,UAAU;YAC3B,MAAM,EAAE,QAAQ;YAChB,SAAS,EAAE,QAAQ;YAEnB,kBAAkB,CAAc,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE;gBAC9D,MAAM,IAAI,GAAG,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAA;gBACxD,OAAO,IAAI,IAAI,IAAI,CAAA;YACrB,CAAC;YAED,UAAU;gBACR,OAAO,IAAI,YAAY,EAAE,CAAA;YAC3B,CAAC;SACF;QACD,WAAW,EAAE;YACX,QAAQ,EAAE,KAAK;SAChB;QACD,SAAS,EAAE;YACT,OAAO,EAAE,KAAK;YACd,UAAU,EAAE,MAAM;YAClB,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI;SACrB;QAED,aAAa,EAAE,CAAC;QAChB,cAAc,EAAE,CAAC;QACjB,eAAe,EAAE,CAAC;QAClB,sBAAsB,EAAE,IAAI;QAC5B,uBAAuB,EAAE,KAAK;QAC9B,yBAAyB,EAAE,IAAI;QAC/B,yBAAyB,EAAE,IAAI;QAC/B,WAAW,EAAE;YACX,gBAAgB,EAAE,KAAK;SACxB;QAED,KAAK,EAAE,IAAI;QACX,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,GAAG,EAAE,CAAC,KAAK;KACnB,CAAA;AACH,CAAC,EA9FgB,OAAO,KAAP,OAAO,QA8FvB"}