{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../src/registry/router/util.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAA;AAC3C,OAAO,EAAS,SAAS,EAAE,MAAM,mBAAmB,CAAA;AAOpD,MAAM,UAAU,YAAY,CAAC,CAAQ;IACnC,OAAO,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;AACtC,CAAC;AAED,MAAM,UAAU,aAAa,CAAC,UAA0B,EAAE;IACxD,MAAM,KAAK,GAAG,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC,CAAA;IAE7D,OAAO;QACL,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI;QACd,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG;QACb,KAAK,EAAE,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK;QAC/B,MAAM,EAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM;KACjC,CAAA;AACH,CAAC;AAED,MAAM,UAAU,aAAa,CAAC,IAAc,EAAE,UAA0B,EAAE;IACxE,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAA;AACtE,CAAC;AAED,MAAM,UAAU,aAAa,CAAC,IAAc,EAAE,UAA0B,EAAE;IACxE,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAA;AACtE,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,IAAc,EAAE,UAA0B,EAAE;IAC1E,IAAI,IAAI,CAAC,YAAY,EAAE;QACrB,OAAO,IAAI,CAAC,YAAY,CAAA;KACzB;IACD,MAAM,IAAI,GAAG,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IACzC,OAAO,IAAI,CAAC,SAAS,EAAE,CAAA;AACzB,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,IAAc,EAAE,UAA0B,EAAE;IAC1E,IAAI,IAAI,CAAC,YAAY,EAAE;QACrB,OAAO,IAAI,CAAC,YAAY,CAAA;KACzB;IAED,MAAM,IAAI,GAAG,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IACzC,OAAO,IAAI,CAAC,SAAS,EAAE,CAAA;AACzB,CAAC"}