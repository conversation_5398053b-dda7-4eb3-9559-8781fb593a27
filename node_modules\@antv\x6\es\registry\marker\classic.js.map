{"version": 3, "file": "classic.js", "sourceRoot": "", "sources": ["../../../src/registry/marker/classic.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAA;AACxC,OAAO,EAAE,SAAS,EAAY,MAAM,iBAAiB,CAAA;AACrD,OAAO,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAA;AAkBlC,MAAM,CAAC,MAAM,KAAK,GAAuC,CAAC,EAOzD,EAAE,EAAE;QAPqD,EACxD,IAAI,EACJ,KAAK,EACL,MAAM,EACN,MAAM,EACN,IAAI,OAEL,EADI,KAAK,cANgD,6CAOzD,CADS;IAER,OAAO,mBAAmB,CACxB,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAC/B,IAAI,KAAK,IAAI,EACb,IAAI,EACJ,SAAS,EACT,KAAK,CACN,CAAA;AACH,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,OAAO,GAAyC,CAAC,EAO7D,EAAE,EAAE;QAPyD,EAC5D,IAAI,EACJ,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,OAEP,EADI,KAAK,cANoD,+CAO7D,CADS;IAER,OAAO,mBAAmB,CACxB,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAC/B,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,CACN,CAAA;AACH,CAAC,CAAA;AAED,SAAS,mBAAmB,CAC1B,OAAe,EACf,IAAa,EACb,IAAa,EACb,SAAiB,CAAC,GAAG,CAAC,EACtB,QAAkB,EAAE;IAEpB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,EAAE,CAAA;IAC/B,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,CAAA;IACnC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,IAAI,CAAA;IACrC,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAA;IACvB,MAAM,UAAU,GAAsB,EAAE,CAAA;IAExC,IAAI,IAAI,EAAE;QACR,IAAI;aACD,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;aAChB,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC;aACrB,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;QACxB,UAAU,CAAC,IAAI,GAAG,MAAM,CAAA;KACzB;SAAM;QACL,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAA;QAC1B,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;QAErB,IAAI,CAAC,IAAI,EAAE;YACT,MAAM,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;YACvC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAA;SACnC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;QAC1B,IAAI,CAAC,KAAK,EAAE,CAAA;KACb;IAED,qDACK,UAAU,GACV,KAAK,KACR,OAAO,EAAE,MAAM,EACf,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;YAC7B,CAAC,EAAE,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;SACxD,CAAC,IACH;AACH,CAAC"}