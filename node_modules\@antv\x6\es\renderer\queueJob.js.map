{"version": 3, "file": "queueJob.js", "sourceRoot": "", "sources": ["../../src/renderer/queueJob.ts"], "names": [], "mappings": "AAAA,MAAM,OAAO,QAAQ;IAArB;QACU,eAAU,GAAG,KAAK,CAAA;QAClB,mBAAc,GAAG,KAAK,CAAA;QACtB,eAAU,GAAG,CAAC,CAAA;QACd,UAAK,GAAU,EAAE,CAAA;QACjB,kBAAa,GAAG,EAAE,CAAA;QAClB,gBAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;IA+HlC,CAAC;IA7HC,QAAQ,CAAC,GAAQ;QACf,IAAI,GAAG,CAAC,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE;YACrC,GAAG,CAAC,EAAE,EAAE,CAAA;SACT;aAAM;YACL,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAA;YAC1C,IAAI,KAAK,IAAI,CAAC,EAAE;gBACd,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,CAAC,CAAA;aACjC;SACF;IACH,CAAC;IAED,UAAU;QACR,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YAC5C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;YAC1B,IAAI,CAAC,WAAW,EAAE,CAAA;SACnB;IACH,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YAC5C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;YAC1B,IAAI,CAAC,aAAa,EAAE,CAAA;SACrB;IACH,CAAC;IAED,SAAS;QACP,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA;QACrB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAA;QACvB,IAAI,CAAC,cAAc,GAAG,KAAK,CAAA;QAC3B,IAAI,CAAC,iBAAiB,EAAE,CAAA;IAC1B,CAAC;IAED,SAAS;QACP,IAAI,CAAC,cAAc,GAAG,KAAK,CAAA;QAC3B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QAEtB,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,EAAE,CAAA;QAEvC,IAAI,GAAG,CAAA;QACP,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;YACjC,GAAG,CAAC,EAAE,EAAE,CAAA;YACR,IAAI,IAAI,CAAC,cAAc,EAAE,GAAG,SAAS,IAAI,IAAI,CAAC,aAAa,EAAE;gBAC3D,MAAK;aACN;SACF;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAA;QAEvB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACrB,IAAI,CAAC,UAAU,EAAE,CAAA;SAClB;IACH,CAAC;IAED,aAAa;QACX,IAAI,CAAC,cAAc,GAAG,KAAK,CAAA;QAC3B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QAEtB,IAAI,GAAG,CAAA;QACP,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;YACjC,IAAI;gBACF,GAAG,CAAC,EAAE,EAAE,CAAA;aACT;YAAC,OAAO,KAAK,EAAE;gBACd,2BAA2B;gBAC3B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;aACnB;SACF;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAA;IACzB,CAAC;IAEO,kBAAkB,CAAC,GAAQ;QACjC,IAAI,IAAI,GAAG,CAAC,CAAA;QACZ,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAA;QAC3B,IAAI,KAAK,GAAG,GAAG,GAAG,CAAC,CAAA;QACnB,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAA;QAC7B,OAAO,IAAI,IAAI,KAAK,EAAE;YACpB,MAAM,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAA;YACxC,IAAI,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;gBACxC,IAAI,GAAG,GAAG,GAAG,CAAC,CAAA;aACf;iBAAM;gBACL,GAAG,GAAG,GAAG,CAAA;gBACT,KAAK,GAAG,GAAG,GAAG,CAAC,CAAA;aAChB;SACF;QACD,OAAO,GAAG,CAAA;IACZ,CAAC;IAEO,WAAW;QACjB,IAAI,qBAAqB,IAAI,MAAM,EAAE;YACnC,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,IAAI,CAAC,iBAAiB,EAAE,CAAA;aACzB;YACD,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACtE,OAAO,EAAE,GAAG;aACb,CAAC,CAAA;SACH;aAAM;YACL,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,IAAI,CAAC,iBAAiB,EAAE,CAAA;aACzB;YACD,IAAI,CAAC,UAAU,GAAI,MAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;SAC3E;IACH,CAAC;IAEO,iBAAiB;QACvB,IAAI,oBAAoB,IAAI,MAAM,EAAE;YAClC,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;aAC3C;YACD,IAAI,CAAC,UAAU,GAAG,CAAC,CAAA;SACpB;aAAM;YACL,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;aAC9B;YACD,IAAI,CAAC,UAAU,GAAG,CAAC,CAAA;SACpB;IACH,CAAC;IAEO,cAAc;QACpB,MAAM,iBAAiB,GACrB,OAAO,WAAW,KAAK,QAAQ,IAAI,OAAO,WAAW,CAAC,GAAG,KAAK,UAAU,CAAA;QAC1E,IAAI,iBAAiB,EAAE;YACrB,OAAO,WAAW,CAAC,GAAG,EAAE,CAAA;SACzB;QACD,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,CAAA;IACtC,CAAC;CACF;AAQD,MAAM,CAAN,IAAY,YAKX;AALD,WAAY,YAAY;IACtB,mDAAwB,CAAA;IACxB,2DAAwB,CAAA;IACxB,2DAAwB,CAAA;IACxB,uDAAyB,CAAA;AAC3B,CAAC,EALW,YAAY,KAAZ,YAAY,QAKvB;AAED,0CAA0C;AAC1C,kBAAkB;AAClB,2DAA2D;AAC3D,yBAAyB;AACzB,6BAA6B;AAC7B,0CAA0C;AAC1C,QAAQ;AACR,oEAAoE;AACpE,kBAAkB;AAClB,QAAQ;AACR,wCAAwC;AACxC,mBAAmB;AACnB,QAAQ;AACR,MAAM;AACN,iBAAiB;AACjB,IAAI"}