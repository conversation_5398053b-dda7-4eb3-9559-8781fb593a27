{"version": 3, "file": "editor.js", "sourceRoot": "", "sources": ["../../../src/registry/tool/editor.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAA;AACzC,OAAO,EAAE,GAAG,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAA;AACxE,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAA;AAG3C,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAA;AAEjC,MAAM,OAAO,UAAW,SAAQ,SAAS,CAAC,QAGzC;IAHD;;QAKU,eAAU,GAAG,CAAC,CAAC,CAAA;QACf,aAAQ,GAAG,GAAG,CAAA;QAEd,aAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IAqQnD,CAAC;IAnQC,QAAQ;QACN,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAoB,CAAA;QAC1C,IAAI,QAAQ,EAAE;YACZ,QAAQ,CAAC,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;SAC5C;IACH,CAAC;IAED,aAAa;QACX,MAAM,UAAU,GAAG;YACjB,IAAI,CAAC,eAAe,CAClB,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,cAAc,CACtD;YACD,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC;SACzC,CAAA;QACD,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,CAAmB,CAAA;QACrE,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;QACtC,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,MAAM,CAAA;QACpC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IACzC,CAAC;IAED,aAAa;QACX,IAAI,CAAC,wBAAwB,EAAE,CAAA;QAC/B,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YACvC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;SACnB;IACH,CAAC;IAED,YAAY;QACV,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAA;QAE7B,IAAI,CAAC,MAAM,EAAE;YACX,OAAM;SACP;QAED,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA;QAExB,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;YACjB,IAAI,CAAC,yBAAyB,EAAE,CAAA;SACjC;aAAM,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;YACxB,IAAI,CAAC,yBAAyB,EAAE,CAAA;SACjC;QAED,iBAAiB;QACjB,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAA;QAC9B,KAAK,CAAC,QAAQ,GAAG,GAAG,KAAK,CAAC,QAAQ,IAAI,CAAA;QACtC,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAA;QACnC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAA;QACzB,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC,eAAe,CAAA;QAE7C,iBAAiB;QACjB,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,CAAA;QACrC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAA;QACvB,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAA,CAAC,8DAA8D;QAEnF,OAAO,IAAI,CAAA;IACb,CAAC;IAED,yBAAyB;QACvB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAA;QAEpC,IAAI,CAAC,MAAM,EAAE;YACX,OAAM;SACP;QAED,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAA;QACxB,IAAI,QAAQ,GAAG,EAAE,CAAA;QACjB,IAAI,SAAS,GAAG,EAAE,CAAA;QAClB,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,CAAA;QAC3B,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAA;QAEtC,IAAI,OAAO,CAAC,KAAK,WAAW,IAAI,OAAO,CAAC,KAAK,WAAW,EAAE;YACxD,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAA;YAC3B,CAAC,GAAG,SAAS,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;YAChD,CAAC,GAAG,SAAS,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;YACjD,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;YAClC,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAA;SAC9B;aAAM;YACL,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAA;YAC3B,GAAG,GAAG,IAAI,CAAC,MAAM,CAAA;YACjB,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;YACzB,SAAS,GAAG,uBAAuB,CAAA;SACpC;QAED,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA;QACxB,GAAG,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;QAC7B,KAAK,CAAC,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI,CAAA;QACzB,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI,CAAA;QACxB,KAAK,CAAC,SAAS,GAAG,SAAS,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,EAAE,CAAA;QAChE,KAAK,CAAC,QAAQ,GAAG,GAAG,QAAQ,IAAI,CAAA;QAEhC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,KAAK,CAAC,KAAK,GAAG,GAAG,KAAK,IAAI,CAAA;SAC3B;QACD,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YAC9B,KAAK,CAAC,MAAM,GAAG,GAAG,MAAM,IAAI,CAAA;SAC7B;IACH,CAAC;IAED,yBAAyB;QACvB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACf,OAAM;SACP;QAED,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAAA;QAC9B,IAAI,CAAC,MAAM,EAAE;YACX,OAAM;SACP;QAED,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAA;QACxB,IAAI,QAAQ,GAAG,EAAE,CAAA;QACjB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAA;QAChC,MAAM,MAAM,GAAG,MAAM,CAAC,aAAa,CAAA;QACnC,MAAM,WAAW,GACf,MAAM,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAA;QACpE,IAAI,WAAW,EAAE;YACf,MAAM,KAAK,GAAG,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,GAAG,CAAA;YACtD,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;YACrC,MAAM,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;YAC/C,MAAM,EAAE,WAAW,EAAE,GAAG,GAAG,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAA;YACxD,GAAG,GAAG,IAAI,KAAK,CAAC,WAAW,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE,CAAC,CAAA;YAC/C,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,CAAA;SACtC;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;gBAC9B,OAAO,IAAI,CAAA;aACZ;YACD,GAAG,GAAG,KAAK,CAAC,aAAa,CACvB,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CACrD,CAAA;YACD,MAAM,IAAI,GAAG,IAAI,CAAC,QAAoB,CAAA;YACtC,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAA;YAC3C,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAA;YACjB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAA;SACrB;QAED,GAAG,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;QAC7B,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAC3B,KAAK,CAAC,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI,CAAA;QACzB,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI,CAAA;QACxB,KAAK,CAAC,QAAQ,GAAG,GAAG,QAAQ,IAAI,CAAA;QAChC,KAAK,CAAC,SAAS,GAAG,SAAS,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,yBAAyB,CAAA;IAC3E,CAAC;IAED,iBAAiB,CAAC,CAAqB;QACrC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE;YAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,EAAE,CAAA;YAC5D,8DAA8D;YAC9D,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;YAC7C,cAAc;YACd,IAAI,CAAC,aAAa,EAAE,CAAA;SACrB;IACH,CAAC;IAED,cAAc,CAAC,EAAE,CAAC,EAA+B;QAC/C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,CAAC,CAAC,eAAe,EAAE,CAAA;YACnB,IAAI,CAAC,aAAa,EAAE,CAAA;YACpB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;YACd,IAAI,CAAC,aAAa,EAAE,CAAA;YACpB,IAAI,CAAC,YAAY,EAAE,CAAA;YACnB,IAAI,CAAC,SAAS,EAAE,CAAA;YAChB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,cAAe,CAAC,CAAA;SAC1D;IACH,CAAC;IAED,WAAW,CAAC,CAAqB;QAC/B,CAAC,CAAC,eAAe,EAAE,CAAA;IACrB,CAAC;IAED,SAAS;QACP,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAA;gBACnB,IAAI,CAAC,UAAU,EAAE,CAAA;aAClB;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,UAAU;QACR,IAAI,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,EAAE;YACtC,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAA;YACpC,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAG,CAAA;YACxC,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YACrC,SAAS,CAAC,eAAe,EAAE,CAAA;YAC3B,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;SAC1B;IACH,CAAC;IAED,WAAW;QACT,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,CAAA;QAChC,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;YACjC,OAAO,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE;gBAC9C,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,KAAK,EAAE,IAAI,CAAC,UAAU;aACvB,CAAC,CAAA;SACH;QACD,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC/B,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;gBACtB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;aAC/B;YACD,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;gBACtB,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,CAAC,EAAE;oBAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,UAAU,OAAO,EAAE,CAAC,CAAA;iBACpE;aACF;SACF;IACH,CAAC;IAED,WAAW,CAAC,KAAoB;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAA;QACpC,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;YACjC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE;gBACvC,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,KAAK;gBACL,KAAK,EAAE,IAAI,CAAC,UAAU;gBACtB,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB,CAAC,CAAA;YACF,OAAM;SACP;QACD,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC/B,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;gBACtB,IAAI,KAAK,KAAK,IAAI,EAAE;oBAClB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;iBAC/B;gBACD,OAAM;aACP;YACD,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;gBACtB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAY,CAAA;gBAC9B,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,CAAC,EAAE;oBAC1B,IAAI,KAAK,EAAE;wBACT,MAAM,QAAQ,GAAG;4BACf,QAAQ,EAAE;gCACR,QAAQ,EAAE,IAAI,CAAC,QAAQ;6BACxB;4BACD,KAAK,EAAE,EAAE;yBACV,CAAA;wBACD,SAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,OAAO,EAAE,EAAE,KAAK,CAAC,CAAA;wBACxD,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAA;qBAC3B;iBACF;qBAAM;oBACL,IAAI,KAAK,KAAK,IAAI,EAAE;wBAClB,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,UAAU,OAAO,EAAE,EAAE,KAAK,CAAC,CAAA;qBAC/D;yBAAM,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE;wBAC9C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;qBACpC;iBACF;aACF;SACF;IACH,CAAC;IAES,QAAQ;QAChB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAoB,CAAA;QAC1C,IAAI,QAAQ,EAAE;YACZ,QAAQ,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;SAC7C;QACD,IAAI,CAAC,aAAa,EAAE,CAAA;IACtB,CAAC;CACF;AAsCD,WAAiB,UAAU;IACzB,UAAU,CAAC,MAAM,CAAC;QAChB,OAAO,EAAE,KAAK;QACd,YAAY,EAAE,KAAK;QACnB,MAAM,EAAE;YACN,SAAS,EAAE,aAAa;YACxB,UAAU,EAAE,aAAa;SAC1B;QACD,cAAc,EAAE;YACd,OAAO,EAAE,mBAAmB;YAC5B,QAAQ,EAAE,mBAAmB;YAC7B,WAAW,EAAE,mBAAmB;SACjC;KACF,CAAC,CAAA;AACJ,CAAC,EAdgB,UAAU,KAAV,UAAU,QAc1B;AAED,WAAiB,UAAU;IACZ,qBAAU,GAAG,UAAU,CAAC,MAAM,CAAoB;QAC7D,KAAK,EAAE;YACL,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE,8BAA8B;YAC1C,KAAK,EAAE,MAAM;YACb,eAAe,EAAE,MAAM;SACxB;QACD,OAAO,EAAE,WAAW;QACpB,OAAO,EAAE,WAAW;KACrB,CAAC,CAAA;IAEW,qBAAU,GAAG,UAAU,CAAC,MAAM,CAAoB;QAC7D,KAAK,EAAE;YACL,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE,8BAA8B;YAC1C,KAAK,EAAE,MAAM;YACb,eAAe,EAAE,MAAM;SACxB;QACD,YAAY,EAAE,IAAI;QAClB,OAAO,EAAE,YAAY;QACrB,OAAO,EAAE,YAAY;KACtB,CAAC,CAAA;AACJ,CAAC,EAvBgB,UAAU,KAAV,UAAU,QAuB1B"}