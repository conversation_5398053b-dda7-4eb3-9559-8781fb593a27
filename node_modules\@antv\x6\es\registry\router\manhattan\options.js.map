{"version": 3, "file": "options.js", "sourceRoot": "", "sources": ["../../../../src/registry/router/manhattan/options.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAA;AAC3C,OAAO,EAAE,KAAK,EAAa,KAAK,EAAE,MAAM,mBAAmB,CAAA;AAG3D,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAA;AAsI9B,MAAM,CAAC,MAAM,QAAQ,GAA2B;IAC9C,IAAI,EAAE,EAAE;IACR,YAAY,EAAE,IAAI;IAClB,SAAS,EAAE,CAAC;IACZ,kBAAkB,EAAE,EAAE;IACtB,aAAa,EAAE,IAAI;IACnB,gBAAgB,EAAE,EAAE;IACpB,YAAY,EAAE,EAAE;IAChB,aAAa,EAAE,EAAE;IACjB,eAAe,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;IACnD,aAAa,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;IACjD,YAAY,EAAE;QACZ,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;QACpB,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACrB,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACtB,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;KACtB;IAED,IAAI;QACF,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QACrC,OAAO,IAAI,CAAA;IACb,CAAC;IAED,UAAU;QACR,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QACrC,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QAErC,OAAO;YACL,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE;YACnC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE;YACpC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE;YACnC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,IAAI,EAAE;SACrC,CAAA;IACH,CAAC;IAED,SAAS;QACP,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QACrC,OAAO;YACL,CAAC,EAAE,CAAC;YACJ,EAAE,EAAE,IAAI,GAAG,CAAC;YACZ,EAAE,EAAE,IAAI,GAAG,CAAC;SACb,CAAA;IACH,CAAC;IAED,UAAU;QACR,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QACrC,OAAO;YACL,CAAC,EAAE,CAAC,IAAI;YACR,CAAC,EAAE,CAAC,IAAI;YACR,KAAK,EAAE,CAAC,GAAG,IAAI;YACf,MAAM,EAAE,CAAC,GAAG,IAAI;SACjB,CAAA;IACH,CAAC;IAED,cAAc,EAAE,IAAI;IACpB,cAAc,EAAE,IAAI;IACpB,UAAU,EAAE,IAAI;CACjB,CAAA;AAED,MAAM,UAAU,OAAO,CACrB,KAAkB,EAClB,OAA+B;IAE/B,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;QAC/B,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;KAC3B;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,OAA+B;IAC5D,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CACxC,CAAC,IAAI,EAAE,GAA0B,EAAE,EAAE;QACnC,MAAM,GAAG,GAAG,IAAW,CAAA;QACvB,IACE,GAAG,KAAK,gBAAgB;YACxB,GAAG,KAAK,gBAAgB;YACxB,GAAG,KAAK,eAAe,EACvB;YACA,GAAG,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAA;SACxB;aAAM;YACL,GAAG,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAA;SAC1C;QACD,OAAO,IAAI,CAAA;IACb,CAAC,EACD,EAAqB,CACtB,CAAA;IAED,IAAI,MAAM,CAAC,OAAO,EAAE;QAClB,MAAM,KAAK,GAAG,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;QACtD,MAAM,CAAC,UAAU,GAAG;YAClB,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI;YACd,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG;YACb,KAAK,EAAE,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK;YAC/B,MAAM,EAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM;SACjC,CAAA;KACF;IAED,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;QACtC,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;QAC9B,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC,OAAO,CAAC,CAAA;QAC9D,SAAS,CAAC,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAA;IACzD,CAAC,CAAC,CAAA;IAEF,OAAO,MAAM,CAAA;AACf,CAAC"}