{"version": 3, "file": "html.js", "sourceRoot": "", "sources": ["../../src/shape/html.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,iBAAiB,CAAA;AACrC,OAAO,EAAE,MAAM,EAAE,MAAM,SAAS,CAAA;AAEhC,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAA;AACpC,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAA;AACvC,OAAO,EAAE,KAAK,EAAE,MAAM,gBAAgB,CAAA;AAEtC,MAAM,OAAO,IAEX,SAAQ,IAAgB;CAAG;AAM7B,WAAiB,IAAI;IACnB,MAAa,IAAK,SAAQ,QAAc;QAC5B,IAAI;YACZ,KAAK,CAAC,IAAI,EAAE,CAAA;YACZ,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;QACtD,CAAC;QAES,eAAe,CAAC,EAAE,GAAG,EAA8B;YAC3D,MAAM,OAAO,GAAG,KAAA,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAC1C,IAAI,OAAO,EAAE;gBACX,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAA;gBAC1B,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;oBACnC,IAAI,CAAC,mBAAmB,EAAE,CAAA;iBAC3B;aACF;QACH,CAAC;QAED,aAAa,CAAC,IAAY;YACxB,MAAM,GAAG,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;YACrC,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAC9C,IAAI,CAAC,mBAAmB,EAAE,CAC3B,CAAA;QACH,CAAC;QAES,mBAAmB;YAC3B,MAAM,SAAS,GACb,IAAI,CAAC,SAAS,IAAK,IAAI,CAAC,SAAS,CAAC,SAA4B,CAAA;YAChE,IAAI,SAAS,EAAE;gBACb,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;gBACpB,MAAM,OAAO,GAAG,KAAA,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBAC1C,IAAI,CAAC,OAAO,EAAE;oBACZ,OAAM;iBACP;gBAED,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAA;gBACtB,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;oBAC9B,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;iBACvB;gBACD,IAAI,IAAI,EAAE;oBACR,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;wBAC5B,SAAS,CAAC,SAAS,GAAG,IAAI,CAAA;qBAC3B;yBAAM;wBACL,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;qBAC5B;iBACF;aACF;QACH,CAAC;QAGD,OAAO;YACL,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;QACvD,CAAC;KACF;IAHC;QADC,IAAI,CAAC,OAAO,EAAE;uCAGd;IAlDU,SAAI,OAmDhB,CAAA;IAED,WAAiB,IAAI;QACN,WAAM,GAAG,MAAa,CAAA;QAEnC,IAAI,CAAC,MAAM,CAAC;YACV,SAAS,EAAE,CAAC,KAAA,MAAM,CAAC;YACnB,OAAO,EAAE;gBACP,IAAI,EAAE,KAAA,MAAM;aACb;SACF,CAAC,CAAA;QAEF,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;IACrD,CAAC,EAXgB,IAAI,GAAJ,SAAI,KAAJ,SAAI,QAWpB;AACH,CAAC,EAlEgB,IAAI,KAAJ,IAAI,QAkEpB;AAED,WAAiB,IAAI;IACnB,IAAI,CAAC,MAAM,CAAC;QACV,IAAI,EAAE,WAAW;QACjB,MAAM,EAAE;YACN;gBACE,OAAO,EAAE,MAAM;gBACf,QAAQ,EAAE,MAAM;aACjB;8BAEI,MAAM,CAAC,sBAAsB,EAAE;YAEpC;gBACE,OAAO,EAAE,MAAM;gBACf,QAAQ,EAAE,OAAO;aAClB;SACF;QACD,KAAK,EAAE;YACL,IAAI,EAAE;gBACJ,IAAI,EAAE,MAAM;gBACZ,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,MAAM;gBAChB,SAAS,EAAE,MAAM;aAClB;YACD,EAAE,EAAE;gBACF,QAAQ,EAAE,MAAM;gBAChB,SAAS,EAAE,MAAM;aAClB;SACF;KACF,CAAC,CAAA;IAEF,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AAC5C,CAAC,EA/BgB,IAAI,KAAJ,IAAI,QA+BpB;AAED,WAAiB,IAAI;IAaN,cAAS,GAMlB,EAAE,CAAA;IAEN,SAAgB,QAAQ,CAAC,MAAuB;QAC9C,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,KAAgB,MAAM,EAAjB,MAAM,UAAK,MAAM,EAApD,sCAA2C,CAAS,CAAA;QAC1D,IAAI,CAAC,KAAK,EAAE;YACV,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;SAClD;QACD,KAAA,SAAS,CAAC,KAAK,CAAC,GAAG;YACjB,IAAI;YACJ,MAAM;SACP,CAAA;QAED,KAAK,CAAC,YAAY,CAChB,KAAK,kBAEH,OAAO,EAAE,OAAO,IAAI,MAAM,IACvB,MAAM,GAEX,IAAI,CACL,CAAA;IACH,CAAC;IAlBe,aAAQ,WAkBvB,CAAA;AACH,CAAC,EAxCgB,IAAI,KAAJ,IAAI,QAwCpB"}