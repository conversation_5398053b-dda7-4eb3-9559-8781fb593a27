{"version": 3, "file": "connection.js", "sourceRoot": "", "sources": ["../../../src/registry/attr/connection.ts"], "names": [], "mappings": "AAGA,MAAM,UAAU,GAAyB,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;IACzD,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAA;AAC3B,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,UAAU,GAAoB;IACzC,OAAO,EAAE,UAAU;IACnB,GAAG,CAAC,GAAG,EAAE,IAAI;;QACX,MAAM,IAAI,GAAG,IAAI,CAAC,IAAgB,CAAA;QAClC,MAAM,OAAO,GAAG,CAAE,GAAW,CAAC,OAAO,IAAI,KAAK,CAAY,CAAA;QAC1D,MAAM,KAAK,GAAG,CAAE,GAAW,CAAC,KAAK,IAAI,CAAC,CAAW,CAAA;QACjD,IAAI,CAAC,CAAA;QACL,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE;YACzC,IAAI,CAAC,OAAO,EAAE;gBACZ,IAAI,MAAM,CAAA;gBACV,IAAI,KAAK,GAAG,CAAC,EAAE;oBACb,MAAM,GAAG,GAAG,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAA;oBAC3C,MAAM,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,CAAA;iBAC3B;qBAAM;oBACL,MAAM,GAAG,KAAK,CAAA;iBACf;gBAED,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;gBACjC,IAAI,IAAI,EAAE;oBACR,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;oBAC/C,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAA;oBAChD,IAAI,WAAW,IAAI,WAAW,EAAE;wBAC9B,CAAC,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,EAAE,CAAA;qBAClE;iBACF;aACF;iBAAM;gBACL,IAAI,MAAM,CAAA;gBACV,IAAI,MAAM,CAAA;gBACV,MAAM,GAAG,GAAG,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAA;gBAC3C,IAAI,KAAK,GAAG,CAAC,EAAE;oBACb,MAAM,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,CAAA;oBAC1B,MAAM,GAAG,CAAC,KAAK,CAAA;iBAChB;qBAAM;oBACL,MAAM,GAAG,KAAK,CAAA;oBACd,MAAM,GAAG,GAAG,GAAG,KAAK,GAAG,CAAC,CAAA;iBACzB;gBAED,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;gBACjC,CAAC,GAAG,MAAA,MAAA,MAAA,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CACJ,cAAc,CAAC,MAAM,CAAC,0CAAG,CAAC,CAAC,0CAC3B,cAAc,CAAC,MAAM,CAAC,0CAAG,CAAC,CAAC,0CAC3B,SAAS,EAAE,CAAA;aAChB;SACF;QAED,OAAO,EAAE,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAA;IACjD,CAAC;CACF,CAAA;AAED,MAAM,CAAC,MAAM,8BAA8B,GAAoB;IAC7D,OAAO,EAAE,UAAU;IACnB,GAAG,EAAE,mBAAmB,CAAC,oBAAoB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;CACjE,CAAA;AAED,MAAM,CAAC,MAAM,gCAAgC,GAAoB;IAC/D,OAAO,EAAE,UAAU;IACnB,GAAG,EAAE,mBAAmB,CAAC,oBAAoB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;CAClE,CAAA;AAED,MAAM,CAAC,MAAM,6BAA6B,GAAoB;IAC5D,OAAO,EAAE,UAAU;IACnB,GAAG,EAAE,mBAAmB,CAAC,mBAAmB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;CAChE,CAAA;AAED,MAAM,CAAC,MAAM,+BAA+B,GAAoB;IAC9D,OAAO,EAAE,UAAU;IACnB,GAAG,EAAE,mBAAmB,CAAC,mBAAmB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;CACjE,CAAA;AAED,UAAU;AACV,UAAU;AACV,MAAM,CAAC,MAAM,kBAAkB,GAAG,8BAA8B,CAAA;AAChE,MAAM,CAAC,MAAM,iBAAiB,GAAG,6BAA6B,CAAA;AAE9D,QAAQ;AACR,QAAQ;AAER,SAAS,mBAAmB,CAC1B,MAAkD,EAClD,OAA4B;IAE5B,MAAM,UAAU,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAA;IAEjC,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;QACrB,IAAI,CAAC,CAAA;QACL,IAAI,KAAK,CAAA;QAET,MAAM,IAAI,GAAG,IAAI,CAAC,IAAgB,CAAA;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;QAC3C,IAAI,OAAO,EAAE;YACX,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACrE,CAAC,GAAG,OAAO,CAAC,KAAK,CAAA;SAClB;aAAM;YACL,CAAC,GAAI,IAAY,CAAC,IAAI,CAAC,KAAK,CAAA;YAC5B,KAAK,GAAG,CAAC,CAAA;SACV;QAED,IAAI,KAAK,KAAK,CAAC,EAAE;YACf,OAAO,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;SAClD;QAED,OAAO;YACL,SAAS,EAAE,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,KAAK,GAAG;SACxD,CAAA;IACH,CAAC,CAAA;AACH,CAAC"}