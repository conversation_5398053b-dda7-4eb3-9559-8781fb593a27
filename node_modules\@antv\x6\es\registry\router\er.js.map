{"version": 3, "file": "er.js", "sourceRoot": "", "sources": ["../../../src/registry/router/er.ts"], "names": [], "mappings": "AAQA,MAAM,CAAC,MAAM,EAAE,GAAuC,UACpD,QAAQ,EACR,OAAO,EACP,QAAQ;IAER,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,IAAI,EAAE,CAAA;IACtC,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAA;IAElD,IAAI,MAAM,GAAG,CAAC,CAAA;IACd,IAAI,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;IAEjC,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAA;IACtC,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAA;IACtC,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,CAAA;IAC1C,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,CAAA;IAE1C,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;QACjC,MAAM,GAAG,SAAS,CAAA;KACnB;IAED,IAAI,SAAS,IAAI,IAAI,EAAE;QACrB,IAAI,EAAE,GAAG,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,KAAK,CAAA;QAC3C,IAAI,EAAE,GAAG,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,MAAM,CAAA;QAE3C,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;YACtB,SAAS,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;SACjC;aAAM,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;YAC7B,EAAE,GAAG,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,KAAK,CAAA;YACvC,IAAI,EAAE,IAAI,CAAC,EAAE;gBACX,SAAS,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;aACjC;iBAAM;gBACL,SAAS,GAAG,GAAG,CAAA;aAChB;SACF;aAAM,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;YAC7B,EAAE,GAAG,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,MAAM,CAAA;YACvC,IAAI,EAAE,IAAI,CAAC,EAAE;gBACX,SAAS,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;aACjC;iBAAM;gBACL,SAAS,GAAG,GAAG,CAAA;aAChB;SACF;aAAM;YACL,EAAE,GAAG,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,KAAK,CAAA;YACvC,EAAE,GAAG,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,MAAM,CAAA;YACvC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;gBACtB,SAAS,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;aACjC;iBAAM,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;gBAC7B,SAAS,GAAG,GAAG,CAAA;aAChB;iBAAM,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;gBAC7B,SAAS,GAAG,GAAG,CAAA;aAChB;iBAAM;gBACL,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;aACpD;SACF;KACF;IAED,IAAI,SAAS,KAAK,GAAG,EAAE;QACrB,SAAS,GAAG,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;KAC3D;SAAM,IAAI,SAAS,KAAK,GAAG,EAAE;QAC5B,SAAS,GAAG,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;KAC3D;IAED,IAAI,SAAS,KAAK,QAAQ,EAAE;QAC1B,IAAI,SAAS,KAAK,GAAG,EAAE;YACrB,MAAM,GAAG,CAAC,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;SAClD;aAAM,IAAI,SAAS,KAAK,GAAG,EAAE;YAC5B,MAAM,GAAG,CAAC,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;SAClD;aAAM,IAAI,SAAS,KAAK,GAAG,EAAE;YAC5B,MAAM,GAAG,CAAC,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;SAClD;aAAM,IAAI,SAAS,KAAK,GAAG,EAAE;YAC5B,MAAM,GAAG,CAAC,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;SAClD;KACF;IAED,IAAI,KAAgB,CAAA;IACpB,IAAI,GAAuB,CAAA;IAC3B,IAAI,MAAM,CAAA;IACV,MAAM,UAAU,GAAG,SAAS,KAAK,GAAG,IAAI,SAAS,KAAK,GAAG,CAAA;IAEzD,IAAI,UAAU,EAAE;QACd,IAAI,WAAW,CAAC,CAAC,KAAK,WAAW,CAAC,CAAC,EAAE;YACnC,OAAO,CAAC,GAAG,QAAQ,CAAC,CAAA;SACrB;QAED,MAAM,GAAG,SAAS,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACnC,KAAK,GAAG,GAAG,CAAA;QACX,GAAG,GAAG,OAAO,CAAA;KACd;SAAM;QACL,IAAI,WAAW,CAAC,CAAC,KAAK,WAAW,CAAC,CAAC,EAAE;YACnC,OAAO,CAAC,GAAG,QAAQ,CAAC,CAAA;SACrB;QAED,MAAM,GAAG,SAAS,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACnC,KAAK,GAAG,GAAG,CAAA;QACX,GAAG,GAAG,QAAQ,CAAA;KACf;IAED,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,EAAE,CAAA;IAClC,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,EAAE,CAAA;IAElC,MAAM,CAAC,KAAK,CAAC,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAA;IACxD,MAAM,CAAC,KAAK,CAAC,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAA;IAExD,IAAI,UAAU,EAAE;QACd,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAA;QACxB,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAA;QACxB,MAAM,WAAW,GAAG,UAAU,CAAC,KAAK,GAAG,CAAC,GAAG,GAAG,CAAA;QAC9C,MAAM,WAAW,GAAG,UAAU,CAAC,KAAK,GAAG,CAAC,GAAG,GAAG,CAAA;QAC9C,IAAI,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,EAAE;YACjC,IAAI,OAAO,IAAI,OAAO,EAAE;gBACtB,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAA;gBACzD,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAA;aAC1D;SACF;aAAM,IAAI,OAAO,IAAI,OAAO,EAAE;YAC7B,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAA;YACzD,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAA;SAC1D;KACF;SAAM;QACL,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAA;QACxB,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAA;QACxB,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,CAAA;QAC/C,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,CAAA;QAC/C,IAAI,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,EAAE;YACjC,IAAI,OAAO,IAAI,OAAO,EAAE;gBACtB,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAA;gBACzD,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAA;aAC1D;SACF;aAAM,IAAI,OAAO,IAAI,OAAO,EAAE;YAC7B,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAA;YACzD,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAA;SAC1D;KACF;IAED,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,GAAG,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAA;AACxD,CAAC,CAAA"}