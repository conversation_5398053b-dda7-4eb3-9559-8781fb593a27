{"version": 3, "file": "panning.js", "sourceRoot": "", "sources": ["../../src/graph/panning.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,+CAAkD;AAClD,iCAA6B;AAE7B,MAAa,cAAe,SAAQ,WAAI;IAOtC,IAAc,aAAa;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAA;IAC7B,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,KAAK,IAAI,CAAA;IAClE,CAAC;IAES,IAAI;QACZ,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACxD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChD,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,IAAI,CAAC,eAAe,EAAE,CAAA;IACxB,CAAC;IAES,cAAc;QACtB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,iBAAiB,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;QACxD,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,0BAA0B,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;QACjE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,0BAA0B,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;QACjE,eAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAA;QACtE,eAAG,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE;YAC1B,OAAO,EAAE,IAAI,CAAC,cAAc;YAC5B,KAAK,EAAE,IAAI,CAAC,YAAY;SACzB,CAAC,CAAA;QACF,IAAI,CAAC,gBAAgB,GAAG,IAAI,eAAG,CAAC,gBAAgB,CAC9C,IAAI,CAAC,KAAK,CAAC,SAAS,EACpB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAC5B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAChC,CAAA;QACD,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAA;IAChC,CAAC;IAES,aAAa;QACrB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;QACzD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;QAClE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;QAClE,eAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAA;QACvE,eAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;YAC3B,OAAO,EAAE,IAAI,CAAC,cAAc;YAC5B,KAAK,EAAE,IAAI,CAAC,YAAY;SACzB,CAAC,CAAA;QACF,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAA;SAChC;IACH,CAAC;IAED,YAAY,CAAC,CAAqB,EAAE,MAAgB;QAClD,CAAC;QAAC,CAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAA;QAC7C,OAAO,CACL,IAAI,CAAC,QAAQ;YACb,uBAAW,CAAC,OAAO,CACjB,CAAC,EACD,IAAI,CAAC,aAAa,CAAC,SAAwB,EAC3C,MAAM,CACP,CACF,CAAA;IACH,CAAC;IAES,YAAY,CAAC,GAAuB;QAC5C,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAA;QACvC,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAA;QACxB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAA;QACxB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;QACnB,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,eAAG,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE;YAC1B,qCAAqC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;YAC1D,kCAAkC,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;YAC/D,oBAAoB,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;SAClD,CAAC,CAAA;QACF,eAAG,CAAC,KAAK,CAAC,EAAE,CAAC,MAAa,EAAE,iBAAiB,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;IAC7E,CAAC;IAES,GAAG,CAAC,GAAuB;QACnC,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAA;QACvC,MAAM,EAAE,GAAG,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QACnC,MAAM,EAAE,GAAG,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QACnC,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAA;QACxB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAA;QACxB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;IAChC,CAAC;IAED,2BAA2B;IACjB,WAAW,CAAC,CAAmB;QACvC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;QACpB,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,eAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAA;QACxC,eAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAa,EAAE,UAAU,CAAC,CAAA;IAC1C,CAAC;IAES,eAAe;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAA;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAA;QAC1D,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAA;QAC5D,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,eAAG,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;gBAChC,eAAG,CAAC,WAAW,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAA;aACrC;iBAAM;gBACL,eAAG,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;gBACnC,eAAG,CAAC,QAAQ,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAA;aAClC;SACF;aAAM;YACL,eAAG,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;YACnC,eAAG,CAAC,WAAW,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAA;SACrC;IACH,CAAC;IAES,WAAW,CAAC,EAAE,CAAC,EAA6B;QACpD,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE;YAChC,OAAM;SACP;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAM,WAAW,CAAC,CAAA;QACxD,MAAM,eAAe,GAAG,SAAS,IAAI,SAAS,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;QACvE,IACE,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC;YAC1B,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,EAC1C;YACA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;SACrB;IACH,CAAC;IAES,gBAAgB,CAAC,CAAqB;QAC9C,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAA;QAChD,IAAI,CAAC,CAAC,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,QAAQ,CAAC,gBAAgB,CAAC,KAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;YAC/D,OAAM;SACP;QACD,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE;YAC9B,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;SACrB;IACH,CAAC;IAES,YAAY,CAAC,CAAa,EAAE,MAAc,EAAE,MAAc;QAClE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAA;IAC1C,CAAC;IAES,cAAc,CAAC,CAAmB;QAC1C,IAAI,CAAC,CAAC,KAAK,KAAK,EAAE,EAAE;YAClB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAA;SAC9B;IACH,CAAC;IACS,YAAY,CAAC,CAAiB;QACtC,IAAI,CAAC,CAAC,KAAK,KAAK,EAAE,EAAE;YAClB,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAA;SAC/B;IACH,CAAC;IACS,mBAAmB,CAAC,CAAqB;QACjD,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAA;QAChD,OAAO,CACL,CAAC,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,QAAQ,CAAC,eAAe,CAAC,KAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;YACzD,CAAC,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,QAAQ,CAAC,gBAAgB,CAAC,KAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAC3D,CAAA;IACH,CAAC;IAES,eAAe,CAAC,CAAa;;QACrC,OAAO,CACL,IAAI,CAAC,QAAQ;YACb,CAAC,CAAC,CAAC,OAAO;aACV,MAAA,IAAI,CAAC,aAAa,CAAC,UAAU,0CAAE,QAAQ,CAAC,YAAY,CAAC,CAAA,CACtD,CAAA;IACH,CAAC;IAED,WAAW,CAAC,CAAS,EAAE,CAAS;QAC9B,MAAM,MAAM,GAAG,EAAE,CAAA;QACjB,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAA;QAE3C,IAAI,EAAE,GAAG,CAAC,CAAA;QACV,IAAI,EAAE,GAAG,CAAC,CAAA;QACV,IAAI,CAAC,IAAI,SAAS,CAAC,IAAI,GAAG,MAAM,EAAE;YAChC,EAAE,GAAG,CAAC,MAAM,CAAA;SACb;QAED,IAAI,CAAC,IAAI,SAAS,CAAC,GAAG,GAAG,MAAM,EAAE;YAC/B,EAAE,GAAG,CAAC,MAAM,CAAA;SACb;QAED,IAAI,CAAC,IAAI,SAAS,CAAC,KAAK,GAAG,MAAM,EAAE;YACjC,EAAE,GAAG,MAAM,CAAA;SACZ;QAED,IAAI,CAAC,IAAI,SAAS,CAAC,MAAM,GAAG,MAAM,EAAE;YAClC,EAAE,GAAG,MAAM,CAAA;SACZ;QAED,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;YACxB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAA;SACjC;IACH,CAAC;IAED,aAAa;QACX,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAA;YACjC,IAAI,CAAC,eAAe,EAAE,CAAA;SACvB;IACH,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,KAAK,CAAA;YAClC,IAAI,CAAC,eAAe,EAAE,CAAA;SACvB;IACH,CAAC;IAGD,OAAO;QACL,IAAI,CAAC,aAAa,EAAE,CAAA;IACtB,CAAC;CACF;AAHC;IADC,WAAI,CAAC,OAAO,EAAE;6CAGd;AAtNH,wCAuNC"}