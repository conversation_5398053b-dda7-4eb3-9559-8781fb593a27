import axios from 'axios';
import { Message, MessageBox } from 'element-ui';
// 全局检测u盘插拔状态
 const myonLoad=  function () {
			try {
				var s_pnp = new SoftKey3W();
				s_pnp.Socket_UK.onopen = function () {
					bConnect = 1; //代表已经连接，用于判断是否安装了客户端服务
				};

				//在使用事件插拨时，注意，一定不要关掉Sockey，否则无法监测事件插拨
				s_pnp.Socket_UK.onmessage = function got_packet(Msg) {
					var PnpData = JSON.parse(Msg.data);
					if (PnpData.type == 'PnpEvent') {
						//如果是插拨事件处理消息
						if (!PnpData.IsIn) {
                            this.$Message.warning('UKEY已被拨出!!!');
                            this.$router.push('/login');
						}
					}
				};

				s_pnp.Socket_UK.onclose = function () {};
			} catch (e) {
				alert(e.name + ': ' + e.message);
				return false;
			}
		}
// 创建 axios 实例
const service = axios.create({
    baseURL: process.env.VUE_APP_BASE_API,
    // baseURL: "http://192.168.7.170:9091/",
    timeout: 50000,
    headers: { 'Content-Type': 'application/json' }
});

// 添加请求拦截器
service.interceptors.request.use(
    config => {
        // 在发送请求之前做些什么 token
        // if (Session.get('token')) {
        // 	config.headers.common['Authorization'] = `${Session.get('token')}`;
        // }
        myonLoad();
        if (localStorage.getItem('tokenjm')) {
            config.headers['Token'] = localStorage.getItem('tokenjm');
        }

        return config;
    },
    error => {
        // 对请求错误做些什么
        return Promise.reject(error);
    }
);

// 添加响应拦截器
service.interceptors.response.use(
    response => {
        // 对响应数据做点什么
        if (response.status == 401) {
            //重新登录
            this.$Message.warning('登录失效，请重新登录');
            this.$router.push('/login');
            return;
        }
        return response.data;
    },
    error => {
        // 对响应错误做点什么
        localStorage.setItem('errorType', 'ng');
        if (error == 'Error: Request failed with status code 401') {
            //
            //重新登录
            window.location = '/#/login';
            return;
        }
        return Promise.reject(error);
    }
);

// 导出 axios 实例
export default service;