{"name": "element-resize-detector", "version": "1.2.4", "description": "Resize event emitter for elements.", "homepage": "https://github.com/wnr/element-resize-detector", "repository": {"type": "git", "url": "git://github.com/wnr/element-resize-detector.git"}, "main": "src/element-resize-detector.js", "private": false, "license": "MIT", "dependencies": {"batch-processor": "1.0.0"}, "devDependencies": {"grunt": "1.0.1", "grunt-banner": "0.6.0", "grunt-browserify": "5.2.0", "grunt-contrib-jshint": "1.1.0", "grunt-contrib-uglify": "2.3.0", "grunt-karma": "2.0.0", "jasmine-core": "2.9.0", "jquery": "3.1.1", "karma": "1.7.1", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-jasmine": "1.1.1", "karma-safari-launcher": "1.0.0", "load-grunt-tasks": "3.5.2", "lodash": "4.17.4"}, "scripts": {"build": "grunt build", "dist": "grunt dist", "test": "grunt test", "test-ci": "grunt ci"}}