{"version": 3, "file": "animation.js", "sourceRoot": "", "sources": ["../../src/model/animation.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAY,MAAM,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAA;AAGrE,MAAM,OAAO,SAAS;IAUpB,YAA+B,IAAU;QAAV,SAAI,GAAJ,IAAI,CAAM;QATtB,QAAG,GAA+B,EAAE,CAAA;QACpC,UAAK,GAMpB,EAAE,CAAA;IAEsC,CAAC;IAE7C,GAAG;QACD,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC9B,CAAC;IAED,KAAK,CACH,IAAuB,EACvB,WAAc,EACd,UAAqC,EAAE,EACvC,KAAK,GAAG,GAAG;QAEX,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAI,IAAI,CAAC,CAAA;QACnD,MAAM,YAAY,GAAG,SAAS,CAAC,QAAQ,CAAC,OAAO,EAAE,SAAS,CAAC,cAAc,CAAC,CAAA;QAC1E,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;QAClD,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAChC,YAAY,CAAC,MAAM,EACnB,UAAU,EACV,WAAW,CACZ,CAAA;QAED,IAAI,SAAS,GAAG,CAAC,CAAA;QACjB,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;QACzD,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAC5D,MAAM,OAAO,GAAG,GAAG,EAAE;YACnB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAA;YAChC,IAAI,SAAS,KAAK,CAAC,EAAE;gBACnB,SAAS,GAAG,GAAG,CAAA;aAChB;YAED,MAAM,MAAM,GAAG,GAAG,GAAG,SAAS,CAAA;YAC9B,IAAI,QAAQ,GAAG,MAAM,GAAG,YAAY,CAAC,QAAQ,CAAA;YAC7C,IAAI,QAAQ,GAAG,CAAC,EAAE;gBAChB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAA;aAC/C;iBAAM;gBACL,QAAQ,GAAG,CAAC,CAAA;aACb;YAED,MAAM,YAAY,GAAG,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAM,CAAA;YACvD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,YAAY,CAAC,CAAA;YAE5C,IAAI,OAAO,CAAC,QAAQ,EAAE;gBACpB,OAAO,CAAC,QAAQ,iBAAG,QAAQ,EAAE,YAAY,IAAK,IAAI,CAAC,OAAO,CAAI,GAAG,CAAC,EAAG,CAAA;aACtE;YAED,IAAI,QAAQ,KAAK,CAAC,EAAE;gBAClB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAI,GAAG,CAAC,CAAC,CAAA;gBAC7D,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAI,GAAG,CAAC,CAAC,CAAA;gBAE1D,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAI,GAAG,CAAC,CAAC,CAAA;gBAC3D,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAI,GAAG,CAAC,CAAC,CAAA;gBACtD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;aAChB;QACH,CAAC,CAAA;QAED,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAA;YACjC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,CAAA;YACpE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAA;YAE9C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,IAAI,CAAC,OAAO,CAAI,GAAG,CAAC,CAAC,CAAA;YAC1D,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAI,GAAG,CAAC,CAAC,CAAA;QACtD,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAA;QAEjB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;IACnD,CAAC;IAED,IAAI,CACF,IAAuB,EACvB,UAAoC,EAAE,EACtC,KAAK,GAAG,GAAG;QAEX,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAC5D,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;aAClB,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CACd,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAClE;aACA,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YACf,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;YACnC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAI,GAAG,CAAC,CAAA;YACvC,MAAM,YAAY,mCAAQ,IAAI,CAAC,OAAO,GAAK,OAAO,CAAE,CAAA;YACpD,MAAM,WAAW,GAAG,YAAY,CAAC,WAAW,CAAA;YAC5C,IAAI,WAAW,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,EAAE;gBAC3C,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;gBAE9C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,oBAAO,UAAU,EAAG,CAAA;gBACrD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,oBAAO,UAAU,EAAG,CAAA;gBAC1D,YAAY,CAAC,QAAQ,IAAI,YAAY,CAAC,QAAQ,mBAAM,UAAU,EAAG,CAAA;aAClE;YAED,MAAM,QAAQ,mBAAK,WAAW,IAAK,UAAU,CAAE,CAAA;YAC/C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,oBAAO,QAAQ,EAAG,CAAA;YACpD,YAAY,CAAC,IAAI,IAAI,YAAY,CAAC,IAAI,mBAAM,QAAQ,EAAG,CAAA;YAEvD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,oBAAO,UAAU,EAAG,CAAA;YACxD,YAAY,CAAC,MAAM,IAAI,YAAY,CAAC,MAAM,mBAAM,UAAU,EAAG,CAAA;YAE7D,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACjB,CAAC,CAAC,CAAA;QAEJ,OAAO,IAAI,CAAA;IACb,CAAC;IAEO,KAAK,CAAC,GAAW;QACvB,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IACxB,CAAC;IAEO,SAAS,CAAC,MAAwC;QACxD,OAAO,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;IAC7D,CAAC;IAEO,SAAS,CACf,MAA0C,EAC1C,UAAa,EACb,WAAc;QAEd,IAAI,MAAM,EAAE;YACV,OAAO,MAAM,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;SACvC;QAED,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACnC,OAAO,MAAM,CAAC,MAAM,CAAC,UAAoB,EAAE,WAAW,CAAC,CAAA;SACxD;QAED,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACnC,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gBAC1B,OAAO,MAAM,CAAC,KAAK,CAAC,UAAoB,EAAE,WAAW,CAAC,CAAA;aACvD;YAED,OAAO,MAAM,CAAC,IAAI,CAAC,UAAoB,EAAE,WAAW,CAAC,CAAA;SACtD;QAED,OAAO,MAAM,CAAC,MAAM,CAClB,UAA8B,EAC9B,WAA+B,CAChC,CAAA;IACH,CAAC;IAEO,OAAO,CACb,GAAW;QAEX,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC5B,OAAO;YACL,IAAI,EAAE,GAAG;YACT,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAA;IACH,CAAC;CACF;AAED,WAAiB,SAAS;IA4DX,wBAAc,GAAgB;QACzC,KAAK,EAAE,EAAE;QACT,QAAQ,EAAE,GAAG;QACb,MAAM,EAAE,QAAQ;KACjB,CAAA;AACH,CAAC,EAjEgB,SAAS,KAAT,SAAS,QAiEzB"}