'use strict';

exports.__esModule = true;
exports.default = {
  el: {
    colorpicker: {
      confirm: 'OK',
      clear: '<PERSON><PERSON><PERSON>'
    },
    datepicker: {
      now: '<PERSON><PERSON>wagt',
      today: '<PERSON>ügün',
      cancel: 'Bes et',
      clear: '<PERSON><PERSON><PERSON>',
      confirm: 'OK',
      selectDate: '<PERSON><PERSON><PERSON> saýla<PERSON>',
      selectTime: 'Wagty saýlaň',
      startDate: '<PERSON><PERSON><PERSON><PERSON><PERSON> güni',
      startTime: 'Başlaýan wagty',
      endDate: '<PERSON><PERSON><PERSON><PERSON><PERSON> güni',
      endTime: 'G<PERSON>rýan wagty',
      prevYear: 'Previous Year', // to be translated
      nextYear: 'Next Year', // to be translated
      prevMonth: 'Previous Month', // to be translated
      nextMonth: 'Next Month', // to be translated
      year: '',
      month1: 'Ýan',
      month2: 'Few',
      month3: 'Mar',
      month4: 'Apr',
      month5: 'Maý',
      month6: 'Iýn',
      month7: 'Iýl',
      month8: 'Awg',
      month9: 'Sen',
      month10: 'Okt',
      month11: 'Noý',
      month12: 'Dek',
      // week: 'week',
      weeks: {
        sun: 'Ýek',
        mon: 'Duş',
        tue: 'Siş',
        wed: 'Çar',
        thu: 'Pen',
        fri: 'Ann',
        sat: 'Şen'
      },
      months: {
        jan: 'Ýan',
        feb: 'Few',
        mar: 'Mar',
        apr: 'Apr',
        may: 'Maý',
        jun: 'Iýn',
        jul: 'Iýl',
        aug: 'Awg',
        sep: 'Sep',
        oct: 'Okt',
        nov: 'Noý',
        dec: 'Dek'
      }
    },
    select: {
      loading: 'Indirilýär',
      noMatch: 'Hiçzat tapylmady',
      noData: 'Hiçzat ýok',
      placeholder: 'Saýla'
    },
    cascader: {
      noMatch: 'Hiçzat tapylmady',
      loading: 'Indirilýär',
      placeholder: 'Saýlaň',
      noData: 'Hiçzat ýok'
    },
    pagination: {
      goto: 'Git',
      pagesize: '/sahypa',
      total: 'Umumy {total}',
      pageClassifier: ''
    },
    messagebox: {
      title: 'Hat',
      confirm: 'OK',
      cancel: 'Bes et',
      error: 'Ýalňyş girizme'
    },
    upload: {
      deleteTip: 'Pozmak üçin "poz" düwmä basyň',
      delete: 'Poz',
      preview: 'Gör',
      continue: 'Dowam et'
    },
    table: {
      emptyText: 'Maglumat ýok',
      confirmFilter: 'Tassykla',
      resetFilter: 'Arassala',
      clearFilter: 'Hemmesi',
      sumText: 'Jemi'
    },
    tree: {
      emptyText: 'Maglumat ýok'
    },
    transfer: {
      noMatch: 'Hiçzat tapylmady',
      noData: 'Hiçzat ýok',
      titles: ['Sanaw 1', 'Sanaw 2'],
      filterPlaceholder: 'Gözleg sözlerini giriziň',
      noCheckedFormat: '{total} sany',
      hasCheckedFormat: '{checked}/{total} saýlanan'
    },
    image: {
      error: 'FAILED' // to be translated
    },
    pageHeader: {
      title: 'Back' // to be translated
    },
    popconfirm: {
      confirmButtonText: 'Yes', // to be translated
      cancelButtonText: 'No' // to be translated
    },
    empty: {
      description: 'Maglumat ýok'
    }
  }
};