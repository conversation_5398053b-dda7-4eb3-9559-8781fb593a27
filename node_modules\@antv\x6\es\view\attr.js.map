{"version": 3, "file": "attr.js", "sourceRoot": "", "sources": ["../../src/view/attr.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,SAAS,EACT,QAAQ,EACR,GAAG,EACH,WAAW,EACX,UAAU,EACV,SAAS,GACV,MAAM,iBAAiB,CAAA;AACxB,OAAO,EAAa,KAAK,EAAE,MAAM,mBAAmB,CAAA;AACpD,OAAO,EAAE,IAAI,EAAE,MAAM,kBAAkB,CAAA;AACvC,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAA;AAG7B,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAA;AAE9B,MAAM,OAAO,WAAW;IACtB,YAAsB,IAAc;QAAd,SAAI,GAAJ,IAAI,CAAU;IAAG,CAAC;IAExC,IAAc,IAAI;QAChB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA;IACvB,CAAC;IAES,aAAa,CAAC,QAAgB;QACtC,OAAO,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAA;IAC9C,CAAC;IAES,YAAY,CACpB,IAAa,EACb,GAAsB;QAEtB,IAAI,MAAoC,CAAA;QACxC,IAAI,GAAkC,CAAA;QACtC,IAAI,MAAqC,CAAA;QACzC,IAAI,QAAuC,CAAA;QAE3C,MAAM,QAAQ,GAAoD,EAAE,CAAA;QAEpE,mDAAmD;QACnD,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YAChC,MAAM,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAA;YACrB,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;YAC3C,MAAM,OAAO,GAAG,WAAW,CAAC,IAAI,CAC9B,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,IAAI,EACT,UAAU,EACV,GAAG,EACH;gBACE,IAAI;gBACJ,KAAK,EAAE,GAAG;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CACF,CAAA;YAED,IAAI,UAAU,IAAI,OAAO,EAAE;gBACzB,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;oBAClC,IAAI,MAAM,IAAI,IAAI,EAAE;wBAClB,MAAM,GAAG,EAAE,CAAA;qBACZ;oBACD,MAAM,CAAC,UAAU,CAAC,GAAG,GAA2B,CAAA;iBACjD;qBAAM,IAAI,GAAG,KAAK,IAAI,EAAE;oBACvB,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAA;iBACpC;aACF;iBAAM;gBACL,IAAI,MAAM,IAAI,IAAI,EAAE;oBAClB,MAAM,GAAG,EAAE,CAAA;iBACZ;gBACD,MAAM,UAAU,GAAG,GAAG,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC;oBACvD,CAAC,CAAC,IAAI;oBACN,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;gBAC7B,MAAM,CAAC,UAAU,CAAC,GAAG,GAA2B,CAAA;aACjD;QACH,CAAC,CAAC,CAAA;QAEF,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,EAAE,EAAE;YACxC,MAAM,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAA;YAErB,MAAM,SAAS,GAAG,UAAgC,CAAA;YAClD,IAAI,OAAO,SAAS,CAAC,GAAG,KAAK,UAAU,EAAE;gBACvC,IAAI,GAAG,IAAI,IAAI,EAAE;oBACf,GAAG,GAAG,EAAE,CAAA;iBACT;gBACD,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAA;aAChB;YAED,MAAM,YAAY,GAAG,UAAmC,CAAA;YACxD,IAAI,OAAO,YAAY,CAAC,MAAM,KAAK,UAAU,EAAE;gBAC7C,IAAI,MAAM,IAAI,IAAI,EAAE;oBAClB,MAAM,GAAG,EAAE,CAAA;iBACZ;gBACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAA;aACnB;YAED,MAAM,cAAc,GAAG,UAAqC,CAAA;YAC5D,IAAI,OAAO,cAAc,CAAC,QAAQ,KAAK,UAAU,EAAE;gBACjD,IAAI,QAAQ,IAAI,IAAI,EAAE;oBACpB,QAAQ,GAAG,EAAE,CAAA;iBACd;gBACD,QAAQ,CAAC,IAAI,CAAC,GAAG,GAAG,CAAA;aACrB;QACH,CAAC,CAAC,CAAA;QAEF,OAAO;YACL,GAAG;YACH,MAAM;YACN,GAAG;YACH,MAAM;YACN,QAAQ;SACT,CAAA;IACH,CAAC;IAES,mBAAmB,CAC3B,iBAA6C,EAC7C,gBAA4C;QAE5C,iBAAiB,CAAC,GAAG,mCAChB,iBAAiB,CAAC,GAAG,GACrB,gBAAgB,CAAC,GAAG,CACxB,CAAA;QAED,iBAAiB,CAAC,QAAQ,mCACrB,iBAAiB,CAAC,QAAQ,GAC1B,gBAAgB,CAAC,QAAQ,CAC7B,CAAA;QAED,iBAAiB,CAAC,MAAM,mCACnB,iBAAiB,CAAC,MAAM,GACxB,gBAAgB,CAAC,MAAM,CAC3B,CAAA;QAED,8CAA8C;QAC9C,MAAM,SAAS,GACb,iBAAiB,CAAC,MAAM,IAAI,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAA;QAChE,IAAI,SAAS,IAAI,IAAI,IAAI,gBAAgB,CAAC,MAAM,EAAE;YAChD,gBAAgB,CAAC,MAAM,CAAC,SAAS,GAAG,SAAS,CAAA;SAC9C;QACD,iBAAiB,CAAC,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAA;IACpD,CAAC;IAES,SAAS,CACjB,SAAyB,EACzB,QAAiB,EACjB,aAAgD,EAChD,SAA2B;QAE3B,MAAM,KAAK,GAAc,EAAE,CAAA;QAC3B,MAAM,MAAM,GAQR,IAAI,UAAU,EAAE,CAAA;QAEpB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC1C,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAA;YACjC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;gBACnC,OAAM;aACP;YAED,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAA;YACzE,aAAa,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAA;YAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;gBAC/C,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;gBACrB,MAAM,MAAM,GAAG,SAAS,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAA;gBACxD,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;gBAC7B,IAAI,IAAI,EAAE;oBACR,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;wBACf,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;wBAChB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;wBACjB,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAA0B,CAAC,CAAA;wBAC9C,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAkB,CAAC,CAAA;qBAC1C;oBAED,MAAM,UAAU,GAAG,IAAI,CAAC,KAA4B,CAAA;oBACpD,MAAM,cAAc,GAAG,IAAI,CAAC,QAAoB,CAAA;oBAChD,IAAI,MAAM,EAAE;wBACV,gCAAgC;wBAChC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;wBACzB,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;qBAC3B;yBAAM;wBACL,oDAAoD;wBACpD,MAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,CACpC,cAAc,EACd,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACvB,CAAA;wBAED,UAAU,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE,KAAK,CAAC,CAAA;wBACtC,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;qBACvC;iBACF;qBAAM;oBACL,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;wBACf,IAAI;wBACJ,KAAK;wBACL,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACzB,KAAK,EAAE,KAAK;qBACb,CAAC,CAAA;iBACH;aACF;QACH,CAAC,CAAC,CAAA;QAEF,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACrB,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAE,CAAA;YAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,KAA4B,CAAA;YAC7C,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,WAAW,CAC1B,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAC7C,EAAE,CACH,CAAA;QACH,CAAC,CAAC,CAAA;QAEF,OAAO,MAQN,CAAA;IACH,CAAC;IAES,mBAAmB,CAC3B,IAAa,EACb,cAA0C,EAC1C,OAAkB;QAElB,MAAM,QAAQ,GAAG,cAAc,CAAC,GAAG,IAAI,EAAE,CAAA;QACzC,IAAI,SAAS,GAAG,cAAc,CAAC,MAAM,IAAI,EAAE,CAAA;QAC3C,MAAM,QAAQ,GAAG,cAAc,CAAC,GAAG,CAAA;QACnC,MAAM,aAAa,GAAG,cAAc,CAAC,QAAQ,CAAA;QAC7C,MAAM,WAAW,GAAG,cAAc,CAAC,MAAM,CAAA;QACzC,MAAM,UAAU,GAAG,GAAG,EAAE,CAAC,CAAC;YACxB,IAAI;YACJ,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE;SACzB,CAAC,CAAA;QAEF,IAAI,QAAQ,IAAI,IAAI,EAAE;YACpB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBACrC,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAA;gBAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;gBACpC,IAAI,GAAG,IAAI,IAAI,EAAE;oBACf,MAAM,GAAG,GAAG,WAAW,CAAC,IAAI,CACzB,GAA0B,CAAC,GAAG,EAC/B,IAAI,CAAC,IAAI,EACT,GAAG,EACH,UAAU,EAAE,CACb,CAAA;oBACD,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;wBAC3B,SAAS,mCACJ,SAAS,GACT,GAAG,CACP,CAAA;qBACF;yBAAM,IAAI,GAAG,IAAI,IAAI,EAAE;wBACtB,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,CAAA;qBACtB;iBACF;YACH,CAAC,CAAC,CAAA;SACH;QAED,IAAI,IAAI,YAAY,WAAW,EAAE;YAC/B,0DAA0D;YAC1D,8DAA8D;YAC9D,4CAA4C;YAC5C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;YACnC,OAAM;SACP;QAED,2CAA2C;QAC3C,MAAM,aAAa,GAAG,SAAS,CAAC,SAAS,CAAA;QACzC,MAAM,SAAS,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,aAAa,EAAE,CAAC,CAAC,CAAC,IAAI,CAAA;QAC3D,MAAM,UAAU,GAAG,GAAG,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAA;QACzD,MAAM,YAAY,GAAG,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAA;QAC1D,IAAI,aAAa,EAAE;YACjB,OAAO,SAAS,CAAC,SAAS,CAAA;YAC1B,UAAU,CAAC,CAAC,GAAG,CAAC,CAAA;YAChB,UAAU,CAAC,CAAC,GAAG,CAAC,CAAA;SACjB;QAED,IAAI,UAAU,GAAG,KAAK,CAAA;QACtB,IAAI,aAAa,IAAI,IAAI,EAAE;YACzB,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBAC1C,MAAM,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,CAAA;gBAC/B,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;gBACpC,IAAI,GAAG,IAAI,IAAI,EAAE;oBACf,MAAM,EAAE,GAAG,WAAW,CAAC,IAAI,CACxB,GAA+B,CAAC,QAAQ,EACzC,IAAI,CAAC,IAAI,EACT,GAAG,EACH,UAAU,EAAE,CACb,CAAA;oBAED,IAAI,EAAE,IAAI,IAAI,EAAE;wBACd,UAAU,GAAG,IAAI,CAAA;wBACjB,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;qBACzC;iBACF;YACH,CAAC,CAAC,CAAA;SACH;QAED,mDAAmD;QACnD,8BAA8B;QAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;QAEnC,IAAI,QAAQ,GAAG,KAAK,CAAA;QACpB,IAAI,WAAW,IAAI,IAAI,EAAE;YACvB,+BAA+B;YAC/B,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAA;YACjE,IAAI,gBAAgB,CAAC,KAAK,GAAG,CAAC,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC7D,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAA;gBAEtE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;oBACxC,MAAM,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC,CAAA;oBAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;oBACpC,IAAI,GAAG,IAAI,IAAI,EAAE;wBACf,MAAM,EAAE,GAAG,WAAW,CAAC,IAAI,CACxB,GAA6B,CAAC,MAAM,EACrC,IAAI,CAAC,IAAI,EACT,GAAG,EACH;4BACE,IAAI;4BACJ,IAAI,EAAE,IAAI,CAAC,IAAI;4BACf,IAAI,EAAE,IAAI,CAAC,IAAI;4BACf,KAAK,EAAE,QAAQ;4BACf,OAAO,EAAE,QAAQ;yBAClB,CACF,CAAA;wBAED,IAAI,EAAE,IAAI,IAAI,EAAE;4BACd,QAAQ,GAAG,IAAI,CAAA;4BACf,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;yBACzC;qBACF;gBACH,CAAC,CAAC,CAAA;aACH;SACF;QAED,IAAI,aAAa,IAAI,IAAI,IAAI,UAAU,IAAI,QAAQ,EAAE;YACnD,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;YACrB,UAAU,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAA;YAC7B,UAAU,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAA;YAC7B,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,GAAG,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC,CAAA;SACxE;IACH,CAAC;IAED,MAAM,CACJ,QAAiB,EACjB,KAAqB,EACrB,OAAkC;QAElC,MAAM,aAAa,GAAsC,EAAE,CAAA;QAC3D,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAC/B,OAAO,CAAC,KAAK,IAAI,KAAK,EACtB,QAAQ,EACR,aAAa,EACb,OAAO,CAAC,SAAS,CAClB,CAAA;QAED,uDAAuD;QACvD,iDAAiD;QACjD,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK;YACjC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,OAAO,CAAC,SAAS,CAAC;YACnE,CAAC,CAAC,UAAU,CAAA;QAEd,MAAM,YAAY,GAKZ,EAAE,CAAA;QAER,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;YACvB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;YACtB,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAA;YAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;YACpD,IACE,SAAS,CAAC,GAAG,IAAI,IAAI;gBACrB,SAAS,CAAC,QAAQ,IAAI,IAAI;gBAC1B,SAAS,CAAC,MAAM,IAAI,IAAI,EACxB;gBACA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;aAC3C;iBAAM;gBACL,MAAM,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;gBACpC,MAAM,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAA;gBAC7C,MAAM,WAAW,GACf,YAAY,IAAI,SAAS,CAAC,GAAG,IAAI,IAAI;oBACnC,CAAC,CAAC,YAAY,CAAC,GAAG;oBAClB,CAAC,CAAC,SAAS,CAAC,GAAG,CAAA;gBAEnB,IAAI,OAAuB,CAAA;gBAC3B,IAAI,WAAW,EAAE;oBACf,OAAO,GAAG,CAAC,aAAa,CAAC,WAAqB,CAAC;wBAC7C,IAAI,CAAC,IAAI,CAAC,IAAI,CACZ,WAAqB,EACrB,QAAQ,EACR,OAAO,CAAC,SAAS,CAClB,CAAC,CAAC,CAAC,CAAC,CAAA;oBACP,IAAI,CAAC,OAAO,EAAE;wBACZ,MAAM,IAAI,KAAK,CAAC,IAAI,WAAW,6BAA6B,CAAC,CAAA;qBAC9D;iBACF;qBAAM;oBACL,OAAO,GAAG,IAAI,CAAA;iBACf;gBAED,MAAM,IAAI,GAAG;oBACX,IAAI;oBACJ,OAAO;oBACP,UAAU,EAAE,YAAY;oBACxB,mBAAmB,EAAE,SAAS;iBAC/B,CAAA;gBAED,qEAAqE;gBACrE,oDAAoD;gBACpD,MAAM,KAAK,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,CAAA;gBACrE,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;oBACd,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,CAAA;iBACpC;qBAAM;oBACL,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;iBACxB;aACF;QACH,CAAC,CAAC,CAAA;QAEF,MAAM,SAAS,GAAmC,IAAI,UAAU,EAAE,CAAA;QAClE,IAAI,eAA0B,CAAA;QAC9B,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;YACtB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;YAE5B,IAAI,gBAAuC,CAAA;YAC3C,MAAM,kBAAkB,GACtB,OAAO,IAAI,IAAI;gBACf,OAAO,CAAC,aAAa,IAAI,IAAI;gBAC7B,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,CAAA;YAE9C,+DAA+D;YAC/D,8CAA8C;YAC9C,IAAI,OAAO,EAAE;gBACX,gBAAgB,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;aAC1C;YAED,IAAI,CAAC,gBAAgB,EAAE;gBACrB,MAAM,MAAM,GAAG,CACb,kBAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,aAAc,CAAC,CAAC,CAAC,QAAQ,CACzC,CAAA;gBAEf,gBAAgB,GAAG,OAAO;oBACxB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAqB,EAAE,EAAE,MAAM,EAAE,CAAC;oBACjD,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAA;gBAEpB,IAAI,OAAO,EAAE;oBACX,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,gBAAiB,CAAC,CAAA;iBAC1C;aACF;YAED,IAAI,cAAc,CAAA;YAClB,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,UAAU,EAAE;gBACpC,kEAAkE;gBAClE,gEAAgE;gBAChE,oEAAoE;gBACpE,wCAAwC;gBACxC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;gBACzD,IAAI,CAAC,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAA;aACnE;iBAAM;gBACL,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAA;aAC1C;YAED,IAAI,OAAO,GAAG,gBAAiB,CAAA;YAC/B,IACE,kBAAkB;gBAClB,OAAO,CAAC,aAAa,IAAI,IAAI;gBAC7B,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,EACrC;gBACA,iEAAiE;gBACjE,8DAA8D;gBAC9D,+BAA+B;gBAC/B,IAAI,CAAC,eAAe,EAAE;oBACpB,eAAe,GAAG,GAAG,CAAC,uBAAuB,CAC3C,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,WAAW,CAAC,CAC7C,CAAA;iBACF;gBACD,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,gBAAiB,EAAE,eAAe,CAAC,CAAA;aACtE;YAED,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,cAAc,EAAE,OAAO,CAAC,CAAA;QACzD,CAAC,CAAC,CAAA;IACJ,CAAC;CACF"}