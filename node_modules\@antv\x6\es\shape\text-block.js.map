{"version": 3, "file": "text-block.js", "sourceRoot": "", "sources": ["../../src/shape/text-block.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAA;AACvE,OAAO,EAAE,IAAI,EAAE,MAAM,aAAa,CAAA;AAClC,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAA;AAE7B,MAAM,CAAC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;IACnC,KAAK,EAAE,YAAY;IACnB,MAAM,EAAE;QACN;YACE,OAAO,EAAE,MAAM;YACf,QAAQ,EAAE,MAAM;SACjB;QACD,QAAQ,CAAC,qBAAqB;YAC5B,CAAC,CAAC;gBACE,OAAO,EAAE,eAAe;gBACxB,QAAQ,EAAE,eAAe;gBACzB,QAAQ,EAAE;oBACR;wBACE,OAAO,EAAE,KAAK;wBACd,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK;wBAChB,QAAQ,EAAE,OAAO;wBACjB,KAAK,EAAE;4BACL,KAAK,EAAE,MAAM;4BACb,MAAM,EAAE,MAAM;4BACd,QAAQ,EAAE,QAAQ;4BAClB,eAAe,EAAE,aAAa;4BAC9B,SAAS,EAAE,QAAQ;4BACnB,MAAM,EAAE,CAAC;4BACT,OAAO,EAAE,SAAS;4BAClB,SAAS,EAAE,YAAY;4BACvB,OAAO,EAAE,MAAM;4BACf,UAAU,EAAE,QAAQ;4BACpB,cAAc,EAAE,QAAQ;yBACzB;qBACF;iBACF;aACF;YACH,CAAC,CAAC;gBACE,OAAO,EAAE,MAAM;gBACf,QAAQ,EAAE,OAAO;gBACjB,KAAK,EAAE;oBACL,UAAU,EAAE,QAAQ;iBACrB;aACF;KACN;IACD,KAAK,EAAE;QACL,IAAI,kCACC,IAAI,CAAC,QAAQ,KAChB,QAAQ,EAAE,MAAM,EAChB,SAAS,EAAE,MAAM,GAClB;QACD,aAAa,EAAE;YACb,QAAQ,EAAE,MAAM;YAChB,SAAS,EAAE,MAAM;SAClB;QACD,KAAK,EAAE;YACL,KAAK,EAAE;gBACL,QAAQ,EAAE,EAAE;aACb;SACF;KACF;IACD,SAAS,CAAC,QAAQ;QAChB,MAAM,EAAE,IAAI,KAAgB,QAAQ,EAAnB,MAAM,UAAK,QAAQ,EAA9B,QAAmB,CAAW,CAAA;QACpC,IAAI,IAAI,EAAE;YACR,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,kBAAkB,EAAE,IAAI,CAAC,CAAA;SACtD;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IACD,SAAS,EAAE;QACT,IAAI,EAAE;YACJ,GAAG,CAAC,IAAY,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE;gBACpD,IAAI,IAAI,YAAY,WAAW,EAAE;oBAC/B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;iBACxB;qBAAM;oBACL,oBAAoB;oBACpB,MAAM,KAAK,GAAI,KAAK,CAAC,KAA0B,IAAI,EAAE,CAAA;oBACrD,MAAM,SAAS,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAA;oBACrD,MAAM,SAAS,mBACb,kBAAkB,EAAE,QAAQ,IACzB,KAAK,CACT,CAAA;oBAED,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAA8B,CAAA;oBAC5D,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE;wBAC9C,IAAI;wBACJ,IAAI;wBACJ,IAAI;wBACJ,OAAO;wBACP,KAAK,EAAE,SAAS;qBACjB,CAAC,CAAA;oBAEF,OAAO,EAAE,IAAI,EAAG,KAAK,CAAC,KAAgB,IAAI,IAAI,EAAE,CAAA;iBACjD;YACH,CAAC;YACD,QAAQ,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;gBAC9B,IAAI,IAAI,YAAY,UAAU,EAAE;oBAC9B,OAAO,OAAO,CAAC,SAAS,EAAE,CAAA;iBAC3B;YACH,CAAC;SACF;KACF;CACF,CAAC,CAAA"}