{"version": 3, "file": "renderer.js", "sourceRoot": "", "sources": ["../../src/renderer/renderer.ts"], "names": [], "mappings": ";;;;;;AAAA,sDAAsD;AACtD,OAAO,EAAS,SAAS,EAAE,MAAM,mBAAmB,CAAA;AACpD,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAA;AACpC,OAAO,EAAE,IAAI,EAAE,MAAM,UAAU,CAAA;AAC/B,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAA;AAEvC,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAA;AAE9B,MAAM,OAAO,QAAS,SAAQ,IAAI;IAAlC;;QACmB,aAAQ,GAAc,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAwHlE,CAAC;IAtHC,iBAAiB,CAAC,IAAc,EAAE,IAAY,EAAE,UAAe,EAAE;QAC/D,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAA;IACtD,CAAC;IAED,aAAa,CAAC,IAAc;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;IAC1C,CAAC;IAED,aAAa,CAAC,IAAgB;QAC5B,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;IACnC,CAAC;IAED,cAAc,CAAC,IAAyC;QACtD,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,OAAO,IAAI,CAAA;SACZ;QACD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAA;QACxC,MAAM,MAAM,GACV,OAAO,IAAI,KAAK,QAAQ;YACtB,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC;YAC/B,CAAC,CAAC,IAAI,YAAY,OAAO;gBACzB,CAAC,CAAC,IAAI;gBACN,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAEb,IAAI,MAAM,EAAE;YACV,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,MAAM,CAAC,CAAA;YAC3D,IAAI,EAAE,EAAE;gBACN,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAA;gBACjC,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE;oBACb,OAAO,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAA;iBACtB;aACF;SACF;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAID,cAAc,CACZ,IAA+C;QAE/C,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,OAAO,IAAI,CAAA;SACZ;QACD,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAA;QAC7C,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAA;QACjC,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE;YACb,OAAO,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAA;SACtB;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB,CAAC,CAAkB;QACnC,MAAM,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;QAC9B,OAAO,IAAI,CAAC,KAAK;aACd,QAAQ,EAAE;aACV,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;aACxC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;YACf,IAAI,IAAI,IAAI,IAAI,EAAE;gBAChB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAuB,EAAE;oBAChD,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK;iBACxB,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;aACtB;YACD,OAAO,KAAK,CAAA;QACd,CAAC,CAAe,CAAA;IACpB,CAAC;IAED,sBAAsB,CAAC,CAAkB,EAAE,SAAS,GAAG,CAAC;QACtD,OAAO,IAAI,CAAC,KAAK;aACd,QAAQ,EAAE;aACV,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;aACxC,MAAM,CAAC,CAAC,IAAc,EAAE,EAAE;YACzB,IAAI,IAAI,IAAI,IAAI,EAAE;gBAChB,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAA;gBACrC,IAAI,KAAK,EAAE;oBACT,OAAO,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,SAAS,CAAA;iBACtC;aACF;YACD,OAAO,KAAK,CAAA;QACd,CAAC,CAAe,CAAA;IACpB,CAAC;IAED,eAAe,CACb,IAA6B,EAC7B,UAAoD,EAAE;QAEtD,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACnC,OAAO,IAAI,CAAC,KAAK;aACd,QAAQ,EAAE;aACV,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;aACxC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;YACf,IAAI,IAAI,EAAE;gBACR,IAAI,OAAO,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;oBAC1C,OAAO,KAAK,CAAA;iBACb;gBAED,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAuB,EAAE;oBACtD,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK;iBACxB,CAAC,CAAA;gBACF,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE;oBACpB,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;iBACnB;qBAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;oBAC5B,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;iBACnB;gBACD,OAAO,OAAO,CAAC,MAAM;oBACnB,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;oBACzB,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;aACnC;YACD,OAAO,KAAK,CAAA;QACd,CAAC,CAAe,CAAA;IACpB,CAAC;IAGD,OAAO;QACL,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAA;IACzB,CAAC;CACF;AAHC;IADC,IAAI,CAAC,OAAO,EAAE;uCAGd"}