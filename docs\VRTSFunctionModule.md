# THE VRTS备份软件功能模块

## 登录WEB管理界面

- 用户登录界面

    ![THE VRTS备份软件功能模块](/dist/img/2-3-1.png)

## 	进入管理界面首页

- 管理界面首页，主要有备份任务量、备份成功数量、备份失败任务量、备份数据量、服务器统计、设备统计、告警记录等；

- 在管理界面首页，可以看到当前登录用户、密码有效期，还可有注销和锁定当前用户；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-2.png)

## 	用户管理

- 在左侧导航栏点击“用户管理”；

- 在用户管理界面，主要有新建用户、删除用户等功能；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-3-01.png)

-	在用户管理界面点击“新建用户”图标；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-3-02.png)

-	在新建用户界面填写“用户名”、“IP地址”、“密码有效期”、选择客户端，点击确定即可创建用户；

-	在用户用户管理界面选中要删除的用户，点击右上侧的删除用户即可删除用户；

-	确认是否删除该用户；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-3-03.png)

    ![THE VRTS备份软件功能模块](/dist/img/2-3-3-04.png)

## 	角色管理

-	在左侧导航栏点击角色管理，在角色管理界面包含角色信息、新建角色；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-4-01.png)
 
-	在角色管理界面，点击新建角色；

-	在新建名称栏中，填写角色名称和描述，点击确认；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-4-02.png)

## 	任务监控

- 点击左侧任务监控导航栏；

- 在任务监控界面是备份和恢复任务的详细信息；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-5.png)

## 客户端

-	点击左侧客户端导航栏；

-	在客户端任务栏中是客户端的详细信息，包含客户端的机器名、操作系统类型、IP地址、软件版本号、状态、操作；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-6.png)

## 日志管理

-	点击左侧日志管理导航栏；

-	进入日志管理界面的系统日志界面；

-	在系统日志界面包含级别、时间、用户、来源、描述、详情等信息；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-7-01.png)

-	在日志管理界面点击系统事件；

-	进入系统事件界面，在该界面包含系统事件的级别、时间、来源、描述、详情等；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-7-02.png)

## 介质服务

介质服务是对介质服务器、磁盘设备、磁带库设备的新建和删除；

### 介质服务器

-	点击左侧介质服务导航栏；

-	进入介质服务器界面，点击下角的新建介质服务器；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-8-01.png)

-	进入添加介质服务器界面；

-	在添加介质服务器界面，填写介质服务器名称、选择存储服务器，点击保存；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-8-02.png)

-	保存完成后，在介质服务器界面将会看到新建的介质服务器；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-8-03.png)

### 磁盘设备

- 	在介质服务界面，点击磁盘设备；

- 	进入磁盘设备界面，点击下角新建磁盘；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-8-04.png)

-	进入添加磁盘设备界面；

-	在该界面，填写设备名称、设备类型、选择介质服务器、设备路径等，点击保存；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-8-05.png)

- 	保存完成后，在磁盘设备界面将会看到新建的磁盘设备；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-8-06.png)

### 磁带库设备

- 在介质服务界面，点击磁带库设备；

- 进入磁带库设备设备界面，点击下角新建磁带库；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-8-07.png)

- 	进入新建磁带库界面；

- 	在该界面，填写设备名称、选择介质服务器和机械臂，点击保存；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-8-08.png)

-	保存完成后，在磁带库设备界面将会看到新建的磁带库；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-8-09.png)

## 介质管理    

介质管理是对介质池和介质的管理，包括新建介质池、删除介质池、修改介质池名称，以及对介质的绑定、删除、重命名、回收等；

### 介质池

- 点击左侧介质管理，进入介质池界面；

- 在介质池界面，点击下角的新建介质池；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-9-01.png)

-	进入新建介质池界面；

-	在该界面，填写介质池名称、保留周期、覆盖周期，点击保存；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-9-02.png)


-	保存完成后，在介质池界面将会看到新建的介质池；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-9-03.png)


-	在介质池界面，在相应介质池的操作任务栏，点击删除；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-9-04.png)

-	点击删除后，进入删除介质池界面，点击确定，即可删除该介质池；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-9-05.png)


### 介质

-	在介质管理界面点击介质，进入介质界面；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-9-06.png)


-	在介质界面，在相应的介质后面的操作任务栏，点击绑定介质图标进入绑定介质池界面；

-	在绑定介质池界面，选择要绑定的介质池，点击保存；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-9-07.png)


-	在介质界面，在相应的介质后面的操作任务栏，点击删除介质图标进入删除介质界面；

-	在删除介质界面，点击确定，即可删除该介质；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-9-08.png)


-	在介质界面，在相应的介质后面的操作任务栏，点击介质重命名图标进入重命名介质界面；

-	在重命名介质界面，选择要重命名的介质，点击保存；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-9-09.png)

-	在介质界面，在相应的介质后面的操作任务栏，点击回收介质池图标进入回收介质池界面；

-	在回收介质池界面，选择要回收的介质池，点击确定；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-9-10.png)

## 策略管理

介质管理是对策略的创建和导出，主要能够新建文件备份、Oracle备份、MySQL备份、SQL server备份、VMware备份、系统备份、DB2备份等策略，相关备份策略详情在第四章创建备份策略

### 新建策略

-	点击左侧策略管理，进入策略管理界面；

-	在策略管理界面，点击右边的新建新建策略，即可创建相应的备份策略；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-10-01.png)

### 	导出报表数据

-	在策略管理界面，点击右边的导出报表数据，即可导出相关的策略报表；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-10-02.png)


## 恢复管理

恢复管理主要对文件备份、MySQL备份、SQL server备份、VMware备份、系统备份等进行恢复


-	点击左侧恢复管理，进入恢复管理界面；

-	在恢复管理界面选择客户端名称、策略类型、点击查询；

-	在选择相关要备份的策略名称，在下方的“选择需要恢复的数据”中选择相应的数据，点击恢复即可恢复；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-11.png)

## 报表管理


-	点击左侧报表管理，进入报表管理界面的“运行记录报表”；
-	在“运行记录报表”界面，包含运行记录的ID、任务类型、策略名称、客户端、策略类型、调度类型、开始时间、结束时间、备份大小、速率、设备、状态、操作等信息；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-12-01.png)

### 报表管理

-	在报表管理里面，点击设备报表，即可进入设备报表
；
-	在“设备报表”界面，包含设备的名称、类型、路径、最大任务数、介质服务器、状态、备份任务数、总备份数据量等信息；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-12-02.png)

### 介质报表

-	在报表管理里面，点击介质报表，即可进入介质报表；

-	在“介质报表”界面，包含介质的名称、条形码、已使用容量、介质池、介质状态、在线状态、镜像数量、最后写入时间、最后回收时间等信息；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-12-03.png)

## 系统设置

### 系统安全设置

-	点击左侧系统设置，进入系统设置界面的“系统安全设置”；

-	修改系统安全等级，在安全等级中分位高、中、低；
    ![THE VRTS备份软件功能模块](/dist/img/2-3-13-01.png)
 
-	设置密码，先输入原密码，再输入新密码，重新输入密码，点击确认修改；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-13-02.png)
 
### 系统参数设置
-	在系统设置界面点击“系统参数设置”；
-	修改调度间隔时间单位以分记时；
-	修改设备状态检查间隔时间单位以分记时；
-	修改介质回收间隔时间单位以分记时；
-	修改客户端最大并行任务数单位以个；
-	修改系统日志保留时最长时间单位以天记时；
-	修改控制台保持时间单位以分钟记时；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-13-03.png)
 
### 邮件设置：
-	在系统设置界面，点击“邮件设置”；

-	在邮件设置界面可以设置用户的发送邮件账号，填写邮件账号、EMAL地址、邮件密码、SMTP服务器地址、SMTP服务器端口号、点击添加就可添加该邮件账号；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-13-04.png)

 
### 导出系统设置：
-	在系统设置界面，点击“导出系统设置”；

-	在导出系统设置界面可以对用户、角色、客户端、设备、介质、介质服务器、介质池、备份策略、系统参数等数据进行导入导出；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-13-05.png)
 
### 授权信息：
-	在系统设置界面，点击“授权信息”；

-	在授权信息首页，时授权的基本信息；

-	在基本信息界面，包括，授权方式、选择授权类型、系统注册码等功能；
 
    ![THE VRTS备份软件功能模块](/dist/img/2-3-13-06.png)

-	在授权方式功能模块，包含功能授权、容量授权两种模式；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-13-07.png)
 
-	在选择授权类型模块，包含测试授权和商用授权两种类型；
 
    ![THE VRTS备份软件功能模块](/dist/img/2-3-13-08.png)

-	在系统注册码模块，显示该系统的注册码；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-13-09.png)
 
-	在该系统直查备份系统可以打开文件备份和重复数据删除功能，如有需要，只需勾选该功能；
 
    ![THE VRTS备份软件功能模块](/dist/img/2-3-13-10.png)

-	授权该备份系统；

-	在系统设置界面点击授权系统，然后在选择授权文件后面点击浏览，选择授权文件即可授权该系统；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-13-11.png)

 
-	在授权信息点击存储服务授权；

-	在存储服务授权界面可以看到该系统支持的操作系统版本、授权数量；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-13-12.png)
 
-	在授权信息点击客户端授权；

-	在客户端界面可以看到该系统支持的操作系统版本、授权数量；

    ![THE VRTS备份软件功能模块](/dist/img/2-3-13-13.png)
 
-	在授权信息点击备份代理授权；

-	在备份代理授权界面可以看到该系统支持的代理类型、系统类型、授权数量；
 
    ![THE VRTS备份软件功能模块](/dist/img/2-3-13-14.png)


