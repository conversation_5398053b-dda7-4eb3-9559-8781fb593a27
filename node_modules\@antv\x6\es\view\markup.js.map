{"version": 3, "file": "markup.js", "sourceRoot": "", "sources": ["../../src/view/markup.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,EAAqB,MAAM,iBAAiB,CAAA;AAoD3E,2BAA2B;AAC3B,MAAM,KAAW,MAAM,CAiMtB;AAjMD,WAAiB,MAAM;IACrB,SAAgB,YAAY,CAAC,MAAwB;QACnD,OAAO,MAAM,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;IAClD,CAAC;IAFe,mBAAY,eAE3B,CAAA;IAED,SAAgB,cAAc,CAAC,MAAwB;QACrD,OAAO,MAAM,IAAI,IAAI,IAAI,OAAO,MAAM,KAAK,QAAQ,CAAA;IACrD,CAAC;IAFe,qBAAc,iBAE7B,CAAA;IAED,SAAgB,KAAK,CAAC,MAAwB;QAC5C,OAAO,MAAM,IAAI,IAAI,IAAI,cAAc,CAAC,MAAM,CAAC;YAC7C,CAAC,CAAC,MAAM;YACR,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;IACjC,CAAC;IAJe,YAAK,QAIpB,CAAA;IAED;;OAEG;IACH,SAAgB,QAAQ,CAAC,MAAc;QACrC,OAAO,GAAG,MAAM,EAAE;aACf,IAAI,EAAE;aACN,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC;aACxB,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;IAC5B,CAAC;IALe,eAAQ,WAKvB,CAAA;IAED,SAAgB,eAAe,CAC7B,MAAiC,EACjC,UAA2B,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE;QAE7C,MAAM,QAAQ,GAAG,QAAQ,CAAC,sBAAsB,EAAE,CAAA;QAClD,MAAM,MAAM,GAAwB,EAAE,CAAA;QACtC,MAAM,SAAS,GAAc,EAAE,CAAA;QAE/B,MAAM,KAAK,GAIL;YACJ;gBACE,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;gBACjD,MAAM,EAAE,QAAQ;gBAChB,EAAE,EAAE,OAAO,CAAC,EAAE;aACf;SACF,CAAA;QAED,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,EAAG,CAAA;YACzB,IAAI,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC,GAAG,CAAA;YAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAA;YAC3B,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAA;YAE9B,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBACzB,UAAU;gBACV,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAA;gBAC9B,IAAI,CAAC,OAAO,EAAE;oBACZ,MAAM,IAAI,SAAS,CAAC,iBAAiB,CAAC,CAAA;iBACvC;gBAED,KAAK;gBACL,IAAI,MAAM,CAAC,EAAE,EAAE;oBACb,EAAE,GAAG,MAAM,CAAC,EAAE,CAAA;iBACf;gBAED,MAAM,IAAI,GAAG,EAAE;oBACb,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC,OAAO,EAAE,EAAE,CAAC;oBAClC,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;gBAE9B,QAAQ;gBACR,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;gBAC1B,IAAI,KAAK,EAAE;oBACT,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAA;iBAC1C;gBAED,QAAQ;gBACR,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;gBAC1B,IAAI,KAAK,EAAE;oBACT,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;iBACrB;gBAED,YAAY;gBACZ,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAA;gBAClC,IAAI,SAAS,IAAI,IAAI,EAAE;oBACrB,IAAI,CAAC,YAAY,CACf,OAAO,EACP,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAC3D,CAAA;iBACF;gBAED,cAAc;gBACd,IAAI,MAAM,CAAC,WAAW,EAAE;oBACtB,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAA;iBACtC;gBAED,WAAW;gBACX,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAA;gBAChC,IAAI,QAAQ,IAAI,IAAI,EAAE;oBACpB,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE;wBACvB,MAAM,IAAI,SAAS,CAAC,yBAAyB,CAAC,CAAA;qBAC/C;oBAED,SAAS,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAA;iBAC3B;gBAED,QAAQ;gBACR,IAAI,MAAM,CAAC,aAAa,EAAE;oBACxB,IAAI,UAAU,GAAG,MAAM,CAAC,aAAa,CAAA;oBACrC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;wBAC9B,UAAU,GAAG,CAAC,UAAU,CAAC,CAAA;qBAC1B;oBAED,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;wBAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;4BACjB,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAA;yBAClB;wBACD,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;oBACzB,CAAC,CAAC,CAAA;iBACH;gBAED,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;gBAE5B,WAAW;gBACX,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAA;gBAChC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBAC3B,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAA;iBACnD;YACH,CAAC,CAAC,CAAA;SACH;QAED,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;YACxC,IAAI,SAAS,CAAC,SAAS,CAAC,EAAE;gBACxB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;aAC5C;YACD,SAAS,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;QAC1C,CAAC,CAAC,CAAA;QAEF,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,CAAA;IACxC,CAAC;IA/Ge,sBAAe,kBA+G9B,CAAA;IAED,SAAS,eAAe,CAAC,UAAmB;QAC1C,OAAO,UAAU,YAAY,UAAU;YACrC,CAAC,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC;YAC3B,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;IAC9B,CAAC;IAED,SAAgB,YAAY,CAAC,MAAc;QAIzC,IAAI,cAAc,CAAC,MAAM,CAAC,EAAE;YAC1B,MAAM,KAAK,GAAG,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;YAC1C,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAA;YAE1B,IAAI,KAAK,KAAK,CAAC,EAAE;gBACf,OAAO;oBACL,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAe;iBAC/B,CAAA;aACF;YAED,IAAI,KAAK,GAAG,CAAC,EAAE;gBACb,MAAM,IAAI,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;gBAC3C,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;oBACrB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAC7B,CAAC,CAAC,CAAA;gBAEF,OAAO,EAAE,IAAI,EAAE,CAAA;aAChB;YAED,OAAO,EAAE,CAAA;SACV;QAED,MAAM,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAA;QACtC,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAA;QAChC,IAAI,IAAI,GAAmB,IAAI,CAAA;QAC/B,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAClC,IAAI,GAAG,eAAe,CAAC,QAAQ,CAAC,UAAqB,CAAC,CAAA;YACtD,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAA;SAC3B;aAAM;YACL,IAAI,GAAG,QAAQ,CAAC,UAAqB,CAAA;SACtC;QAED,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,CAAA;IAC9C,CAAC;IArCe,mBAAY,eAqC3B,CAAA;IAED,SAAgB,sBAAsB,CAAC,MAAc;QACnD,MAAM,QAAQ,GAAG,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;QAC7C,MAAM,QAAQ,GAAG,QAAQ,CAAC,sBAAsB,EAAE,CAAA;QAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;YAClD,MAAM,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;YACrC,QAAQ,CAAC,WAAW,CAAC,YAAY,CAAC,CAAA;SACnC;QAED,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,EAAE,EAAE,CAAA;IACpC,CAAC;IATe,6BAAsB,yBASrC,CAAA;AACH,CAAC,EAjMgB,MAAM,KAAN,MAAM,QAiMtB;AAED,2BAA2B;AAC3B,WAAiB,MAAM;IACrB,SAAgB,WAAW,CACzB,IAAa,EACb,IAAa,EACb,IAAa;QAEb,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,IAAI,QAAQ,CAAA;YACZ,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAA;YAE1C,IAAI,IAAI,KAAK,IAAI,EAAE;gBACjB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;oBAC5B,QAAQ,GAAG,KAAK,OAAO,MAAM,IAAI,EAAE,CAAA;iBACpC;qBAAM;oBACL,QAAQ,GAAG,KAAK,OAAO,EAAE,CAAA;iBAC1B;gBACD,OAAO,QAAQ,CAAA;aAChB;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAA;YAC9B,IAAI,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC1C,MAAM,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBAC/B,QAAQ,GAAG,GAAG,OAAO,cAAc,GAAG,GAAG,CAAA;aAC1C;iBAAM;gBACL,QAAQ,GAAG,OAAO,CAAA;aACnB;YAED,IAAI,IAAI,EAAE;gBACR,QAAQ,IAAI,MAAM,IAAI,EAAE,CAAA;aACzB;YAED,OAAO,WAAW,CAAC,IAAI,CAAC,UAAqB,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAA;SAC/D;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAlCe,kBAAW,cAkC1B,CAAA;AACH,CAAC,EApCgB,MAAM,KAAN,MAAM,QAoCtB;AAED,2BAA2B;AAC3B,WAAiB,MAAM;IACrB,SAAgB,sBAAsB;QACpC,OAAO,GAAG,CAAA;IACZ,CAAC;IAFe,6BAAsB,yBAErC,CAAA;IAED,SAAgB,aAAa;QAC3B,OAAO;YACL,OAAO,EAAE,QAAQ;YACjB,QAAQ,EAAE,QAAQ;YAClB,KAAK,EAAE;gBACL,CAAC,EAAE,EAAE;gBACL,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,SAAS;aAClB;SACF,CAAA;IACH,CAAC;IAVe,oBAAa,gBAU5B,CAAA;IAED,SAAgB,kBAAkB;QAChC,OAAO;YACL,OAAO,EAAE,MAAM;YACf,QAAQ,EAAE,MAAM;YAChB,KAAK,EAAE;gBACL,IAAI,EAAE,SAAS;aAChB;SACF,CAAA;IACH,CAAC;IARe,yBAAkB,qBAQjC,CAAA;AACH,CAAC,EA1BgB,MAAM,KAAN,MAAM,QA0BtB;AAED,2BAA2B;AAC3B,WAAiB,MAAM;IACrB,SAAgB,aAAa;QAC3B,OAAO;YACL;gBACE,OAAO,EAAE,MAAM;gBACf,QAAQ,EAAE,MAAM;gBAChB,aAAa,EAAE,OAAO;gBACtB,KAAK,EAAE;oBACL,IAAI,EAAE,MAAM;oBACZ,MAAM,EAAE,SAAS;oBACjB,MAAM,EAAE,aAAa;oBACrB,aAAa,EAAE,OAAO;iBACvB;aACF;YACD;gBACE,OAAO,EAAE,MAAM;gBACf,QAAQ,EAAE,MAAM;gBAChB,aAAa,EAAE,OAAO;gBACtB,KAAK,EAAE;oBACL,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,MAAM;iBACtB;aACF;SACF,CAAA;IACH,CAAC;IAvBe,oBAAa,gBAuB5B,CAAA;AACH,CAAC,EAzBgB,MAAM,KAAN,MAAM,QAyBtB;AAED,2BAA2B;AAC3B,WAAiB,MAAM;IACrB,SAAgB,sBAAsB,CAAC,IAAI,GAAG,KAAK;QACjD,OAAO;YACL,OAAO,EAAE,eAAe;YACxB,QAAQ,EAAE,IAAI;YACd,QAAQ,EAAE;gBACR;oBACE,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK;oBAChB,OAAO,EAAE,MAAM;oBACf,QAAQ,EAAE,QAAQ;oBAClB,KAAK,EAAE;wBACL,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK;qBACpB;oBACD,KAAK,EAAE;wBACL,KAAK,EAAE,MAAM;wBACb,MAAM,EAAE,MAAM;wBACd,UAAU,EAAE,aAAa;qBAC1B;oBACD,QAAQ,EAAE,IAAI;wBACZ,CAAC,CAAC,EAAE;wBACJ,CAAC,CAAC;4BACE;gCACE,OAAO,EAAE,KAAK;gCACd,QAAQ,EAAE,WAAW;gCACrB,KAAK,EAAE;oCACL,KAAK,EAAE,MAAM;oCACb,MAAM,EAAE,MAAM;iCACf;6BACF;yBACF;iBACN;aACF;SACF,CAAA;IACH,CAAC;IAhCe,6BAAsB,yBAgCrC,CAAA;AACH,CAAC,EAlCgB,MAAM,KAAN,MAAM,QAkCtB"}