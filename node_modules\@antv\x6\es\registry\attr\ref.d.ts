import { Attr } from './index';
export declare const ref: Attr.Definition;
export declare const refX: Attr.Definition;
export declare const refY: Attr.Definition;
export declare const refDx: Attr.Definition;
export declare const refDy: Attr.Definition;
export declare const refWidth: Attr.Definition;
export declare const refHeight: Attr.Definition;
export declare const refRx: Attr.Definition;
export declare const refRy: Attr.Definition;
export declare const refRInscribed: Attr.Definition;
export declare const refRCircumscribed: Attr.Definition;
export declare const refCx: Attr.Definition;
export declare const refCy: Attr.Definition;
export declare const refDResetOffset: Attr.Definition;
export declare const refDKeepOffset: Attr.Definition;
export declare const refPointsResetOffset: Attr.Definition;
export declare const refPointsKeepOffset: Attr.Definition;
export declare const refR: Attr.SetDefinition;
export declare const refD: Attr.SetDefinition;
export declare const refPoints: Attr.SetDefinition;
export declare const refX2: Attr.PositionDefinition;
export declare const refY2: Attr.PositionDefinition;
export declare const refWidth2: Attr.SetDefinition;
export declare const refHeight2: Attr.SetDefinition;
