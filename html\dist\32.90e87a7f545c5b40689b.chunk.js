webpackJsonp([32],{

/***/ 2147:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});
exports.getPlaceonfileData = exports.getVolpools = exports.getZvolumeTableData = exports.getZvolumeRowData = exports.getZvolumeTree = exports.unmountFun = exports.copyFun = exports.getDeviceList = exports.getNoFileTree = exports.getFileTree = undefined;

var _request = __webpack_require__(316);

var _request2 = _interopRequireDefault(_request);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

var getFileTree = exports.getFileTree = function getFileTree(data) {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/zvolume/browse/?device=' + data.dev_id + '&path=' + data.dev_name + '/' + data.parent + '@' + data.names + '/' + data.path
	});
};
var getNoFileTree = exports.getNoFileTree = function getNoFileTree(data) {
	return (0, _request2.default)({
		method: 'GET',
		url: 'rest-ful/v3.0/zvolume/browse/' + data.id + '?volume=' + data.volume_id + '&path=' + data.path
	});
};

var getDeviceList = exports.getDeviceList = function getDeviceList() {
	return (0, _request2.default)({
		method: 'GET',
		url: 'rest-ful/v3.0/devices?cdm=1'
	});
};
var copyFun = exports.copyFun = function copyFun(data) {
	return (0, _request2.default)({
		method: 'POST',
		url: 'rest-ful/v3.0/zvolume/copy',
		data: data
	});
};
var unmountFun = exports.unmountFun = function unmountFun(data) {
	return (0, _request2.default)({
		method: 'POST',
		url: 'rest-ful/v3.0/zvolume/unmount',
		data: data
	});
};

var getZvolumeTree = exports.getZvolumeTree = function getZvolumeTree() {
	return (0, _request2.default)({
		method: 'GET',
		url: 'rest-ful/v3.0/zvolume/buildzvolumetree'
	});
};
var getZvolumeRowData = exports.getZvolumeRowData = function getZvolumeRowData(rowData) {
	return (0, _request2.default)({
		method: 'GET',
		url: 'rest-ful/v3.0/zvolume/volumelist/' + rowData.policy
	});
};

var getZvolumeTableData = exports.getZvolumeTableData = function getZvolumeTableData(rowData) {
	return (0, _request2.default)({
		method: 'GET',
		url: 'rest-ful/v3.0/zvolume/snapshot/list/' + rowData.volume
	});
};

var getVolpools = exports.getVolpools = function getVolpools(data) {
	return (0, _request2.default)({
		method: 'GET',
		url: 'rest-ful/v3.0/volpools'
	});
};

var getPlaceonfileData = exports.getPlaceonfileData = function getPlaceonfileData(data) {
	return (0, _request2.default)({
		method: 'POST',
		url: 'rest-ful/v3.0/zvolume/archived',
		data: data
	});
};

/***/ }),

/***/ 2829:
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function($) {

Object.defineProperty(exports, "__esModule", {
	value: true
});

var _defineProperty2 = __webpack_require__(89);

var _defineProperty3 = _interopRequireDefault(_defineProperty2);

var _util = __webpack_require__(16);

var _util2 = _interopRequireDefault(_util);

var _index = __webpack_require__(2147);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = (0, _defineProperty3.default)({
	data: function data() {
		var _this = this,
		    _ref7;

		return _ref7 = {
			copyvalue: '',
			zfsdevice: 0,
			copyshow: false,
			noImgUrl: __webpack_require__(317),
			force: 0,
			forceStatus: false,
			bgid: '',
			expireidarr: [],
			expiremodel: false,
			storelist: [],
			gzformRule: {
				id: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择实例名',
					trigger: 'change'
				}],
				dc: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择数据中心',
					trigger: 'change'
				}],
				hosts: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择主机',
					trigger: 'change'
				}],
				network: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择网段',
					trigger: 'change'
				}],
				name: [{ required: true, message: '重命名', trigger: 'blur' }],
				client: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择网段',
					trigger: 'change'
				}],
				agre: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择网段',
					trigger: 'change'
				}],
				store: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择存储',
					trigger: 'change'
				}],
				diskmode: [{
					transform: function transform(value) {
						return String(value);
					},
					required: true,
					message: '请选择模式',
					trigger: 'change'
				}],
				datapath: [{ required: true, message: '数据文件路径', trigger: 'blur' }],
				cliName: [{ required: true, message: '实例名', trigger: 'blur' }]
			},

			tilbt: '挂载',

			str: '',
			setting: {
				check: {
					enable: true
				},
				view: {
					selectedMulti: false
				},
				callback: {
					onClick: this.zTreeOnClick,
					onCheck: this.zTreeOnCheck
				}
			},
			glancePath: '',
			db2id: '',

			db2List: [],

			stecur: 0,

			gztotal: '',
			curentPage: 1,
			pageSize: 10,
			sqltype: '',

			typedis: false,
			statusSelect: [],
			cliSelect: [],
			restypeSelect: []
		}, (0, _defineProperty3.default)(_ref7, 'statusSelect', []), (0, _defineProperty3.default)(_ref7, 'tooltipdata', ''), (0, _defineProperty3.default)(_ref7, 'tooltipdefint', '选择开始和结束时间'), (0, _defineProperty3.default)(_ref7, 'tooltipdataxt', ''), (0, _defineProperty3.default)(_ref7, 'tooltipdefintxt', '选择开始和结束时间'), (0, _defineProperty3.default)(_ref7, 'vmwarecol', [{
			type: 'selection',
			width: 60,
			align: 'center'
		}, {
			title: '机器名',
			key: 'name'
		}]), (0, _defineProperty3.default)(_ref7, 'vamTableList', []), (0, _defineProperty3.default)(_ref7, 'vamsldata', []), (0, _defineProperty3.default)(_ref7, 'sqlsldata', []), (0, _defineProperty3.default)(_ref7, 'dmsldata', []), (0, _defineProperty3.default)(_ref7, 'centerlist', []), (0, _defineProperty3.default)(_ref7, 'hostslist', []), (0, _defineProperty3.default)(_ref7, 'networklist', []), (0, _defineProperty3.default)(_ref7, 'vamlist', []), (0, _defineProperty3.default)(_ref7, 'resPost', []), (0, _defineProperty3.default)(_ref7, 'optionArr', []), (0, _defineProperty3.default)(_ref7, 'modelist', []), (0, _defineProperty3.default)(_ref7, 'tableHeight', 0), (0, _defineProperty3.default)(_ref7, 'taskType', '挂载数据库'), (0, _defineProperty3.default)(_ref7, 'oracletasttype', '1'), (0, _defineProperty3.default)(_ref7, 'oracleResTime', ''), (0, _defineProperty3.default)(_ref7, 'mountType', ''), (0, _defineProperty3.default)(_ref7, 'parentid', ''), (0, _defineProperty3.default)(_ref7, 'gzform', {
			id: '',
			client: '',
			agre: '',
			path: '',
			cliName: '',
			slName: '',
			host: '',
			user: '',
			hosts: '',
			dc: '',
			name: '',
			network: '',
			datapath: '',
			dataStroe: '',
			diskmode: ''
		}), (0, _defineProperty3.default)(_ref7, 'loading', false), (0, _defineProperty3.default)(_ref7, 'agreArr', []), (0, _defineProperty3.default)(_ref7, 'clientSelect', []), (0, _defineProperty3.default)(_ref7, 'onemodel', false), (0, _defineProperty3.default)(_ref7, 'twomodel', false), (0, _defineProperty3.default)(_ref7, 'dupdata', []), (0, _defineProperty3.default)(_ref7, 'mountdata', []), (0, _defineProperty3.default)(_ref7, 'twocol', [{
			title: '客户端地址',
			key: 'server',
			render: function render(h, _ref) {
				var row = _ref.row;

				return h('div', [h('Tooltip', {
					props: {
						placement: 'top',
						transfer: true
					}
				}, [_this.onTip(row.server), h('span', {
					slot: 'content',
					style: {
						whiteSpace: 'normal'
					}
				}, row.server)])]);
			}
		}, {
			title: '快照名称',
			key: 'source',
			render: function render(h, _ref2) {
				var row = _ref2.row;

				return h('div', [h('Tooltip', {
					props: {
						placement: 'top',
						transfer: true
					}
				}, [_this.onTip(row.source), h('span', {
					slot: 'content',
					style: {
						whiteSpace: 'normal'
					}
				}, row.source)])]);
			}
		}, {
			title: '卷名称',
			key: 'name',
			render: function render(h, _ref3) {
				var row = _ref3.row;

				return h('div', [h('Tooltip', {
					props: {
						placement: 'top',
						transfer: true
					}
				}, [_this.onTip(row.name), h('span', {
					slot: 'content',
					style: {
						whiteSpace: 'normal'
					}
				}, row.name)])]);
			}
		}, {
			title: '操作系统',
			key: 'os',
			render: function render(h, _ref4) {
				var row = _ref4.row;

				return h('div', [h('Tooltip', {
					props: {
						placement: 'top',
						transfer: true
					}
				}, [_this.onTip(row.os), h('span', {
					slot: 'content',
					style: {
						whiteSpace: 'normal'
					}
				}, row.os)])]);
			}
		}, {
			title: '资源类型',
			key: 'resource_type'
		}, {
			title: '挂载路径',
			key: 'mount_path',
			render: function render(h, _ref5) {
				var row = _ref5.row;

				return h('div', [h('Tooltip', {
					props: {
						placement: 'top',
						transfer: true
					}
				}, [_this.onTip(row.mount_path), h('span', {
					slot: 'content',
					style: {
						whiteSpace: 'normal'
					}
				}, row.mount_path)])]);
			}
		}, {
			title: '挂载时间',
			key: 'mount_time',
			render: function render(h, _ref6) {
				var row = _ref6.row;

				return h('div', [h('Tooltip', {
					props: {
						placement: 'top',
						transfer: true
					}
				}, [_this.onTip(row.mount_time), h('span', {
					slot: 'content',
					style: {
						whiteSpace: 'normal'
					}
				}, row.mount_time)])]);
			}
		}, {
			title: '挂载说明',
			key: 'desc'
		}, {
			title: '操作',
			key: 'operation',

			render: function render(h, params) {
				return h('div', {
					'class': {
						role: true
					},
					style: {
						display: 'flex',
						justifyContent: 'center',
						alignItems: 'center'
					}
				}, [_this.hasPrivilege(_this.getPower.VRTS_FUNC_UNMOUNT_COPY) ? h('div', {
					style: {
						marginRight: '5px',

						width: '30px',
						height: '30px',
						borderRadius: '3px',
						textAlign: 'center'
					}
				}, [h('span', {
					'class': 'iconfont',
					style: {
						fontSize: '22px',
						color: '#ff9130'
					},
					domProps: {
						innerHTML: '&#xe652;'
					},
					on: {
						click: function click() {
							_this.twomodel = true;
						}
					},
					directives: [{
						name: 'tooltip',
						value: '解除挂载'
					}]
				})]) : '']);
			}
		}]), (0, _defineProperty3.default)(_ref7, 'volid', ''), (0, _defineProperty3.default)(_ref7, 'zfsDeviceList', []), _ref7;
	},
	created: function created() {
		_util2.default.restfullCall('/rest-ful/v3.0/mount/protocol', null, 'get', this.agreData);
		_util2.default.restfullCall('rest-ful/v3.0/zvolume/list', null, 'get', this.getdupData);
		_util2.default.restfullCall('rest-ful/v3.0/mountedvolume/list', null, 'get', this.getgzData);
		_util2.default.restfullCall('/rest-ful/v3.0/clients?nums=0', null, 'get', this.cliData);
		_util2.default.restfullCall('/rest-ful/v3.0/resourcetype?mode=R', null, 'get', this.restypeData);
	},
	mounted: function mounted() {
		window.addEventListener('resize', this.getTableHeight);
		this.getTableHeight();
	},

	computed: {
		getPower: function getPower() {
			return this.$store.state.power.module;
		}
	},
	watch: {
		copyshow: {
			handler: function handler(val) {
				this.zfsdevice = 0;
				this.copyvalue = '';
				if (val == true) {
					this.getzfsDataFun();
				}
			},

			deep: true
		}
	},
	methods: {
		agreData: function agreData(res) {
			this.optionArr = res.data;
		}
	}
}, 'methods', {
	getzfsDataFun: function getzfsDataFun() {
		var _this2 = this;

		(0, _index.getDeviceList)().then(function (res) {
			if (res.code == 0) {
				_this2.zfsDeviceList = res.data;
			}
		});
	},
	copyFun: function copyFun() {
		var postData = {
			Device: this.zfsdevice,
			Policy: 0,
			Volume: this.volid,
			SnapshotId: 0,
			Desc: this.copyvalue
		};
	},
	onTip: function onTip(val) {
		var rowVal = val;
		if (val != null) {
			if (val.length > 18) {
				rowVal = val.slice(0, 18) + '...';
			} else {
				rowVal = val;
			}
		}
		return rowVal;
	},
	changeForce: function changeForce(val) {
		if (val) {
			this.force = 1;
		} else {
			this.force = 0;
		}
	},
	tishi: function tishi(currentRow, index) {
		if (currentRow.id == this.bgid) {
			return 'trbgshow';
		}
		return 'trbgshow_a';
	},
	onexpire: function onexpire() {
		_util2.default.restfullCall('/rest-ful/v3.0/zvolume/expired', this.expireidarr, 'post', this.expiredData);
	},
	expiredData: function expiredData(val) {},

	diskModeChange: function diskModeChange(open) {
		if (open == true) _util2.default.restfullCall('/rest-ful/v3.0/vmware/diskmode', null, 'get', this.diskModeData);
	},
	diskModeData: function diskModeData(res) {
		this.modelist = res.data.data;
	},
	onDistMode: function onDistMode(res) {
		this.gzform.diskmode = res.value;
	},

	dataStoreChange: function dataStoreChange(open) {
		if (open == true) _util2.default.restfullCall('/rest-ful/v3.0/vmware/datastore?server=' + this.gzform.host + '&user=' + this.gzform.user + '&password=' + this.gzform.password + '&dc=' + this.gzform.dc.value + '&host=' + this.gzform.hosts, null, 'get', this.dataStoreData);
	},
	dataStoreData: function dataStoreData(res) {
		this.storelist = res.data.data;
	},
	onStoreChange: function onStoreChange(res) {
		this.gzform.dataStroe = res.label;
	},


	build_path_by_tree_node: function build_path_by_tree_node(treeNode) {
		var path = '';
		var cid = 0;
		do {
			var parent = treeNode.getParentNode();
			if (!parent) {
				cid = treeNode.id;
				name = treeNode.name;
				break;
			}

			if (parent.nodetype != 0) {
				path = '/' + treeNode.name + path;
			} else {
				path = treeNode.name + path;
			}
			if (parent.nodetype != 1) {}
			treeNode = parent;
		} while (true);
		if (path.indexOf('//') == 0) {
			path = path.substr(1);
		}
		return {
			client: cid,
			path: path,
			name: name
		};
	},

	zTreeOnClick: function zTreeOnClick(event, treeId, treeNode) {
		var _this3 = this;

		if (!treeNode.hasOwnProperty('children')) {
			var path = this.build_path_by_tree_node(treeNode);


			var treeObj = $.fn.zTree.getZTreeObj(treeId);

			var nodes = treeObj.getSelectedNodes();

			if (nodes.length > 0) {

				nodes.forEach(function (el, i) {

					if (el.parentTId == null) {
						_this3.str = 'rest-ful/v3.0/zvolume/browse/' + _this3.volid + '?volume=' + _this3.db2id + '&path=' + path.name;
					} else {
						_this3.str = 'rest-ful/v3.0/zvolume/browse/' + _this3.volid + '?volume=' + _this3.db2id + '&path=' + path.name + '/' + treeNode.name;
					}
				});
			}

			_util2.default.restfullCall(this.str, null, 'get', function (obj) {
				if (obj.data.code == 0 && obj.data.data != null) {
					var arrays = new Array();
					var objj = obj.data.data;
					for (var i = 0; i < objj.length; i++) {
						arrays.push({
							name: objj[i].name,
							iconSkin: 'catalog',
							nodetype: 1,
							typetree: objj[i].type
						});
					}
					var ztreeobj = $.fn.zTree.getZTreeObj(treeId);
					ztreeobj.addNodes(treeNode, arrays);
				}
			});
		}
	},

	zTreeOnCheck: function zTreeOnCheck(event, treeId, treeNode) {
		var path = this.build_path_by_tree_node(treeNode);
		var pathList = path.path;
		if (treeNode.checked == true) {
			this.resPost.push({
				type: Number(treeNode.typetree),
				path: path.name + path.path,
				desc: '',
				exclude: 1
			});
		} else if (treeNode.checked == false) {
			var indexToRemove = -1;

			for (var i = 0; i < this.resPost.length; i++) {
				if (this.resPost[i].path === path.name + path.path) {
					indexToRemove = i;
					break;
				}
			}
			if (indexToRemove > -1) {
				this.resPost.splice(indexToRemove, 1);
			}
		}
	},

	nextstep: function nextstep() {
		this.stecur += 1;
	},
	upstep: function upstep() {
		this.stecur -= 1;
	},


	restypeData: function restypeData(obj) {
		var array = new Array();
		for (var i = 0; i < obj.data.length; i++) {
			array.push({
				restype: obj.data[i].type,
				name: obj.data[i].name
			});
		}
		this.restypeSelect = array;
	},

	gzhandleSizeChange: function gzhandleSizeChange(val) {
		this.pageSize = val;
		_util2.default.restfullCall('rest-ful/v3.0/mountedvolume/list?pageno=' + 1 + '&nums=' + this.pageSize, null, 'get', this.getgzData);
	},
	gzchangePage: function gzchangePage(index) {
		this.curentPage = index;
		_util2.default.restfullCall('rest-ful/v3.0/mountedvolume/list?pageno=' + index + '&nums=' + this.pageSize, null, 'get', this.getgzData);
	},
	torowdata: function torowdata(res, i) {
		this.$refs.selection.toggleSelect(i);
	},
	tovawlist: function tovawlist(res) {
		var _this4 = this;

		var aa = res;

		if (aa.length > 0) {
			aa.forEach(function (item) {
				if (_this4.resPost.length > 0) {
					_this4.resPost.forEach(function (el, i) {
						if (el.path == item.name) {
							_this4.resPost.slice(i, 1);
						}
					});
				} else {
					_this4.resPost.push({ type: Number(item.type), path: item.name, desc: '', exclude: 1 });
				}
			});
		} else {
			this.resPost = [];
		}
	},

	netWorkChange: function netWorkChange(open) {
		if (open == true) _util2.default.restfullCall('/rest-ful/v3.0/vmware/network?server=' + this.gzform.host + '&user=' + this.gzform.user + '&password=' + this.gzform.password + '&dc=' + this.gzform.dc.value + '&host=' + this.gzform.hosts, null, 'get', this.netWorkData);
	},
	netWorkData: function netWorkData(res) {
		this.networklist = res.data.data;
	},
	onNetWork: function onNetWork(res) {
		this.gzform.network = res.label;
	},
	getDataCenter: function getDataCenter(res) {
		this.centerlist = res.data.data;
	},
	onCenterChange: function onCenterChange(res) {
		this.gzform.dc = res;
	},

	hostsChange: function hostsChange(open) {
		if (open == true) _util2.default.restfullCall('/rest-ful/v3.0/vmware/hosts?server=' + this.gzform.host + '&user=' + this.gzform.user + '&password=' + this.gzform.password + '&dc=' + this.gzform.dc.value, null, 'get', this.gethostsData);
	},
	gethostsData: function gethostsData(res) {
		this.hostslist = res.data.data;
	},
	vawarehostData: function vawarehostData(val) {},
	vawarechange: function vawarechange(val) {
		var _this5 = this;

		this.vamsldata.forEach(function (item, i) {
			if (val == item.id) {
				_this5.gzform.user = item.user;
				_this5.gzform.password = item.password;
				_this5.gzform.host = item.host;
				_util2.default.restfullCall('/rest-ful/v3.0/vmware/datacenter?server=' + _this5.gzform.host + '&user=' + _this5.gzform.user + '&password=' + _this5.gzform.password, null, 'get', _this5.getDataCenter);
			}
		});
	},
	sqlchange: function sqlchange(val) {
		this.gzform.cliName = val;
	},
	oraclechange: function oraclechange(val) {
		this.gzform.id = val;
	},
	getTableHeight: function getTableHeight() {
		this.tableHeight = window.innerHeight - 220;
	},
	oracleTime: function oracleTime(res) {
		this.oracleResTime = res;
	},
	ontasktype: function ontasktype(res) {
		if (res == '挂载数据库') {
			this.oracletasttype = '1';
		} else if (res == '挂载文件') {
			this.oracletasttype = '2';
		}
	},
	onmount: function onmount() {
		var postObj = {};

		if (this.mountType == '达梦') {
			var dmArr = [];
			dmArr.push({
				type: 35,
				value: this.gzform.id.toString()
			}, {
				type: 119,
				value: this.oracletasttype
			});
			this.optionArr = dmArr;
		}

		if (this.mountType == '文件') {
			var fileArr = [];
			fileArr.push({
				type: 119,
				value: this.oracletasttype
			});
			this.optionArr = fileArr;
		}
		if (this.mountType == 'MYSQL') {
			var mysqlArr = [];
			mysqlArr.push({
				type: 22,
				value: this.gzform.cliName == '' ? '' : this.gzform.cliName
			}, {
				type: 119,
				value: this.oracletasttype
			});
			this.optionArr = mysqlArr;
		}
		if (this.mountType == 'ORACLE') {
			var oracleArr = [];
			oracleArr.push({
				type: 22,
				value: this.gzform.cliName == '' ? '' : this.gzform.cliName
			}, {
				type: 25,
				value: this.oracleResTime
			}, {
				type: 119,
				value: this.oracletasttype
			});
			this.optionArr = oracleArr;
			postObj.protocol = this.gzform.agre;
		}
		if (this.mountType == 'VMWARE') {
			if (this.gzform.host != '' && this.gzform.dc.value != '' && this.gzform.hosts != '' && this.gzform.name != '') {
				var vmwareArr = [];
				if (this.sqltype == '恢复') {
					vmwareArr.push({
						type: 22,
						value: this.gzform.cliName == '' ? '' : this.gzform.cliName
					}, {
						type: 35,
						value: this.gzform.host
					}, {
						type: 97,
						value: this.gzform.dataStroe
					}, {
						type: 96,
						value: this.gzform.dc.value
					}, {
						type: 98,
						value: this.gzform.hosts
					}, {
						type: 95,
						value: this.gzform.network
					}, {
						type: 92,
						value: this.gzform.diskmode.toString()
					}, {
						type: 120,
						value: ''
					}, {
						type: 34,
						value: this.gzform.name
					});
				} else {
					vmwareArr.push({
						type: 22,
						value: this.gzform.cliName == '' ? '' : this.gzform.cliName
					}, {
						type: 35,
						value: this.gzform.host
					}, {
						type: 96,
						value: this.gzform.dc.value
					}, {
						type: 98,
						value: this.gzform.hosts
					}, {
						type: 95,
						value: this.gzform.network
					}, {
						type: 34,
						value: this.gzform.name
					}, {
						type: 119,
						value: this.oracletasttype
					});
				}

				this.optionArr = vmwareArr;
			} else {
				this.$Message.warning('请填写必填信息');
				return;
			}
		}

		if (this.mountType == 'DB2') {
			var db2Arr = [];
			db2Arr.push({
				type: 119,
				value: this.oracletasttype
			});
			this.optionArr = db2Arr;
			postObj = {
				volid: this.volid,
				client: this.gzform.client,
				protocol: this.gzform.agre,
				mountpath: this.gzform.path,
				options: this.optionArr,
				resources: this.resPost
			};
		}

		if (this.mountType == 'SQLSERVER') {
			if (this.gzform.id != '' && this.gzform.client != '' && this.gzform.agre != '' && this.gzform.name != '') {
				var sqlArr = [];
				if (this.sqltype == '恢复') {
					sqlArr.push({
						type: 35,
						value: this.gzform.id.toString()
					}, {
						type: 34,
						value: this.gzform.name
					}, {
						type: 120,
						value: ''
					}, {
						type: 121,
						value: this.gzform.datapath
					}, {
						type: 119,
						value: this.oracletasttype
					});
					this.optionArr = sqlArr;
				} else {
					sqlArr.push({
						type: 35,
						value: this.gzform.id.toString()
					}, {
						type: 34,
						value: this.gzform.name
					});
					this.optionArr = sqlArr;
				}

				postObj = {
					volid: this.volid,
					client: this.gzform.client,
					protocol: this.gzform.agre,
					mountpath: this.gzform.path,
					options: this.optionArr,
					resources: this.resPost
				};
			} else {
				this.$Message.warning('请填写必填信息');
				return;
			}
		}

		if (this.mountType != '文件') {
			postObj = {
				volid: this.volid,
				client: this.gzform.client,
				protocol: this.gzform.agre,
				mountpath: this.gzform.path,

				options: this.optionArr,
				resources: this.resPost
			};
		} else {
			postObj = {
				volid: this.volid,
				client: this.gzform.client,
				protocol: this.gzform.agre,
				mountpath: this.gzform.path,
				options: this.optionArr,
				resources: this.resPost
			};
		}

		_util2.default.restfullCall('/rest-ful/v3.0/zvolume/mount', postObj, 'POST', this.mountData);
	},
	mountData: function mountData(val) {
		this.loading = true;
		if (val.data.code == 0) {
			this.$Message.success(val.data.message);
			this.optionArr = [];
			$.fn.zTree.init($('#treeDemo'), this.setting, null);
			this.resPost = [];
			this.sqlsldata = [];
			this.dmsldata = [];
			this.sqltype = '';
		} else {
			this.$Message.warning(val.data.message);
		}
		this.loading = false;
		this.onemodel = false;
	},
	onunmount: function onunmount() {
		var _this6 = this;

		var postData = {
			Device: this.zfsdevice,
			Force: this.forceStatus,
			Volume: this.parentid,
			Save: this.copyshow,
			Mark: this.copyvalue
		};
		(0, _index.unmountFun)(postData).then(function (res) {
			if (res.code == 0) {
				_this6.$Message.success('提交解除挂载任务成功');
				setTimeout(function () {
					_util2.default.restfullCall('rest-ful/v3.0/mountedvolume/list', null, 'get', _this6.getgzData);
				}, 500);
			} else {
				_this6.$Message.warning(res.message);
			}
			_this6.twomodel = false;
		});
	},
	unmountData: function unmountData(val) {
		var _this7 = this;

		if (val.data.code == 0) {
			this.$Message.success('解除挂载任务成功');
			setTimeout(function () {
				_util2.default.restfullCall('rest-ful/v3.0/mountedvolume/list', null, 'get', _this7.getgzData);
			}, 1500);
		} else {
			this.$Message.warning(val.data.message);
		}
		this.twomodel = false;
	},
	getRowData: function getRowData(row) {
		var _this8 = this;

		this.volid = row.id;
		this.db2id = row.volume_id;
		this.expireidarr.push(row.id);
		if (row.resource_type == 'VMWARE') {
			this.typedis = true;
			this.$nextTick(function () {
				_this8.gzform.client = row.client_id;
			});
			_util2.default.restfullCall('rest-ful/v3.0/zvolume/browse/' + row.id + '?volume=' + row.volume_id, null, 'get', this.getvmwareData);

			var url = 'rest-ful/v3.0/client/agent/instances?cid=' + row.client_id + '&type=' + '327680';

			_util2.default.restfullCall(url, null, 'get', function (obj) {
				var vamsldata = [];
				for (var i = 0; i < obj.data.length; i++) {
					var object = JSON.parse(obj.data[i].conf);
					object.id = obj.data[i].id;

					vamsldata.push(object);
				}
				for (var _i = 0; _i < vamsldata.length; _i++) {
					_this8.vamsldata.push(vamsldata[_i]);
				}
			});
		}

		if (row.resource_type == 'DB2') {
			this.typedis = false;
			this.$nextTick(function () {
				_this8.gzform.client = row.client_id;
			});
			_util2.default.restfullCall('rest-ful/v3.0/zvolume/browse/' + row.id + '?volume=' + row.volume_id, null, 'get', this.getdb2Data);
		}

		if (row.resource_type == 'SQLSERVER') {
			this.typedis = false;
			this.$nextTick(function () {
				_this8.gzform.client = row.client_id;
			});
			_util2.default.restfullCall('rest-ful/v3.0/zvolume/browse/' + row.id + '?volume=' + row.volume_id, null, 'get', this.getSqlserverData);

			var _url = 'rest-ful/v3.0/client/agent/instances?cid=' + row.client_id + '&type=' + '262144';

			_util2.default.restfullCall(_url, null, 'get', function (obj) {
				var sqlsldata = [];
				for (var i = 0; i < obj.data.length; i++) {
					var object = JSON.parse(obj.data[i].conf);
					object.id = obj.data[i].id;

					sqlsldata.push(object);
				}
				for (var _i2 = 0; _i2 < sqlsldata.length; _i2++) {
					_this8.sqlsldata.push(sqlsldata[_i2]);
				}
			});
		}
		if (row.resource_type == '达梦') {
			this.typedis = false;


			var _url2 = 'rest-ful/v3.0/client/agent/instances?cid=' + row.client_id + '&type=' + '655360';

			_util2.default.restfullCall(_url2, null, 'get', function (obj) {
				var dmsldata = [];
				for (var i = 0; i < obj.data.length; i++) {
					var object = JSON.parse(obj.data[i].conf);
					object.id = obj.data[i].id;

					dmsldata.push(object);
				}
				for (var _i3 = 0; _i3 < dmsldata.length; _i3++) {
					_this8.dmsldata.push(dmsldata[_i3]);
				}
			});
		}
		if (row.resource_type == 'ORACLE') {
			this.typedis = false;
			var _url3 = 'rest-ful/v3.0/client/agent/instances?cid=' + row.client_id + '&type=' + '131072';

			_util2.default.restfullCall(_url3, null, 'get', function (obj) {
				var dmsldata = [];
				for (var i = 0; i < obj.data.length; i++) {
					var object = JSON.parse(obj.data[i].conf);
					object.id = obj.data[i].id;

					dmsldata.push(object);
				}
				for (var _i4 = 0; _i4 < dmsldata.length; _i4++) {
					_this8.dmsldata.push(dmsldata[_i4]);
				}
			});
		}
		if (row.resource_type == 'MYSQL') {
			this.typedis = false;
			var _url4 = 'rest-ful/v3.0/client/agent/instances?cid=' + row.client_id + '&type=' + '196608';

			_util2.default.restfullCall(_url4, null, 'get', function (obj) {
				var dmsldata = [];
				for (var i = 0; i < obj.data.length; i++) {
					var object = JSON.parse(obj.data[i].conf);
					object.id = obj.data[i].id;

					dmsldata.push(object);
				}
				for (var _i5 = 0; _i5 < dmsldata.length; _i5++) {
					_this8.dmsldata.push(dmsldata[_i5]);
				}
			});
		}
	},
	getSqlserverData: function getSqlserverData(res) {
		this.db2List = [];
		if (res.data.code == 0) {
			this.db2List = res.data.data;
		}

		$.fn.zTree.init($('#treeDemo'), this.setting, this.db2List);
	},
	getdb2Data: function getdb2Data(res) {
		this.db2List = [];
		if (res.data.code == 0) {
			this.db2List = res.data.data;
		}

		$.fn.zTree.init($('#treeDemo'), this.setting, this.db2List);
	},
	getvmwareData: function getvmwareData(res) {
		var vamData = [];
		this.loading = true;
		if (res.data.code == 0) {
			this.vamTableList = res.data.data;
		} else if (res.data.data == null) {
			this.vamTableList = [];
		}
		this.loading = false;
	},
	gettwoRowData: function gettwoRowData(row) {
		this.parentid = row.id;
		this.bgid = row.id;
	},
	getgzData: function getgzData(res) {
		if (res.data.code == 0) {
			this.gztotal = res.data.data.nums;
			this.mountdata = res.data.data.Records;
		}
	},
	getdupData: function getdupData(res) {
		this.loading = true;
		if (res.data.code == 0) {
			this.dupdata = res.data.data.Records;
			this.loading = false;
		}
	},

	openAgre: function openAgre(openCli) {
		if (openCli == true) _util2.default.restfullCall('/rest-ful/v3.0/mount/protocol', null, 'get', this.agreData);
	},

	agreData: function agreData(obj) {
		if (obj.data.code == 0) {
			this.agreArr = obj.data.data;
		}
	},

	agreOptionId: function agreOptionId(datas) {
		this.gzform.agre = datas;
	},

	openClient: function openClient(openCli) {
		if (openCli == true) _util2.default.restfullCall('/rest-ful/v3.0/clients?nums=0', null, 'get', this.cliData);
	},

	cliData: function cliData(obj) {
		var array = new Array();
		for (var i = 0; i < obj.data.data.vrts_client_data.length; i++) {
			array.push({
				id: obj.data.data.vrts_client_data[i].id,
				machine: obj.data.data.vrts_client_data[i].machine
			});
		}

		this.clientSelect = array;
	},

	optionId: function optionId(datas) {
		this.gzform.client = datas;
	},
	cancel: function cancel() {
		this.onemodel = false;
		this.twomodel = false;
		$.fn.zTree.init($('#treeDemo'), this.setting, null);
		this.resPost = [];
		this.sqlsldata = [];
		this.vamTableList = [];
		this.vamsldata = [];
		this.dmsldata = [];
		this.sqltype = '';
		this.gzform.id = '';
	}
});
/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(74)))

/***/ }),

/***/ 3779:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3780);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("b3688628", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-48ac89ab\",\"scoped\":false,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./mountMag.vue", function() {
     var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-48ac89ab\",\"scoped\":false,\"hasInlineConfig\":false}!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./mountMag.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3780:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.ivu-steps-item.ivu-steps-status-process .ivu-steps-head-inner{border-color:#00b818;background-color:#00b818\n}\n.ivu-steps-item.ivu-steps-status-finish .ivu-steps-head-inner{background-color:#fff;border-color:#00b818\n}\n.ivu-steps-item.ivu-steps-status-finish .ivu-steps-tail>i:after{width:100%;background:#00b818;transition:all .2s ease-in-out;opacity:1\n}", ""]);

// exports


/***/ }),

/***/ 3781:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3782);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("019e55e2", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-48ac89ab\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/less-loader/dist/cjs.js!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=1!./mountMag.vue", function() {
     var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-48ac89ab\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/less-loader/dist/cjs.js!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=1!./mountMag.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3782:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.marBox[data-v-48ac89ab]{margin:16px 15px 20px;overflow-y:auto\n}\n.searchBar[data-v-48ac89ab]{margin-right:10px\n}\n.tree[data-v-48ac89ab]{width:100%;float:left;overflow:auto\n}\n.tree[data-v-48ac89ab],.tree-box[data-v-48ac89ab]{height:250px\n}\n.page-wrap[data-v-48ac89ab]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:end;-ms-flex-pack:end;justify-content:flex-end;position:absolute;bottom:32px;right:35px\n}\n.mode-content p[data-v-48ac89ab]{margin-bottom:8px\n}\n.copy-box[data-v-48ac89ab]{margin-bottom:30px\n}\n.copy-box .check-box[data-v-48ac89ab]{margin-bottom:8px;margin-left:20px\n}", ""]);

// exports


/***/ }),

/***/ 3783:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", [_c("Row", [_c("Col", { staticClass: "breadstyle", attrs: { span: "24" } }, [_c("Breadcrumb", [_c("BreadcrumbItem", {
    staticClass: "breadlist",
    staticStyle: { display: "flex", "align-items": "center" }
  }, [_c("img", {
    staticStyle: {
      width: "1.2rem",
      height: "1.2rem",
      "margin-right": "0.3rem"
    },
    attrs: { src: __webpack_require__(318) }
  }), _vm._v(" "), _c("span", [_vm._v("数据副本 / 挂载记录")])])], 1)], 1)], 1), _vm._v(" "), _c("el-card", {
    staticClass: "marBox",
    staticStyle: { height: "calc(100vh - 145px)" }
  }, [_c("Table", {
    attrs: {
      loading: _vm.loading,
      columns: _vm.twocol,
      height: _vm.tableHeight,
      "row-class-name": _vm.tishi,
      data: _vm.mountdata,
      "no-data-text": "<div class='no-img-url-text'><img class='noImgUrl' src=" + _vm.noImgUrl + " /><div class='text'>暂无数据</div><div>"
    },
    on: { "on-row-click": _vm.gettwoRowData }
  }), _vm._v(" "), _c("div", { staticClass: "fy-page" }, [_c("el-pagination", {
    staticClass: "page-wrap",
    attrs: {
      background: "",
      "current-page": _vm.curentPage,
      "page-sizes": [10, 20, 30, 40],
      "page-size": 10,
      layout: "total, sizes, prev, pager, next, jumper",
      total: _vm.gztotal
    },
    on: {
      "size-change": _vm.gzhandleSizeChange,
      "current-change": _vm.gzchangePage
    }
  })], 1)], 1), _vm._v(" "), _c("Modal", {
    attrs: { title: _vm.sqltype == "恢复" ? "恢复" : "挂载" },
    model: {
      value: _vm.onemodel,
      callback: function callback($$v) {
        _vm.onemodel = $$v;
      },
      expression: "onemodel"
    }
  }, [_c("div", [_c("Form", {
    ref: "gzform",
    attrs: {
      "label-width": 90,
      model: _vm.gzform,
      rules: _vm.gzformRule
    }
  }, [(_vm.mountType != "VMWARE" && _vm.stecur == 0 || _vm.mountType == "DB2" ? true : false) ? _c("FormItem", { attrs: { label: "客户端", prop: "client" } }, [_c("Select", {
    attrs: {
      placeholder: "请选择客户端",
      disabled: _vm.typedis
    },
    on: {
      "on-open-change": _vm.openClient,
      "on-change": _vm.optionId
    },
    model: {
      value: _vm.gzform.client,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "client", $$v);
      },
      expression: "gzform.client"
    }
  }, _vm._l(_vm.clientSelect, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.id } }, [_vm._v(_vm._s(item.machine))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), (_vm.mountType == "VMWARE" ? false : true) ? _c("FormItem", { attrs: { label: "协议", prop: "agre" } }, [_c("Select", {
    attrs: { placeholder: "请选择协议" },
    on: {
      "on-open-change": _vm.openAgre,
      "on-change": _vm.agreOptionId
    },
    model: {
      value: _vm.gzform.agre,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "agre", $$v);
      },
      expression: "gzform.agre"
    }
  }, _vm._l(_vm.agreArr, function (item) {
    return _c("Option", { key: item, attrs: { value: item } }, [_vm._v(_vm._s(item))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), (_vm.mountType != "VMWARE" && _vm.stecur == 0 && _vm.mountType != "SQLSERVER" ? true : false) ? _c("FormItem", { attrs: { label: "路径" } }, [_c("Input", {
    model: {
      value: _vm.gzform.path,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "path", $$v);
      },
      expression: "gzform.path"
    }
  })], 1) : _vm._e(), _vm._v(" "), (_vm.mountType == "VMWARE" ? false : true) ? _c("FormItem", { attrs: { label: "任务类型" } }, [_c("RadioGroup", {
    on: { "on-change": _vm.ontasktype },
    model: {
      value: _vm.taskType,
      callback: function callback($$v) {
        _vm.taskType = $$v;
      },
      expression: "taskType"
    }
  }, [_c("Radio", { attrs: { label: "挂载数据库" } }), _vm._v(" "), _c("Radio", { attrs: { label: "挂载文件" } })], 1)], 1) : _vm._e(), _vm._v(" "), _vm.mountType == "ORACLE" ? _c("div", [_vm.taskType == "挂载数据库" ? _c("FormItem", { attrs: { label: "实例名", prop: "id" } }, [_c("Select", {
    attrs: { placeholder: "请选择实例名" },
    on: { "on-change": _vm.oraclechange },
    model: {
      value: _vm.gzform.cliName,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "cliName", $$v);
      },
      expression: "gzform.cliName"
    }
  }, _vm._l(_vm.dmsldata, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.name }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), _vm.taskType == "挂载数据库" ? _c("FormItem", { attrs: { label: "恢复时间点" } }, [_c("DatePicker", {
    staticStyle: { width: "200px" },
    attrs: {
      type: "datetime",
      placeholder: "请选择恢复时间节点"
    },
    on: { "on-change": _vm.oracleTime }
  })], 1) : _vm._e()], 1) : _vm._e(), _vm._v(" "), _vm.mountType == "VMWARE" && _vm.sqltype == "挂载" ? _c("div", [_c("Steps", {
    staticStyle: { margin: "20px 0" },
    attrs: { current: _vm.stecur }
  }, [_c("Step", { attrs: { title: "选择虚拟机" } }), _vm._v(" "), _c("Step", { attrs: { title: "选项" } })], 1), _vm._v(" "), (_vm.stecur == 1 ? true : false) ? _c("FormItem", { attrs: { label: "客户端" } }, [_c("Select", {
    attrs: {
      placeholder: "请选择客户端",
      disabled: _vm.typedis
    },
    on: {
      "on-open-change": _vm.openClient,
      "on-change": _vm.optionId
    },
    model: {
      value: _vm.gzform.client,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "client", $$v);
      },
      expression: "gzform.client"
    }
  }, _vm._l(_vm.clientSelect, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.id }
    }, [_vm._v(_vm._s(item.machine))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), (_vm.stecur == 1 ? true : false) ? _c("FormItem", { attrs: { label: "实例名", prop: "id" } }, [_c("Select", {
    attrs: { placeholder: "请选择实例名" },
    on: { "on-change": _vm.vawarechange },
    model: {
      value: _vm.gzform.id,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "id", $$v);
      },
      expression: "gzform.id"
    }
  }, _vm._l(_vm.vamsldata, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.id }
    }, [_vm._v(_vm._s(item.host))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), (_vm.stecur == 1 ? true : false) ? _c("FormItem", { attrs: { label: "数据中心:", prop: "dc" } }, [_c("Select", {
    attrs: { "label-in-value": true },
    on: { "on-change": _vm.onCenterChange },
    model: {
      value: _vm.gzform.dc.value,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform.dc, "value", $$v);
      },
      expression: "gzform.dc.value"
    }
  }, _vm._l(_vm.centerlist, function (item, i) {
    return _c("Option", {
      key: i,
      attrs: {
        value: item + "",
        label: item
      }
    }, [_vm._v(_vm._s(item))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), (_vm.stecur == 1 ? true : false) ? _c("FormItem", { attrs: { label: "主机", prop: "hosts" } }, [_c("Select", {
    attrs: { placeholder: "请选择主机" },
    on: { "on-open-change": _vm.hostsChange },
    model: {
      value: _vm.gzform.hosts,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "hosts", $$v);
      },
      expression: "gzform.hosts"
    }
  }, _vm._l(_vm.hostslist, function (item) {
    return _c("Option", {
      key: item,
      attrs: { value: item + "" }
    }, [_vm._v(_vm._s(item))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), (_vm.stecur == 1 ? true : false) ? _c("FormItem", { attrs: { label: "网段:", prop: "network" } }, [_c("Select", {
    attrs: { "label-in-value": true },
    on: {
      "on-change": _vm.onNetWork,
      "on-open-change": _vm.netWorkChange
    },
    model: {
      value: _vm.gzform.network,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "network", $$v);
      },
      expression: "gzform.network"
    }
  }, _vm._l(_vm.networklist, function (item) {
    return _c("Option", {
      key: item,
      attrs: { value: item, label: item }
    }, [_vm._v(_vm._s(item))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), (_vm.stecur == 1 ? true : false) ? _c("FormItem", {
    attrs: { label: "重命名虚拟机", prop: "name" }
  }, [_c("Input", {
    attrs: { type: "text" },
    model: {
      value: _vm.gzform.name,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "name", $$v);
      },
      expression: "gzform.name"
    }
  })], 1) : _vm._e(), _vm._v(" "), _c("div", { staticStyle: { "text-align": "right" } }, [(_vm.stecur == 0 ? true : false) ? _c("Table", {
    ref: "selection",
    attrs: {
      loading: _vm.loading,
      border: "",
      columns: _vm.vmwarecol,
      data: _vm.vamTableList
    },
    on: {
      "on-row-click": _vm.torowdata,
      "on-selection-change": _vm.tovawlist
    }
  }) : _vm._e()], 1)], 1) : _vm._e(), _vm._v(" "), _vm.mountType == "VMWARE" && _vm.sqltype == "恢复" ? _c("div", [_c("Steps", {
    staticStyle: { margin: "20px 0" },
    attrs: { current: _vm.stecur }
  }, [_c("Step", { attrs: { title: "选择虚拟机" } }), _vm._v(" "), _c("Step", { attrs: { title: "选项" } })], 1), _vm._v(" "), (_vm.stecur == 1 ? true : false) ? _c("FormItem", { attrs: { label: "客户端" } }, [_c("Select", {
    attrs: {
      placeholder: "请选择客户端",
      disabled: _vm.typedis
    },
    on: {
      "on-open-change": _vm.openClient,
      "on-change": _vm.optionId
    },
    model: {
      value: _vm.gzform.client,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "client", $$v);
      },
      expression: "gzform.client"
    }
  }, _vm._l(_vm.clientSelect, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.id }
    }, [_vm._v(_vm._s(item.machine))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), (_vm.stecur == 1 ? true : false) ? _c("FormItem", { attrs: { label: "实例名", prop: "id" } }, [_c("Select", {
    attrs: { placeholder: "请选择实例名" },
    on: { "on-change": _vm.vawarechange },
    model: {
      value: _vm.gzform.id,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "id", $$v);
      },
      expression: "gzform.id"
    }
  }, _vm._l(_vm.vamsldata, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.id }
    }, [_vm._v(_vm._s(item.host))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), (_vm.stecur == 1 ? true : false) ? _c("FormItem", { attrs: { label: "数据中心:", prop: "dc" } }, [_c("Select", {
    attrs: { "label-in-value": true },
    on: { "on-change": _vm.onCenterChange },
    model: {
      value: _vm.gzform.dc.value,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform.dc, "value", $$v);
      },
      expression: "gzform.dc.value"
    }
  }, _vm._l(_vm.centerlist, function (item, i) {
    return _c("Option", {
      key: i,
      attrs: {
        value: item + "",
        label: item
      }
    }, [_vm._v(_vm._s(item))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), (_vm.stecur == 1 ? true : false) ? _c("FormItem", { attrs: { label: "主机", prop: "hosts" } }, [_c("Select", {
    attrs: { placeholder: "请选择主机" },
    on: { "on-open-change": _vm.hostsChange },
    model: {
      value: _vm.gzform.hosts,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "hosts", $$v);
      },
      expression: "gzform.hosts"
    }
  }, _vm._l(_vm.hostslist, function (item) {
    return _c("Option", {
      key: item,
      attrs: { value: item + "" }
    }, [_vm._v(_vm._s(item))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), (_vm.stecur == 1 ? true : false) ? _c("FormItem", { attrs: { label: "存储:", prop: "store" } }, [_c("Select", {
    attrs: { "label-in-value": true },
    on: {
      "on-change": _vm.onStoreChange,
      "on-open-change": _vm.dataStoreChange
    },
    model: {
      value: _vm.gzform.dataStroe,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "dataStroe", $$v);
      },
      expression: "gzform.dataStroe"
    }
  }, _vm._l(_vm.storelist, function (item) {
    return _c("Option", {
      key: item,
      attrs: { value: item, label: item }
    }, [_vm._v(_vm._s(item))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), (_vm.stecur == 1 ? true : false) ? _c("FormItem", { attrs: { label: "网段:", prop: "network" } }, [_c("Select", {
    attrs: { "label-in-value": true },
    on: {
      "on-change": _vm.onNetWork,
      "on-open-change": _vm.netWorkChange
    },
    model: {
      value: _vm.gzform.network,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "network", $$v);
      },
      expression: "gzform.network"
    }
  }, _vm._l(_vm.networklist, function (item) {
    return _c("Option", {
      key: item,
      attrs: { value: item, label: item }
    }, [_vm._v(_vm._s(item))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), (_vm.stecur == 1 ? true : false) ? _c("FormItem", {
    attrs: {
      label: "磁盘模式:",
      prop: "diskmode"
    }
  }, [_c("Select", {
    attrs: { "label-in-value": true },
    on: {
      "on-change": _vm.onDistMode,
      "on-open-change": _vm.diskModeChange
    },
    model: {
      value: _vm.gzform.diskmode,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "diskmode", $$v);
      },
      expression: "gzform.diskmode"
    }
  }, _vm._l(_vm.modelist, function (item, i) {
    return _c("Option", {
      key: i,
      attrs: {
        value: item.Mode,
        label: item.Name
      }
    }, [_vm._v(_vm._s(item.Name))]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), (_vm.stecur == 1 ? true : false) ? _c("FormItem", {
    attrs: { label: "重命名虚拟机", prop: "name" }
  }, [_c("Input", {
    attrs: { type: "text" },
    model: {
      value: _vm.gzform.name,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "name", $$v);
      },
      expression: "gzform.name"
    }
  })], 1) : _vm._e(), _vm._v(" "), _c("div", { staticStyle: { "text-align": "right" } }, [(_vm.stecur == 0 ? true : false) ? _c("Table", {
    ref: "selection",
    attrs: {
      loading: _vm.loading,
      border: "",
      columns: _vm.vmwarecol,
      data: _vm.vamTableList
    },
    on: {
      "on-row-click": _vm.torowdata,
      "on-selection-change": _vm.tovawlist
    }
  }) : _vm._e()], 1)], 1) : _vm._e(), _vm._v(" "), _vm.mountType == "SQLSERVER" ? _c("div", [_c("FormItem", { attrs: { label: "实例名", prop: "id" } }, [_c("Select", {
    attrs: { placeholder: "请选择实例名" },
    on: { "on-change": _vm.sqlchange },
    model: {
      value: _vm.gzform.id,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "id", $$v);
      },
      expression: "gzform.id"
    }
  }, _vm._l(_vm.sqlsldata, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.server }
    }, [_vm._v(_vm._s(item.server))]);
  }), 1)], 1), _vm._v(" "), (_vm.sqltype == "恢复" ? true : false) ? _c("FormItem", {
    attrs: {
      label: "数据文件路径",
      prop: "datapath"
    }
  }, [_c("Input", {
    attrs: { type: "text" },
    model: {
      value: _vm.gzform.datapath,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "datapath", $$v);
      },
      expression: "gzform.datapath"
    }
  })], 1) : _vm._e(), _vm._v(" "), _c("FormItem", { attrs: { label: "重命名", prop: "name" } }, [_c("Input", {
    attrs: { type: "text" },
    model: {
      value: _vm.gzform.name,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "name", $$v);
      },
      expression: "gzform.name"
    }
  })], 1)], 1) : _vm._e(), _vm._v(" "), _vm.mountType == "MYSQL" ? _c("div", [_vm.taskType == "挂载数据库" ? _c("FormItem", { attrs: { label: "实例名", prop: "id" } }, [_c("Select", {
    attrs: { placeholder: "请选择实例名" },
    on: { "on-change": _vm.oraclechange },
    model: {
      value: _vm.gzform.cliName,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "cliName", $$v);
      },
      expression: "gzform.cliName"
    }
  }, _vm._l(_vm.dmsldata, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.host }
    }, [_vm._v(_vm._s(item.host))]);
  }), 1)], 1) : _vm._e()], 1) : _vm._e(), _vm._v(" "), _vm.mountType == "DB2" || _vm.mountType == "SQLSERVER" ? _c("div", [_c("div", { staticClass: "tree-box" }, [_c("div", { staticClass: "tree" }, [_c("ul", {
    staticClass: "ztree",
    attrs: { id: "treeDemo" }
  })])])]) : _vm._e(), _vm._v(" "), _vm.mountType == "达梦" ? _c("div", [_c("FormItem", { attrs: { label: "实例名", prop: "id" } }, [_c("Select", {
    attrs: { placeholder: "请选择实例名" },
    on: { "on-change": _vm.sqlchange },
    model: {
      value: _vm.gzform.id,
      callback: function callback($$v) {
        _vm.$set(_vm.gzform, "id", $$v);
      },
      expression: "gzform.id"
    }
  }, _vm._l(_vm.dmsldata, function (item) {
    return _c("Option", {
      key: item.id,
      attrs: { value: item.name }
    }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1)], 1) : _vm._e()], 1)], 1), _vm._v(" "), _c("div", { attrs: { slot: "footer" }, slot: "footer" }, [(_vm.stecur == 1 && _vm.mountType == "VMWARE" ? true : false) ? _c("Button", {
    attrs: { icon: "md-arrow-back" },
    on: { click: _vm.upstep }
  }, [_vm._v("上一步")]) : _vm._e(), _vm._v(" "), (_vm.mountType == "VMWARE" && _vm.stecur == 0 ? false : true) ? _c("Button", {
    attrs: { icon: "md-close-circle" },
    on: { click: _vm.cancel }
  }, [_vm._v("取消")]) : _vm._e(), _vm._v(" "), (_vm.mountType == "VMWARE" && _vm.stecur == 0 ? false : true) ? _c("Button", {
    attrs: { type: "primary", icon: "md-checkmark-circle" },
    on: { click: _vm.onmount }
  }, [_vm._v("确定")]) : _vm._e(), _vm._v(" "), (_vm.stecur == 0 && _vm.mountType == "VMWARE" ? true : false) ? _c("Button", {
    attrs: { icon: "md-arrow-forward" },
    on: { click: _vm.nextstep }
  }, [_vm._v("下一步")]) : _vm._e()], 1)]), _vm._v(" "), _c("Modal", {
    attrs: { closable: false, "footer-hide": true, width: "540" },
    model: {
      value: _vm.twomodel,
      callback: function callback($$v) {
        _vm.twomodel = $$v;
      },
      expression: "twomodel"
    }
  }, [_c("div", { staticClass: "addPolicyModeStyle" }, [_c("h3", [_vm._v("解除挂载")]), _vm._v(" "), _c("span", { staticClass: "iconfont", on: { click: _vm.cancel } }, [_vm._v("")])]), _vm._v(" "), _c("div", {
    staticClass: "mode-content",
    staticStyle: { "margin-left": "20px" }
  }, [_c("p", { staticStyle: { color: "#f60" } }, [_c("span", [_vm._v("解除挂载会导致数据不可用，请确保应用已经停止，是否继续解除挂载?")])]), _vm._v(" "), _c("p", { staticStyle: { color: "#f60" } }, [_c("span", { staticClass: "iconfont" }, [_vm._v("")]), _vm._v(" "), _c("Checkbox", {
    on: { "on-change": _vm.changeForce },
    model: {
      value: _vm.forceStatus,
      callback: function callback($$v) {
        _vm.forceStatus = $$v;
      },
      expression: "forceStatus"
    }
  }, [_vm._v("强制解除 (强制解除可能导致客户端数据异常)\n\t\t\t\t\t")])], 1), _vm._v(" "), _c("div", { staticClass: "copy-box" }, [_c("Checkbox", {
    staticClass: "check-box",
    model: {
      value: _vm.copyshow,
      callback: function callback($$v) {
        _vm.copyshow = $$v;
      },
      expression: "copyshow"
    }
  }, [_vm._v("解除挂载后保存该副本")]), _vm._v(" "), _vm.copyshow ? _c("div", [_c("p", [_c("span", [_vm._v("选择保存新副本的设备：")]), _vm._v(" "), _c("Select", {
    attrs: {
      placeholder: "请选择新副本的保存设备"
    },
    model: {
      value: _vm.zfsdevice,
      callback: function callback($$v) {
        _vm.zfsdevice = $$v;
      },
      expression: "zfsdevice"
    }
  }, _vm._l(_vm.zfsDeviceList, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.id } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("p", [_c("span", [_vm._v("备注:")]), _vm._v(" "), _c("Input", {
    attrs: { type: "textarea", placeholder: "" },
    model: {
      value: _vm.copyvalue,
      callback: function callback($$v) {
        _vm.copyvalue = $$v;
      },
      expression: "copyvalue"
    }
  })], 1)]) : _vm._e()], 1)]), _vm._v(" "), _c("div", { staticClass: "modefooter", staticStyle: { width: "100%" } }, [_c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { size: "large", icon: "md-close-circle" },
    on: { click: _vm.cancel }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      size: "large",
      icon: "md-checkmark-circle"
    },
    on: { click: _vm.onunmount }
  }, [_vm._v("确定")])], 1)]), _vm._v(" "), _c("Modal", {
    attrs: { closable: false, "footer-hide": true, width: "540" },
    model: {
      value: _vm.expiremodel,
      callback: function callback($$v) {
        _vm.expiremodel = $$v;
      },
      expression: "expiremodel"
    }
  }, [_c("div", { staticClass: "addPolicyModeStyle" }, [_c("h3", [_vm._v("过期")]), _vm._v(" "), _c("span", { staticClass: "iconfont", on: { click: _vm.cancel } }, [_vm._v("")])]), _vm._v(" "), _c("div", {
    staticClass: "modeContent",
    staticStyle: { "margin-left": "20px" }
  }, [_c("p", { staticStyle: { color: "#f60" } }, [_c("span", { staticClass: "iconfont" }, [_vm._v("")]), _vm._v(" "), _c("span", [_vm._v("过期")])])]), _vm._v(" "), _c("div", { staticClass: "modefooter", staticStyle: { width: "100%" } }, [_c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { size: "large", icon: "md-close-circle" },
    on: { click: _vm.cancel }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      size: "large",
      icon: "md-checkmark-circle"
    },
    on: { click: _vm.onexpire }
  }, [_vm._v("确定")])], 1)])], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-48ac89ab", esExports);
  }
}

/***/ }),

/***/ 586:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_mountMag_vue__ = __webpack_require__(2829);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_mountMag_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_mountMag_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_mountMag_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_mountMag_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_48ac89ab_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_mountMag_vue__ = __webpack_require__(3783);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_48ac89ab_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_mountMag_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_48ac89ab_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_mountMag_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3779)
  __webpack_require__(3781)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = "data-v-48ac89ab"
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_mountMag_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_48ac89ab_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_mountMag_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/dupMag/mountMag.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-48ac89ab", Component.options)
  } else {
    hotAPI.reload("data-v-48ac89ab", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ })

});