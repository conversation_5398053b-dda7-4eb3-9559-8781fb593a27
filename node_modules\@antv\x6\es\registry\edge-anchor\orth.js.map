{"version": 3, "file": "orth.js", "sourceRoot": "", "sources": ["../../../src/registry/edge-anchor/orth.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAS,MAAM,mBAAmB,CAAA;AAC/C,OAAO,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAA;AAC7C,OAAO,EAAkB,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAA;AAC7E,OAAO,EAAE,eAAe,EAAE,MAAM,WAAW,CAAA;AAO3C,MAAM,UAAU,GACd,UAAU,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO;IACvC,MAAM,MAAM,GAAG,GAAG,CAAA;IAClB,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,EAAG,CAAA;IAClC,MAAM,mBAAmB,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAA;IAC5D,MAAM,KAAK,GAAG,IAAI,IAAI,CACpB,QAAQ,CAAC,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,EACrC,QAAQ,CAAC,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CACvC,CAAA;IACD,MAAM,KAAK,GAAG,IAAI,IAAI,CACpB,QAAQ,CAAC,KAAK,EAAE,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,EACrC,QAAQ,CAAC,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CACvC,CAAA;IAED,MAAM,cAAc,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE;QAC3C,mBAAmB;KACpB,CAAC,CAAA;IAEF,MAAM,cAAc,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE;QAC3C,mBAAmB;KACpB,CAAC,CAAA;IAEF,MAAM,aAAa,GAAG,EAAE,CAAA;IACxB,IAAI,cAAc,EAAE;QAClB,aAAa,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAA;KACtC;IACD,IAAI,cAAc,EAAE;QAClB,aAAa,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAA;KACtC;IAED,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;QAC5B,OAAO,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAE,CAAA;KACxC;IAED,IAAI,OAAO,CAAC,UAAU,IAAI,IAAI,EAAE;QAC9B,OAAO,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAE,CAAA;KACjD;IAED,OAAO,WAAW,CAAC,IAAI,CACrB,eAAe,EACf,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,OAAO,CACR,CAAA;AACH,CAAC,CAAA;AAEH,MAAM,CAAC,MAAM,IAAI,GAAG,OAAO,CAGzB,UAAU,CAAC,CAAA"}