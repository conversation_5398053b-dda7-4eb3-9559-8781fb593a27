<style scoped lang="less">
@font-face {
	font-family: 'iconfont';
	src: url('iconfont.woff2?t=1702373537134') format('woff2'),
		url('iconfont.woff?t=1702373537134') format('woff'),
		url('iconfont.ttf?t=1702373537134') format('truetype'),
		url('iconfont.svg?t=1702373537134#iconfont') format('svg');
}
.iconfont {
	font-family: 'iconfont' !important;
	font-size: 16px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
html,
body {
	height: 100%;
}
.container {
	width: 100%;
	height: 100vh;
	display: flex;
}
.down-box {
	color: rgb(0, 140, 255);
	margin-left: 0px;
	cursor: pointer;
}
.left-content {
	/* 左边元素不需要特别设置，自动填充剩余空间 */
	background: url('../../images/loginimgbg.jpg') no-repeat center;
	background-size: 100% 100%;
	flex-grow: 1;
	.thxhtitle {
		padding-top: 2.5rem;
		padding-left: 3.125rem;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		width: 100%;
		height: 100px;
		padding-right: 6%;
	}
}
.right-content {
	flex-basis: 580px;
	/* 右边元素固定宽度 */
	flex-shrink: 0;
	/* 防止右边元素宽度导致左边内容溢出 */
	background: #fff;
	.loginkey {
		margin: 20rem auto;
		margin-top: 20rem;
		width: 22.75rem;
	}
}
@media screen and (min-width: 1360px) and (max-width: 1440px) {
	.right-content {
		flex-basis: 580px;
		/* 右边元素固定宽度 */
		flex-shrink: 0;
		/* 防止右边元素宽度导致左边内容溢出 */
		background: #fff;
		.loginkey {
			margin: 8rem auto;
			margin-top: 8rem;
			width: 22.75rem;
		}
	}
}
.login-p {
	color: #010000;
	text-align: center;
	font-size: 1.4375rem;
	margin-bottom: 4.375rem;
}
.login-btn {
	width: 22.75rem;
	height: 3rem;
	background-color: #ff9000;
	border: 1px #ff9000;
	display: block;
	margin: 0 auto;
	margin-top: 4.375rem;
}
.login-input {
	margin-bottom: 1.875rem;
}
.psimg {
	width: 1.5rem;
	height: 1.5rem;
	position: absolute;
	top: 0.625rem;
	left: 1.25rem;
	z-index: 99;
}
.footeb {
	position: fixed;
	bottom: 1.875rem;
	width: 36.25rem;
	/* padding-right: 6%; */
	/* background: #ff9000; */
	font-size: 13px;
	color: #6a6d7a;
	/* color: #000; */
	text-align: center;
}
/deep/ .ivu-input {
	height: 3rem;
	text-indent: 2.8125rem;
	font-size: 0.875rem;
}
/deep/ .ivu-form-item {
	margin-bottom: 0px;
}
/deep/ .inputHeight {
	.ivu-input-inner-container {
		input {
			height: 1.875rem;
			text-indent: 0.5rem;
		}
	}
}
.prompt {
	margin-bottom: 1.25rem;
	background: #f6f6f6;
	border-radius: 3px;
	color: #ff0000;
	padding: 5px 30px;
	text-align: center;
	font-size: 14;
	font-weight: bold;
}
.modify-password {
	margin-bottom: 1.25rem;
}
</style>
<template>
	<div class="container">
		<div class="left-content">
			<div class="thxhtitle">
				<img src="../../images/newlogo.png" alt style="width: 278px; height: 57px" />
			</div>
		</div>
		<div class="right-content">
			<!-- UKey： -->
			<div v-show="authpin">
				<Form :model="authsub" ref="formauth" class="login-formukey">
					<p class="login-pkey">UKey登录</p>
					<FormItem label="UKey：" :label-width="80">
						<Input v-model="authsub.key" placeholder="输入UKey：" style="width: 80%"></Input>
					</FormItem>
					<FormItem label="PIN：" :label-width="80">
						<Input
							v-model="authsub.pin"
							type="password"
							password
							placeholder="输入PIN："
							style="width: 80%"
						></Input>
					</FormItem>
					<FormItem>
						<Button type="primary" @click="devdalist" class="login-btnkey">登录</Button>
					</FormItem>
				</Form>
			</div>
			<!-- loginkey -->
			<div v-show="loginkey" class="loginkey">
				<Form ref="formInline" :model="formInline">
					<p class="login-p">
						<strong>天华星航数据备份与恢复系统</strong>
						<!-- <strong>THE VRTS数据备份与恢复软件</strong> -->
						<!-- <span><em>V</em>aluabe <em>R</em>ellable <em>T</em>rustable <em>S</em>table</span> -->
					</p>
					<FormItem prop="user">
						<img v-if="userbg" class="psimg" src="../../images/user.png" />
						<img v-else class="psimg" src="../../images/userh.png" />
						<!-- class="login-input" -->
						<Input
							v-model="formInline.user"
							@keyup.enter.native="handleSubmit('formInline')"
							@on-focus="userChangebg"
							@on-blur="userlostbg"
							placeholder="用户名"
							class="login-input"
							style="position: relative"
						/>
					</FormItem>
					<FormItem prop="password">
						<img v-if="passwordbg" class="psimg" src="../../images/password.png" />
						<img v-else class="psimg" src="../../images/passwordh.png" />
						<Input
							v-model="formInline.password"
							type="password"
							@keyup.enter.native="handleSubmit('formInline')"
							@on-focus="pswChangebg"
							@on-blur="pswlostbg"
							placeholder="密码"
							class="login-input input1"
							style="position: relative"
						/>
					</FormItem>
					<FormItem>
						<Button
							type="primary"
							@click="handleSubmit('formInline')"
							class="login-btn"
							style="font-size: 18px"
							>登录</Button
						>
					</FormItem>
				</Form>
			</div>
			<div class="footeb">
				<p>欢迎使用 应用版本号 {{ sysversion }} 建议使用Chrome 浏览器</p>
				<p>版权所有:北京天华星航科技有限公司 技术支持:北京天华星航科技有限公司</p>
				<p v-if="closeval">
					未检测到认证组件,MAC地址限制及UKEY登录功能不能使用,如需要使用请安装认证组件
					<span class="down-box" @click="downVRTSUkeyClient">点击下载</span>
				</p>
			</div>
		</div>

		<Modal v-model="modal1" :closable="false" :footer-hide="true" width="540">
			<div class="addPolicyModeStyle">
				<h3>修改密码</h3>
				<span class="iconfont" @click="passcancel">&#xe626;</span>
			</div>
			<Form :model="formItem" ref="formItem" :rules="ruleValidate" :label-width="90">
				<FormItem label="新密码：" prop="password" class="modify-password">
					<Input
						type="password"
						v-model="formItem.password"
						placeholder="请输入新密码"
						class="inputHeight"
						style="width: 90%"
					></Input>
				</FormItem>
				<FormItem label="重输密码：" prop="rpassword" class="modify-password">
					<Input
						type="password"
						v-model="formItem.rpassword"
						placeholder="重新输入新密码"
						class="inputHeight"
						style="width: 90%"
					></Input>
				</FormItem>
			</Form>
			<!-- <Form :label-width="50">
          <FormItem>
              <Button style="margin-right: 8px" @click="Reset">重 置</Button>
          </FormItem>
      </Form> -->
			<div class="prompt">
				请输入8-32个以字母开头、可带数字、“@”、“#”、“$”、“_”、“&”的密码且必须有一个指定的特殊字符
			</div>
			<div class="modefooter" style="width: 100%">
				<Button class="footerbnt bntclose" size="large" @click="passcancel" icon="md-close-circle"
					>取消</Button
				>
				<Button type="primary" size="large" @click="confirm" icon="md-checkmark-circle"
					>确定</Button
				>
			</div>
		</Modal>
		<Modal v-model="modal2" :closable="false" :footer-hide="true" width="540">
			<div class="addPolicyModeStyle">
				<h3>用户登录</h3>
				<span class="iconfont" @click="passcancel">&#xe626;</span>
			</div>
			<div style="margin-left: 20px">
				<p style="color: #f60">
					<Icon type="information-circled"></Icon>
					<span>登录失败：</span>
					<span>{{ this.messageValue }}</span>
				</p>
			</div>
			<div class="modefooter" style="width: 100%">
				<Button class="footerbnt bntclose" size="large" @click="passcancel">取消</Button>
				<Button type="primary" size="large" @click="errerok" icon="md-checkmark-circle"
					>确定</Button
				>
			</div>
		</Modal>
	</div>
</template>
<script>
import SoftKey3W from '../../utils/Syunew3W.js'
import md5 from 'js-md5';
import Vue from 'vue';
import axios from 'axios';
import util from '../../libs/util.js';
import { async } from '@antv/x6/lib/registry/marker/async.js';
// import { testbbbb } from '../../api/apitest.js'
export default {
	data() {
		const validatePass = (rule, value, callback) => {
			if (value === '') {
				callback(new Error('请输入密码'));
			} else {
				callback();
			}
		};
		const validatePassCheck = (rule, value, callback) => {
			if (this.formItem.password != '' && value === '') {
				callback(new Error('确认密码不能为空'));
			} else if (this.formItem.rpassword != value) {
				callback(new Error('新密码和确认密码应相同'));
			} else {
				callback();
			}
		};
		return {
			closeval: true,
			downloadUrl: '',
			effectiveMode: false,
			nowLists: [],
			sysversion: '',
			homehide: 0,
			privilegesList: [],
			passwordtime: '',
			tokenData: '',
			userbg: true,
			passwordbg: true,
			// tiname: "THE VRTS备份软件",
			authmodeuser: '',
			authname: '',
			authsub: {
				key: '',
				pin: ''
			},
			authpin: false,
			loginkey: true,
			modal1: false,
			modal2: false,
			messageValue: '',
			formItem: {
				old: '',
				password: '',
				rpassword: ''
			},
			formInline: {
				user: '',
				password: ''
			},
			ruleInline: {
				user: [
					{
						required: true,
						message: 'Please fill in the user name',
						trigger: 'blur'
					}
				],
				password: [
					{
						required: true,
						message: 'Please fill in the password.',
						trigger: 'blur'
					},
					{
						type: 'string',
						min: 8,
						message: 'The password length cannot be less than 6 bits',
						trigger: 'blur'
					}
				]
			},
			// 重复密码验证
			ruleValidate: {
				password: [
					{
						required: true,
						message: '密码不能为空',
						pattern: /^[a-zA-Z]{1}([a-zA-Z0-9]|[@#$_&]){5,19}$/,
						validator: validatePass,
						trigger: 'blur'
					}
					// {
					//     min: 8,
					//     message: "请输入8-20个以字母开头、可带数字、“@”、“#”、“$”、“_”、“&”的密码且必须有一个指定的特殊字符"
					// }
				],
				rpassword: [
					{
						required: true,
						validator: validatePassCheck,
						trigger: 'blur'
					},
					{
						min: 1
					}
				]
			},
			mac: ''
		};
	},
	created() {
		localStorage.clear();
		this.getauth();
		this.systemtype();
		localStorage.setItem('stauses', '1');
		var _this = this;
		document.onkeydown = function (e) {
			let key = window.event.keyCode;
			if (key == 13) {
				_this.handleSubmit('formInline');
			}
		};
		// 查询系统版本号
		util.restfullCall('/rest-ful/v3.0/upgrade/version', null, 'get', this.callbacksysban);
	},
	mounted() {
		//  this.initWebSocket();
		function getQueryParameter(name, url) {
			//地址栏带用户密码自动登录
			url = new URL(url);
			const params = new URLSearchParams(url.search);
			return params.get(name);
		}
		let str = window.location.href;
		let newUrl = str.replace(/#/g, '');
		const url = newUrl;
		if (window.location.href.indexOf('login?user=') != -1) {
			this.formInline.user = getQueryParameter('user', url);
			this.formInline.password = getQueryParameter('password', url);
			this.handleSubmit();
		}
		this.getmacaddress();
		this.$nextTick(() => {
			clearInterval(this.$parent.clearMessageInterval());
		});
	},
	methods: {
		downVRTSUkeyClient() {
			// 获取协议部分
			const protocol = window.location.protocol;
			// 获取主机名
			const hostname = window.location.hostname;
			const url = protocol + '//' + hostname + '/install/windows/VRTSUkeyClient_X64.exe';

			const a = document.createElement('a');
			a.href = url;
			a.download = 'VRTSUkeyClient_X64.exe'; // 设置下载的文件名
			document.body.appendChild(a);
			a.click();
			document.body.removeChild(a);
		},

		//取MAC地址
		getmacaddress() {
			let dynamicDort = '';
			let newhostname = '';
			// 获取完整的 URL
			const fullUrl = window.location.href;

			// 获取协议部分
			const protocol = window.location.protocol;

			// 获取端口号
			const port = window.location.port;

			// 获取主机名
			const hostname = window.location.hostname;

			if (hostname == 'localhost') {
				newhostname = '127.0.0.1';
			} else {
				newhostname = hostname;
			}
			if (protocol == 'https:' && port == '') {
				dynamicDort = 443;
			} else if (protocol == 'http:' && port == '') {
				dynamicDort = 80;
			} else {
				dynamicDort = port;
			}
			let mess = {
				Command: 'GetMAC',
				Data: {
					Address: newhostname,
					Port: Number(dynamicDort)
				}
			};
			//
			const socket = new WebSocket('ws://localhost:8080');
			// 监听连接建立事件
			socket.addEventListener('open', event => {
				const message = JSON.stringify(mess);
				// 发送消息给服务器
				socket.send(message);
			});
			// 监听消息接收事件
			socket.addEventListener('message', event => {
				if (event.data) {
					this.closeval = false;
					this.mac = JSON.parse(event.data).Data.Mac;
				}
				// this.mac = JSON.parse(event.data).Data.Mac;
			});
			// 监听连接关闭事件
			socket.addEventListener('close', event => {
				// this.closeval = false;
				// this.$Message.warning({ content: 'WebSocket连接失败,获取不到MAC地址!', duration: 3 });
			});
		},
		callbacksysban(res) {
			this.sysversion = res.data.data;
		},
		userChangebg(val) {
			if (val.isTrusted) {
				this.userbg = false;
			}
		},
		pswChangebg(val) {
			if (val.isTrusted) {
				this.passwordbg = false;
			}
		},
		userlostbg(val) {
			if (val.isTrusted) {
				this.userbg = true;
			}
		},
		pswlostbg(val) {
			if (val.isTrusted) {
				this.passwordbg = true;
			}
		},
		systemtype() {
			util.restfullCall('/rest-ful/v3.0/systemtype', null, 'get', this.syslocal);
		},
		syslocal(obj) {
			if (obj.data.data == 0) {
				localStorage.setItem('headname', 'THE数据备份与恢复系统');
				this.tiname = 'THE数据备份与恢复系统';
			}
		},
		getauth() {
			util.restfullCall('/rest-ful/v3.0/authmode', null, 'get', this.authdata);
		},
		authdata: function (obj) {
			// localStorage.setItem("auth",obj.data.mode)
			this.authmodeuser = obj.data.data;
			if (this.authmodeuser == 'UKey') {
				this.localSocket();
			}
		},
		localSocket() {
			let that = this;
			if ('WebSocket' in window) {
				//
				// location.host
				that.ws = new WebSocket('ws://127.0.0.1:8080');
				that.global.setWs(that.ws);
				that.ws.onmessage = that.websocketonmessage;
				that.ws.onopen = function () {
					// that.global.ws.send("WUSYANMING");
					let actions = {
						Command: 'DevList'
					};
					that.websocketsend(JSON.stringify(actions));
				};
				that.ws.onclose = function () {
					// 关闭 websocket

					//断线重新连接
					setTimeout(() => {
						that.localSocket();
					}, 2000);
				};
			} else {
				// 浏览器不支持 WebSocket

				this.openNotificationWithIcon('error', '浏览器', '您的浏览器不支持显示消息请更换', 1, 1);
			}
		},
		websocketsend(Data) {
			//数据发送
			this.global.ws.send(Data);
		},
		websocketonmessage(e) {
			//数据接收
			var obj = JSON.parse(e.data);
			//  if(obj.Command=="Export"){

			//  }
			if (obj.Command == 'DevList') {
				if (obj.Data.length) {
					let status = localStorage.getItem('stauses');
					if (status == '1') {
						this.loginkey = false;
						this.authpin = true;
					}

					this.authsub.key = obj.Data[0].SerialNo;
					// this.websocketsend(JSON.stringify({"Command":"QueryUserInfo", "Data":{"Device":"201810051101533", "PIN":"123456"}}))
				} else {
					// alert("请插入U盾")
					this.$Message.warning('请插入U盾!');
					// this.websocketclose;
				}
			}
			if (obj.Command == 'QueryUserInfo') {
				this.authname = obj.Data.Name;
				if (obj.Data) {
					this.websocketsend(
						JSON.stringify({
							Command: 'ExportRSAPublicKey',
							Data: {
								Device: this.authsub.key,
								PIN: this.authsub.pin
							}
						})
					);
				} else {
					alert('获取不到用户信息');
				}
			}
			if (obj.Command == 'ExportRSAPublicKey') {
				var keypublic = obj.Data.RSAPublicKey;
				var objlist = JSON.stringify({
					name: this.authname,
					key: keypublic,
					serial: this.authsub.key
				});

				util.restfullCall(
					'/rest-ful/v3.0/user/regist',
					objlist,
					'post'
					// this.registcall
				);
				util.restfullCall('/rest-ful/v3.0/rand', null, 'get', this.randcall);
			}
			if (obj.Command == 'RSASignData') {
				var signdata = JSON.stringify({
					name: this.authname,
					signature: obj.Data.RSASignData
				});

				util.restfullCall('/rest-ful/v3.0/user/auth', signdata, 'post', this.authcsll);
			}
			if (obj.Command == 'UsbKeyEvent' && obj.Data.State == '2') {
				this.adminClick();
			}
		},
		authcsll: function (res) {
			if (res.data.code == 0) {
				localStorage.setItem('stauses', '0');
				this.$Message.success('验证成功!');
				// this.$router.push("/home");
				this.$router.push('/home');
				var obj = {
					uid: res.data.uid
				};
				// this.formItem.old = this.formInline.password;
				localStorage.setItem('userName', this.authname);
				localStorage.setItem('lockstate', '1');
				// localStorage.setItem("timeValidity", res.data.period);
				localStorage.setItem('userInfo', JSON.stringify(obj));

				if (localStorage.errorLogin) {
					localStorage.removeItem('errorLogin');
				}
			} else {
				this.$Message.warning(res.data.message);
			}
		},
		randcall: function (obj) {
			var randata = obj.data.Data;

			let datasend = JSON.stringify({
				Command: 'RSASignData',
				Data: {
					Device: this.authsub.key,
					PIN: this.authsub.pin,
					Content: randata
				}
			});

			this.websocketsend(datasend);
		},
		devdalist: function () {
			this.authpin = false;
			let UKey = this.authsub.key;
			let PIN = this.authsub.pin;
			let senddata = JSON.stringify({
				Command: 'QueryUserInfo',
				Data: {
					Device: UKey,
					PIN: PIN
				}
			});

			this.websocketsend(senddata);
		},
		adminClick() {
			util.restfullCall('/rest-ful/v3.0/logout', null, 'get', this.logoutBack);
		},
		logoutBack(data) {
			if (data.data.code == 0) {
				this.$router.push('/login');
				localStorage.removeItem('errorLogin');
				localStorage.removeItem('lockstate');
				localStorage.removeItem('headname');
				localStorage.setItem('stauses', '0');
			}
		},
		errerok() {
			this.modal2 = false;
			this.messageValue = '';
		},

		methods1(arr) {
			return Array.from(new Set(arr));
		},

		// 设置密码确认修改回调
		passwordData: function (obj) {
			if (obj.data.code == 0) {
				localStorage.setItem('timeValidity', this.passwordtime);
				this.formItem.old = '';
				this.formItem.password = '';
				this.formItem.rpassword = '';
				this.$Message.success('密码修改成功！请重新输入密码');

				this.$router.push('/login');
				this.modal1 = false;
			} else {
				this.$Message.warning(obj.data.message);
			}
		},
		// 设置密码确认修改
		confirm: function () {
			var word = /^[a-zA-Z]{1}([a-zA-Z0-9]|[@#$_&]){5,19}$/;
			var user = JSON.parse(localStorage.getItem('userInfo'));

			if (this.formItem.password == this.formItem.rpassword && word.test(this.formItem.password)) {
				this.formInline.password = this.formItem.password;

				util.restfullCall(
					'/rest-ful/v3.0/usr/password/' + user.uid,
					{
						oldpassword: this.formItem.old,
						newpassword: this.formItem.password
					},
					// withCredentials: false,
					// headers: {
					//     Toden: this.tokenData,
					// },
					'put',
					this.passwordData
				);
			} else {
				this.$Message.error('输入密码格式错误！修改失败');
			}
		},
		// 重置密码按钮重置表单
		Reset: function () {
			// this.formItem.old = "";
			this.formItem.password = '';
			this.formItem.rpassword = '';
		},
		passcancel() {
			this.modal1 = false;
			this.modal2 = false;
			// this.$Message.info("关闭");
		},
		padDate: function (value) {
			return value < 10 ? '0' + value : value;
		},
		async handleSubmit() {
		const res = 	await this.mylogin_onclick();
		// if(!res) {
		// 	return
		// }
			//判断是否为登陆页面，防止点击回车按钮默认走此程序
			let _this = this;
			let path = _this.$route.path;
			if (path !== '/login') return;
			var date = new Date();
			var year = date.getFullYear();
			var month = _this.padDate(date.getMonth() + 1);
			var day = _this.padDate(date.getDate());
			var hours = _this.padDate(date.getHours());
			var minutes = _this.padDate(date.getMinutes());
			var second = _this.padDate(date.getSeconds());
			var now = year + month + day + hours + minutes + second;
			var key = md5(_this.formInline.user + _this.formInline.password + now);
			axios({
				method: 'get',
				url:
					'/rest-ful/v3.0/login?uid=' + _this.formInline.user + '&key=' + key + '&timestamp=' + now,
				headers: {
					mac: _this.mac
				}
			})
				.then(res => {
					if (res.data.code == 0) {
						_this.$store.commit('stroerole', res.data.data.role);
						localStorage.setItem('localrole', res.data.data.role);
					}
					if (res.data.code == 0 && res.data.data.needmodify == 0) {
						localStorage.setItem('token', res.data.data.accesskey);
						localStorage.setItem('tokenjm', res.headers.token);
						_this.$store.commit('tokenHeaders', res.headers.token);
						_this.tokenData = res.headers.token;

						_this.$store.commit('topbox', true);
						localStorage.setItem('getUid', res.data.data.uid);
						localStorage.setItem('roleUid', res.data.data.role);
						util.restfullCall(
							'/rest-ful/v3.0/user/privilege/' + res.data.data.uid,
							null,
							'get',
							_this.prinowList
						);
						// util.restfullCall(
						//     "/rest-ful/v3.0/system/license",
						//     null,
						//     "get",
						//     this.licenseList
						// );
						if (_this.authmodeuser == 'Normal') {
							var obj = {
								uid: res.data.data.uid
							};
							_this.passwordtime = res.data.data.period;

							_this.formItem.old = _this.formInline.password;
							localStorage.setItem('userName', _this.formInline.user);
							localStorage.setItem('lockstate', '1');
							localStorage.setItem('timeValidity', res.data.data.period);
							localStorage.setItem('userInfo', JSON.stringify(obj));
							// localStorage.setItem("roleUid", res.data.data.uid);
							if (localStorage.errorLogin) {
								localStorage.removeItem('errorLogin');
							}
							localStorage.setItem('priv', res.data.data.privileges);
							_this.privilegesList = res.data.data.privileges;
							if (res.data.data.privileges.length > 0) {
								let modlist = res.data.data.privileges;
								let modarr = [];
								modlist.forEach((item, i) => {
									//
									modarr.push(item.id);
									if (item.id == 124) {
										localStorage.setItem('cdpId', item.id);
									}
									if (item.id == 125) {
										localStorage.setItem('cdpBakId', item.id);
									}
									if (item.id == 126) {
										localStorage.setItem('cdpResId', item.id);
									}
								});
								//
								if (modarr.indexOf(1) == -1) {
									_this.homehide = 1;
									localStorage.setItem('hohide', _this.homehide);
									//
								} else {
									_this.homehide = 0;
									localStorage.setItem('hohide', _this.homehide);
									//
								}
								const uniqueArr = [];
								for (let i = 0; i < modarr.length; i++) {
									if (uniqueArr.indexOf(modarr[i]) === -1) {
										uniqueArr.push(modarr[i]);
									}
								}

								//
								// uniqueArr.sort((a, b) => (b === 1 ? -1 : 1));
								//
								// 	'uniqueArr-sort--',
								// 	uniqueArr.sort((a, b) => (b === 1 ? -1 : 1))
								// );

								for (let i = 0; i < uniqueArr.length; index++) {
									if (uniqueArr[i] == 1) {
										_this.$router.push('/home');

										break;
									}
									if (uniqueArr[i] == 2) {
										_this.$router.push('/usermanager');

										break;
									}
									if (uniqueArr[i] == 3) {
										_this.$router.push('/taskmonitor');
										break;
									}
									if (uniqueArr[i] == 4) {
										this.$router.push('/client');
										break;
									}
									if (uniqueArr[i] == 5) {
										_this.$router.push('/replicaMag');
										break;
									}
									if (uniqueArr[i] == 6) {
										_this.$router.push('/suniqueArr[i]ag');
										break;
									}
									if (uniqueArr[i] == 7) {
										_this.$router.push('/runRecReport');
										break;
									}
									if (uniqueArr[i] == 8) {
										// 系统管理
										_this.$router.push('/sysset');
										break;
									}
									if (uniqueArr[i] == 9) {
										_this.$router.push('/userMag');
										break;
									}
									if (uniqueArr[i] == 10) {
										_this.$router.push('/userGroupMag');
										break;
									}
									// if (uniqueArr[i] == 11) {
									// 	_this.$router.push('/policy');
									// 	break;
									// }
									if (uniqueArr[i] == 12) {
										_this.$router.push('/client');
										break;
									}
									if (uniqueArr[i] == 13) {
										_this.$router.push('/mediaserver');
										break;
									}
									if (uniqueArr[i] == 14) {
										_this.$router.push('/cliConfig');
										break;
									}
									if (uniqueArr[i] == 14) {
										_this.$router.push('/sysset');
										break;
									}
									if (uniqueArr[i] == 15) {
										_this.$router.push('/restoreManagement');
										break;
									}
									if (uniqueArr[i] == 16) {
										_this.$router.push('/remoteControl');
										break;
									}
									if (uniqueArr[i] == 17) {
										_this.$router.push('/mountMag');
										break;
									}
									if (uniqueArr[i] == 18) {
										_this.$router.push('/suniqueArr[i]ag');
										break;
									}
									if (uniqueArr[i] == 19) {
										_this.$router.push('/dataGuardMag');
										break;
									}
									if (uniqueArr[i] == 20) {
										_this.$router.push('/runRecReport');
										break;
									}
									if (uniqueArr[i] == 21) {
										_this.$router.push('/deviceReport');
										break;
									}
									if (uniqueArr[i] == 22) {
										_this.$router.push('/mediumReport');
										break;
									}
									if (uniqueArr[i] == 23) {
										_this.$router.push('/importCatalog');
										break;
									}
									if (uniqueArr[i] == 24) {
										_this.$router.push('/serverMag');
										break;
									}
									if (uniqueArr[i] == 25) {
										_this.$router.push('/systemlog');
										break;
									}
									if (uniqueArr[i] == 26) {
										_this.$router.push('/systemevent');
										break;
									}
									if (uniqueArr[i] == 27) {
										_this.$router.push('/sysset');
										break;
									}
									if (uniqueArr[i] == 28) {
										_this.$router.push('/sysPatrol');
										break;
									}
									if (uniqueArr[i] == 124) {
										_this.$router.push('/cdp_backup');
										break;
									}
									if (uniqueArr[i] == 125) {
										_this.$router.push('/cdp_backup');
										break;
									}
									if (uniqueArr[i] == 126) {
										_this.$router.push('/cdp_restore');
										break;
									}
								}
							} else {
								_this.$router.push('/welcome');
							}
						} else {
							_this.localSocket();
						}
					}
					if (res.data.code == 0 && res.data.data.needmodify == 1) {
						_this.privilegesList = res.data.data.privileges;

						_this.passwordtime = res.data.data.period;

						localStorage.setItem('userName', _this.formInline.user);
						localStorage.setItem('tokenjm', res.headers.token);
						var obj = {
							uid: res.data.data.uid
						};
						_this.formItem.old = _this.formInline.password;
						_this.modal1 = true;
						localStorage.setItem('userInfo', JSON.stringify(obj));
					}
					if (res.data.code != 0) {
						_this.messageValue = res.data.message;
						_this.modal2 = true;
					}
					if (res.data.code === 0) {
						this.$parent.messageInterval();
					}
				})
				.catch(error => {});
		},
		async mylogin_onclick(){
			var DevicePath;
			var ret, version, versionex;
			var mSoftKey3A = new SoftKey3W(); //注意，由于USB的操作是异步的，所以所有函数的调用前必须加await !!!!!!!!!
			DevicePath = await mSoftKey3A.FindPort(1); //'查找是否存在多把加密锁
			if (mSoftKey3A.GetLastError() == 0) {
				window.alert('系统上发现有2把及以上的加密锁，请只插入要进行的加密锁。');
				return false;
			}
			DevicePath = await mSoftKey3A.FindPort(0); //'查找是否存在加密锁
			if (mSoftKey3A.GetLastError() != 0) {
				window.alert('未发现加密锁，请插入加密锁');
				return false;
			}
			var addr;
			//储存在加密锁的用户名
			{
				addr = 0; //用户名储存在0开如的地址中，之前要用我们的设用户名及密码工具设置
				//先从地址0读取字符串的长度,使用默认的读密码"FFFFFFFF","FFFFFFFF"
				var OutLenBuf = await mSoftKey3A.YReadEx(addr, 1, 'ffffffff', 'ffffffff', DevicePath);
				var nlen = OutLenBuf[0];
				if (mSoftKey3A.GetLastError() != 0) {
					window.alert('读取用户名长度时错误。错误码：' + mSoftKey3A.GetLastError().toString());
					return false;
				}
				//再从地址1读取相应的长度的字符串，,使用默认的读密码"FFFFFFFF","FFFFFFFF"
				this.formInline.user = await mSoftKey3A.YReadString(addr + 1, nlen, 'ffffffff', 'ffffffff', DevicePath);
				if (mSoftKey3A.GetLastError() != 0) {
					window.alert('读取用户名时错误。错误码：' + mSoftKey3A.GetLastError().toString());
					return false;
				}
			}
			//储存在加密锁的用户密码，与上面的区别在于地址不一样，
			{
				addr = 20; //用户密码储存在0开如的地址中，之前要用我们的设用户名及密码工具设置
				//先从地址0读取字符串的长度,使用默认的读密码"FFFFFFFF","FFFFFFFF"
				var OutLenBuf = await mSoftKey3A.YReadEx(addr, 1, 'ffffffff', 'ffffffff', DevicePath);
				var nlen = OutLenBuf[0];
				if (mSoftKey3A.GetLastError() != 0) {
					window.alert('读取用户密码长度时错误。错误码：' + mSoftKey3A.GetLastError().toString());
					return false;
				}
				//再从地址1读取相应的长度的字符串，,使用默认的读密码"FFFFFFFF","FFFFFFFF"
				this.formInline.password = await mSoftKey3A.YReadString(addr + 1, nlen, 'ffffffff', 'ffffffff', DevicePath);
				if (mSoftKey3A.GetLastError() != 0) {
					window.alert('读取用户密码时错误。错误码：' + mSoftKey3A.GetLastError().toString());
					return false;
				}
			}
			return true;
		},
		// licenseList(obj) {
		//
		//     if (obj.data.code == 0) {
		//         localStorage.setItem("getEffectiveDays", obj.data.data.EffectiveDays);
		//         localStorage.setItem("getLicenseType", obj.data.data.Base.LicenseType);
		//     } else {
		//         this.$Message.warning("license连接失败");
		//     }
		// },
		cancel() {
			this.effectiveMode = false;
		},
		prinowList(data) {
			//
			let list = [];
			data.data.data.map(item => {
				list.push(item.id);
				if (item.id == 124) {
					localStorage.setItem('cdpId', item.id);
				}
				if (item.id == 125) {
					localStorage.setItem('cdpBakId', item.id);
				}
				if (item.id == 126) {
					localStorage.setItem('cdpResId', item.id);
				}
			});
			this.nowLists = list;
			this.$store.commit('privilegeData', this.nowLists);
		}
	}
};
</script>
