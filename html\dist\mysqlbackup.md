# MySQL数据库的备份与恢复

##	MySQL数据库手动备份
-	点击左侧策略管理，进入策略管理界面；
-	在策略管理界面，在要手动发起的备份策略右边，点击全量备份、日志备份等；

    ![MySQL数据库的备份与恢复](/dist/img/5-3-1-01.png)
 
##	MySQL数据库原路径恢复
-	点击左侧恢复管理，进入恢复管理界面；
-	第一步，在恢复管理界面，选择客户端名称、策略类型、起始时间、结束时间；
-	第二步，点击查询；
-	第三步，在显示的资源模块，选择要恢复的备份；
-	第四步，在选择需要恢复的数据模块，选择要恢复的资源，然后点击恢复，将进入恢复选项界面；

    ![MySQL数据库的备份与恢复](/dist/img/5-3-2-01.png)
 
 
-	在恢复选项界面，包括恢复到原路径、重定向恢复等；
-	下一步，选择恢复到原路径，点击开始恢复；

    ![MySQL数据库的备份与恢复](/dist/img/5-3-2-02.png)
 
-	点击左侧报表管理，在报表管理界面，可以看到恢复的任务状态，是否成功；

    ![MySQL数据库的备份与恢复](/dist/img/5-3-2-03.png)
 
##	MySQL数据库重定向恢复
-	点击左侧恢复管理，进入恢复管理界面；
-	第一步，在恢复管理界面，选择客户端名称、策略类型、起始时间、结束时间；
-	第二步，点击查询；
-	第三步，在显示的资源模块，选择要恢复的备份；
-	第四步，在选择需要恢复的数据模块，选择要恢复的资源，然后点击恢复，将进入恢复选项界面；
 
    ![MySQL数据库的备份与恢复](/dist/img/5-3-3-01.png)
 
-	在恢复选项界面，包括恢复到原路径、重定向恢复等；
-	下一步，勾选重定向恢复，并选择目的机器，实例名，点击下方开始恢复；

    ![MySQL数据库的备份与恢复](/dist/img/5-3-3-02.png)
 
 
-	点击左侧报表管理，在报表管理界面，可以看到恢复的任务状态，是否成功；
 
    ![MySQL数据库的备份与恢复](/dist/img/5-3-3-03.png)
