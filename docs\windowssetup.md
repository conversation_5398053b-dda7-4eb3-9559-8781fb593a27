# Windows系统客户端的配置

## THE VRTS客户端模块安装

-	点击VRTSClient_X64应用程序，将进入安装THEVRTS客户端程序界面；

    ![Windows系统客户端的配置](/dist/img/3-2-1-1-01.png)
 
-	在安装THEVRTS客户端程序的许可协议界面，选择“我同意此协议”点击“下一步”；
 
    ![Windows系统客户端的配置](/dist/img/3-2-1-1-02.png)
 
 
-	在安装THEVRTS客户端程序的选择目标位置界面，选择要安装目录，点击“下一步”；

    ![Windows系统客户端的配置](/dist/img/3-2-1-1-03.png)

-	在安装THEVRTS客户端程序的准备安装界面，点击“安装”；

    ![Windows系统客户端的配置](/dist/img/3-2-1-1-04.png)
 
-	在安装THEVRTS客户端程序的设置THE VRTS Master地址界面，填写MASTER服务的IP地址，点击“下一步”；

    ![Windows系统客户端的配置](/dist/img/3-2-1-1-05.png)
 
-	在安装THEVRTS客户端程序的正在安装界面；

    ![Windows系统客户端的配置](/dist/img/3-2-1-1-06.png)
 
-	在安装THEVRTS客户端程序的安装完成界面，点击“完成”；

    ![Windows系统客户端的配置](/dist/img/3-2-1-1-07.png)
 
## 	THE VRTS代理模块安装

## 	SQL server代理模块安装
-	点击VRTSMSSQLAgent_X64应用程序，将进入安装VRTS MSSQL备份代理程序界面； 

    ![Windows系统客户端的配置](/dist/img/3-2-1-2-01.png)

-	在安装VRTS MSSQL备份代理程序的许可协议界面，选择“我同意此协议”点击“下一步”；

    ![Windows系统客户端的配置](/dist/img/3-2-1-2-02.png)

-	在安装VRTS MSSQL备份代理程序的选择目标位置界面，选择要安装目录，点击“下一步”；
 
    ![Windows系统客户端的配置](/dist/img/3-2-1-2-03.png)

-	在安装VRTS MSSQL备份代理程序的选择目标位置界面，出现文件存在界面，点击“是”；

    ![Windows系统客户端的配置](/dist/img/3-2-1-2-04.png)

 
-	在安装VRTS MSSQL备份代理程序的设置准备安装界面，点击“安装”；
  
    ![Windows系统客户端的配置](/dist/img/3-2-1-2-05.png)
 
-	在安装VRTS MSSQL备份代理程序的安装完成界面，点击“完成”；
 
    ![Windows系统客户端的配置](/dist/img/3-2-1-2-06.png)

## 	MySQL代理模块安装
-	点击VRTSMYSQLAgent_X64应用程序，将进入安装VRTS MYSQL备份代理程序界面；

    ![Windows系统客户端的配置](/dist/img/3-2-1-3-01.png)
 
 
-	在安装VRTS MSSQL备份代理程序的许可协议界面，选择“我同意此协议”点击“下一步”；

    ![Windows系统客户端的配置](/dist/img/3-2-1-3-02.png)

-	在安装VRTS MYSQL备份代理程序的选择目标位置界面，选择要安装目录，点击“下一步”；

 
    ![Windows系统客户端的配置](/dist/img/3-2-1-3-03.png)

-	在安装VRTS MYSQL备份代理程序的选择目标位置界面，出现文件存在界面，点击“是”；

    ![Windows系统客户端的配置](/dist/img/3-2-1-3-04.png)
 
-	在安装VRTS MYSQL备份代理程序的设置准备安装界面，点击“安装”；

    ![Windows系统客户端的配置](/dist/img/3-2-1-3-05.png)
 
-	在安装VRTS MYSQL备份代理程序的安装完成界面，点击“完成”；

    ![Windows系统客户端的配置](/dist/img/3-2-1-3-06.png)
 
## 	Oracle代理模块安装
-	点击VRTSOracleAgent_X64应用程序，将进入安装VRTS ORACLE备份代理程序界面；
  
    ![Windows系统客户端的配置](/dist/img/3-2-1-4-01.png)
 
-	在安装VRTS ORACLE备份代理程序的许可协议界面，选择“我同意此协议”点击“下一步”；
  
    ![Windows系统客户端的配置](/dist/img/3-2-1-4-02.png)

-	在安装VRTS ORACLE备份代理程序的选择目标位置界面，选择要安装目录，点击“下一步”；
   
    ![Windows系统客户端的配置](/dist/img/3-2-1-4-03.png)

-	在安装VRTS ORACLE备份代理程序的选择目标位置界面，出现文件存在界面，点击“是”；

    ![Windows系统客户端的配置](/dist/img/3-2-1-4-04.png)
 
-	在安装VRTS ORACLE备份代理程序的设置准备安装界面，点击“安装”；

    ![Windows系统客户端的配置](/dist/img/3-2-1-4-05.png)

 
-	在安装VRTS ORACLE备份代理程序的安装完成界面，点击“完成”；

    ![Windows系统客户端的配置](/dist/img/3-2-1-4-06.png)
 
## DB2代理模块安装
-	点击VRTSDB2Agent_X64应用程序，将进入安装VRTS DB2 V9备份代理程序界面；
 
    ![Windows系统客户端的配置](/dist/img/3-2-1-5-01.png)
 
-	在安装VRTS DB2 V9备份代理程序的许可协议界面，选择“我同意此协议”点击“下一步”；

    ![Windows系统客户端的配置](/dist/img/3-2-1-5-02.png)

-	在安装VRTS DB2 V9备份代理程序的选择目标位置界面，选择要安装目录，点击“下一步”；

    ![Windows系统客户端的配置](/dist/img/3-2-1-5-03.png)
 
-	在安装VRTS DB2 V9备份代理程序的选择目标位置界面，出现文件存在界面，点击“是”；

    ![Windows系统客户端的配置](/dist/img/3-2-1-5-04.png)
 
-	在安装VRTS DB2 V9备份代理程序的设置准备安装界面，点击“安装”；
 
    ![Windows系统客户端的配置](/dist/img/3-2-1-5-05.png)
 
-	在安装VRTS DB2 V9备份代理程序的安装完成界面，点击“完成”；
 
    ![Windows系统客户端的配置](/dist/img/3-2-1-5-06.png)

##	VMware代理模块安装
-	点击VRTSVMwareAgent_X64应用程序，将进入安装VRTS VMWARE备份代理程序界面；

    ![Windows系统客户端的配置](/dist/img/3-2-1-6-01.png)
 
-	在安装VRTS VMWARE备份代理程序的许可协议界面，选择“我同意此协议”点击“下一步”
 
    ![Windows系统客户端的配置](/dist/img/3-2-1-6-02.png)
 
-	在安装VRTS VMWARE备份代理程序的选择目标位置界面，选择要安装目录，点击“下一步”；

    ![Windows系统客户端的配置](/dist/img/3-2-1-6-03.png)
 
-	在安装VRTS VMWARE备份代理程序的选择目标位置界面，出现文件存在界面，点击“是”；

    ![Windows系统客户端的配置](/dist/img/3-2-1-6-04.png)
 
-	在安装VRTS VMWARE备份代理程序的设置准备安装界面，点击“安装”；
 
    ![Windows系统客户端的配置](/dist/img/3-2-1-6-05.png)
 
-	在安装VRTS VMWARE备份代理程序的安装完成界面，点击“完成”；

    ![Windows系统客户端的配置](/dist/img/3-2-1-6-06.png)
 
## 	THE VRTS备份软件客户端的代理配置

## SQL server代理设置
-	点击左侧客户端，进入客户端界面；
-	在客户端界面可以看到要配置的客户端信息；
-	点击该客户端右侧的操作；
    ![Windows系统客户端的配置](/dist/img/3-2-2-1-01.png)
 
-	点击该客户端右侧的操作，进入客户端配置界面；
-	可以看到客户端的基本信息，包括客户端的机器名称、操作系统类型、软件版本、CPU、内存、状态；
    ![Windows系统客户端的配置](/dist/img/3-2-2-1-02.png)
 
-	在客户端配置点击右侧的MYSQL数据库，进入SQL server数据库配置界面；
-	在SQL server数据库配置界面，填写实例名、认证方式、用户、密码，点击测试连接；
 
    ![Windows系统客户端的配置](/dist/img/3-2-2-1-03.png)

 
-	如果测试连接通过，会显示测试连接成功；
-	测试显示成功后，点击确定；
    ![Windows系统客户端的配置](/dist/img/3-2-2-1-04.png)
 
-	点击添加实例，该客户端的SQL server代理配置完成；
-	最后点击确定配置SQL server代理完成。
 
    ![Windows系统客户端的配置](/dist/img/3-2-2-1-05.png)
 
##	MySQL代理设置
-	点击左侧客户端，进入客户端界面；
-	在客户端界面可以看到要配置的客户端信息；
-	点击该客户端右侧的操作；
    ![Windows系统客户端的配置](/dist/img/3-2-2-2-01.png)
 
-	点击该客户端右侧的操作，进入客户端配置界面；
-	可以看到客户端的基本信息，包括客户端的机器名称、操作系统类型、软件版本、CPU、内存、状态；
    ![Windows系统客户端的配置](/dist/img/3-2-2-2-02.png)
 

 
-	在客户端配置点击右侧的MYSQL数据库，进入MySQL数据库配置界面；
-	在MySQL数据库配置界面，填写服务地址、用户名、密码、端口，点击测试连接；
    ![Windows系统客户端的配置](/dist/img/3-2-2-2-03.png)
 
-	如果测试连接通过，会显示测试连接成功；
-	测试显示成功后，点击确定；
    ![Windows系统客户端的配置](/dist/img/3-2-2-2-04.png)
 
 
-	点击添加实例，该客户端的MySQL代理配置完成；
-	最后点击确定配置MySQL代理完成。

    ![Windows系统客户端的配置](/dist/img/3-2-2-2-05.png)
 
##	Oracle代理设置
-	点击左侧客户端，进入客户端界面；
-	在客户端界面可以看到要配置的客户端信息；
-	点击该客户端右侧的操作；
    ![Windows系统客户端的配置](/dist/img/3-2-2-3-01.png)
 
 
-	点击该客户端右侧的操作，进入客户端配置界面；
-	可以看到客户端的基本信息，包括客户端的机器名称、操作系统类型、软件版本、CPU、内存、状态；
    ![Windows系统客户端的配置](/dist/img/3-2-2-3-02.png)
 
-	在客户端配置点击右侧的MYSQL数据库，进入MySQL数据库配置界面；
-	在MySQL数据库配置界面，填写实例名、用户名、密码，点击测试连接；
    ![Windows系统客户端的配置](/dist/img/3-2-2-3-03.png)
 
 
-	如果测试连接通过，会显示测试连接成功；
-	测试显示成功后，点击确定；
    ![Windows系统客户端的配置](/dist/img/3-2-2-3-04.png)
 
-	点击添加实例，该客户端的MySQL代理配置完成；
-	最后点击确定配置MySQL代理完成。
    ![Windows系统客户端的配置](/dist/img/3-2-2-3-05.png)
 
##	DB2代理设置
DB2数据库在客户端中不用配置，只需在创建DB2备份策略时，添加DB2系统用户即可。详情请参考4.6创建DB2数据库备份策略；

##	VMware代理设置
-	点击左侧客户端，进入客户端界面；
-	在客户端界面可以看到要配置的客户端信息；
-	点击该客户端右侧的操作；
    ![Windows系统客户端的配置](/dist/img/3-2-2-5-01.png)
    
-	点击该客户端右侧的操作，进入客户端配置界面；
-	可以看到客户端的基本信息，包括客户端的机器名称、操作系统类型、软件版本、CPU、内存、状态；
    ![Windows系统客户端的配置](/dist/img/3-2-2-5-02.png)
 
 
- VMware虚拟化配置：
    ![Windows系统客户端的配置](/dist/img/3-2-2-5-03.png)
