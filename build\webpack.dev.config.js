const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const ExtractTextPlugin = require('extract-text-webpack-plugin');
const merge = require('webpack-merge');
const webpackBaseConfig = require('./webpack.base.config.js');
const fs = require('fs');
const package = require('../package.json');

module.exports = merge(webpackBaseConfig, {
	devtool: '#source-map',
	output: {
		publicPath: '/dist/',
		filename: '[name].js',
		chunkFilename: '[name].chunk.js'
	},
	plugins: [
		new ExtractTextPlugin({
			filename: '[name].css',
			allChunks: true
		}),
		new HtmlWebpackPlugin({
			title: 'THE VRTS备份软件',
			filename: '../index.html',
			inject: false
		})
	],
	devServer: {
		proxy: {
			'/rest-ful': {
				// target: "http://192.168.1.153:80",
				// target: "http://192.168.203.221",
				// target: "http://192.168.115.221",
				// target: "http://192.168.3.231",
				// target: "http://192.168.1.62",
				// target: 'http://192.168.101.62',
				// target: 'http://111.57.74.52:50067/',
				// target: "http://192.168.3.141",
				// target: "http://121.228.40.27:10070",
				// http://121.228.40.27:10070/
				// target: "http://192.168.203.155",
				// target: "http://47.99.202.132:10070/",
				// target: "http://121.196.207.204:10070/",
				// target: "http://192.168.3.162",
				// target: "http://192.168.203.155",
				// target: "http://192.168.2.11",
				target: 'http://192.168.101.62',
				// target: 'http://192.168.8.14',
				// target: 'http://111.57.74.52:50067',
				// target: 'http://120.46.6.67',
				changeOrigin: true
			},
			'/cdp': {
				// target: "http://192.168.7.170:9091/",
				target: 'http://192.168.203.155',
				// target: "http://192.168.8.14:9091/",
				// target: "http://192.168.114.19:9090/",
				changeOrigin: true,
				ws: true
				// pathRewrite: {
				// 	'^/cdp': '',
				// },
			}
		}
	}
});
