{"name": "loader-utils", "version": "0.2.17", "author": "<PERSON> @sokra", "description": "utils for webpack loaders", "dependencies": {"big.js": "^3.1.3", "emojis-list": "^2.0.0", "json5": "^0.5.0", "object-assign": "^4.0.1"}, "scripts": {"test": "mocha", "travis": "npm run cover -- --report lcovonly", "cover": "istanbul cover -x *.runtime.js node_modules/mocha/bin/_mocha", "publish-patch": "mocha && npm version patch && git push && git push --tags && npm publish"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/webpack/loader-utils.git"}, "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.3.14", "mocha": "^1.21.4"}, "files": ["index.js"]}