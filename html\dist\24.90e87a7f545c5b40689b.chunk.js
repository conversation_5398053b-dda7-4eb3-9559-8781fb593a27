webpackJsonp([24],{

/***/ 1538:
/***/ (function(module, exports, __webpack_require__) {

module.exports = { "default": __webpack_require__(1596), __esModule: true };

/***/ }),

/***/ 1596:
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(1597);
module.exports = __webpack_require__(31).Object.assign;


/***/ }),

/***/ 1597:
/***/ (function(module, exports, __webpack_require__) {

// ******** Object.assign(target, source)
var $export = __webpack_require__(43);

$export($export.S + $export.F, 'Object', { assign: __webpack_require__(1598) });


/***/ }),

/***/ 1598:
/***/ (function(module, exports, __webpack_require__) {

"use strict";

// ******** Object.assign(target, source, ...)
var DESCRIPTORS = __webpack_require__(58);
var getKeys = __webpack_require__(212);
var gOPS = __webpack_require__(320);
var pIE = __webpack_require__(213);
var toObject = __webpack_require__(133);
var IObject = __webpack_require__(319);
var $assign = Object.assign;

// should work with symbols and should have deterministic property order (V8 bug)
module.exports = !$assign || __webpack_require__(114)(function () {
  var A = {};
  var B = {};
  // eslint-disable-next-line no-undef
  var S = Symbol();
  var K = 'abcdefghijklmnopqrst';
  A[S] = 7;
  K.split('').forEach(function (k) { B[k] = k; });
  return $assign({}, A)[S] != 7 || Object.keys($assign({}, B)).join('') != K;
}) ? function assign(target, source) { // eslint-disable-line no-unused-vars
  var T = toObject(target);
  var aLen = arguments.length;
  var index = 1;
  var getSymbols = gOPS.f;
  var isEnum = pIE.f;
  while (aLen > index) {
    var S = IObject(arguments[index++]);
    var keys = getSymbols ? getKeys(S).concat(getSymbols(S)) : getKeys(S);
    var length = keys.length;
    var j = 0;
    var key;
    while (length > j) {
      key = keys[j++];
      if (!DESCRIPTORS || isEnum.call(S, key)) T[key] = S[key];
    }
  } return T;
} : $assign;


/***/ }),

/***/ 1617:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


exports.__esModule = true;

var _assign = __webpack_require__(1538);

var _assign2 = _interopRequireDefault(_assign);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

exports.default = _assign2.default || function (target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i];

    for (var key in source) {
      if (Object.prototype.hasOwnProperty.call(source, key)) {
        target[key] = source[key];
      }
    }
  }

  return target;
};

/***/ }),

/***/ 1708:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


exports.__esModule = true;

var _from = __webpack_require__(541);

var _from2 = _interopRequireDefault(_from);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

exports.default = function (arr) {
  if (Array.isArray(arr)) {
    for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) {
      arr2[i] = arr[i];
    }

    return arr2;
  } else {
    return (0, _from2.default)(arr);
  }
};

/***/ }),

/***/ 2005:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});
exports.getDeviceImageDelete = exports.disasterReschedule = exports.d2d2tReschedule = exports.getDeviceImageList = exports.getDevices = exports.getclientsList = undefined;

var _request = __webpack_require__(316);

var _request2 = _interopRequireDefault(_request);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

var getclientsList = exports.getclientsList = function getclientsList(params) {
	return (0, _request2.default)({
		method: 'get',
		url: '/rest-ful/v3.0/restore/clients',
		params: params
	});
};

var getDevices = exports.getDevices = function getDevices(params) {
	return (0, _request2.default)({
		method: 'get',
		url: '/rest-ful/v3.0/devices?type=0',
		params: params
	});
};

var getDeviceImageList = exports.getDeviceImageList = function getDeviceImageList(params) {
	return (0, _request2.default)({
		method: 'get',
		url: '/rest-ful/v3.0/images',
		params: params
	});
};

var d2d2tReschedule = exports.d2d2tReschedule = function d2d2tReschedule(params) {
	return (0, _request2.default)({
		method: 'get',
		url: '/rest-ful/v3.0/d2d2t/reschedule/' + params
	});
};

var disasterReschedule = exports.disasterReschedule = function disasterReschedule(params) {
	return (0, _request2.default)({
		method: 'get',
		url: '/rest-ful/v3.0/disaster/reschedule/' + params
	});
};

var getDeviceImageDelete = exports.getDeviceImageDelete = function getDeviceImageDelete(data) {
	return (0, _request2.default)({
		method: 'POST',
		url: '/rest-ful/v3.0/images/delete',
		data: data
	});
};

/***/ }),

/***/ 2947:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _extends2 = __webpack_require__(1617);

var _extends3 = _interopRequireDefault(_extends2);

var _toConsumableArray2 = __webpack_require__(1708);

var _toConsumableArray3 = _interopRequireDefault(_toConsumableArray2);

var _SearchComponent = __webpack_require__(4268);

var _SearchComponent2 = _interopRequireDefault(_SearchComponent);

var _imageManagement = __webpack_require__(2005);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
	provide: function provide() {
		return {
			parent: this
		};
	},

	components: {
		SearchComponent: _SearchComponent2.default
	},
	data: function data() {
		return {
			disasterLilt: ['拷贝失败', '复制失败'],
			noImgUrl: __webpack_require__(317),
			tableLoading: true,
			tableHeight: window.innerHeight - 270,
			columns: [{
				type: 'selection',
				width: 60,
				align: 'center'
			}, {
				title: '任务ID',
				key: 'task',
				width: 80
			}, {
				title: '镜像ID',
				key: 'id',
				width: 80
			}, {
				title: '客户端',

				slot: 'client'
			}, {
				title: '创建时间',

				slot: 'createtime',
				width: 150
			}, {
				title: '设备',

				slot: 'device',
				width: 150
			}, {
				title: '过期时间',

				slot: 'expiredtime'
			}, {
				title: '镜像名称',

				slot: 'image'
			}, {
				title: '策略名称',

				slot: 'policy'
			}, {
				title: '站点名称',

				slot: 'station'
			}, {
				title: '数据量',

				slot: 'Bytes'
			}, {
				title: '介质池',
				key: 'pool',
				width: 110
			}, {
				title: '保留天数',
				key: 'savedays',
				width: 100
			}, {
				title: '状态',
				key: 'status'
			}, {
				title: 'D2D2T状态',
				key: 'D2D2T',
				width: 110
			}, {
				title: '远程复制状态',
				key: 'disasterStatus',
				width: 110
			}, {
				title: '操作',
				slot: 'action',
				width: 180,
				align: 'center'
			}],
			fromData: [],
			drawer: false,
			showColumns: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
			policytotal: 0,
			params: {
				pageno: 1,
				nums: 20
			},
			searchData: {},
			selectVal: [] };
	},
	mounted: function mounted() {},
	created: function created() {
		this.init();
	},


	watch: {},

	computed: {
		filterColumns: function filterColumns() {
			var columns = [].concat((0, _toConsumableArray3.default)(this.columns));
			var filterColumns = [];
			this.showColumns.sort(function (a, b) {
				return a - b;
			}).forEach(function (item) {
				filterColumns.push(columns[item]);
			});
			return filterColumns;
		}
	},

	methods: {
		init: function init() {
			var _this = this;

			var params = (0, _extends3.default)({}, this.params, this.searchData);
			(0, _imageManagement.getDeviceImageList)(params).then(function (res) {
				if (!_this.$isNull(res.data.images)) {
					_this.fromData = res.data.images;
				} else {
					_this.fromData = [];
				}
				_this.policytotal = res.data.nums;
				_this.tableLoading = false;
			}).catch(function (err) {
				_this.tableLoading = false;
			});
		},
		tishi: function tishi(currentRow, index) {
			return 'trbgshow_a';
		},
		sizeChange: function sizeChange(value) {
			this.params.nums = value;
			this.init();
		},
		currentChange: function currentChange(value) {
			this.params.pageno = value;
			this.init();
		},
		onSearch: function onSearch(data) {
			this.searchData = data;
			this.params.pageno = 1;
			this.init();
		},
		onReset: function onReset() {
			this.searchData = {};
			this.params.pageno = 1;
			this.init();
		},
		onDelete: function onDelete(row, index) {
			this.$Modal.confirm;
			this.init();
		},
		onD2d2t: function onD2d2t(row, index) {
			var _this2 = this;

			(0, _imageManagement.d2d2tReschedule)(row.id).then(function (res) {
				if (res.code === 0) {
					_this2.$Message.success('调度成功');
				} else {
					_this2.$Message.error('调度失败');
				}
			});
		},
		onRemotecopy: function onRemotecopy(row, index) {
			var _this3 = this;

			(0, _imageManagement.disasterReschedule)(row.id).then(function (res) {
				if (res.code === 0) {
					_this3.$Message.success('调度成功');
				} else {
					_this3.$Message.error('调度失败');
				}
			});
		},
		handleSelectionChange: function handleSelectionChange(val) {
			this.selectVal = val.map(function (item) {
				return item.id;
			});
		},
		onShowColumns: function onShowColumns(val) {
			this.showColumns = val;
		}
	}
};

/***/ }),

/***/ 2948:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _regenerator = __webpack_require__(537);

var _regenerator2 = _interopRequireDefault(_regenerator);

var _asyncToGenerator2 = __webpack_require__(538);

var _asyncToGenerator3 = _interopRequireDefault(_asyncToGenerator2);

var _imageManagement = __webpack_require__(2005);

var _ModalBox = __webpack_require__(540);

var _ModalBox2 = _interopRequireDefault(_ModalBox);

var _index = __webpack_require__(210);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
	inject: ['parent'],
	components: {
		ModalBox: _ModalBox2.default
	},
	data: function data() {
		return {
			showModalBox: false,
			form: {
				policy: '',
				client: '',
				d2d2t: '',
				remotecopy: '',
				taskId: '',
				device: ''
			},
			clientSelect: [],
			statusList: [{
				code: 0,
				name: '不需要复制'
			}, {
				code: 1,
				name: '等待复制'
			}, {
				code: 2,
				name: '正在复制'
			}, {
				code: 3,
				name: '复制失败'
			}, {
				code: 4,
				name: '复制完成'
			}],
			devicesList: [],
			tableTh: [{ title: '多选', key: 0 }, { title: '任务ID', key: 1 }, { title: '镜像ID', key: 2 }, { title: '客户端', key: 3 }, { title: '创建时间', key: 4 }, { title: '设备', key: 5 }, { title: '过期时间', key: 6 }, { title: '镜像名称', key: 7 }, { title: '策略名称', key: 8 }, { title: '站点名称', key: 9 }, { title: '数据量', key: 10 }, { title: '介质池', key: 11 }, { title: '保存天数', key: 12 }, { title: '状态', key: 13 }, { title: 'D2D2T状态', key: 14 }, { title: '远程复制状态', key: 15 }, { title: '操作栏', key: 16 }],
			showColumns: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
			delectValue: ''
		};
	},
	mounted: function mounted() {},
	created: function created() {
		this.getclientsList();
		this.getDevices();
	},


	watch: {
		form: {
			handler: function handler(newValue) {
				if (newValue) {
					this.parent.onSearch(this.form);
				}
			},

			deep: true },
		showColumns: function showColumns(newVal) {
			if (newVal) {
				this.parent.onShowColumns(newVal);
			}
		}
	},

	computed: {},

	methods: {
		getclientsList: function () {
			var _ref = (0, _asyncToGenerator3.default)(_regenerator2.default.mark(function _callee() {
				var _this = this;

				var params;
				return _regenerator2.default.wrap(function _callee$(_context) {
					while (1) {
						switch (_context.prev = _context.next) {
							case 0:
								params = {
									nums: 0,
									delete_flag: 1
								};
								_context.next = 3;
								return (0, _imageManagement.getclientsList)(params).then(function (res) {
									_this.clientSelect = res.data.map(function (item) {
										return { id: item.id, name: item.name };
									});
								});

							case 3:
							case 'end':
								return _context.stop();
						}
					}
				}, _callee, this);
			}));

			function getclientsList() {
				return _ref.apply(this, arguments);
			}

			return getclientsList;
		}(),
		getDevices: function () {
			var _ref2 = (0, _asyncToGenerator3.default)(_regenerator2.default.mark(function _callee2() {
				var _this2 = this;

				return _regenerator2.default.wrap(function _callee2$(_context2) {
					while (1) {
						switch (_context2.prev = _context2.next) {
							case 0:
								_context2.next = 2;
								return (0, _imageManagement.getDevices)().then(function (res) {
									_this2.devicesList = res.data;
								});

							case 2:
							case 'end':
								return _context2.stop();
						}
					}
				}, _callee2, this);
			}));

			function getDevices() {
				return _ref2.apply(this, arguments);
			}

			return getDevices;
		}(),
		showDrawerTitle: function showDrawerTitle() {
			this.parent.drawer = true;
		},
		batchDeleteFun: function batchDeleteFun() {
			this.showModalBox = true;
		},
		postStatus: function postStatus() {
			var _this3 = this;

			if (this.$isNull(this.delectValue)) {
				this.$Message.warning('请输入确认信息');
				return;
			}
			if (this.delectValue !== 'DELETE' && this.delectValue !== 'delete') {
				this.$Message.warning('请输入正确的确认信息');
				return;
			}

			(0, _imageManagement.getDeviceImageDelete)(this.parent.selectVal).then(function (res) {
				if (res.code === 0) {
					_this3.$Message.success('删除成功');
					_this3.showModalBox = false;
					_this3.delectValue = '';
					_this3.parent.init();
				}
			});
		},
		deleteclance: function deleteclance() {
			this.showModalBox = false;
			this.delectValue = '';
		}
	}
};

/***/ }),

/***/ 4266:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(4267);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("fc4c61ba", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-d992fd52\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/less-loader/dist/cjs.js!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./index.vue", function() {
     var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-d992fd52\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/less-loader/dist/cjs.js!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./index.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 4267:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.image-management[data-v-d992fd52]{width:100%;height:100%;overflow:hidden\n}\n.image-management .main[data-v-d992fd52]{width:100%;height:100%;padding:20px 15px 70px\n}\n.image-management .main .table-box[data-v-d992fd52]{width:100%;height:calc(100vh - 205px);border-radius:0 0 20px 20px;padding:10px 15px 0;margin-bottom:15px;background:#fff;overflow:hidden;position:relative\n}\n.image-management .main[data-v-d992fd52] .ivu-table-row td{white-space:nowrap;overflow:hidden;-o-text-overflow:ellipsis;text-overflow:ellipsis\n}\n.image-management .main .page-wrap[data-v-d992fd52]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:end;-ms-flex-pack:end;justify-content:flex-end;position:absolute;bottom:10px;right:10px\n}\n.image-management[data-v-d992fd52] .trbgshow_a{cursor:default!important\n}\n.image-management .row-slot-box[data-v-d992fd52]{cursor:pointer;white-space:nowrap;overflow:hidden;-o-text-overflow:ellipsis;text-overflow:ellipsis\n}\n.image-management .row-action-box[data-v-d992fd52]{-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center\n}\n.image-management .row-action-box[data-v-d992fd52],.title-list[data-v-d992fd52]{display:-webkit-box;display:-ms-flexbox;display:flex\n}\n.title-list[data-v-d992fd52]{-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column\n}\n.d2d2-color[data-v-d992fd52]{color:#635df7;cursor:pointer\n}\n.d2d2-color[data-v-d992fd52],.d2d2t-dis-color[data-v-d992fd52]{font-size:1.25rem;margin-right:5px\n}\n.d2d2t-dis-color[data-v-d992fd52]{color:#c1bfff;cursor:no-drop\n}\n.copy-color[data-v-d992fd52]{color:#165dff;cursor:pointer\n}\n.copy-color[data-v-d992fd52],.copy-dis-color[data-v-d992fd52]{font-size:1.25rem;margin-left:5px\n}\n.copy-dis-color[data-v-d992fd52]{color:#aac4ff;cursor:no-drop\n}", ""]);

// exports


/***/ }),

/***/ 4268:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_SearchComponent_vue__ = __webpack_require__(2948);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_SearchComponent_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_SearchComponent_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_SearchComponent_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_SearchComponent_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_77b1aae7_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_SearchComponent_vue__ = __webpack_require__(4271);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_77b1aae7_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_SearchComponent_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_77b1aae7_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_SearchComponent_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(4269)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = "data-v-77b1aae7"
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_SearchComponent_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_77b1aae7_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_SearchComponent_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/imageManagement/components/SearchComponent.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-77b1aae7", Component.options)
  } else {
    hotAPI.reload("data-v-77b1aae7", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ }),

/***/ 4269:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(4270);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("564c3ae9", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-77b1aae7\",\"scoped\":true,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./SearchComponent.vue", function() {
     var newContent = require("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-77b1aae7\",\"scoped\":true,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./SearchComponent.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 4270:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.search-top[data-v-77b1aae7]{width:100%;padding:0 20px;height:55px;background:#fff;border-radius:20px 20px 0 0\n}\n.search-top .line[data-v-77b1aae7]{width:100%;height:100%;border-bottom:1px solid #e1e2e8;padding-left:10px\n}\n.search-top .line[data-v-77b1aae7],.search-top .policy-name[data-v-77b1aae7]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center\n}\n.search-top .policy-name[data-v-77b1aae7]{margin-left:15px\n}\n.search-top .policy-name .lable[data-v-77b1aae7]{font-size:0.75rem;padding-right:8px;color:#4e516b\n}\n.search-top .button-box[data-v-77b1aae7]{margin-left:50px;display:-webkit-box;display:-ms-flexbox;display:flex\n}\n.search-top .button-box .export-but-wrap[data-v-77b1aae7]{width:80px;height:35px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;font-size:0.875rem;margin-right:10px\n}\n.search-top .button-box .clear-but[data-v-77b1aae7]:hover,.search-top .button-box .sarch-but[data-v-77b1aae7]:hover{background:#f7cbad!important;color:#fff!important;border-color:#f7cbad!important\n}\n.search-top .but-box[data-v-77b1aae7]{margin-left:10px\n}\n.search-top .policy-name-select[data-v-77b1aae7] .ivu-select-selection{height:32px;overflow:hidden\n}\n.delect-verify[data-v-77b1aae7]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:left;-ms-flex-align:left;align-items:left;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;margin-top:-10px;padding-bottom:30px;padding-left:50px\n}\n.delect-verify p[data-v-77b1aae7]{font-size:1rem;color:#f24e4e;padding-bottom:5px\n}\n.modeContent-wrap[data-v-77b1aae7]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;font-size:1rem;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;min-height:50px;margin-bottom:15px;margin-left:-30px\n}\n.modeContent-wrap span[data-v-77b1aae7]{margin-right:5px;color:#f24e4e\n}\n.modeContent-wrap p[data-v-77b1aae7]{font-size:1.0625rem;color:#f24e4e\n}", ""]);

// exports


/***/ }),

/***/ 4271:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", [_c("div", { staticClass: "search-top" }, [_c("div", { staticClass: "line" }, [_c("Button", {
    staticClass: "export-but-wrap",
    staticStyle: { "margin-right": "15px" },
    attrs: { type: "primary", size: "small" },
    on: { click: _vm.batchDeleteFun }
  }, [_c("span", { staticClass: "iconfont export-icon-wrap" }, [_vm._v("")]), _vm._v("  批量删除\n\t\t\t")]), _vm._v(" "), _c("div", { staticClass: "policy-name" }, [_c("div", { staticClass: "lable" }, [_vm._v("任务ID")]), _vm._v(" "), _c("div", [_c("Input", {
    attrs: { clearable: "", placeholder: "请输入任务ID" },
    model: {
      value: _vm.form.task,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "task", $$v);
      },
      expression: "form.task"
    }
  })], 1)]), _vm._v(" "), _c("div", { staticClass: "policy-name" }, [_c("div", { staticClass: "lable" }, [_vm._v("策略名称")]), _vm._v(" "), _c("div", [_c("Input", {
    attrs: { clearable: "", placeholder: "请输入策略名称" },
    model: {
      value: _vm.form.policy,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "policy", $$v);
      },
      expression: "form.policy"
    }
  })], 1)]), _vm._v(" "), _c("div", { staticClass: "policy-name" }, [_c("div", { staticClass: "lable" }, [_vm._v("客户端")]), _vm._v(" "), _c("div", [_c("Select", {
    staticStyle: { width: "160px" },
    attrs: { clearable: "", filterable: "" },
    model: {
      value: _vm.form.client,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "client", $$v);
      },
      expression: "form.client"
    }
  }, _vm._l(_vm.clientSelect, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.id } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1)]), _vm._v(" "), _c("div", { staticClass: "policy-name" }, [_c("div", { staticClass: "lable" }, [_vm._v("D2D2T")]), _vm._v(" "), _c("div", [_c("Select", {
    staticStyle: { width: "160px" },
    attrs: { clearable: "", filterable: "" },
    model: {
      value: _vm.form.d2d2t,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "d2d2t", $$v);
      },
      expression: "form.d2d2t"
    }
  }, _vm._l(_vm.statusList, function (item) {
    return _c("Option", { key: item.code, attrs: { value: item.code } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1)]), _vm._v(" "), _c("div", { staticClass: "policy-name" }, [_c("div", { staticClass: "lable" }, [_vm._v("远程复制")]), _vm._v(" "), _c("div", [_c("Select", {
    staticStyle: { width: "160px" },
    attrs: { clearable: "", filterable: "" },
    model: {
      value: _vm.form.remotecopy,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "remotecopy", $$v);
      },
      expression: "form.remotecopy"
    }
  }, _vm._l(_vm.statusList, function (item) {
    return _c("Option", { key: item.code, attrs: { value: item.code } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1)]), _vm._v(" "), _c("div", { staticClass: "policy-name" }, [_c("div", { staticClass: "lable" }, [_vm._v("设备")]), _vm._v(" "), _c("div", [_c("Select", {
    staticStyle: { width: "160px" },
    attrs: { clearable: "", filterable: "" },
    model: {
      value: _vm.form.device,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "device", $$v);
      },
      expression: "form.device"
    }
  }, _vm._l(_vm.devicesList, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.id } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1)]), _vm._v(" "), _c("div", { staticClass: "policy-name policy-name-select" }, [_c("div", { staticClass: "lable" }, [_vm._v("表格列")]), _vm._v(" "), _c("div", [_c("Select", {
    staticStyle: { width: "150px" },
    attrs: { multiple: "" },
    model: {
      value: _vm.showColumns,
      callback: function callback($$v) {
        _vm.showColumns = $$v;
      },
      expression: "showColumns"
    }
  }, _vm._l(_vm.tableTh, function (item) {
    return _c("Option", { key: item.key, attrs: { value: item.key } }, [_vm._v(_vm._s(item.title))]);
  }), 1)], 1)])], 1)]), _vm._v(" "), _c("Modal", {
    attrs: { closable: false, "footer-hide": true, width: "540" },
    model: {
      value: _vm.showModalBox,
      callback: function callback($$v) {
        _vm.showModalBox = $$v;
      },
      expression: "showModalBox"
    }
  }, [_c("div", { staticClass: "addPolicyModeStyle" }, [_c("h3", [_vm._v("删除镜像")]), _vm._v(" "), _c("span", { staticClass: "iconfont", on: { click: _vm.deleteclance } }, [_vm._v("")])]), _vm._v(" "), _c("div", { staticClass: "modeContent-wrap" }, [_c("span", { staticClass: "iconfont" }, [_vm._v("")]), _vm._v(" "), _c("p", [_vm._v("删除镜像后可能会影响数据的恢复,是否确认删除?")])]), _vm._v(" "), _c("div", { staticClass: "delect-verify" }, [_c("p", [_vm._v("请输入“DELETE”进行确认删除，确认后将无法恢复！")]), _vm._v(" "), _c("Input", {
    staticStyle: { width: "300px" },
    attrs: { placeholder: "请输入DELETE" },
    model: {
      value: _vm.delectValue,
      callback: function callback($$v) {
        _vm.delectValue = $$v;
      },
      expression: "delectValue"
    }
  })], 1), _vm._v(" "), _c("div", { staticClass: "modefooter", staticStyle: { width: "100%" } }, [_c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { size: "large", icon: "md-close-circle" },
    on: { click: _vm.deleteclance }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      size: "large",
      icon: "md-checkmark-circle"
    },
    on: { click: _vm.postStatus }
  }, [_vm._v("确定")])], 1)])], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-77b1aae7", esExports);
  }
}

/***/ }),

/***/ 4272:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", { staticClass: "image-management" }, [_c("BreadcrumbNav", { attrs: { title: "镜像管理" } }), _vm._v(" "), _c("div", { staticClass: "main" }, [_c("SearchComponent"), _vm._v(" "), _c("div", { staticClass: "table-box" }, [_c("Table", {
    ref: "selection",
    attrs: {
      border: "",
      loading: _vm.tableLoading,
      "row-class-name": _vm.tishi,
      columns: _vm.filterColumns,
      data: _vm.fromData,
      height: _vm.tableHeight,
      "no-data-text": "<div class='no-img-url-text'><img class='noImgUrl' src=" + _vm.noImgUrl + " /><div class='text'>暂无数据</div><div>"
    },
    on: { "on-selection-change": _vm.handleSelectionChange },
    scopedSlots: _vm._u([{
      key: "client",
      fn: function fn(ref) {
        var row = ref.row;
        return [_c("Tooltip", { attrs: { content: row.client, placement: "top" } }, [_c("div", { staticClass: "row-slot-box" }, [_vm._v(_vm._s(row.client))])])];
      }
    }, {
      key: "createtime",
      fn: function fn(ref) {
        var row = ref.row;
        return [_c("Tooltip", {
          attrs: { content: row.createtime, placement: "top" }
        }, [_c("div", { staticClass: "row-slot-box" }, [_vm._v(_vm._s(row.createtime))])])];
      }
    }, {
      key: "device",
      fn: function fn(ref) {
        var row = ref.row;
        return [_c("Tooltip", { attrs: { content: row.device, placement: "top" } }, [_c("div", { staticClass: "row-slot-box" }, [_vm._v(_vm._s(row.device))])])];
      }
    }, {
      key: "expiredtime",
      fn: function fn(ref) {
        var row = ref.row;
        return [_c("Tooltip", {
          attrs: {
            content: row.expiredtime,
            placement: "top"
          }
        }, [_c("div", { staticClass: "row-slot-box" }, [_vm._v(_vm._s(row.expiredtime))])])];
      }
    }, {
      key: "image",
      fn: function fn(ref) {
        var row = ref.row;
        return [_c("Tooltip", { attrs: { content: row.image, placement: "top" } }, [_c("div", { staticClass: "row-slot-box" }, [_vm._v(_vm._s(row.image))])])];
      }
    }, {
      key: "policy",
      fn: function fn(ref) {
        var row = ref.row;
        return [_c("Tooltip", { attrs: { content: row.policy, placement: "top" } }, [_c("div", { staticClass: "row-slot-box" }, [_vm._v(_vm._s(row.policy))])])];
      }
    }, {
      key: "station",
      fn: function fn(ref) {
        var row = ref.row;
        return [_c("Tooltip", { attrs: { content: row.station, placement: "top" } }, [_c("div", { staticClass: "row-slot-box" }, [_vm._v(_vm._s(row.station))])])];
      }
    }, {
      key: "Bytes",
      fn: function fn(ref) {
        var row = ref.row;
        return [_c("Tooltip", { attrs: { content: row.Bytes, placement: "top" } }, [_c("div", { staticClass: "row-slot-box" }, [_vm._v(_vm._s(row.Bytes))])])];
      }
    }, {
      key: "action",
      fn: function fn(ref) {
        var row = ref.row;
        var index = ref.index;
        return [_c("div", { staticClass: "row-action-box" }, [_vm.disasterLilt.includes(row.D2D2T) ? _c("span", {
          directives: [{
            name: "tooltip",
            rawName: "v-tooltip.top",
            value: "D2D2T",
            expression: "'D2D2T'",
            modifiers: { top: true }
          }],
          staticClass: "iconfont d2d2-color",
          on: {
            click: function click($event) {
              return _vm.onD2d2t(row, index);
            }
          }
        }, [_vm._v("")]) : _c("span", {
          directives: [{
            name: "tooltip",
            rawName: "v-tooltip.top",
            value: "D2D2T",
            expression: "'D2D2T'",
            modifiers: { top: true }
          }],
          staticClass: "iconfont d2d2t-dis-color"
        }, [_vm._v("")]), _vm._v(" "), _vm.disasterLilt.includes(row.disasterStatus) ? _c("span", {
          directives: [{
            name: "tooltip",
            rawName: "v-tooltip.top",
            value: "远程复制",
            expression: "'远程复制'",
            modifiers: { top: true }
          }],
          staticClass: "iconfont copy-color",
          on: {
            click: function click($event) {
              return _vm.onRemotecopy(row, index);
            }
          }
        }, [_vm._v("\n\t\t\t\t\t\t")]) : _c("span", {
          directives: [{
            name: "tooltip",
            rawName: "v-tooltip.top",
            value: "远程复制",
            expression: "'远程复制'",
            modifiers: { top: true }
          }],
          staticClass: "iconfont copy-dis-color"
        }, [_vm._v("\n\t\t\t\t\t\t")])])];
      }
    }])
  }), _vm._v(" "), _c("el-pagination", {
    staticClass: "page-wrap",
    attrs: {
      background: "",
      "current-page": _vm.params.pageno,
      "page-sizes": [10, 20, 30, 40, 50],
      "page-size": _vm.params.nums,
      layout: "total, sizes, prev, pager, next, jumper",
      total: _vm.policytotal
    },
    on: {
      "size-change": _vm.sizeChange,
      "current-change": _vm.currentChange
    }
  })], 1)], 1)], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-d992fd52", esExports);
  }
}

/***/ }),

/***/ 600:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_index_vue__ = __webpack_require__(2947);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_index_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_index_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_index_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_index_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_d992fd52_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_index_vue__ = __webpack_require__(4272);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_d992fd52_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_index_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_d992fd52_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_index_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(4266)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = "data-v-d992fd52"
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_index_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_d992fd52_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_index_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/imageManagement/index.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-d992fd52", Component.options)
  } else {
    hotAPI.reload("data-v-d992fd52", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ })

});