{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../../src/registry/router/manhattan/util.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAa,YAAY,EAAE,MAAM,mBAAmB,CAAA;AAK/E,MAAM,UAAU,aAAa,CAAC,IAAc,EAAE,OAAwB;IACpE,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAA;IACpC,IAAI,OAAO,IAAI,OAAO,CAAC,UAAU,EAAE;QACjC,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;KAC9C;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAED,MAAM,UAAU,aAAa,CAAC,IAAc,EAAE,OAAwB;IACpE,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAA;IACpC,IAAI,OAAO,IAAI,OAAO,CAAC,UAAU,EAAE;QACjC,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;KAC9C;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,IAAc,EAAE,OAAwB;IACxE,IAAI,IAAI,CAAC,YAAY,EAAE;QACrB,OAAO,IAAI,CAAC,YAAY,CAAA;KACzB;IAED,MAAM,UAAU,GAAG,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IAC/C,OAAO,UAAU,CAAC,SAAS,EAAE,CAAA;AAC/B,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,IAAc,EAAE,OAAwB;IACxE,IAAI,IAAI,CAAC,YAAY,EAAE;QACrB,OAAO,IAAI,CAAC,YAAY,CAAA;KACzB;IAED,MAAM,UAAU,GAAG,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IAC/C,OAAO,UAAU,CAAC,SAAS,EAAE,CAAA;AAC/B,CAAC;AAED,0DAA0D;AAC1D,sDAAsD;AACtD,MAAM,UAAU,iBAAiB,CAC/B,KAAY,EACZ,GAAU,EACV,cAAsB,EACtB,IAAU,EACV,OAAwB;IAExB,MAAM,QAAQ,GAAG,GAAG,GAAG,cAAc,CAAA;IACrC,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAA;IACtE,MAAM,eAAe,GAAG,KAAK,CAAC,SAAS,CAAC,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC,CAAA;IAClE,OAAO,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,QAAQ,CAAC,CAAA;AAC1D,CAAC;AAED,SAAS,WAAW,CAClB,KAAY,EACZ,GAAU,EACV,IAAU,EACV,OAAwB;IAExB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;IAEzB,MAAM,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAA;IAC7B,MAAM,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAA;IAE7B,MAAM,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,CAAA;IACjC,MAAM,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,CAAA;IAEjC,MAAM,SAAS,GAAG,UAAU,GAAG,IAAI,CAAA;IACnC,MAAM,SAAS,GAAG,UAAU,GAAG,IAAI,CAAA;IAEnC,OAAO,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,SAAS,EAAE,KAAK,CAAC,CAAC,GAAG,SAAS,CAAC,CAAA;AAC5D,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,kBAAkB,CAAC,MAAc,EAAE,MAAc;IAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,CAAA;IACxC,OAAO,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAA;AAC7C,CAAC;AAED,kDAAkD;AAClD,MAAM,UAAU,cAAc,CAAC,IAAU,EAAE,OAAwB;IACjE,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;IAEzB,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;QACvC,SAAS,CAAC,WAAW,GAAG,CAAC,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAA;QAC3D,SAAS,CAAC,WAAW,GAAG,CAAC,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAA;IAC7D,CAAC,CAAC,CAAA;IAEF,OAAO,OAAO,CAAC,UAAU,CAAA;AAC3B,CAAC;AAQD,8EAA8E;AAC9E,MAAM,UAAU,OAAO,CAAC,IAAY,EAAE,MAAa,EAAE,MAAa;IAChE,OAAO;QACL,MAAM,EAAE,MAAM,CAAC,KAAK,EAAE;QACtB,CAAC,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC;QAC9C,CAAC,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC;KAC/C,CAAA;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,IAAY,EAAE,IAAY;IAClD,0BAA0B;IAC1B,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,IAAI,CAAA;KACZ;IAED,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,CAAA;IAEpC,2CAA2C;IAC3C,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,GAAG,CAAA;KACX;IAED,mCAAmC;IACnC,MAAM,WAAW,GAAG,KAAK,GAAG,IAAI,CAAA;IAChC,MAAM,SAAS,GAAG,GAAG,GAAG,WAAW,CAAA;IACnC,MAAM,UAAU,GAAG,SAAS,GAAG,KAAK,CAAA;IAEpC,OAAO,IAAI,GAAG,UAAU,CAAA;AAC1B,CAAC;AAED,SAAS,QAAQ,CAAC,KAAY,EAAE,IAAU;IACxC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;IAC1B,MAAM,CAAC,GAAG,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAA;IACxE,MAAM,CAAC,GAAG,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAA;IAExE,OAAO,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AACxB,CAAC;AAED,MAAM,UAAU,KAAK,CAAC,KAAY,EAAE,SAAiB;IACnD,OAAO,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;AAC/B,CAAC;AAED,MAAM,UAAU,KAAK,CAAC,KAAY,EAAE,IAAU,EAAE,SAAiB;IAC/D,OAAO,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,EAAE,SAAS,CAAC,CAAA;AACxD,CAAC;AAED,MAAM,UAAU,MAAM,CAAC,KAAY;IACjC,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAA;AACzB,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,KAAsB;IACnD,OAAO,IAAI,KAAK,CACd,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,EAC/C,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAChD,CAAA;AACH,CAAC;AAED,MAAM,UAAU,OAAO,CAAC,IAAW,EAAE,OAAgB;IACnD,IAAI,GAAG,GAAG,QAAQ,CAAA;IAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;QACrD,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;QAC/C,IAAI,IAAI,GAAG,GAAG,EAAE;YACd,GAAG,GAAG,IAAI,CAAA;SACX;KACF;IAED,OAAO,GAAG,CAAA;AACZ,CAAC;AAED,mEAAmE;AACnE,0EAA0E;AAC1E,mFAAmF;AACnF,6EAA6E;AAC7E,wDAAwD;AACxD,MAAM,UAAU,aAAa,CAC3B,MAAa,EACb,IAAe,EACf,aAA0B,EAC1B,IAAU,EACV,OAAwB;IAExB,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;IACnC,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,CAAA;IACzC,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAA;IAElD,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CACjD,CAAC,GAAG,EAAE,GAAc,EAAE,EAAE;QACtB,IAAI,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YAC/B,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,CAAA;YAEnC,iEAAiE;YACjE,2DAA2D;YAC3D,MAAM,MAAM,GAAG,IAAI,KAAK,CACtB,MAAM,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAChE,MAAM,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAClE,CAAA;YACD,MAAM,gBAAgB,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;YAEjD,sDAAsD;YACtD,6CAA6C;YAC7C,MAAM,aAAa,GAAG,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAA;YAC5D,IAAI,4BAA4B,CAAA;YAChC,IAAI,oBAAoB,GAAG,IAAI,CAAA;YAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;gBAChD,MAAM,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC,CAAA;gBACrC,MAAM,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAA;gBACrD,IACE,4BAA4B,IAAI,IAAI;oBACpC,QAAQ,GAAG,4BAA4B,EACvC;oBACA,4BAA4B,GAAG,QAAQ,CAAA;oBACvC,oBAAoB,GAAG,YAAY,CAAA;iBACpC;aACF;YAED,sEAAsE;YACtE,IAAI,oBAAoB,EAAE;gBACxB,IAAI,MAAM,GAAG,KAAK,CAAC,oBAAoB,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;gBACzD,oEAAoE;gBACpE,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE;oBAC9B,MAAM,GAAG,KAAK,CACZ,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAC5D,IAAI,EACJ,SAAS,CACV,CAAA;iBACF;gBAED,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;aACjB;SACF;QAED,OAAO,GAAG,CAAA;IACZ,CAAC,EACD,EAAE,CACH,CAAA;IAED,gEAAgE;IAChE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE;QAC/B,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAA;KAChD;IAED,OAAO,UAAU,CAAA;AACnB,CAAC;AAED,kEAAkE;AAClE,MAAM,UAAU,gBAAgB,CAC9B,OAAwB,EACxB,MAAuB,EACvB,SAAgB,EAChB,IAAW,EACX,EAAS;IAET,MAAM,KAAK,GAAG,EAAE,CAAA;IAEhB,IAAI,QAAQ,GAAG,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAA;IAEjD,6CAA6C;IAC7C,IAAI,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;IAClC,IAAI,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,CAAA;IAEhC,IAAI,KAAK,CAAA;IACT,OAAO,MAAM,EAAE;QACb,yCAAyC;QACzC,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;QAE1B,MAAM,IAAI,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAA;QAC/C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YAC1B,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;YACpB,QAAQ,GAAG,IAAI,CAAA;SAChB;QAED,0CAA0C;QAC1C,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;QAC3B,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,CAAA;KAC7B;IAED,6CAA6C;IAC7C,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;IAEpC,MAAM,QAAQ,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;IACrD,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;QAC9B,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;KACzB;IAED,OAAO,KAAK,CAAA;AACd,CAAC"}