/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/

/**
 * Coordinate System Interface:
 *
 *
 * Class members:
 *
 *  + dimensions {Array.<strign>}: mandatory
 *
 *
 * Instance members:
 *
 *  + dimensions {Array.<strign>}: mandatory
 *
 *  + model {module:echarts/model/Model}: mandatory
 *
 *  + create: mandatory
 *     @param {module:echarts/model/Global} ecModel
 *     @param {module:echarts/ExtensionAPI} api
 *     @return {Object} coordinate system instance
 *
 *  + update: mandatory
 *     @param {module:echarts/model/Global} ecModel
 *     @param {module:echarts/ExtensionAPI} api
 *
 *  + getAxis {Function}: mandatory
 *      @param {string} dim
 *      @return {module:echarts/coord/Axis}
 *
 *  + getAxes: {Function}: optional
 *      @return {Array.<module:echarts/coord/Axis>}
 *
 *  + axisPointerEnabled {boolean}
 *
 *  + dataToPoint {Function}: mandatory
 *      @param {*|Array.<*>} data
 *      @param {*} Defined by the coordinate system itself
 *      @param {Array.<*>} out
 *      @return {Array.<number>} point Point in global pixel coordinate system.
 *
 *  + pointToData {Function}: mandatory
 *      @param {Array.<number>} point Point in global pixel coordinate system.
 *      @param {*} Defined by the coordinate system itself
 *      @param {Array.<*>} out
 *      @return {*|Array.<*>} data
 *
 *  + containPoint {Function}: mandatory
 *      @param {Array.<number>} point Point in global pixel coordinate system.
 *      @return {boolean}
 *
 *  + getDimensionsInfo {Function}: optional
 *      @return {Array.<string|Object>} dimensionsInfo
 *              Like [{name: ..., type: ...}, 'xxx', ...]
 *
 *  + convertToPixel:
 *  + convertFromPixel:
 *        These two methods is also responsible for determine whether this
 *        coodinate system is applicable to the given `finder`.
 *        Each coordinate system will be tried, util one returns none
 *        null/undefined value.
 *        @param {module:echarts/model/Global} ecModel
 *        @param {Object} finder
 *        @param {Array|number} value
 *        @return {Array|number} convert result.
 *
 *
 */
