{"version": 3, "file": "loop.js", "sourceRoot": "", "sources": ["../../../src/registry/router/loop.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAA;AAUtD,SAAS,MAAM,CAAC,MAAyB,EAAE,KAAwB;IACjE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE;QACpC,MAAM,MAAM,GAAG,OAAO,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;QACrD,IAAI,MAAM,GAAG,CAAC,EAAE;YACd,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAA;YAC/D,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAA;YAC/D,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,GAAG,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAA;SACvD;QACD;YACE,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAA;YACxB,OAAO,mBAAM,MAAM,GAAI,GAAG,MAAM,oBAAO,MAAM,EAAG,CAAA;SACjD;KACF;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAED,MAAM,CAAC,MAAM,IAAI,GAAyC,UACxD,QAAQ,EACR,OAAO,EACP,QAAQ;IAER,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,EAAE,CAAA;IACjC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,EAAE,CAAA;IACnC,MAAM,UAAU,GAAG,MAAM,GAAG,CAAC,CAAA;IAC7B,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,MAAM,CAAA;IAErC,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAA;IAC1C,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAA;IAC1C,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAA;IACtC,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAA;IAEtC,IAAI,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE;QACrC,MAAM,WAAW,GAAG,CAAC,KAAa,EAAE,EAAE;YACpC,MAAM,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YACzB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YAEzB,MAAM,MAAM,GAAG,IAAI,KAAK,CACtB,YAAY,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,EAC5B,YAAY,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAC7B,CAAA;YACD,MAAM,GAAG,GAAG,IAAI,KAAK,CACnB,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,UAAU,EAC3B,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,UAAU,CAC5B,CAAA;YACD,MAAM,EAAE,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA;YAC1C,MAAM,EAAE,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA;YAEzC,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,CAAA;QACpD,CAAC,CAAA;QAED,MAAM,QAAQ,GAAG,CAAC,GAAoB,EAAE,EAAE;YACxC,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;YAChD,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;YACjC,OAAO,CACL,CAAC,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,CACvE,CAAA;QACH,CAAC,CAAA;QAED,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;QAEnD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,OAAO,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAA;SACjD;QAED,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,CAAA;QACrC,IAAI,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE;YAC/B,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAA;SAC7C;QAED,MAAM,GAAG,GAAG,MAAM,CAAC,YAAY,CAC7B,YAAY,EACZ,MAAM,CAAC,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAC/B,CAAA;QACD,IAAI,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,CAAA;QAC1B,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;YACpB,OAAO,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,CAAA;SAClC;QAED,2BAA2B;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;YAChD,GAAG,GAAG,WAAW,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;YAClC,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;gBACpB,OAAO,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,CAAA;aAClC;SACF;QACD,OAAO,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,CAAA;KAClC;IACD;QACE,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,CAAA;QACjD,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAA;QACpC,IAAI,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAA;QACjC,IAAI,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,UAAU,CAAC,CAAA;QAC9D,IAAI,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QAE9D,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;QAC7B,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;QACzC,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;QAEvC,IACE,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC;YAChC,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC;YAChC,UAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC;YACpC,UAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC;YACpC,UAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC;YACpC,UAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,EACpC;YACA,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;YAC/B,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAA;YAC7B,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,UAAU,CAAC,CAAA;YAC1D,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;SAC3D;QAED,IAAI,OAAO,CAAC,KAAK,EAAE;YACjB,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,CAAA;YACjD,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,SAAS,CACpD,MAAM,CAAC,gBAAgB,CACxB,CAAA;YACD,MAAM,WAAW,GAAG,UAAU,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAA;YACzD,MAAM,WAAW,GAAG,UAAU,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAA;YACzD,MAAM,UAAU,GAAG,WAAW;gBAC5B,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC1B,CAAC,CAAC,WAAW;oBACb,CAAC,CAAC,CAAC,WAAW,CAAC;gBACjB,CAAC,CAAC,EAAE,CAAA;YACN,IAAI,WAAW,EAAE;gBACf,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;oBAC9B,UAAU,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAA;iBAChC;qBAAM;oBACL,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;iBAC7B;aACF;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;YAC9C,IAAI,MAAM,EAAE;gBACV,QAAQ,CAAC,YAAY,GAAG,MAAM,CAAC,KAAK,EAAE,CAAA;gBACtC,QAAQ,CAAC,YAAY,GAAG,MAAM,CAAC,KAAK,EAAE,CAAA;aACvC;iBAAM;gBACL,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAA;gBAC3C,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAA;aAC5C;SACF;QAED,OAAO,MAAM,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAA;KAC1E;AACH,CAAC,CAAA"}