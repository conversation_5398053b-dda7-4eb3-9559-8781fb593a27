/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


/**
 * AxisPointer Interface:
 *
 *
 * Instance members:
 *
 *  + render {Function}: mandatory.
 *      If `show` called, axisPointer must be displayed or remain its original status.
 *      @param {module:echarts/model/Model} axisModel
 *      @param {module:echarts/model/Model} axisPointerModel
 *      @param {module:echarts/coord/ICoordinateSystem} coordSys
 *      @param {module:echarts/ExtensionAPI} api
 *      @param {boolean} forceRender
 *
 *  + remove {Function}: mandatory.
 *      If `hide` called, axisPointer must be hidden.
 *      @param {module:echarts/ExtensionAPI} api
 *
 *  + dispose {Function}: mandatory
 *      @param {module:echarts/ExtensionAPI} api
 */