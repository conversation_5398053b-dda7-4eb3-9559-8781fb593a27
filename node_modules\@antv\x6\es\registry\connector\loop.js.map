{"version": 3, "file": "loop.js", "sourceRoot": "", "sources": ["../../../src/registry/connector/loop.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAA;AAO/C,MAAM,CAAC,MAAM,IAAI,GAA+C,UAC9D,WAAW,EACX,WAAW,EACX,WAAW,EACX,OAAO,GAAG,EAAE;IAEZ,MAAM,GAAG,GAAG,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAC5C,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAA;IAC7C,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAA;IAC7C,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAA;IAEjD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE;QAC3C,MAAM,MAAM,GAAG,IAAI,KAAK,CACtB,CAAC,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,EACnC,CAAC,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CACpC,CAAA;QACD,MAAM,KAAK,GAAG,MAAM,CAAC,YAAY,CAC/B,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAC5C,MAAM,CACP,CAAA;QACD,IAAI,KAAK,GAAG,CAAC,EAAE;YACb,EAAE,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,EAAE,MAAM,CAAC,CAAA;YAC9B,EAAE,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,EAAE,MAAM,CAAC,CAAA;YAC9B,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,EAAE,MAAM,CAAC,CAAA;SACnC;KACF;IAED,MAAM,QAAQ,GAAG;SACV,WAAW,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC;SAC9B,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC;SACpC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC;GACpD,CAAA;IAED,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAA;AACtD,CAAC,CAAA"}