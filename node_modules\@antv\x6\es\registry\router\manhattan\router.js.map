{"version": 3, "file": "router.js", "sourceRoot": "", "sources": ["../../../../src/registry/router/manhattan/router.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAY,MAAM,iBAAiB,CAAA;AACvD,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAA;AAGpD,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAA;AACxC,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAA;AAC5C,OAAO,KAAK,IAAI,MAAM,QAAQ,CAAA;AAC9B,OAAO,EACL,cAAc,GAGf,MAAM,WAAW,CAAA;AAElB;;GAEG;AACH,SAAS,SAAS,CAChB,QAAkB,EAClB,IAAuB,EACvB,EAAqB,EACrB,GAAgB,EAChB,OAAwB;IAExB,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;IAEnC,IAAI,cAAc,CAAA;IAClB,IAAI,cAAc,CAAA;IAElB,IAAI,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;QAC/B,cAAc,GAAG,IAAI,CAAC,KAAK,CACzB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,KAAK,EAAE,EACjD,SAAS,CACV,CAAA;KACF;SAAM;QACL,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,CAAC,CAAA;KACrD;IAED,IAAI,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE;QAC7B,cAAc,GAAG,IAAI,CAAC,KAAK,CACzB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,KAAK,EAAE,EACjD,SAAS,CACV,CAAA;KACF;SAAM;QACL,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,SAAS,CAAC,CAAA;KACnD;IAED,2BAA2B;IAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,cAAc,EAAE,cAAc,CAAC,CAAA;IAEvE,0BAA0B;IAC1B,0BAA0B;IAE1B,MAAM,UAAU,GAAG,cAAc,CAAA;IACjC,MAAM,QAAQ,GAAG,cAAc,CAAA;IAC/B,IAAI,WAAW,CAAA;IACf,IAAI,SAAS,CAAA;IAEb,IAAI,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;QAC/B,WAAW,GAAG,IAAI,CAAC,aAAa,CAC9B,UAAU,EACV,IAAI,EACJ,OAAO,CAAC,eAAe,EACvB,IAAI,EACJ,OAAO,CACR,CAAA;KACF;SAAM;QACL,WAAW,GAAG,CAAC,UAAU,CAAC,CAAA;KAC3B;IAED,IAAI,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE;QAC7B,SAAS,GAAG,IAAI,CAAC,aAAa,CAC5B,cAAc,EACd,EAAE,EACF,OAAO,CAAC,aAAa,EACrB,IAAI,EACJ,OAAO,CACR,CAAA;KACF;SAAM;QACL,SAAS,GAAG,CAAC,QAAQ,CAAC,CAAA;KACvB;IAED,4EAA4E;IAC5E,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAA;IAC5D,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAA;IAExD,oDAAoD;IACpD,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;QAClD,MAAM,OAAO,GAAG,IAAI,SAAS,EAAE,CAAA;QAC/B,2DAA2D;QAC3D,MAAM,MAAM,GAAoB,EAAE,CAAA;QAClC,kEAAkE;QAClE,MAAM,OAAO,GAAoB,EAAE,CAAA;QACnC,oDAAoD;QACpD,MAAM,KAAK,GAAqB,EAAE,CAAA;QAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;YACrD,8CAA8C;YAC9C,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAA;YACjC,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAA;YACnC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAA;YACrD,MAAM,CAAC,GAAG,CAAC,GAAG,UAAU,CAAA;YACxB,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;SACf;QAED,MAAM,2BAA2B,GAAG,OAAO,CAAC,sBAAsB,CAAA;QAClE,4BAA4B;QAC5B,MAAM,eAAe,GAAG,2BAA2B,KAAK,SAAS,CAAA;QAEjE,aAAa;QACb,IAAI,SAAS,CAAA;QACb,IAAI,eAAe,CAAA;QACnB,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;QACrD,MAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CAAA;QACvC,MAAM,aAAa,GAAG,SAAS,CAAC,MAAM,CAAW,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;YACjE,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;YACjC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACb,OAAO,GAAG,CAAA;QACZ,CAAC,EAAE,EAAE,CAAC,CAAA;QAEN,0BAA0B;QAC1B,MAAM,kBAAkB,GAAG,KAAK,CAAC,WAAW,CAAC,WAAW,EAAE,SAAS,CAAC,CAAA;QACpE,IAAI,cAAc,GAAG,OAAO,CAAC,YAAY,CAAA;QACzC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,cAAc,GAAG,CAAC,EAAE;YAC/C,0CAA0C;YAC1C,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,EAAG,CAAA;YACjC,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;YACvC,MAAM,aAAa,GAAG,OAAO,CAAC,UAAU,CAAC,CAAA;YACzC,MAAM,WAAW,GAAG,KAAK,CAAC,UAAU,CAAC,CAAA;YAErC,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,CAAA;YACpD,MAAM,gBAAgB,GAAG,aAAa,IAAI,IAAI,CAAA;YAE9C,IAAI,sBAAiD,CAAA;YACrD,IAAI,CAAC,gBAAgB,EAAE;gBACrB,sBAAsB,GAAG,IAAI,CAAC,iBAAiB,CAC7C,aAAa,EACb,YAAY,EACZ,aAAa,EACb,IAAI,EACJ,OAAO,CACR,CAAA;aACF;iBAAM,IAAI,CAAC,eAAe,EAAE;gBAC3B,wBAAwB;gBACxB,sBAAsB,GAAG,2BAA2B,CAAA;aACrD;iBAAM,IAAI,CAAC,YAAY,EAAE;gBACxB,iCAAiC;gBACjC,sBAAsB,GAAG,IAAI,CAAC,iBAAiB,CAC7C,UAAU,EACV,YAAY,EACZ,aAAa,EACb,IAAI,EACJ,OAAO,CACR,CAAA;aACF;iBAAM;gBACL,sBAAsB,GAAG,IAAI,CAAA;aAC9B;YAED,mCAAmC;YACnC,MAAM,YAAY,GAAG,gBAAgB,IAAI,kBAAkB,CAAA;YAC3D,IAAI,CAAC,YAAY,IAAI,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;gBAC3D,OAAO,CAAC,sBAAsB,GAAG,sBAAsB,CAAA;gBACvD,OAAO,IAAI,CAAC,gBAAgB,CAC1B,OAAO,EACP,MAAM,EACN,YAAY,EACZ,UAAU,EACV,QAAQ,CACT,CAAA;aACF;YAED,qDAAqD;YACrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,CAAC,IAAI,CAAC,EAAE;gBACzC,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAA;gBAEzB,MAAM,cAAc,GAAG,SAAS,CAAC,KAAM,CAAA;gBACvC,eAAe,GAAG,IAAI,CAAC,kBAAkB,CACvC,sBAAuB,EACvB,cAAc,CACf,CAAA;gBAED,uCAAuC;gBACvC,IACE,CAAC,CAAC,eAAe,IAAI,YAAY,CAAC;oBAClC,eAAe,GAAG,OAAO,CAAC,kBAAkB,EAC5C;oBACA,SAAQ;iBACT;gBAED,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAC9B,YAAY;qBACT,KAAK,EAAE;qBACP,SAAS,CAAC,SAAS,CAAC,WAAW,IAAI,CAAC,EAAE,SAAS,CAAC,WAAW,IAAI,CAAC,CAAC,EACpE,IAAI,EACJ,SAAS,CACV,CAAA;gBACD,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAA;gBAE9C,wCAAwC;gBACxC,IAAI,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE;oBACpE,SAAQ;iBACT;gBAED,4BAA4B;gBAC5B,IAAI,aAAa,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;oBAC3C,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;oBACjD,IAAI,CAAC,UAAU,EAAE;wBACf,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAC9C,aAAa,EACb,QAAQ,EACR,aAAa,EACb,IAAI,EACJ,OAAO,CACR,CAAA;wBAED,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAChD,cAAc,EACd,iBAAiB,CAClB,CAAA;wBAED,IAAI,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,EAAE;4BACnD,SAAQ;yBACT;qBACF;iBACF;gBAED,+BAA+B;gBAC/B,+BAA+B;gBAE/B,MAAM,YAAY,GAAG,SAAS,CAAC,IAAI,CAAA;gBACnC,MAAM,eAAe,GAAG,YAAY;oBAClC,CAAC,CAAC,CAAC;oBACH,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,eAAe,CAAC,CAAA;gBACtC,MAAM,aAAa,GAAG,WAAW,GAAG,YAAY,GAAG,eAAe,CAAA;gBAElE,2DAA2D;gBAC3D,2DAA2D;gBAC3D,IACE,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC;oBAC5B,aAAa,GAAG,KAAK,CAAC,WAAW,CAAC,EAClC;oBACA,MAAM,CAAC,WAAW,CAAC,GAAG,aAAa,CAAA;oBACnC,OAAO,CAAC,WAAW,CAAC,GAAG,YAAY,CAAA;oBACnC,KAAK,CAAC,WAAW,CAAC,GAAG,aAAa,CAAA;oBAClC,OAAO,CAAC,GAAG,CACT,WAAW,EACX,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CACvD,CAAA;iBACF;aACF;YAED,cAAc,IAAI,CAAC,CAAA;SACpB;KACF;IAED,IAAI,OAAO,CAAC,aAAa,EAAE;QACzB,OAAO,WAAW,CAAC,IAAI,CACrB,OAAO,CAAC,aAAa,EACrB,IAAI,EACJ,UAAU,EACV,QAAQ,EACR,OAAO,CACR,CAAA;KACF;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAS,IAAI,CAAC,QAAiB,EAAE,QAAQ,GAAG,EAAE;IAC5C,IAAI,QAAQ,CAAC,MAAM,IAAI,CAAC,EAAE;QACxB,OAAO,QAAQ,CAAA;KAChB;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;QAC1D,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;QACzB,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;QAC9B,IAAI,KAAK,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,EAAE;YACxB,MAAM,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAA;YACnD,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,GAAG,CAAC,CAAA;gBACX,MAAM,CAAC,CAAC,GAAG,CAAC,CAAA;aACb;SACF;aAAM,IAAI,KAAK,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,EAAE;YAC/B,MAAM,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAA;YACnD,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,GAAG,CAAC,CAAA;gBACX,MAAM,CAAC,CAAC,GAAG,CAAC,CAAA;aACb;SACF;KACF;IAED,OAAO,QAAQ,CAAA;AACjB,CAAC;AAED,MAAM,CAAC,MAAM,MAAM,GAA8C,UAC/D,QAAQ,EACR,UAAU,EACV,QAAQ;IAER,MAAM,OAAO,GAAG,cAAc,CAAC,UAAU,CAAC,CAAA;IAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;IACxD,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;IACxD,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;IAEhE,cAAc;IACd,MAAM,GAAG,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,CACxC,QAAQ,CAAC,KAAK,CAAC,KAAK,EACpB,QAAQ,CAAC,IAAI,CACd,CAAA;IAED,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;IACxD,MAAM,WAAW,GAAY,EAAE,CAAA;IAE/B,2DAA2D;IAC3D,IAAI,SAAS,GAAG,cAAc,CAAA;IAE9B,IAAI,IAAI,CAAA;IACR,IAAI,EAAE,CAAA;IAEN,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;QAC1D,IAAI,YAAY,GAAmB,IAAI,CAAA;QAEvC,IAAI,GAAG,EAAE,IAAI,UAAU,CAAA;QACvB,EAAE,GAAG,WAAW,CAAC,CAAC,CAAC,CAAA;QAEnB,6BAA6B;QAC7B,IAAI,EAAE,IAAI,IAAI,EAAE;YACd,EAAE,GAAG,UAAU,CAAA;YAEf,yDAAyD;YACzD,0DAA0D;YAC1D,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAA;YAC1B,MAAM,eAAe,GACnB,IAAI,CAAC,eAAe,EAAE,IAAI,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE,IAAI,IAAI,CAAA;YAElE,IAAI,eAAe,IAAI,OAAO,OAAO,CAAC,cAAc,KAAK,UAAU,EAAE;gBACnE,MAAM,QAAQ,GAAG,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAA;gBAC5D,MAAM,MAAM,GAAG,EAAE,CAAC,SAAS,EAAE,CAAA;gBAC7B,YAAY,GAAG,WAAW,CAAC,IAAI,CAC7B,OAAO,CAAC,cAAc,EACtB,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,OAAO,CACR,CAAA;aACF;SACF;QAED,yBAAyB;QACzB,IAAI,YAAY,IAAI,IAAI,EAAE;YACxB,YAAY,GAAG,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,CAAC,CAAA;SAC3D;QAED,kCAAkC;QAClC,IAAI,YAAY,KAAK,IAAI,EAAE;YACzB,mBAAmB;YACnB,OAAO,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAA;YAEvE,OAAO,WAAW,CAAC,IAAI,CACrB,OAAO,CAAC,cAAc,EACtB,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,QAAQ,CACT,CAAA;SACF;QAED,2DAA2D;QAC3D,0BAA0B;QAC1B,MAAM,SAAS,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;QACjC,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;YAC5C,YAAY,CAAC,KAAK,EAAE,CAAA;SACrB;QAED,oCAAoC;QACpC,SAAS,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,SAAS,CAAA;QAC9D,WAAW,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAA;KAClC;IAED,IAAI,OAAO,CAAC,UAAU,EAAE;QACtB,OAAO,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAA;KAC5D;IAED,OAAO,WAAW,CAAA;AACpB,CAAC,CAAA"}