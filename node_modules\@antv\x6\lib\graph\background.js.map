{"version": 3, "file": "background.js", "sourceRoot": "", "sources": ["../../src/graph/background.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,+CAA2C;AAC3C,mDAA6C;AAC7C,0CAAwC;AACxC,iCAA6B;AAE7B,MAAa,iBAAkB,SAAQ,WAAI;IAGzC,IAAc,IAAI;QAChB,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAA;IAC7B,CAAC;IAES,IAAI;QACZ,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;YAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;SACnC;IACH,CAAC;IAES,cAAc;QACtB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QACzC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IAC/C,CAAC;IAES,aAAa;QACrB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAC1C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IAChD,CAAC;IAES,qBAAqB,CAAC,UAAqC,EAAE;QACrE,IAAI,cAAc,GAAQ,OAAO,CAAC,IAAI,IAAI,WAAW,CAAA;QACrD,IAAI,kBAAkB,GAAQ,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAA;QAE1D,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAA;QAC7C,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAA;QAEjC,qBAAqB;QACrB,IAAI,OAAO,kBAAkB,KAAK,QAAQ,EAAE;YAC1C,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;YACxD,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;YACxD,kBAAkB,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAA;SACrC;QAED,iBAAiB;QACjB,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE;YACtC,cAAc,GAAG,uBAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CACvD,KAAK,CAAC,EAAE,EACR,KAAK,CAAC,EAAE,CACT,CAAA;YACD,cAAc,GAAG,GAAG,cAAc,CAAC,KAAK,MAAM,cAAc,CAAC,MAAM,IAAI,CAAA;SACxE;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,cAAc,CAAA;QAC/C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,kBAAkB,CAAA;IACzD,CAAC;IAES,mBAAmB,CAC3B,GAA6B,EAC7B,UAAqC,EAAE;QAEvC,IAAI,CAAC,CAAC,GAAG,YAAY,gBAAgB,CAAC,EAAE;YACtC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,EAAE,CAAA;YACpC,OAAM;SACP;QAED,6CAA6C;QAC7C,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAA;QAC/B,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,EAAE;YAC1C,OAAM;SACP;QAED,IAAI,GAAG,CAAA;QACP,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;QAC/B,MAAM,cAAc,GAAQ,OAAO,CAAC,IAAI,CAAA;QACxC,IAAI,gBAAgB,GAAG,OAAO,CAAC,MAAM,IAAI,WAAW,CAAA;QAEpD,MAAM,OAAO,GAAG,qBAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAA;QACzD,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;YACjC,MAAM,OAAO,GAAI,OAAkC,CAAC,OAAO,IAAI,CAAC,CAAA;YAChE,GAAG,CAAC,KAAK,IAAI,OAAO,CAAA;YACpB,GAAG,CAAC,MAAM,IAAI,OAAO,CAAA;YACrB,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;YACpC,IAAI,CAAC,CAAC,MAAM,YAAY,iBAAiB,CAAC,EAAE;gBAC1C,MAAM,IAAI,KAAK,CACb,wDAAwD,CACzD,CAAA;aACF;YAED,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAA;YAEnC,2CAA2C;YAC3C,IAAI,OAAO,CAAC,MAAM,IAAI,gBAAgB,KAAK,OAAO,CAAC,MAAM,EAAE;gBACzD,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAA;aAClC;iBAAM;gBACL,gBAAgB,GAAG,QAAQ,CAAA;aAC5B;YAED,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE;gBACtC,mDAAmD;gBACnD,cAAc,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAA;gBAChD,cAAc,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAA;aACpD;iBAAM,IAAI,cAAc,KAAK,SAAS,EAAE;gBACvC,uCAAuC;gBACvC,OAAO,CAAC,IAAI,GAAG;oBACb,KAAK,EAAE,MAAM,CAAC,KAAK,GAAG,OAAO;oBAC7B,MAAM,EAAE,MAAM,CAAC,MAAM,GAAG,OAAO;iBAChC,CAAA;aACF;SACF;aAAM;YACL,GAAG,GAAG,GAAG,CAAC,GAAG,CAAA;YACb,IAAI,cAAc,KAAK,SAAS,EAAE;gBAChC,OAAO,CAAC,IAAI,GAAG;oBACb,KAAK,EAAE,GAAG,CAAC,KAAK;oBAChB,MAAM,EAAE,GAAG,CAAC,MAAM;iBACnB,CAAA;aACF;SACF;QAED,IACE,KAAK,IAAI,IAAI;YACb,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ;YAChC,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK;YAC7B,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM;YAC9B,OAAkC,CAAC,OAAO;gBACxC,KAAgC,CAAC,OAAO,EAC3C;YACA,KAAK,CAAC,IAAI,GAAG,qBAAS,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;SAC3C;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;QAC7B,KAAK,CAAC,eAAe,GAAG,OAAO,GAAG,GAAG,CAAA;QACrC,KAAK,CAAC,gBAAgB,GAAG,gBAAgB,CAAA;QACzC,KAAK,CAAC,OAAO,GAAG,OAAO,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,OAAO,EAAE,CAAA;QAEnE,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAA;IACrC,CAAC;IAES,qBAAqB,CAAC,KAAqB;QACnD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK,IAAI,EAAE,CAAA;IAC/C,CAAC;IAES,uBAAuB,CAAC,OAAmC;QACnE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,GAAG,OAAO,CAAA;IACzC,CAAC;IAED,MAAM;QACJ,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;SAC9C;IACH,CAAC;IAED,IAAI,CAAC,OAAmC;QACtC,MAAM,IAAI,GAAG,OAAO,IAAI,EAAE,CAAA;QAC1B,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAA;QACrC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAEtC,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,YAAY,GAAG,qBAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YACzC,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;YACzC,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;YACzD,GAAG,CAAC,YAAY,CAAC,aAAa,EAAE,WAAW,CAAC,CAAA;YAC5C,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAA;SACrB;aAAM;YACL,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;YAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;SACzB;IACH,CAAC;IAED,KAAK;QACH,IAAI,CAAC,IAAI,EAAE,CAAA;IACb,CAAC;IAGD,OAAO;QACL,IAAI,CAAC,KAAK,EAAE,CAAA;QACZ,IAAI,CAAC,aAAa,EAAE,CAAA;IACtB,CAAC;CACF;AAJC;IADC,WAAI,CAAC,OAAO,EAAE;gDAId;AA3KH,8CA4KC"}