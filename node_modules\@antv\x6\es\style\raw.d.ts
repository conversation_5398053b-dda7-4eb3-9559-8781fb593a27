/**
 * Auto generated file, do not modify it!
 */
export declare const content = ".x6-graph {\n  position: relative;\n  overflow: hidden;\n  outline: none;\n  touch-action: none;\n}\n.x6-graph-background,\n.x6-graph-grid,\n.x6-graph-svg {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n}\n.x6-graph-background-stage,\n.x6-graph-grid-stage,\n.x6-graph-svg-stage {\n  user-select: none;\n}\n.x6-graph.x6-graph-pannable {\n  cursor: grab;\n  cursor: -moz-grab;\n  cursor: -webkit-grab;\n}\n.x6-graph.x6-graph-panning {\n  cursor: grabbing;\n  cursor: -moz-grabbing;\n  cursor: -webkit-grabbing;\n  user-select: none;\n}\n.x6-node {\n  cursor: move;\n  /* stylelint-disable-next-line */\n}\n.x6-node.x6-node-immovable {\n  cursor: default;\n}\n.x6-node * {\n  -webkit-user-drag: none;\n}\n.x6-node .scalable * {\n  vector-effect: non-scaling-stroke;\n}\n.x6-node [magnet='true'] {\n  cursor: crosshair;\n  transition: opacity 0.3s;\n}\n.x6-node [magnet='true']:hover {\n  opacity: 0.7;\n}\n.x6-node foreignObject {\n  display: block;\n  overflow: visible;\n  background-color: transparent;\n}\n.x6-node foreignObject > body {\n  position: static;\n  width: 100%;\n  height: 100%;\n  margin: 0;\n  padding: 0;\n  overflow: visible;\n  background-color: transparent;\n}\n.x6-edge .source-marker,\n.x6-edge .target-marker {\n  vector-effect: non-scaling-stroke;\n}\n.x6-edge .connection {\n  stroke-linejoin: round;\n  fill: none;\n}\n.x6-edge .connection-wrap {\n  cursor: move;\n  opacity: 0;\n  fill: none;\n  stroke: #000;\n  stroke-width: 15;\n  stroke-linecap: round;\n  stroke-linejoin: round;\n}\n.x6-edge .connection-wrap:hover {\n  opacity: 0.4;\n  stroke-opacity: 0.4;\n}\n.x6-edge .vertices {\n  cursor: move;\n  opacity: 0;\n}\n.x6-edge .vertices .vertex {\n  fill: #1abc9c;\n}\n.x6-edge .vertices .vertex :hover {\n  fill: #34495e;\n  stroke: none;\n}\n.x6-edge .vertices .vertex-remove {\n  cursor: pointer;\n  fill: #fff;\n}\n.x6-edge .vertices .vertex-remove-area {\n  cursor: pointer;\n  opacity: 0.1;\n}\n.x6-edge .vertices .vertex-group:hover .vertex-remove-area {\n  opacity: 1;\n}\n.x6-edge .arrowheads {\n  cursor: move;\n  opacity: 0;\n}\n.x6-edge .arrowheads .arrowhead {\n  fill: #1abc9c;\n}\n.x6-edge .arrowheads .arrowhead :hover {\n  fill: #f39c12;\n  stroke: none;\n}\n.x6-edge .tools {\n  cursor: pointer;\n  opacity: 0;\n}\n.x6-edge .tools .tool-options {\n  display: none;\n}\n.x6-edge .tools .tool-remove circle {\n  fill: #f00;\n}\n.x6-edge .tools .tool-remove path {\n  fill: #fff;\n}\n.x6-edge:hover .vertices,\n.x6-edge:hover .arrowheads,\n.x6-edge:hover .tools {\n  opacity: 1;\n}\n.x6-highlight-opacity {\n  opacity: 0.3;\n}\n.x6-cell-tool-editor {\n  position: relative;\n  display: inline-block;\n  min-height: 1em;\n  margin: 0;\n  padding: 0;\n  line-height: 1;\n  white-space: normal;\n  text-align: center;\n  vertical-align: top;\n  overflow-wrap: normal;\n  outline: none;\n  transform-origin: 0 0;\n  -webkit-user-drag: none;\n}\n.x6-edge-tool-editor {\n  border: 1px solid #275fc5;\n  border-radius: 2px;\n}\n";
