{"version": 3, "file": "oneside.js", "sourceRoot": "", "sources": ["../../../src/registry/router/oneside.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAA;AAQ3C;;GAEG;AACH,MAAM,CAAC,MAAM,OAAO,GAA4C,UAC9D,QAAQ,EACR,OAAO,EACP,QAAQ;IAER,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,QAAQ,CAAA;IACrC,MAAM,OAAO,GAAG,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC,CAAA;IAC/D,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAA;IACtC,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAA;IACtC,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,CAAA;IAC1C,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,CAAA;IAE1C,IAAI,KAAgB,CAAA;IACpB,IAAI,GAAuB,CAAA;IAC3B,IAAI,MAAM,CAAA;IAEV,QAAQ,IAAI,EAAE;QACZ,KAAK,KAAK;YACR,MAAM,GAAG,CAAC,CAAC,CAAA;YACX,KAAK,GAAG,GAAG,CAAA;YACX,GAAG,GAAG,QAAQ,CAAA;YACd,MAAK;QACP,KAAK,MAAM;YACT,MAAM,GAAG,CAAC,CAAC,CAAA;YACX,KAAK,GAAG,GAAG,CAAA;YACX,GAAG,GAAG,OAAO,CAAA;YACb,MAAK;QACP,KAAK,OAAO;YACV,MAAM,GAAG,CAAC,CAAA;YACV,KAAK,GAAG,GAAG,CAAA;YACX,GAAG,GAAG,OAAO,CAAA;YACb,MAAK;QACP,KAAK,QAAQ,CAAC;QACd;YACE,MAAM,GAAG,CAAC,CAAA;YACV,KAAK,GAAG,GAAG,CAAA;YACX,GAAG,GAAG,QAAQ,CAAA;YACd,MAAK;KACR;IAED,mEAAmE;IACnE,WAAW,CAAC,KAAK,CAAC,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAA;IACpE,WAAW,CAAC,KAAK,CAAC,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAA;IAEpE,6DAA6D;IAC7D,IAAI,MAAM,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE;QAC1D,WAAW,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,CAAA;KACxC;SAAM;QACL,WAAW,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,CAAA;KACxC;IAED,OAAO,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,GAAG,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,CAAA;AAClE,CAAC,CAAA"}