{"version": 3, "file": "marker.js", "sourceRoot": "", "sources": ["../../../src/registry/attr/marker.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,+CAAiE;AAEjE,sCAAkC;AAGlC,SAAS,OAAO,CAAC,KAAU;IACzB,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,qBAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;AACpE,CAAC;AAEY,QAAA,YAAY,GAAoB;IAC3C,OAAO;IACP,GAAG,CAAC,MAA2B,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;QAC9C,OAAO,YAAY,CAAC,cAAc,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;IAC1D,CAAC;CACF,CAAA;AAEY,QAAA,YAAY,GAAoB;IAC3C,OAAO;IACP,GAAG,CAAC,MAA2B,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;QAC9C,OAAO,YAAY,CAAC,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE;YACrD,SAAS,EAAE,aAAa;SACzB,CAAC,CAAA;IACJ,CAAC;CACF,CAAA;AAEY,QAAA,YAAY,GAAoB;IAC3C,OAAO;IACP,GAAG,CAAC,MAA2B,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;QAC9C,OAAO,YAAY,CAAC,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;IACxD,CAAC;CACF,CAAA;AAED,SAAS,YAAY,CACnB,IAAkD,EAClD,MAA2B,EAC3B,IAAc,EACd,KAAwB,EACxB,SAA2B,EAAE;IAE7B,MAAM,GAAG,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,MAAM,CAAA;IAClE,MAAM,EAAE,IAAI,EAAE,IAAI,KAAgB,GAAG,EAAd,MAAM,UAAK,GAAG,EAA/B,gBAAyB,CAAM,CAAA;IACrC,IAAI,MAAM,GAAG,MAAM,CAAA;IAEnB,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QACpC,MAAM,EAAE,GAAG,eAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QACpC,IAAI,EAAE,EAAE;YACN,MAAM,GAAG,EAAE,iCAAM,MAAM,GAAM,IAAiB,EAAG,CAAA;SAClD;aAAM;YACL,OAAO,eAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;SACxC;KACF;IAED,MAAM,OAAO,iDACR,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,GAC1B,MAAM,GACN,MAAM,CACV,CAAA;IAED,OAAO;QACL,CAAC,IAAI,CAAC,EAAE,QAAQ,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG;KACpD,CAAA;AACH,CAAC;AAED,SAAS,aAAa,CACpB,IAAuB,EACvB,IAAkD;IAElD,MAAM,MAAM,GAAqB,EAAE,CAAA;IAEnC,sEAAsE;IACtE,yEAAyE;IACzE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;IAC1B,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC9B,MAAM,CAAC,MAAM,GAAG,MAAM,CAAA;QACtB,MAAM,CAAC,IAAI,GAAG,MAAM,CAAA;KACrB;IAED,+CAA+C;IAC/C,IAAI,aAAa,GAAG,IAAI,CAAC,aAAa,CAAA;IACtC,IAAI,aAAa,IAAI,IAAI,EAAE;QACzB,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAA;KACvC;IAED,IAAI,aAAa,IAAI,IAAI,EAAE;QACzB,aAAa,GAAG,IAAI,CAAC,OAAO,CAAA;KAC7B;IAED,IAAI,aAAa,IAAI,IAAI,EAAE;QACzB,MAAM,CAAC,gBAAgB,CAAC,GAAG,aAAuB,CAAA;QAClD,MAAM,CAAC,cAAc,CAAC,GAAG,aAAuB,CAAA;KACjD;IAED,IAAI,IAAI,KAAK,YAAY,EAAE;QACzB,MAAM,WAAW,GAAG,UAAU,CAC5B,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,cAAc,CAAC,CAAW,CACrD,CAAA;QACD,IAAI,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,WAAW,GAAG,CAAC,EAAE;YACnD,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAA;YACzC,MAAM,CAAC,IAAI,GAAG,IAAI,KAAK,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;SACzD;KACF;IAED,OAAO,MAAM,CAAA;AACf,CAAC"}