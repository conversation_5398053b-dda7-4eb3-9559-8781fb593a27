{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../src/registry/connection-point/util.ts"], "names": [], "mappings": "AAAA,OAAO,EAAS,IAAI,EAAE,MAAM,mBAAmB,CAAA;AAE/C,MAAM,UAAU,MAAM,CACpB,EAAS,EACT,EAAS,EACT,MAAiC;IAEjC,IAAI,EAAsB,CAAA;IAC1B,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC9B,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;YAC7B,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;YAC7B,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;YAC9C,EAAE,GAAG,KAAK,CAAA,CAAC,sBAAsB;YACjC,EAAE,GAAG,GAAG,CAAA,CAAC,sBAAsB;SAChC;QACD,EAAE,GAAG,MAAM,CAAC,CAAC,CAAA;KACd;SAAM;QACL,EAAE,GAAG,MAAM,CAAA;KACZ;IAED,IAAI,EAAE,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;QACtC,OAAO,EAAE,CAAA;KACV;IAED,MAAM,MAAM,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;IAC9B,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE;QAC1B,OAAO,EAAE,CAAA;KACV;IACD,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAA;AAC/C,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,MAAkB;IAC/C,MAAM,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC,CAAA;IAClD,IAAI,MAAM,KAAK,IAAI,EAAE;QACnB,OAAO,CAAC,CAAA;KACT;IACD,OAAO,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;AAChC,CAAC;AAED,MAAM,UAAU,aAAa,CAAC,MAAe;IAC3C,IAAI,MAAM,IAAI,IAAI,EAAE;QAClB,OAAO,IAAI,CAAA;KACZ;IAED,IAAI,IAAI,GAAG,MAAM,CAAA;IACjB,GAAG;QACD,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC1B,IAAI,OAAO,OAAO,KAAK,QAAQ;YAAE,OAAO,IAAI,CAAA;QAC5C,OAAO,GAAG,OAAO,CAAC,WAAW,EAAE,CAAA;QAC/B,IAAI,OAAO,KAAK,GAAG,EAAE;YACnB,IAAI,GAAG,IAAI,CAAC,iBAA4B,CAAA;SACzC;aAAM,IAAI,OAAO,KAAK,OAAO,EAAE;YAC9B,IAAI,GAAG,IAAI,CAAC,kBAA6B,CAAA;SAC1C;;YAAM,MAAK;KACb,QAAQ,IAAI,EAAC;IAEd,OAAO,IAAI,CAAA;AACb,CAAC"}