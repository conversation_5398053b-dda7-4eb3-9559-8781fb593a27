webpackJsonp([11],{

/***/ 1528:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});
exports.TOOLTIP_TEXT = exports.WEEK_LIST = exports.DAY_LIST = exports.RECOVER_COMPONENTS_LIST = exports.BASE_TABLE_LIST = exports.CONVENTION_COMPONENTS_LIST = exports.OPTION_LIST_TYPE = exports.VIRTUALIZATION = exports.getBackuptype = exports.getClients = exports.getStations = exports.getVolpools = exports.getDevices = exports.getCompresstype = exports.getBlockSize = exports.getBlockmode = exports.getHistoryTitle = exports.getDevicesVmName = exports.getDevicesCdm = exports.getInfoData = exports.getpieDatas = exports.modifyPolicy = undefined;

var _defineProperty2 = __webpack_require__(89);

var _defineProperty3 = _interopRequireDefault(_defineProperty2);

var _OPTION_LIST_TYPE, _RECOVER_COMPONENTS_L;

var _request = __webpack_require__(316);

var _request2 = _interopRequireDefault(_request);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

var modifyPolicy = exports.modifyPolicy = function modifyPolicy(data) {
	return (0, _request2.default)({
		method: 'put',
		url: '/rest-ful/v3.0/policy',
		data: data
	});
};

var getpieDatas = exports.getpieDatas = function getpieDatas(params) {
	return (0, _request2.default)({
		method: 'get',
		url: '/rest-ful/v3.0/report/statistics?policy=' + params.id
	});
};

var getInfoData = exports.getInfoData = function getInfoData(params) {
	return (0, _request2.default)({
		method: 'get',
		url: '/rest-ful/v3.0/policy/detail/' + params.id
	});
};

var getDevicesCdm = exports.getDevicesCdm = function getDevicesCdm() {
	return (0, _request2.default)({
		method: 'get',
		url: '/rest-ful/v3.0/devices?cdm=1'
	});
};
var getDevicesVmName = exports.getDevicesVmName = function getDevicesVmName(params) {
	return (0, _request2.default)({
		method: 'get',
		url: '/rest-ful/v3.0/policy/detail/' + params.id
	});
};

var getHistoryTitle = exports.getHistoryTitle = function getHistoryTitle(params, query) {
	return (0, _request2.default)({
		method: 'get',
		url: '/rest-ful/v3.0/policy/history/' + params.id + '?pageno=' + query.pageno + '&nums=' + query.size
	});
};

var getBlockmode = exports.getBlockmode = function getBlockmode() {
	return (0, _request2.default)({
		method: 'get',
		url: '/rest-ful/v3.0/policy/blockmode'
	});
};

var getBlockSize = exports.getBlockSize = function getBlockSize() {
	return (0, _request2.default)({
		method: 'get',
		url: '/rest-ful/v3.0/policy/blocksize'
	});
};

var getCompresstype = exports.getCompresstype = function getCompresstype() {
	return (0, _request2.default)({
		method: 'get',
		url: '/rest-ful/v3.0/policy/compresstype'
	});
};

var getDevices = exports.getDevices = function getDevices() {
	return (0, _request2.default)({
		method: 'get',
		url: '/rest-ful/v3.0/devices'
	});
};

var getVolpools = exports.getVolpools = function getVolpools() {
	return (0, _request2.default)({
		method: 'get',
		url: '/rest-ful/v3.0/volpools'
	});
};
var getStations = exports.getStations = function getStations() {
	return (0, _request2.default)({
		method: 'get',
		url: '/rest-ful/v3.0/remote/stations'
	});
};

var getClients = exports.getClients = function getClients() {
	return (0, _request2.default)({
		method: 'get',
		url: '/rest-ful/v3.0/clients?nums=0'
	});
};

var getBackuptype = exports.getBackuptype = function getBackuptype(params) {
	return (0, _request2.default)({
		method: 'get',
		url: params.base.type == 11 ? '/rest-ful/v3.0/policy/backuptype/' + params.base.restype + '?cdm=1' : '/rest-ful/v3.0/policy/backuptype/' + params.base.restype
	});
};

var VIRTUALIZATION = exports.VIRTUALIZATION = [327680, 1441792, 1507328, 458752, 1835008, 2359298, 2359300, 2359302, 2359299, 1900544];

var OPTION_LIST_TYPE = exports.OPTION_LIST_TYPE = (_OPTION_LIST_TYPE = {
	是否启用加密: 0,
	是否启用压缩: 1,
	压缩算法: 1,
	压缩级别: 2,
	策略压缩级别: 2,
	恢复级策略: 2,
	备份前脚本: 3,
	备份后脚本: 4,
	启动高级文件备份: 5,
	只备份指定扩展名的文件: 6,
	排除指定扩展名的文件不备份: 7,
	备份后删除源文件: 8,
	跳过被打开的文件: 9,
	配置通道个数: 10,
	启动压缩: 11,
	指定filesperset参数: 12,
	ORACLE全库备份时同时备份归档: 13,
	备份BINLOG: 13,
	归档备份范围: 14,
	删除已备份的归档日志: 15,
	备份前检查一致性: 16,
	备份后检查一致性: 17,
	恢复演练: 16,
	备份检验数据有效性: 18,
	检查失败后仍继续备份: 19,
	启用MSSQL的压缩功能: 20,
	跳过关机的虚拟机: 21,
	重定向恢复的路径: 22,
	实例名: 22,
	当恢复的文件已经存在时的处理方式: 23,
	指定脚本备份oracle: 24,
	恢复到指定的时间点: 25,
	恢复到指定的SCN: 26,
	恢复指定的线程ID: 27,
	数据传输模式vm: 28,
	跳过失败的虚拟机: 29,
	恢复模式: 30,
	恢复前脚本: 31,
	恢复后脚本: 32,
	恢复后启动虚拟机: 33,
	虚拟机重命名恢复: 34,
	恢复实例虚拟机重定向恢复使用: 35,
	SQLSERVER指定时间点恢复: 36,
	GBASE指定时间点恢复: 36,
	SQLSERVER恢复后执行RECOVEER操作: 37,
	数据库实例名: 38,
	是否为在线备份: 39,
	备份时是否包含日志: 40,
	指定脚本备份: 41,
	重定向恢复: 42,
	当使用磁带库设备是指定是否使用本地磁带缓存: 42,
	原始客户端ID: 43,
	启用d2d2t: 44,
	宿端设备: 45,
	CDM设备: 45,
	介质池: 46,
	调度类型: 47,
	'type=48': 48,
	间隔时间: 49,
	未知1: 50,
	未知3: 51
}, (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u672A\u77E51', 52), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'DM\u6392\u9664\u65E5\u5FD7\u4E0D\u5907\u4EFD', 53), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u5F52\u6863\u65E5\u5FD7', 54), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u65E5\u5FD7\u5F00\u59CB\u65F6\u95F4', 55), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u65E5\u5FD7\u7ED3\u675F\u65F6\u95F4', 56), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u865A\u62DF\u673A\u7684\u5B9E\u4F8Bid', 57), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u8FBE\u68A6\u5907\u4EFD\u6570\u636E\u5B58\u653E\u76EE\u5F55', 58), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'INI\u8DEF\u5F84', 59), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u53EA\u6062\u590D\u6587\u4EF6\u4E0D\u6062\u590D\u6570\u636E\u5E93', 60), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6062\u590D\u5230\u6307\u5B9A\u7684\u65F6\u95F4', 61), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u4F7F\u7528\u538B\u7F29', 62), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u81EA\u52A8\u8BBE\u7F6E\u5F52\u6863', 63), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u4E00\u81F4\u6027\u6062\u590D', 64), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u5907\u4EFD\u70B9\u6062\u590D', 65), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6307\u5B9A\u65F6\u95F4\u70B9\u6062\u590D', 66), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6307\u5B9A\u8FBE\u68A6\u6062\u590D\u65F6\u95F4\u70B9', 67), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6307\u5B9A\u8FBE\u68A6\u5F52\u6863\u76EE\u5F55', 68), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6307\u5B9A\u65E5\u5FD7\u6062\u590D\u8DEF\u5F84', 68), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6307\u5B9A\u5907\u4EFD\u6A21\u5F0F', 69), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6062\u590D\u5230\u6307\u5B9A\u7684\u65F6\u95F4', 70), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6062\u590D\u76EE\u5F55', 71), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u672A\u77E55', 72), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6307\u5B9A\u8DEF\u5F84\u6062\u590D', 72), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6062\u590D\u7684\u65F6\u95F4\u8282\u70B9', 73), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u5206\u5757\u6A21\u5F0F', 74), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6700\u5C0F\u5757\u5927\u5C0F', 75), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6700\u5927\u5757\u5927\u5C0F', 76), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u52A0\u5BC6\u7B97\u6CD5', 77), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u5BC6\u94A5\u957F\u5EA6', 78), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u9650\u901F\u5F00\u59CB\u65F6\u95F4', 79), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u9650\u901F\u7ED3\u675F\u65F6\u95F4', 80), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u901F\u5EA6', 81), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u8FD0\u884C\u6A21\u5F0F', 82), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u7AD9\u70B9\u540D\u79F0', 83), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u8FDC\u7A0B\u590D\u5236\u8C03\u5EA6\u6A21\u5F0F', 84), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u8FDC\u7A0B\u590D\u5236\u8C03\u5EA6\u65F6\u95F4', 85), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u63A7\u5236\u6587\u4EF6\u4F4D\u7F6E', 86), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6062\u590D\u5230\u6307\u5B9Aseq1', 87), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6062\u590D\u5230\u6307\u5B9Aseq2', 88), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u4F7F\u5BA2\u6237\u673A\u6587\u4EF6\u7CFB\u7EDF\u5904\u4E8E\u9759\u9ED8\u72B6\u6001', 89), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6062\u590D\u7684\u5B58\u50A8\u6C60\u540D\u79F0', 90), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6062\u590D\u7684\u4E3B\u673Aid', 91), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6062\u590D\u7684\u78C1\u76D8\u6A21\u5F0F', 92), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u4E3B\u673A\u6C60ID', 93), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u96C6\u7FA4ID', 94), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u7F51\u6BB5', 95), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6570\u636E\u4E2D\u5FC3', 96), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u5B58\u50A8', 97), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u4E3B\u673A', 98), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u672A\u77E56', 99), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u672A\u77E56', 100), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u672A\u77E56', 101), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'DB2\u6062\u590D\u5F00\u59CB\u65F6\u95F4', 102), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'DB2\u6062\u590D\u7ED3\u675F\u65F6\u95F4', 103), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'DB2\u6062\u590D\u7C7B\u578B', 104), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'DB2\u6062\u590D\u662F\u5426\u4E3A\u5F02\u673A', 105), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'DB2\u6062\u590D\u65F6\u95F4\u70B9', 106), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'MYSQL\u6570\u636E\u5E93\u6570\u636E\u6587\u4EF6\u8DEF\u5F84', 107), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'Oracle\u63A7\u5236\u6587\u4EF6\u5907\u4EFD\u7247\u540D\u79F0', 108), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'Oracle\u6570\u636E\u6587\u4EF6\u76EE\u5F55', 109), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6062\u590D\u7684Zone', 110), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u4E3B\u673A\u89C4\u683C', 111), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'CDM\u5907\u4EFD\u7684\u5377', 112), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'CDM\u534F\u8BAE', 113), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u8DF3\u8FC7\u4E0D\u53EF\u8BBF\u95EE\u7684\u6570\u636E\u6587\u4EF6', 114), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u8DF3\u8FC7\u53EA\u8BFB\u7684\u6570\u636E\u6587\u4EF6', 115), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u8DF3\u8FC7\u79BB\u7EBF\u7684\u6570\u636E\u6587\u4EF6', 116), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u8DF3\u8FC7\u4E0D\u53EF\u8BBF\u95EE\u7684\u5F52\u6863\u65E5\u5FD7', 117), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u5DF2\u5907\u4EFD\u8FC7', 118), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'oracle\u53EA\u6302\u8F7D\u4E0D\u6062\u590D', 119), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u672A\u77E56', 120), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u672A\u77E56', 121), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u7528\u4E8E\u4E0E\u4EE3\u7406\u901A\u8BAF\u7684TCP\u7AEF\u53E3\u53F7', 122), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'DB2\u524D\u6EDA\u65E5\u5FD7\u6062\u590D\u65F6\u95F4\u70B9', 123), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'DB2\u6062\u590D\u6570\u636E\u6307\u5B9A\u76EE\u5F55', 124), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u5757\u5927\u5C0F', 125), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u526F\u672C\u5907\u4EFD\u7C7B\u578B', 126), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'TDSQL\u6062\u590D\u53C2\u6570', 127), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u865A\u62DF\u4EA4\u6362\u673A', 128), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u865A\u62DF\u4EA4\u6362\u673Aid', 129), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u672A\u77E56', 130), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6E90\u7AEF\u91CD\u5220', 131), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u4E0D\u8FDB\u884C\u65E5\u5FD7\u5F52\u6863', 132), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u4ECB\u8D28\u670D\u52A1\u5668IP', 133), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'MYSQL\u7269\u7406\u6062\u590D\u6307\u5B9A\u89E3\u538B\u8DEF\u5F84', 134), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'MYSQL\u7269\u7406\u6062\u590D\u6307\u5B9A\u89E3\u538B\u7EBF\u7A0B\u6570\u91CF', 135), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6570\u636E\u4F20\u8F93\u6A21\u5F0F', 148), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u5F3A\u5236\u5907\u4EFD\u4E3B\u8282\u70B9', 150), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u81EA\u52A8\u9009\u62E9\u5907\u8282\u70B9', 151), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6570\u636E\u6E90', 152), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u68C0\u6D4BBinlog\u5907\u4EFD\u65F6\u95F4', 153), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u4E00\u81F4\u6027\u56DE\u6EDA', 154), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6062\u590D\u7684\u65F6\u95F4\u70B9', 155), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u89C4\u683C\u540D', 156), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6C60\u540D', 157), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u79DF\u6237\u540D', 158), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u5F52\u6863\u8BBE\u5907ID', 159), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u5F52\u6863\u4ECB\u8D28\u6C60', 160), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u81EA\u52A8\u8C03\u6574\u5907\u4EFD\u5BF9\u8C61', 161), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6307\u5B9ASCN\u6062\u590D', 164), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u751F\u6210\u865A\u62DF\u673A\u5185\u5B58\u5FEB\u7167', 165), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'TCE\u6062\u590D\u6876\u4E3B\u673A\u5730\u5740', 168), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'TCE\u6062\u590D\u6570\u636E\u6876\u540D', 169), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'TCE\u6062\u590D\u5143\u6570\u636E\u6876\u540D', 170), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'TCCE\u6062\u590D\u6876\u8BBF\u95EEID', 171), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'TCE\u6062\u590D\u6876\u5B89\u5168ID', 172), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u533A\u57DF', 173), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u8FD0\u884C\u7AEFBRC\u7684\u5730\u5740', 174), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u8FD0\u8425\u7AEFBRC\u7684\u8BBF\u95EEID', 175), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u8FD0\u8425\u7AEFBRC\u7684\u5B89\u5168ID', 176), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u7981\u6B62\u6062\u590D\u5230\u539F\u751F\u4EA7\u7CFB\u7EDF', 177), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u91CD\u5B9A\u5411\u6876', 178), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u91CD\u5B9A\u5411\u5BF9\u8C61\u8DEF\u5F84', 179), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u7EC6\u9897\u7C92\u5EA6\u5907\u4EFD', 180), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u96C6\u7FA4\u6062\u590D', 181), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6307\u5B9A\u6570\u636E\u5E93\u8868\u540D\u524D\u7F00', 182), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6570\u636E\u6302\u8F7D\u8DEF\u5F84', 121), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'ORACLESPFILE\u6587\u4EF6\u540D', 196), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'ORACLESPFILE\u8DEF\u5F84', 197), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'backupId', 198), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6307\u5B9A\u79DF\u6237\u5E93', 199), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u534E\u4E3ARDS\u76EE\u7684\u673A\u5668', 200), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u534E\u4E3ARDS\u767B\u5F55\u7528\u6237', 202), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u534E\u4E3ARDS\u767B\u5F55\u5BC6\u7801', 203), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, 'vpcID', 204), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u5B50\u7F51ID', 205), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6570\u636E\u5377\u8BBE\u5907', 206), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u56DE\u6863\u5377\u63CF\u8FF0\u5907\u6CE8', 207), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6307\u5B9A\u5377\u4FDD\u7559\u5929\u6570', 208), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u5907\u4EFD\u65F6\u662F\u5426\u5220\u9664\u65E5\u5FD7', 209), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6062\u590D\u5230\u6307\u5B9Axid\u53F7', 210), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6700\u77ED\u65F6\u95F4\u6062\u590D', 211), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6062\u590D\u5230\u6700\u65B0\u72B6\u6001', 212), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u6062\u590D\u5230\u6307\u5B9Alsn\u53F7', 213), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u5907\u4EFD\u662F\u5426\u5220\u9664\u65E5\u5FD7', 215), (0, _defineProperty3.default)(_OPTION_LIST_TYPE, '\u5907\u4EFD\u4E0D\u4FDD\u5B58GTID\u4FE1\u606F', 217), _OPTION_LIST_TYPE);

var CONVENTION_COMPONENTS_LIST = exports.CONVENTION_COMPONENTS_LIST = {
	'SAP HANA': {
		id: '2162688',
		restype: 2818048,
		name: 'SAP HANA',
		componentsName: 'SAPHANA'
	},
	文件: {
		id: '65536',
		restype: 65536,
		name: '文件',
		componentsName: 'Filer'
	},
	PostgreSQL: {
		id: '983040',
		restype: 983040,
		name: 'PostgreSQL',
		componentsName: 'PostgreSQL'
	},
	KINGBASE: {
		id: '720896',
		restype: 720896,
		name: 'KingBase',
		componentsName: 'KingBase'
	},
	瀚高数据库: {
		id: '2293760',
		restype: 2293760,
		name: 'HanGaoBase',
		componentsName: 'HanGaoBase'
	},
	GBase8a: {
		id: '3014656',
		restype: 3014656,
		name: 'Gbase8a',
		componentsName: 'Gbase8a'
	},
	GBase8C: {
		id: '2883584',
		restype: 2883584,
		name: 'Gbase8c',
		componentsName: 'Gbase8c'
	},
	HLSQL数据库: {
		id: '2686976',
		restype: 2686976,
		name: 'HLSQL',
		componentsName: 'HLSQL'
	},
	OpenGauss: {
		id: '1966080',
		restype: 1966080,
		name: 'OpenGauss',
		componentsName: 'OpenGauss'
	},
	ORACLE: {
		id: '131072',
		restype: 131072,
		name: 'ORACLE',
		componentsName: 'ORACLE'
	},
	MYSQL: {
		id: '196608',
		restype: 196608,
		name: 'MYSQL',
		componentsName: 'MYSQL'
	},
	SQLSERVER: {
		id: '262144',
		restype: 262144,
		name: 'SQLSERVER',
		componentsName: 'SQLSERVER'
	},
	VMWARE: {
		id: '327680',
		restype: 327680,
		name: 'VMWARE',
		componentsName: 'VMWARE'
	},
	CNWare虚拟化: {
		id: '2621440',
		restype: 2621440,
		name: 'CNWare虚拟化',
		componentsName: 'CNWare虚拟化'
	},
	操作系统: {
		id: '393216',
		restype: 393216,
		name: '操作系统',
		componentsName: 'OperatingSystem'
	},
	DB2: {
		id: '524288',
		restype: 524288,
		name: 'DB2',
		componentsName: 'DB2'
	},
	达梦: {
		id: '655360',
		restype: 655360,
		name: '达梦',
		componentsName: 'DaMeng'
	},
	Hcs: {
		id: '1900544',
		restype: 1900544,
		name: 'Hcs',
		componentsName: 'Hcs'
	},
	HwHcs: {
		id: '538771456',
		restype: 538771456,
		name: 'HwHcs',
		componentsName: 'HwHcs'
	},
	HwRDS: {
		id: '538836992',
		restype: 538836992,
		name: 'HwRDS',
		componentsName: 'HwRDS'
	},
	AliRDS: {
		id: '271319040',
		restype: 271319040,
		name: 'AliRDS',
		componentsName: 'AliRDS'
	},
	GoldenDBJq: {
		id: '2424832',
		restype: 2424832,
		name: 'GoldenDBJq',
		componentsName: 'GoldenDBJq'
	},
	'GaussDB': {
		id: '2752512',
		restype: 2752512,
		name: 'GaussDB',
		componentsName: 'GaussDBDWS'
	},
	TCE: {
		id: '2359296',
		restype: 2359296,
		name: 'TCE',
		componentsName: 'TCE'
	}
};

var BASE_TABLE_LIST = exports.BASE_TABLE_LIST = [2424832, 1703936, 538836992];

var RECOVER_COMPONENTS_LIST = exports.RECOVER_COMPONENTS_LIST = (_RECOVER_COMPONENTS_L = {
	'SAP HANA': {
		id: '2162688',
		restype: 2818048,
		name: 'SAP HANA',
		componentsName: 'SAPHANA'
	},
	文件: {
		id: '65536',
		restype: 65536,
		name: '文件',
		componentsName: 'Filer'
	},

	OceanBase: {
		id: '2228224',
		restype: 2228224,
		name: 'OceanBase',
		componentsName: 'OceanBase'
	},

	MYSQL: {
		id: '196608',
		restype: 196608,
		name: 'MYSQL',
		componentsName: 'MYSQL'
	},

	ORACLE: {
		id: '131072',
		restype: 131072,
		name: 'ORACLE',
		componentsName: 'ORACLE'
	},
	操作系统: {
		id: '393216',
		restype: 393216,
		name: '操作系统',
		componentsName: 'OperatingSystem'
	},

	PostgreSQL: {
		id: '983040',
		restype: 983040,
		name: 'PostgreSQL',
		componentsName: 'PostgreSQL'
	},

	VMWARE: {
		id: '327680',
		restype: 327680,
		name: 'VMWARE',
		componentsName: 'VMWARE'
	},

	达梦: {
		id: '655360',
		restype: 655360,
		name: '达梦',
		componentsName: 'DaMeng'
	},

	KINGBASE: {
		id: '720896',
		restype: 720896,
		name: 'KINGBASE',
		componentsName: 'KINGBASE'
	},

	H3CCAS: {
		id: '1441792',
		restype: 1441792,
		name: 'H3CCAS',
		componentsName: 'H3CCAS'
	},

	H3CUIS: {
		id: '1507328',
		restype: 1507328,
		name: 'H3CUIS',
		componentsName: 'H3CUIS'
	},

	GoldenDB: {
		id: '2097152',
		restype: 2097152,
		name: 'GoldenDB',
		componentsName: 'GoldenDB'
	},

	SQLSERVER: {
		id: '262144',
		restype: 262144,
		name: 'SQLSERVER',
		componentsName: 'SQLSERVER'
	},

	神通: {
		id: '851968',
		restype: 851968,
		name: '神通',
		componentsName: 'ShenTong'
	},

	'Hyper-V': {
		id: '458752',
		restype: 458752,
		name: 'Hyper-V',
		componentsName: 'HyperV'
	},

	MongoDB: {
		id: '917504',
		restype: 917504,
		name: 'MongoDB',
		componentsName: 'MongoDB'
	}
}, (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'PostgreSQL', {
	id: '983040',
	restype: 983040,
	name: 'PostgreSQL',
	componentsName: 'PostgreSQL'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'DB2', {
	id: '524288',
	restype: 524288,
	name: 'DB2',
	componentsName: 'DB2'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'Sybase', {
	id: '786432',
	restype: 786432,
	name: 'Sybase',
	componentsName: 'Sybase'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'OpenStack', {
	id: '1048576',
	restype: 1048576,
	name: 'OpenStack',
	componentsName: 'OpenStack'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'GBase', {
	id: '1114112',
	restype: 1114112,
	name: 'GBase',
	componentsName: 'GBase'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, '\u4E1B\u4E91\u6570\u636E\u5E93', {
	id: '1769472',
	restype: 1769472,
	name: '丛云数据库',
	componentsName: 'Congyue'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'TD-SQL数据库', {
	id: '2162688',
	restype: 2162688,
	name: 'TD-SQL数据库',
	componentsName: 'TDSQl'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'AWSS3', {
	id: '1572864',
	restype: 1572864,
	name: 'AWSS3',
	componentsName: 'AWSS3'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'GolderDB\u96C6\u7FA4', {
	id: '2424832',
	restype: 2424832,
	name: 'GolderDB集群',
	componentsName: 'GolderDB集群',
	newName: 'GolderDBJq' }), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'THECloud', {
	id: '1835008',
	restype: 1835008,
	name: 'THECloud',
	componentsName: 'THECloud'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'Informix', {
	id: '2555904',
	restype: 2555904,
	name: 'InformixD',
	componentsName: 'InformixD'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'TIDB', {
	id: '1703936',
	restype: 1703936,
	name: 'TIDB',
	componentsName: 'TIDB'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'CNWare\u865A\u62DF\u5316', {
	id: '2621440',
	restype: 2621440,
	name: 'CNWare虚拟化',
	componentsName: 'CNWare'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'Hcs', {
	id: '1900544',
	restype: 1900544,
	name: 'Hcs',
	componentsName: 'Hcs'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, '\u534E\u4E3A\u4E91', {
	id: '536870912',
	restype: 536870912,
	name: 'HuaWeiYun',
	componentsName: 'HuaWeiYun'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, '华为云(HCS)', {
	id: '538771456',
	restype: 538771456,
	name: 'HuaWeiHcs',
	componentsName: 'HuaWeiHcs'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, '华为云(RDS)', {
	id: '538836992',
	restype: 538836992,
	name: 'HuaWeiRds',
	componentsName: 'HuaWeiRds'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'AliRDS', {
	id: '271319040',
	restype: 271319040,
	name: 'AliRDS',
	componentsName: 'AliRDS'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'AliYun', {
	id: '268435456',
	restype: 268435456,
	name: 'AliYun',
	componentsName: 'AliYun'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'RDS', {
	id: '271319040',
	restype: 271319040,
	name: 'RDS',
	componentsName: 'RDS'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'ECS', {
	id: '271384576',
	restype: 271384576,
	name: 'ECS',
	componentsName: 'ECS'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'TCE', {
	id: '2359296',
	restype: 2359296,
	name: 'TCE',
	componentsName: 'TCE'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'CVM', {
	id: '2359298',
	restype: 2359298,
	name: 'CVM',
	componentsName: 'CVM'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'TCETDSQL', {
	id: '2359299',
	restype: 2359299,
	name: 'TCETDSQL',
	componentsName: 'TCETDSQL'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'MARIALDB', {
	id: '2359300',
	restype: 2359300,
	name: 'MARIALDB',
	componentsName: 'MARIALDB'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'PGDSQL', {
	id: '2359301',
	restype: 2359301,
	name: 'PGDSQL',
	componentsName: 'PGDSQL'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'CBS', {
	id: '2359302',
	restype: 2359302,
	name: 'CBS',
	componentsName: 'CBS'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'CFS', {
	id: '2359303',
	restype: 2359303,
	name: 'CFS',
	componentsName: 'CFS'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, 'QingCloud', {
	id: '2949120',
	restype: 2949120,
	name: 'QingCloud',
	componentsName: 'QingCloud'
}), (0, _defineProperty3.default)(_RECOVER_COMPONENTS_L, '数据卷', {
	id: '2490368',
	restype: 2490368,
	name: 'Volumes',
	componentsName: 'Volumes'
}), _RECOVER_COMPONENTS_L);

var DAY_LIST = exports.DAY_LIST = [{
	name: '1号',
	value: 1
}, {
	name: '2号',
	value: 2
}, {
	name: '3号',
	value: 3
}, {
	name: '4号',
	value: 4
}, {
	name: '5号',
	value: 5
}, {
	name: '6号',
	value: 6
}, {
	name: '7号',
	value: 7
}, {
	name: '8号',
	value: 8
}, {
	name: '9号',
	value: 9
}, {
	name: '10号',
	value: 10
}, {
	name: '11号',
	value: 11
}, {
	name: '12号',
	value: 12
}, {
	name: '13号',
	value: 13
}, {
	name: '14号',
	value: 14
}, {
	name: '15号',
	value: 15
}, {
	name: '16号',
	value: 16
}, {
	name: '17号',
	value: 17
}, {
	name: '18号',
	value: 18
}, {
	name: '19号',
	value: 19
}, {
	name: '20号',
	value: 20
}, {
	name: '21号',
	value: 21
}, {
	name: '22号',
	value: 22
}, {
	name: '23号',
	value: 23
}, {
	name: '24号',
	value: 24
}, {
	name: '25号',
	value: 25
}, {
	name: '26号',
	value: 26
}, {
	name: '27号',
	value: 27
}, {
	name: '28号',
	value: 28
}, {
	name: '29号',
	value: 29
}, {
	name: '30号',
	value: 30
}, {
	name: '31号',
	value: 31
}];

var WEEK_LIST = exports.WEEK_LIST = [{
	value: 1,
	name: '周一'
}, {
	value: 2,
	name: '周二'
}, {
	value: 3,
	name: '周三'
}, {
	value: 4,
	name: '周四'
}, {
	value: 5,
	name: '周五'
}, {
	value: 6,
	name: '周六'
}, {
	value: 7,
	name: '周日'
}];

var TOOLTIP_TEXT = exports.TOOLTIP_TEXT = '调度开始结束时间是限制计划调度开始结束的时间窗口，不影响发起备份任务的运行结束，如:1号13:00:00到7号:13:20:00，则表示1至7号都在13:00:00~13:20:00这个时间范围发起一次任务,如果调度类型为间隔调度，这个时间范围只影响到第一次发起时间，后面发起的时间根据上一次发起时间加间隔时间确定，另外要注意结束时间要大于开始时间。';

/***/ }),

/***/ 2147:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});
exports.getPlaceonfileData = exports.getVolpools = exports.getZvolumeTableData = exports.getZvolumeRowData = exports.getZvolumeTree = exports.unmountFun = exports.copyFun = exports.getDeviceList = exports.getNoFileTree = exports.getFileTree = undefined;

var _request = __webpack_require__(316);

var _request2 = _interopRequireDefault(_request);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

var getFileTree = exports.getFileTree = function getFileTree(data) {
	return (0, _request2.default)({
		method: 'GET',
		url: '/rest-ful/v3.0/zvolume/browse/?device=' + data.dev_id + '&path=' + data.dev_name + '/' + data.parent + '@' + data.names + '/' + data.path
	});
};
var getNoFileTree = exports.getNoFileTree = function getNoFileTree(data) {
	return (0, _request2.default)({
		method: 'GET',
		url: 'rest-ful/v3.0/zvolume/browse/' + data.id + '?volume=' + data.volume_id + '&path=' + data.path
	});
};

var getDeviceList = exports.getDeviceList = function getDeviceList() {
	return (0, _request2.default)({
		method: 'GET',
		url: 'rest-ful/v3.0/devices?cdm=1'
	});
};
var copyFun = exports.copyFun = function copyFun(data) {
	return (0, _request2.default)({
		method: 'POST',
		url: 'rest-ful/v3.0/zvolume/copy',
		data: data
	});
};
var unmountFun = exports.unmountFun = function unmountFun(data) {
	return (0, _request2.default)({
		method: 'POST',
		url: 'rest-ful/v3.0/zvolume/unmount',
		data: data
	});
};

var getZvolumeTree = exports.getZvolumeTree = function getZvolumeTree() {
	return (0, _request2.default)({
		method: 'GET',
		url: 'rest-ful/v3.0/zvolume/buildzvolumetree'
	});
};
var getZvolumeRowData = exports.getZvolumeRowData = function getZvolumeRowData(rowData) {
	return (0, _request2.default)({
		method: 'GET',
		url: 'rest-ful/v3.0/zvolume/volumelist/' + rowData.policy
	});
};

var getZvolumeTableData = exports.getZvolumeTableData = function getZvolumeTableData(rowData) {
	return (0, _request2.default)({
		method: 'GET',
		url: 'rest-ful/v3.0/zvolume/snapshot/list/' + rowData.volume
	});
};

var getVolpools = exports.getVolpools = function getVolpools(data) {
	return (0, _request2.default)({
		method: 'GET',
		url: 'rest-ful/v3.0/volpools'
	});
};

var getPlaceonfileData = exports.getPlaceonfileData = function getPlaceonfileData(data) {
	return (0, _request2.default)({
		method: 'POST',
		url: 'rest-ful/v3.0/zvolume/archived',
		data: data
	});
};

/***/ }),

/***/ 2359:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.RESTYPE_LIST = exports.OPERATION_TYPE = exports.batchDeletePolicy = exports.deletePolicy = exports.setSchedule = exports.getScheduleType = exports.setSwitchStates = exports.getTableData = exports.getPolicyData = exports.getTypelData = exports.getEditClientList = exports.getClientList = undefined;

var _defineProperty2 = __webpack_require__(89);

var _defineProperty3 = _interopRequireDefault(_defineProperty2);

var _RESTYPE_LIST;

var _jquery = __webpack_require__(74);

var _request = __webpack_require__(316);

var _request2 = _interopRequireDefault(_request);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

var getClientList = exports.getClientList = function getClientList() {
    return (0, _request2.default)({
        method: 'get',
        url: '/rest-ful/v3.0/restore/clients?nums=0&delete_flag=1'
    });
};
var getEditClientList = exports.getEditClientList = function getEditClientList() {
    return (0, _request2.default)({
        method: 'get',
        url: '/rest-ful/v3.0/clients?nums=0'
    });
};

var getTypelData = exports.getTypelData = function getTypelData(type) {
    return (0, _request2.default)({
        method: 'get',

        url: type == 11 ? '/rest-ful/v3.0/resourcetype?&cdm=1' : '/rest-ful/v3.0/resourcetype?&cdm=0'
    });
};

var getPolicyData = exports.getPolicyData = function getPolicyData() {
    return (0, _request2.default)({
        method: 'get',
        url: '/rest-ful/v3.0/policytype?mode=all'
    });
};

var getTableData = exports.getTableData = function getTableData(params) {
    return (0, _request2.default)({
        method: 'get',
        url: '/rest-ful/v3.0/policies?pageno=' + params.pageNumber + '&nums=' + params.pageSize + '&client=' + params.client + '&device=' + params.device + '&restype=' + params.restype + '&policytype=' + params.cltype + '&policy=' + params.policy
    });
};

var setSwitchStates = exports.setSwitchStates = function setSwitchStates(params, value) {
    return (0, _request2.default)({
        method: 'get',
        url: value ? '/rest-ful/v3.0/policy/enable/' + params.row.id + '?method=enable' : '/rest-ful/v3.0/policy/enable/' + params.row.id + '?method=disable'
    });
};

var getScheduleType = exports.getScheduleType = function getScheduleType(params) {
    return (0, _request2.default)({
        method: 'get',
        url: params.type == 'CDM备份' ? '/rest-ful/v3.0/policy/scheduletype/' + params.id + '?cdm=1' : '/rest-ful/v3.0/policy/scheduletype/' + params.id
    });
};

var setSchedule = exports.setSchedule = function setSchedule(params, item) {
    return (0, _request2.default)({
        method: 'get',
        url: '/rest-ful/v3.0/policy/schedule/' + params.id + '?type=' + item.type
    });
};

var deletePolicy = exports.deletePolicy = function deletePolicy(params) {
    return (0, _request2.default)({
        method: 'delete',
        url: '/rest-ful/v3.0/policy/' + params.id
    });
};

var batchDeletePolicy = exports.batchDeletePolicy = function batchDeletePolicy(data) {
    return (0, _request2.default)({
        method: 'delete',
        url: '/rest-ful/v3.0/batch/policy',
        data: data
    });
};

var OPERATION_TYPE = exports.OPERATION_TYPE = {
    修改: {
        type: 'update',
        title: '修改策略',
        name: '修改'
    },
    查看: {
        type: 'check',
        title: '查看策略',
        name: '查看'
    }
};

var RESTYPE_LIST = exports.RESTYPE_LIST = (_RESTYPE_LIST = {
    1572864: {
        innerHTML: "&#xe676;"
    },
    1835008: {
        innerHTML: "&#xe68a;"
    }
}, (0, _defineProperty3.default)(_RESTYPE_LIST, '1572864', {
    innerHTML: "&#xe68a;"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 1638400, {
    innerHTML: "&#xe9df;"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 2162688, {
    color: '#ffc000',
    innerHTML: "&#xe656;"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 1769472, {
    color: '#ffc000',
    innerHTML: "&#xe608;"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 393216, {
    innerHTML: "&#xe623;"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 131072, {
    color: '#e8413e',
    innerHTML: "&#xe611;"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 196608, {
    color: '#e8413e',
    innerHTML: "&#xe64a;"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 65536, {
    color: "#ffca28",
    innerHTML: "&#xeac4;"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 655360, {
    color: "#3b97d3",
    innerHTML: "&#xe940;"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 327680, {
    color: "#1296db",
    innerHTML: '&#xe68a;'
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 524288, {
    innerHTML: "&#xe604;"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 720896, {
    color: '#d040ea',
    innerHTML: "&#xed9d;"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 458752, {
    color: '#fd7549',
    innerHTML: "&#xe767;"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 786432, {
    color: '#fd7549',
    innerHTML: "Sybase备份"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 851968, {
    innerHTML: "&#xe6ee;"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 262144, {
    color: '#ffca28',
    innerHTML: "&#xe6ee;"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 917504, {
    innerHTML: "&#xe6ee;"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 983040, {
    innerHTML: "&#xe6ee;"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 1048576, {
    innerHTML: "&#xe6ee;"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 1114112, {
    innerHTML: "&#xe6ee;"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 1441792, {
    innerHTML: "&#xe6ee;"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 1507328, {
    innerHTML: "&#xe6ee"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 2097152, {
    innerHTML: "&#xe6ee;"
}), (0, _defineProperty3.default)(_RESTYPE_LIST, 589824, {
    color: "red",
    innerHTML: "&#xe940;"
}), _RESTYPE_LIST);

/***/ }),

/***/ 2393:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.OPERATION_TYPE = exports.getInstances = exports.getBrowseList = exports.getProtocol = exports.getClients = exports.setExpired = exports.getTableData = exports.getTypelData = undefined;

var _request = __webpack_require__(316);

var _request2 = _interopRequireDefault(_request);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

var getTypelData = exports.getTypelData = function getTypelData() {
  return (0, _request2.default)({
    method: 'get',
    url: '/rest-ful/v3.0/resourcetype?cdm=1'
  });
};

var getTableData = exports.getTableData = function getTableData(params) {
  return (0, _request2.default)({
    method: 'get',
    url: '/rest-ful/v3.0/zvolume/list?pageno=' + params.pageNumber + '&nums=' + params.pageSize + '&client=' + params.client + '&restype=' + params.restype + '&policyname=' + params.policyname + '&starttime=' + params.starttime + '&endtime=' + params.endtime
  });
};

var setExpired = exports.setExpired = function setExpired(data) {
  return (0, _request2.default)({
    method: 'post',
    url: '/rest-ful/v3.0/zvolume/expired',
    data: data
  });
};

var getClients = exports.getClients = function getClients(params) {
  return (0, _request2.default)({
    method: 'get',
    url: '/rest-ful/v3.0/clients?nums=0&restype=' + params.res_type_val
  });
};

var getProtocol = exports.getProtocol = function getProtocol(params) {
  return (0, _request2.default)({
    method: 'get',
    url: '/rest-ful/v3.0/mount/protocol'
  });
};

var getBrowseList = exports.getBrowseList = function getBrowseList(params) {
  return (0, _request2.default)({
    method: 'get',
    url: '/rest-ful/v3.0/zvolume/browse/' + params.id + '?volume=' + params.volume_id
  });
};

var getInstances = exports.getInstances = function getInstances(cid, params) {
  return (0, _request2.default)({
    method: 'get',
    url: '/rest-ful/v3.0/client/agent/instances?cid=' + cid + '&restype=' + params.res_type_val
  });
};

var OPERATION_TYPE = exports.OPERATION_TYPE = {
  挂载: {
    type: 'mount',
    title: '挂载操作',
    name: '挂载'
  },
  恢复: {
    type: 'recover',
    title: '恢复操作',
    name: '恢复'
  }
};

/***/ }),

/***/ 2618:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_Tree_vue__ = __webpack_require__(2825);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_Tree_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_Tree_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_Tree_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_Tree_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_25aa4314_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_Tree_vue__ = __webpack_require__(3763);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_25aa4314_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_Tree_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_25aa4314_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_Tree_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3761)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = "data-v-25aa4314"
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_Tree_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_25aa4314_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_Tree_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/dupMag/components/Tree.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-25aa4314", Component.options)
  } else {
    hotAPI.reload("data-v-25aa4314", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ }),

/***/ 2822:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});

var _index = __webpack_require__(2359);

var _index2 = __webpack_require__(2393);

var _index3 = __webpack_require__(210);

var _ModalBox = __webpack_require__(540);

var _ModalBox2 = _interopRequireDefault(_ModalBox);

var _MountAndRecover = __webpack_require__(3755);

var _MountAndRecover2 = _interopRequireDefault(_MountAndRecover);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
  computed: {
    getPower: function getPower() {
      return this.$store.state.power.module;
    }
  },
  components: {
    ModalBox: _ModalBox2.default,
    MountAndRecover: _MountAndRecover2.default
  },
  data: function data() {
    return {
      query: {
        client: '',
        restype: '',
        policyname: '',
        starttime: '',
        endtime: '',
        pageSize: 10,
        pageNumber: 1
      },
      loading: false,
      tableHeight: window.innerHeight - 320,
      tableList: [],
      columns: [{
        title: "策略",
        key: "policy",
        sortable: true,
        align: 'left'
      }, {
        title: "资源类型",
        key: "resource_type",
        slot: 'restype',
        sortable: true,
        align: 'left'
      }, {
        title: "备份类型",
        key: "backup_type",
        sortable: true
      }, {
        title: "客户端",
        key: "client",
        sortable: true
      }, {
        title: "IP",
        key: "ip",
        sortable: true
      }, {
        title: "创建时间",
        key: "create_time",
        sortable: true
      }, {
        title: "快照时间",
        key: "name",
        sortable: true
      }, {
        title: "卷名称",
        key: "parent",
        sortable: true
      }, {
        title: "过期时间",
        key: "expired_time",
        sortable: true
      }, {
        title: "状态",
        key: "status",
        slot: 'status',
        sortable: true
      }, {
        title: "操作",
        key: "operation",
        slot: 'operation'
      }],
      modelProps: {},
      showStaleDated: false,
      clientSelect: [],
      typeSelect: [],
      OPERATION_TYPE: _index2.OPERATION_TYPE,
      NEW_ICON_NUMBER: _index3.NEW_ICON_NUMBER,
      policytypeListData: [],
      clickId: '',
      drawerData: {},
      selectRow: {},
      selectType: {},
      showMountAndRecover: false
    };
  },
  mounted: function mounted() {
    this.getClientListFun();
    this.getTypelDataFun();
    this.getTableDataFun();
  },

  methods: {
    setOperation: function setOperation(row, type) {
      this.selectRow = row;
      this.selectType = type;
      this.showMountAndRecover = true;
    },
    staleDated: function staleDated(row) {
      if (row.status == '过期') {
        this.$Message.warning('该数据已过期，无需过期操作！');
        return false;
      }
      this.modelProps = {
        title: '过期操作',
        width: '540',
        messageValue: '此操作会使该条数据过期，是否操作？',
        type: _index3.MODEL_TYPE_LIST.warning.type,
        color: _index3.MODEL_TYPE_LIST.warning.color,
        row: row
      };
      this.showStaleDated = true;
    },
    postStatus: function postStatus(type, params) {
      var _this = this;

      if (type) {
        var dataList = [params.row.id];
        (0, _index2.setExpired)(dataList).then(function (res) {
          _this.$Message.success('过期操作成功');
        }).finally(function () {
          _this.getTableDataFun();
        });
      }
    },
    findIcons: function findIcons(resource_type) {
      for (var key in _index3.NEW_ICON_NUMBER) {
        if (_index3.NEW_ICON_NUMBER[key].type == resource_type) {
          return _index3.NEW_ICON_NUMBER[key].use;
        }
      }
    },
    danJiRow: function danJiRow(row, index) {
      this.clickId = row.id;
      this.drawerData = row;
    },
    tishi: function tishi(currentRow, index) {
      if (currentRow.id == this.clickId) {
        return "trbgshow";
      }
      return "trbgshow_a";
    },
    getClientListFun: function getClientListFun() {
      var _this2 = this;

      (0, _index.getClientList)().then(function (res) {
        var dataList = res.data.vrts_client_data;
        if (dataList.length) {
          _this2.clientSelect = dataList;
        }
      });
    },
    sizeChange: function sizeChange(value) {
      this.query.pageSize = value;
      this.getTableDataFun();
    },
    currentChange: function currentChange(value) {
      this.query.pageNumber = value;
      this.getTableDataFun();
    },
    getTypelDataFun: function getTypelDataFun() {
      var _this3 = this;

      (0, _index2.getTypelData)().then(function (res) {
        _this3.typeSelect = res.data;
      });
    },
    setTime: function setTime(res) {
      var _this4 = this;

      res.forEach(function (item, i) {
        if (i == 0) {
          _this4.query.starttime = item;
        }
        if (i == 1) {
          _this4.query.endtime = item;
        }
      });
      this.getTableDataFun();
    },
    getTableDataFun: function getTableDataFun() {
      var _this5 = this;

      this.loading = true;
      (0, _index2.getTableData)(this.query).then(function (res) {
        _this5.tableList = res.data.Records;
      }).finally(function () {
        _this5.loading = false;
      });
    }
  }
};

/***/ }),

/***/ 2823:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});

var _ORACLE = __webpack_require__(3758);

var _ORACLE2 = _interopRequireDefault(_ORACLE);

var _KINGBASE = __webpack_require__(3765);

var _KINGBASE2 = _interopRequireDefault(_KINGBASE);

var _H3CCAS = __webpack_require__(3769);

var _H3CCAS2 = _interopRequireDefault(_H3CCAS);

var _HanGao = __webpack_require__(3773);

var _HanGao2 = _interopRequireDefault(_HanGao);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
  props: {
    value: {
      type: Boolean,
      'default': false
    },
    selectType: {
      type: Object
    },
    selectRow: {
      type: Object
    }
  },
  components: {
    ORACLE: _ORACLE2.default,
    H3CCAS: _H3CCAS2.default,
    HanGao: _HanGao2.default,
    KINGBASE: _KINGBASE2.default
  },
  data: function data() {
    return {
      componentIdName: ''
    };
  },
  mounted: function mounted() {
    if (this.selectRow.resource_type == 'H3C CAS') {
      this.componentIdName = 'H3CCAS';
    } else if (this.selectRow.resource_type == '瀚高数据库') {
      this.componentIdName = 'HanGao';
    } else {
      this.componentIdName = this.selectRow.resource_type;
    }
  },

  methods: {
    closeButs: function closeButs() {
      this.$emit('input', false);
    }
  }
};

/***/ }),

/***/ 2824:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _regenerator = __webpack_require__(537);

var _regenerator2 = _interopRequireDefault(_regenerator);

var _asyncToGenerator2 = __webpack_require__(538);

var _asyncToGenerator3 = _interopRequireDefault(_asyncToGenerator2);

var _index = __webpack_require__(2393);

var _drawerBox = __webpack_require__(1528);

var _Tree = __webpack_require__(2618);

var _Tree2 = _interopRequireDefault(_Tree);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
	props: {
		selectType: {
			type: Object
		},
		selectRow: {
			type: Object
		}
	},
	components: {
		Tree: _Tree2.default
	},
	data: function data() {
		return {
			typedis: false,
			formData: {
				optionsObj: {
					119: 1
				}
			},
			resPost: [],
			clientSelect: [],
			agreArr: [],
			dmsldata: [],
			rulesList: {
				'optionsObj[22]': [{ required: true, message: '请选择服务名', trigger: 'change' }],
				mountpath: [{ required: true, message: '请输入路径', trigger: 'blur' }],
				client: [{ required: true, message: '请选择客户端', trigger: 'change', type: 'number' }],
				agre: [{ required: true, message: '请选择协议', trigger: 'change' }]
			},
			OPERATION_TYPE: _index.OPERATION_TYPE,
			OPTION_LIST_TYPE: _drawerBox.OPTION_LIST_TYPE
		};
	},
	mounted: function mounted() {
		this.getClientsFun();
		this.getProtocolFun();
	},

	methods: {
		getResPost: function getResPost(val) {
			this.resPost = val;
		},
		getClientsFun: function getClientsFun() {
			var _this = this;

			(0, _index.getClients)(this.selectRow).then(function (res) {
				_this.clientSelect = res.data.vrts_client_data;
			});
		},
		getProtocolFun: function getProtocolFun() {
			var _this2 = this;

			(0, _index.getProtocol)().then(function (res) {
				_this2.agreArr = res.data;
			});
		},
		optionId: function optionId(datas) {
			var _this3 = this;

			this.dmsldata = [];
			(0, _index.getInstances)(datas, this.selectRow).then(function (res) {
				_this3.dmsldata = res.data;
			});
		},
		closeAddPolicy: function closeAddPolicy() {
			this.$emit('closeButs', false);
		},
		submitForm: function submitForm(name) {
			var _this4 = this;

			this.$refs[name].validate(function () {
				var _ref = (0, _asyncToGenerator3.default)(_regenerator2.default.mark(function _callee(valid) {
					var newFormData;
					return _regenerator2.default.wrap(function _callee$(_context) {
						while (1) {
							switch (_context.prev = _context.next) {
								case 0:
									if (!valid) {
										_context.next = 7;
										break;
									}

									_this4.$Message.success('Success!');
									_context.next = 4;
									return _this4.setOptionArr();

								case 4:
									newFormData = _context.sent;
									_context.next = 7;
									break;

								case 7:
								case 'end':
									return _context.stop();
							}
						}
					}, _callee, _this4);
				}));

				return function (_x) {
					return _ref.apply(this, arguments);
				};
			}());
		},
		setOptionArr: function setOptionArr() {
			var optionsObj = this.formData.optionsObj;
			this.formData.options = [];
			for (var key in optionsObj) {
				var obj = {
					type: key * 1,
					value: optionsObj[key]
				};
				this.formData.options.push(obj);
			}
			return this.formData;
		}
	}
};

/***/ }),

/***/ 2825:
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function($) {

Object.defineProperty(exports, "__esModule", {
	value: true
});

var _index = __webpack_require__(2147);

exports.default = {
	props: {
		setRowData: {
			type: Object
		}
	},
	data: function data() {
		return {
			resPost: [],
			resources: {
				pathConten: []
			},
			ztreeArray: [],
			setting: {
				check: {
					enable: true
				},
				edit: {
					enable: false,
					editNameSelectAll: false
				},
				callback: {
					onClick: this.zTreeOnClick,
					onCheck: this.zTreeOnCheck
				},
				view: {
					nameIsHTML: true,
					selectedMulti: false
				},
				data: {
					simpleData: {
						enable: true
					}
				}
			}
		};
	},
	mounted: function mounted() {},

	methods: {
		getZtreeArrayData: function getZtreeArrayData() {
			var _this = this;

			(0, _index.getFileTree)(this.setRowData).then(function (res) {
				_this.ztreeArray = [];
				res.data.forEach(function (item, index) {
					var obj = {
						id: _this.setRowData.id,

						iconSkin: 'files',
						name: item.path,
						path: item.path,
						nocheck: true,
						nodetype: 0,
						names: _this.setRowData.name,
						dev_id: _this.setRowData.dev_id,
						dev_name: _this.setRowData.dev_name,
						parent: _this.setRowData.parent,
						fatherName: item.path,

						res_type_val: item.type
					};
					_this.ztreeArray.push(obj);
				});

				$.fn.zTree.init($('#treeDemoTactics'), _this.setting, _this.ztreeArray);
			});
		},
		getNoFileZtreeArrayData: function getNoFileZtreeArrayData() {
			var _this2 = this;

			(0, _index.getNoFileTree)(this.setRowData).then(function (res) {
				_this2.ztreeArray = [];
				res.data.forEach(function (item, index) {
					var obj = {
						id: _this2.setRowData.id,

						iconSkin: 'files',
						name: item.name,
						path: item.name,
						nocheck: false,
						nodetype: 0,
						names: _this2.setRowData.name,
						dev_id: _this2.setRowData.dev_id,
						dev_name: _this2.setRowData.dev_name,
						parent: _this2.setRowData.parent,
						fatherName: item.name,

						res_type_val: Number(item.type),
						volume_id: _this2.setRowData.volume_id
					};
					_this2.ztreeArray.push(obj);
				});

				$.fn.zTree.init($('#treeDemoTactics'), _this2.setting, _this2.ztreeArray);
			});
		},
		zTreeOnClick: function zTreeOnClick(event, treeId, treeNode) {
			var _this3 = this;

			if (treeNode.isClick) {
				return false;
			} else {
				treeNode.isClick = true;
			}
			if ((treeNode.res_type_val & 0xffff0000) != '65536') {
				(0, _index.getNoFileTree)(treeNode).then(function (res) {
					var childrenArr = [];
					res.data.forEach(function (item, index) {
						var obj = {
							id: treeNode.id,

							iconSkin: 'files',
							name: item.name,
							path: item.path,
							nocheck: false,
							nodetype: 0,
							names: _this3.setRowData.name,
							dev_id: _this3.setRowData.dev_id,
							dev_name: _this3.setRowData.dev_name,
							parent: _this3.setRowData.parent,
							fatherName: item.name,

							res_type_val: item.type,
							volume_id: treeNode.volume_id
						};
						childrenArr.push(obj);
					});
					var ztreeobj = $.fn.zTree.getZTreeObj(treeId);
					ztreeobj.addNodes(treeNode, childrenArr);
				});
			} else {
				(0, _index.getFileTree)(treeNode).then(function (res) {
					var childrenArr = [];
					res.data.forEach(function (item) {
						var obj = {
							id: treeNode.id,
							iconSkin: 'files',
							name: item.path,
							path: treeNode.fatherName + '/' + item.path,
							nocheck: false,
							nodetype: 0,
							names: _this3.setRowData.name,
							dev_id: _this3.setRowData.dev_id,
							dev_name: _this3.setRowData.dev_name,
							parent: _this3.setRowData.parent,
							checked: _this3.findCheckout(treeNode),
							fatherName: treeNode.fatherName + '/' + item.Path,

							res_type_val: item.Type
						};
						childrenArr.push(obj);
					});
					var ztreeobj = $.fn.zTree.getZTreeObj(treeId);
					ztreeobj.addNodes(treeNode, childrenArr);
				});
			}
		},
		zTreeOnCheck: function zTreeOnCheck(event, treeId, treeNode) {
			var pdchecked = treeNode.checked;
			var treeObj = $.fn.zTree.getZTreeObj('treeDemoTactics');
			var nodes = treeObj.getCheckedNodes(true);
			this.checkedNodeList = [];

			if (treeNode.checked) {
				this.SelectNode(treeNode, treeId);
			} else {
				this.DisSelectNode(treeNode);
			}
		},

		findCheckout: function findCheckout(state) {
			if (state.checked) {
				return true;
			} else {
				return false;
			}
		},
		SelectNode: function SelectNode(treeNode, treeId) {
			var _this4 = this;

			this.resPost = [];
			var ztreeobj = $.fn.zTree.getZTreeObj(treeId);

			var bNeedInsert = true;
			var tempNode = treeNode;

			if (bNeedInsert == true) {
				var exclude_path = '-' + this.tree_path(treeNode).namePath;
				var bExclude = false;
				this.resources.pathConten.forEach(function (item, i) {
					if (item.name == exclude_path) {
						bExclude = true;
						_this4.resources.pathConten.splice(i, 1);
						return false;
					}
				});
				if (bExclude == true) {
					return;
				}
				this.resources.pathConten.unshift({
					path: this.tree_path(treeNode).path,

					type: Number(treeNode.res_type_val),

					Exclude: 0
				});


				this.resPost = this.resources.pathConten;
				this.$emit('getResPost', this.resPost);
			}
		},
		DisSelectNode: function DisSelectNode(treeNode) {
			var _this5 = this;

			var _treeNode = treeNode;
			var parent = null;

			var partenNow = false;
			this.resPost.forEach(function (item, i) {
				if (treeNode.name == item.path) {
					_this5.resPost.splice(i, 1);
				}
			});

			this.$emit('getResPost', this.resPost);
		},
		DeleteItemFromArrayAll: function DeleteItemFromArrayAll(path, start) {
			for (var index = 0; index < this.resources.pathConten.length;) {
				if (this.resources.pathConten[index].name.substring(start, path.length + 1) == path) {
					this.resources.pathConten.splice(index, 1);
				} else if (this.resources.pathConten[index].name.substring(start) == path) {
					this.resources.pathConten.splice(index, 1);
				} else {
					++index;
				}
			}
		},
		DeleteItemFromBrotherArray: function DeleteItemFromBrotherArray(path, start) {
			for (var index = 0; index < this.resources.pathConten.length;) {
				if (this.resources.pathConten[index].name.substring(start, path.length + 1) == path && this.resources.pathConten[index].name.substring(path.length + 1, path.length + 2) == '/') {
					this.resources.pathConten.unshift({
						name: '+' + path,
						path: path,
						type: treeNode.ResType,
						client: this.tree_path(treeNode).client,
						Exclude: 0
					});
				} else if (this.resources.pathConten[index].name.substring(start) == path) {
					this.resources.pathConten.splice(index, 1);
				} else {
					++index;
				}
			}
		},
		DeleteItemFromArrayOne: function DeleteItemFromArrayOne(path, start) {
			for (var index = 0; index < this.resources.pathConten.length;) {
				if (this.resources.pathConten[index].name.substring(start) == path) {
					this.resources.pathConten.splice(index, 1);
				} else {
					++index;
				}
			}
		},

		tree_path: function tree_path(treeNode) {
			var path = '';
			var cid = 0;
			do {
				var parent = treeNode.getParentNode();
				if (!parent) {
					cid = treeNode.id;
					name = treeNode.name;
					break;
				}

				if (parent.nodetype != 0) {
					path = '/' + treeNode.name + path;
				} else {
					path = treeNode.name + path;
				}
				if (parent.nodetype != 1) {}
				treeNode = parent;
			} while (true);

			if (path.indexOf('//') == 0) {
				path = path.substr(1);
			}
			return {
				client: cid,
				path: path,
				name: name,
				namePath: name + '_' + path
			};
		}
	},
	watch: {
		setRowData: {
			handler: function handler(newVal, oldVal) {
				if (newVal && newVal.res_type_val != 65536 && newVal.dev_id) {
					newVal.path = '';
					newVal.names = newVal.name;
					this.getNoFileZtreeArrayData();
				} else {
					newVal.path = '';
					newVal.names = newVal.name;
					this.getZtreeArrayData();
				}
			},

			deep: true,
			immediate: true
		},
		'resources.pathConten': function resourcesPathConten(newVal, oldVal) {}
	}
};
/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(74)))

/***/ }),

/***/ 2826:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _regenerator = __webpack_require__(537);

var _regenerator2 = _interopRequireDefault(_regenerator);

var _asyncToGenerator2 = __webpack_require__(538);

var _asyncToGenerator3 = _interopRequireDefault(_asyncToGenerator2);

var _index = __webpack_require__(2393);

var _drawerBox = __webpack_require__(1528);

var _Tree = __webpack_require__(2618);

var _Tree2 = _interopRequireDefault(_Tree);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
	props: {
		selectType: {
			type: Object
		},
		selectRow: {
			type: Object
		}
	},
	components: {
		Tree: _Tree2.default
	},
	data: function data() {
		return {
			typedis: false,
			formData: {
				optionsObj: {
					119: 1
				}
			},
			resPost: [],
			clientSelect: [],
			agreArr: [],
			dmsldata: [],
			rulesList: {
				'optionsObj[22]': [{ required: true, message: '请选择服务名', trigger: 'change' }],
				mountpath: [{ required: true, message: '请输入路径', trigger: 'blur' }],
				client: [{ required: true, message: '请选择客户端', trigger: 'change', type: 'number' }],
				agre: [{ required: true, message: '请选择协议', trigger: 'change' }]
			},
			OPERATION_TYPE: _index.OPERATION_TYPE,
			OPTION_LIST_TYPE: _drawerBox.OPTION_LIST_TYPE
		};
	},
	mounted: function mounted() {
		this.getClientsFun();
		this.getProtocolFun();
	},

	methods: {
		getResPost: function getResPost(val) {
			this.resPost = val;
		},
		getClientsFun: function getClientsFun() {
			var _this = this;

			(0, _index.getClients)(this.selectRow).then(function (res) {
				_this.clientSelect = res.data.vrts_client_data;
			});
		},
		getProtocolFun: function getProtocolFun() {
			var _this2 = this;

			(0, _index.getProtocol)().then(function (res) {
				_this2.agreArr = res.data;
			});
		},
		optionId: function optionId(datas) {
			var _this3 = this;

			this.dmsldata = [];
			(0, _index.getInstances)(datas, this.selectRow).then(function (res) {
				_this3.dmsldata = res.data;
			});
		},
		closeAddPolicy: function closeAddPolicy() {
			this.$emit('closeButs', false);
		},
		submitForm: function submitForm(name) {
			var _this4 = this;

			this.$refs[name].validate(function () {
				var _ref = (0, _asyncToGenerator3.default)(_regenerator2.default.mark(function _callee(valid) {
					var newFormData;
					return _regenerator2.default.wrap(function _callee$(_context) {
						while (1) {
							switch (_context.prev = _context.next) {
								case 0:
									if (!valid) {
										_context.next = 7;
										break;
									}

									_this4.$Message.success('Success!');
									_context.next = 4;
									return _this4.setOptionArr();

								case 4:
									newFormData = _context.sent;
									_context.next = 7;
									break;

								case 7:
								case 'end':
									return _context.stop();
							}
						}
					}, _callee, _this4);
				}));

				return function (_x) {
					return _ref.apply(this, arguments);
				};
			}());
		},
		setOptionArr: function setOptionArr() {
			var optionsObj = this.formData.optionsObj;
			this.formData.options = [];
			for (var key in optionsObj) {
				var obj = {
					type: key * 1,
					value: optionsObj[key]
				};
				this.formData.options.push(obj);
			}
			return this.formData;
		}
	}
};

/***/ }),

/***/ 2827:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _regenerator = __webpack_require__(537);

var _regenerator2 = _interopRequireDefault(_regenerator);

var _asyncToGenerator2 = __webpack_require__(538);

var _asyncToGenerator3 = _interopRequireDefault(_asyncToGenerator2);

var _index = __webpack_require__(2393);

var _drawerBox = __webpack_require__(1528);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
	props: {
		selectType: {
			type: Object
		},
		selectRow: {
			type: Object
		}
	},
	components: {},
	data: function data() {
		return {
			platformList: [{
				id: 1441792,
				name: 'H3C CAS'
			}, {
				id: 1507328,
				name: 'H3C UIS'
			}, {
				id: 327680,
				name: 'VMWARE'
			}],
			columns: [{
				type: 'selection',
				width: 60,
				align: 'center'
			}, {
				title: '机器名',
				key: 'name',
				sortable: true,
				align: 'left'
			}],
			loading: false,
			tableHeight: 410,
			tableList: [],
			typedis: false,
			formData: {
				client: '',
				optionsObj: {
					119: 1
				}
			},
			resPost: [],
			clientSelect: [],
			agreArr: [],
			rulesList: {
				mountpath: [{ required: true, message: '请输入路径', trigger: 'blur' }],
				client: [{ required: true, message: '请选择客户端', trigger: 'change', type: 'number' }],
				agre: [{ required: true, message: '请选择协议', trigger: 'change' }]
			},
			clickId: '',
			drawerData: {},
			OPERATION_TYPE: _index.OPERATION_TYPE,
			OPTION_LIST_TYPE: _drawerBox.OPTION_LIST_TYPE
		};
	},
	mounted: function mounted() {
		this.getClientsFun();
		this.getProtocolFun();
		this.getBrowseListFun();
	},

	methods: {
		getBrowseListFun: function getBrowseListFun() {
			var _this = this;

			this.loading = true;
			(0, _index.getBrowseList)(this.selectRow).then(function (res) {
				_this.tableList = res.data;
			}).finally(function () {
				_this.loading = false;
			});
		},
		tishi: function tishi(currentRow, index) {
			if (currentRow.id == this.clickId) {
				return 'trbgshow';
			}
			return 'trbgshow_a';
		},
		danJiRow: function danJiRow(row, index) {
			this.clickId = row.id;
			this.drawerData = row;
			this.$refs.table.toggleSelect(index);
		},
		getResPost: function getResPost(val) {
			this.resPost = val;
		},
		getClientsFun: function getClientsFun() {
			var _this2 = this;

			(0, _index.getClients)(this.selectRow).then(function (res) {
				_this2.clientSelect = res.data.vrts_client_data;
			});
		},
		getProtocolFun: function getProtocolFun() {
			var _this3 = this;

			(0, _index.getProtocol)().then(function (res) {
				_this3.agreArr = res.data;
			});
		},
		closeAddPolicy: function closeAddPolicy() {
			this.$emit('closeButs', false);
		},
		submitForm: function submitForm(name) {
			var _this4 = this;

			this.$refs[name].validate(function () {
				var _ref = (0, _asyncToGenerator3.default)(_regenerator2.default.mark(function _callee(valid) {
					var newFormData;
					return _regenerator2.default.wrap(function _callee$(_context) {
						while (1) {
							switch (_context.prev = _context.next) {
								case 0:
									if (!valid) {
										_context.next = 7;
										break;
									}

									_this4.$Message.success('Success!');
									_context.next = 4;
									return _this4.setOptionArr();

								case 4:
									newFormData = _context.sent;
									_context.next = 7;
									break;

								case 7:
								case 'end':
									return _context.stop();
							}
						}
					}, _callee, _this4);
				}));

				return function (_x) {
					return _ref.apply(this, arguments);
				};
			}());
		},
		setOptionArr: function setOptionArr() {
			var optionsObj = this.formData.optionsObj;
			this.formData.options = [];
			for (var key in optionsObj) {
				var obj = {
					type: key * 1,
					value: optionsObj[key]
				};
				this.formData.options.push(obj);
			}
			return this.formData;
		}
	}
};

/***/ }),

/***/ 2828:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
	value: true
});

var _regenerator = __webpack_require__(537);

var _regenerator2 = _interopRequireDefault(_regenerator);

var _asyncToGenerator2 = __webpack_require__(538);

var _asyncToGenerator3 = _interopRequireDefault(_asyncToGenerator2);

var _index = __webpack_require__(2393);

var _drawerBox = __webpack_require__(1528);

var _Tree = __webpack_require__(2618);

var _Tree2 = _interopRequireDefault(_Tree);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

exports.default = {
	props: {
		selectType: {
			type: Object
		},
		selectRow: {
			type: Object
		}
	},
	components: {
		Tree: _Tree2.default
	},
	data: function data() {
		return {
			typedis: false,
			formData: {
				optionsObj: {
					119: 1
				}
			},
			resPost: [],
			clientSelect: [],
			agreArr: [],
			dmsldata: [],
			rulesList: {
				'optionsObj[22]': [{ required: true, message: '请选择服务名', trigger: 'change' }],
				mountpath: [{ required: true, message: '请输入路径', trigger: 'blur' }],
				client: [{ required: true, message: '请选择客户端', trigger: 'change', type: 'number' }],
				agre: [{ required: true, message: '请选择协议', trigger: 'change' }]
			},
			OPERATION_TYPE: _index.OPERATION_TYPE,
			OPTION_LIST_TYPE: _drawerBox.OPTION_LIST_TYPE
		};
	},
	mounted: function mounted() {
		this.getClientsFun();
		this.getProtocolFun();
	},

	methods: {
		getResPost: function getResPost(val) {
			this.resPost = val;
		},
		getClientsFun: function getClientsFun() {
			var _this = this;

			(0, _index.getClients)(this.selectRow).then(function (res) {
				_this.clientSelect = res.data.vrts_client_data;
			});
		},
		getProtocolFun: function getProtocolFun() {
			var _this2 = this;

			(0, _index.getProtocol)().then(function (res) {
				_this2.agreArr = res.data;
			});
		},
		optionId: function optionId(datas) {
			var _this3 = this;

			this.dmsldata = [];
			(0, _index.getInstances)(datas, this.selectRow).then(function (res) {
				_this3.dmsldata = res.data;
			});
		},
		closeAddPolicy: function closeAddPolicy() {
			this.$emit('closeButs', false);
		},
		submitForm: function submitForm(name) {
			var _this4 = this;

			this.$refs[name].validate(function () {
				var _ref = (0, _asyncToGenerator3.default)(_regenerator2.default.mark(function _callee(valid) {
					var newFormData;
					return _regenerator2.default.wrap(function _callee$(_context) {
						while (1) {
							switch (_context.prev = _context.next) {
								case 0:
									if (!valid) {
										_context.next = 7;
										break;
									}

									_this4.$Message.success('Success!');
									_context.next = 4;
									return _this4.setOptionArr();

								case 4:
									newFormData = _context.sent;
									_context.next = 7;
									break;

								case 7:
								case 'end':
									return _context.stop();
							}
						}
					}, _callee, _this4);
				}));

				return function (_x) {
					return _ref.apply(this, arguments);
				};
			}());
		},
		setOptionArr: function setOptionArr() {
			var optionsObj = this.formData.optionsObj;
			this.formData.options = [];
			for (var key in optionsObj) {
				var obj = {
					type: key * 1,
					value: optionsObj[key]
				};
				this.formData.options.push(obj);
			}
			return this.formData;
		}
	}
};

/***/ }),

/***/ 3753:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3754);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("8910d708", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-0eb7434c\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/less-loader/dist/cjs.js!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./newReplicaMag.vue", function() {
     var newContent = require("!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-0eb7434c\",\"scoped\":true,\"hasInlineConfig\":false}!../../../node_modules/less-loader/dist/cjs.js!../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./newReplicaMag.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3754:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.icon[data-v-0eb7434c]{width:1.2em;height:1.2em;vertical-align:-.15em;fill:currentColor;overflow:hidden\n}\n.new-replicamag-wrap[data-v-0eb7434c]{width:100%;height:100%\n}\n.new-replicamag-wrap[data-v-0eb7434c] .el-card__body{width:100%!important\n}\n.new-replicamag-wrap .query-wrap[data-v-0eb7434c]{width:100%;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between\n}\n.new-replicamag-wrap .query-wrap[data-v-0eb7434c],.new-replicamag-wrap .query-wrap .input-wrap[data-v-0eb7434c]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center\n}\n.new-replicamag-wrap .query-wrap .input-wrap[data-v-0eb7434c]{width:90%;height:100%;-webkit-box-pack:start;-ms-flex-pack:start;justify-content:flex-start\n}\n.new-replicamag-wrap .query-wrap .buts-wrap[data-v-0eb7434c]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-ms-flex:1;flex:1;-webkit-box-align:center;-ms-flex-align:center;align-items:center\n}\n.new-replicamag-wrap .query-wrap .searchbar-wrap[data-v-0eb7434c]{margin-right:20px\n}\n.new-replicamag-wrap .page-wrap[data-v-0eb7434c]{margin-top:12px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:end;-ms-flex-pack:end;justify-content:flex-end\n}\n.new-replicamag-wrap .iconfont[data-v-0eb7434c]{font-size:1.375rem;cursor:pointer;color:#ff9130;margin-right:8px\n}\n.new-replicamag-wrap .icon-color-wrap1[data-v-0eb7434c]{font-size:1.625rem;color:#ccc\n}\n.new-replicamag-wrap .icon-color-wrap2[data-v-0eb7434c]{font-size:1.625rem\n}\n.new-replicamag-wrap .icon-color-wrap3[data-v-0eb7434c]{color:#37f;margin:0 5px 0 8px\n}\n.new-replicamag-wrap .icon-color-wrap4[data-v-0eb7434c]{color:#f56c6c\n}\n.new-replicamag-wrap[data-v-0eb7434c] .ivu-dropdown-rel{height:38px\n}\n.new-replicamag-wrap .export-icon-wrap[data-v-0eb7434c]{font-size:1.125rem!important\n}\n.new-replicamag-wrap[data-v-0eb7434c] .table-icon-wrap{font-size:1rem\n}\n.new-replicamag-wrap[data-v-0eb7434c] .icon-name-wrap{display:-webkit-box;display:-ms-flexbox;display:flex;width:100%;-webkit-box-pack:start;-ms-flex-pack:start;justify-content:flex-start\n}", ""]);

// exports


/***/ }),

/***/ 3755:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_MountAndRecover_vue__ = __webpack_require__(2823);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_MountAndRecover_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_MountAndRecover_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_MountAndRecover_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_MountAndRecover_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_55b07331_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_MountAndRecover_vue__ = __webpack_require__(3777);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_55b07331_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_MountAndRecover_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_55b07331_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_MountAndRecover_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3756)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = "data-v-55b07331"
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_MountAndRecover_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_55b07331_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_MountAndRecover_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/dupMag/newReplicaMag/MountAndRecover.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-55b07331", Component.options)
  } else {
    hotAPI.reload("data-v-55b07331", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ }),

/***/ 3756:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3757);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("3d3a3481", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-55b07331\",\"scoped\":true,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./MountAndRecover.vue", function() {
     var newContent = require("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-55b07331\",\"scoped\":true,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./MountAndRecover.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3757:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.modal-wrap[data-v-55b07331]{height:620px;padding:10px;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column\n}\n.modal-wrap[data-v-55b07331],.modal-wrap .add-policy-wrap[data-v-55b07331]{width:100%;display:-webkit-box;display:-ms-flexbox;display:flex\n}\n.modal-wrap .add-policy-wrap[data-v-55b07331]{font-size:1rem;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between\n}\n.modal-wrap .add-policy-wrap span[data-v-55b07331]{font-size:1.25rem;cursor:pointer\n}\n.modal-wrap .components-wap[data-v-55b07331]{-webkit-box-flex:1;-ms-flex:1;flex:1;padding-top:25px;height:50%;margin-bottom:10px\n}", ""]);

// exports


/***/ }),

/***/ 3758:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_ORACLE_vue__ = __webpack_require__(2824);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_ORACLE_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_ORACLE_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_ORACLE_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_ORACLE_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_55f906fa_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_ORACLE_vue__ = __webpack_require__(3764);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_55f906fa_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_ORACLE_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_55f906fa_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_ORACLE_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3759)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = "data-v-55f906fa"
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_ORACLE_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_55f906fa_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_ORACLE_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/dupMag/newReplicaMag/ORACLE.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-55f906fa", Component.options)
  } else {
    hotAPI.reload("data-v-55f906fa", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ }),

/***/ 3759:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3760);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("2b7be47a", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-55f906fa\",\"scoped\":true,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./ORACLE.vue", function() {
     var newContent = require("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-55f906fa\",\"scoped\":true,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./ORACLE.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3760:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.container-wrap[data-v-55f906fa]{width:100%;height:100%;overflow:auto;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column\n}\n.container-wrap .form-wrap[data-v-55f906fa]{-webkit-box-flex:1;-ms-flex:1;flex:1;overflow:auto\n}\n.container-wrap .modefooter-wrap[data-v-55f906fa]{width:100%;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:end;-ms-flex-pack:end;justify-content:flex-end\n}", ""]);

// exports


/***/ }),

/***/ 3761:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3762);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("32add741", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-25aa4314\",\"scoped\":true,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./Tree.vue", function() {
     var newContent = require("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-25aa4314\",\"scoped\":true,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./Tree.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3762:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.tree-wrap[data-v-25aa4314]{width:100%;height:350px\n}", ""]);

// exports


/***/ }),

/***/ 3763:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", { staticClass: "tree-wrap" }, [_c("ul", {
    directives: [{
      name: "show",
      rawName: "v-show",
      value: _vm.ztreeArray.length,
      expression: "ztreeArray.length"
    }],
    staticClass: "ztree",
    staticStyle: { height: "100%", "overflow-y": "auto" },
    attrs: { id: "treeDemoTactics" }
  })]);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-25aa4314", esExports);
  }
}

/***/ }),

/***/ 3764:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", { staticClass: "container-wrap" }, [_c("div", { staticClass: "form-wrap" }, [_c("Form", {
    ref: "formValidate",
    attrs: {
      "label-width": 100,
      model: _vm.formData,
      rules: _vm.rulesList
    }
  }, [_c("FormItem", { attrs: { label: "客户端", prop: "client" } }, [_c("Select", {
    attrs: {
      placeholder: "请选择客户端",
      disabled: _vm.typedis
    },
    on: { "on-change": _vm.optionId },
    model: {
      value: _vm.formData.client,
      callback: function callback($$v) {
        _vm.$set(_vm.formData, "client", $$v);
      },
      expression: "formData.client"
    }
  }, _vm._l(_vm.clientSelect, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.id } }, [_vm._v(_vm._s(item.machine))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "协议", prop: "agre" } }, [_c("Select", {
    attrs: { placeholder: "请选择协议" },
    model: {
      value: _vm.formData.agre,
      callback: function callback($$v) {
        _vm.$set(_vm.formData, "agre", $$v);
      },
      expression: "formData.agre"
    }
  }, _vm._l(_vm.agreArr, function (item) {
    return _c("Option", { key: item, attrs: { value: item } }, [_vm._v(_vm._s(item))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "路径", prop: "mountpath" } }, [_c("Input", {
    attrs: { placeholder: "请输入路径" },
    model: {
      value: _vm.formData.mountpath,
      callback: function callback($$v) {
        _vm.$set(_vm.formData, "mountpath", $$v);
      },
      expression: "formData.mountpath"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "任务类型" } }, [_c("RadioGroup", {
    model: {
      value: _vm.formData.optionsObj[_vm.OPTION_LIST_TYPE["oracle只挂载不恢复"]],
      callback: function callback($$v) {
        _vm.$set(_vm.formData.optionsObj, _vm.OPTION_LIST_TYPE["oracle只挂载不恢复"], $$v);
      },
      expression: "formData.optionsObj[OPTION_LIST_TYPE['oracle只挂载不恢复']]"
    }
  }, [_c("Radio", { attrs: { label: 1 } }, [_vm._v("挂载数据库")]), _vm._v(" "), _c("Radio", { attrs: { label: 2 } }, [_vm._v("挂载文件")])], 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "备注" } }, [_c("Input", {
    attrs: { placeholder: "请输入备注" },
    model: {
      value: _vm.formData.desc,
      callback: function callback($$v) {
        _vm.$set(_vm.formData, "desc", $$v);
      },
      expression: "formData.desc"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "服务名", prop: "optionsObj[22]" } }, [_c("Select", {
    attrs: { placeholder: "请选择服务名" },
    model: {
      value: _vm.formData.optionsObj[_vm.OPTION_LIST_TYPE["实例名"]],
      callback: function callback($$v) {
        _vm.$set(_vm.formData.optionsObj, _vm.OPTION_LIST_TYPE["实例名"], $$v);
      },
      expression: "formData.optionsObj[OPTION_LIST_TYPE['实例名']]"
    }
  }, _vm._l(_vm.dmsldata, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.name } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "恢复时间点" } }, [_c("DatePicker", {
    staticStyle: { width: "100%" },
    attrs: {
      type: "datetime",
      placeholder: "请选择恢复时间节点"
    },
    model: {
      value: _vm.formData.optionsObj[_vm.OPTION_LIST_TYPE["恢复到指定的时间点"]],
      callback: function callback($$v) {
        _vm.$set(_vm.formData.optionsObj, _vm.OPTION_LIST_TYPE["恢复到指定的时间点"], $$v);
      },
      expression: "formData.optionsObj[OPTION_LIST_TYPE['恢复到指定的时间点']]"
    }
  })], 1), _vm._v(" "), _vm.selectType.type == _vm.OPERATION_TYPE.恢复.type ? _c("FormItem", { attrs: { label: "选择文件" } }, [_c("Tree", {
    attrs: { setRowData: _vm.selectRow },
    on: { getResPost: _vm.getResPost }
  })], 1) : _vm._e()], 1)], 1), _vm._v(" "), _c("div", { staticClass: "modefooter-wrap" }, [_c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { size: "large", icon: "md-close-circle" },
    on: {
      click: function click($event) {
        return _vm.closeAddPolicy();
      }
    }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      size: "large",
      icon: "md-checkmark-circle"
    },
    on: {
      click: function click($event) {
        return _vm.submitForm("formValidate");
      }
    }
  }, [_vm._v("确定")])], 1)]);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-55f906fa", esExports);
  }
}

/***/ }),

/***/ 3765:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_KINGBASE_vue__ = __webpack_require__(2826);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_KINGBASE_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_KINGBASE_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_KINGBASE_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_KINGBASE_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_59fe2dad_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_KINGBASE_vue__ = __webpack_require__(3768);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_59fe2dad_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_KINGBASE_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_59fe2dad_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_KINGBASE_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3766)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = "data-v-59fe2dad"
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_KINGBASE_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_59fe2dad_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_KINGBASE_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/dupMag/newReplicaMag/KINGBASE.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-59fe2dad", Component.options)
  } else {
    hotAPI.reload("data-v-59fe2dad", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ }),

/***/ 3766:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3767);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("73a542d3", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-59fe2dad\",\"scoped\":true,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./KINGBASE.vue", function() {
     var newContent = require("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-59fe2dad\",\"scoped\":true,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./KINGBASE.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3767:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.container-wrap[data-v-59fe2dad]{width:100%;height:100%;overflow:auto;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column\n}\n.container-wrap .form-wrap[data-v-59fe2dad]{-webkit-box-flex:1;-ms-flex:1;flex:1;overflow:auto\n}\n.container-wrap .modefooter-wrap[data-v-59fe2dad]{width:100%;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:end;-ms-flex-pack:end;justify-content:flex-end\n}", ""]);

// exports


/***/ }),

/***/ 3768:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", { staticClass: "container-wrap" }, [_c("div", { staticClass: "form-wrap" }, [_c("Form", {
    ref: "formValidate",
    attrs: {
      "label-width": 100,
      model: _vm.formData,
      rules: _vm.rulesList
    }
  }, [_c("FormItem", { attrs: { label: "客户端", prop: "client" } }, [_c("Select", {
    attrs: {
      placeholder: "请选择客户端",
      disabled: _vm.typedis
    },
    on: { "on-change": _vm.optionId },
    model: {
      value: _vm.formData.client,
      callback: function callback($$v) {
        _vm.$set(_vm.formData, "client", $$v);
      },
      expression: "formData.client"
    }
  }, _vm._l(_vm.clientSelect, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.id } }, [_vm._v(_vm._s(item.machine))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "协议", prop: "agre" } }, [_c("Select", {
    attrs: { placeholder: "请选择协议" },
    model: {
      value: _vm.formData.agre,
      callback: function callback($$v) {
        _vm.$set(_vm.formData, "agre", $$v);
      },
      expression: "formData.agre"
    }
  }, _vm._l(_vm.agreArr, function (item) {
    return _c("Option", { key: item, attrs: { value: item } }, [_vm._v(_vm._s(item))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "路径", prop: "mountpath" } }, [_c("Input", {
    attrs: { placeholder: "请输入路径" },
    model: {
      value: _vm.formData.mountpath,
      callback: function callback($$v) {
        _vm.$set(_vm.formData, "mountpath", $$v);
      },
      expression: "formData.mountpath"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "任务类型" } }, [_c("RadioGroup", {
    model: {
      value: _vm.formData.optionsObj[_vm.OPTION_LIST_TYPE["oracle只挂载不恢复"]],
      callback: function callback($$v) {
        _vm.$set(_vm.formData.optionsObj, _vm.OPTION_LIST_TYPE["oracle只挂载不恢复"], $$v);
      },
      expression: "formData.optionsObj[OPTION_LIST_TYPE['oracle只挂载不恢复']]"
    }
  }, [_c("Radio", { attrs: { label: 1 } }, [_vm._v("挂载数据库")]), _vm._v(" "), _c("Radio", { attrs: { label: 2 } }, [_vm._v("挂载文件")])], 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "备注" } }, [_c("Input", {
    attrs: { placeholder: "请输入备注" },
    model: {
      value: _vm.formData.desc,
      callback: function callback($$v) {
        _vm.$set(_vm.formData, "desc", $$v);
      },
      expression: "formData.desc"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "服务名", prop: "optionsObj[22]" } }, [_c("Select", {
    attrs: { placeholder: "请选择服务名" },
    model: {
      value: _vm.formData.optionsObj[_vm.OPTION_LIST_TYPE["实例名"]],
      callback: function callback($$v) {
        _vm.$set(_vm.formData.optionsObj, _vm.OPTION_LIST_TYPE["实例名"], $$v);
      },
      expression: "formData.optionsObj[OPTION_LIST_TYPE['实例名']]"
    }
  }, _vm._l(_vm.dmsldata, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.name } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "恢复时间点" } }, [_c("DatePicker", {
    staticStyle: { width: "100%" },
    attrs: {
      type: "datetime",
      placeholder: "请选择恢复时间节点"
    },
    model: {
      value: _vm.formData.optionsObj[_vm.OPTION_LIST_TYPE["恢复到指定的时间点"]],
      callback: function callback($$v) {
        _vm.$set(_vm.formData.optionsObj, _vm.OPTION_LIST_TYPE["恢复到指定的时间点"], $$v);
      },
      expression: "formData.optionsObj[OPTION_LIST_TYPE['恢复到指定的时间点']]"
    }
  })], 1), _vm._v(" "), _vm.selectType.type == _vm.OPERATION_TYPE.恢复.type ? _c("FormItem", { attrs: { label: "选择文件" } }, [_c("Tree", {
    attrs: { setRowData: _vm.selectRow },
    on: { getResPost: _vm.getResPost }
  })], 1) : _vm._e()], 1)], 1), _vm._v(" "), _c("div", { staticClass: "modefooter-wrap" }, [_c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { size: "large", icon: "md-close-circle" },
    on: {
      click: function click($event) {
        return _vm.closeAddPolicy();
      }
    }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      size: "large",
      icon: "md-checkmark-circle"
    },
    on: {
      click: function click($event) {
        return _vm.submitForm("formValidate");
      }
    }
  }, [_vm._v("确定")])], 1)]);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-59fe2dad", esExports);
  }
}

/***/ }),

/***/ 3769:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_H3CCAS_vue__ = __webpack_require__(2827);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_H3CCAS_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_H3CCAS_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_H3CCAS_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_H3CCAS_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_1f0b70fc_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_H3CCAS_vue__ = __webpack_require__(3772);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_1f0b70fc_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_H3CCAS_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_1f0b70fc_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_H3CCAS_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3770)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = "data-v-1f0b70fc"
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_H3CCAS_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_1f0b70fc_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_H3CCAS_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/dupMag/newReplicaMag/H3CCAS.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-1f0b70fc", Component.options)
  } else {
    hotAPI.reload("data-v-1f0b70fc", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ }),

/***/ 3770:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3771);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("4e85daf8", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-1f0b70fc\",\"scoped\":true,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./H3CCAS.vue", function() {
     var newContent = require("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-1f0b70fc\",\"scoped\":true,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./H3CCAS.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3771:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.container-wrap[data-v-1f0b70fc]{width:100%;height:100%;overflow:auto;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column\n}\n.container-wrap .form-wrap[data-v-1f0b70fc]{-webkit-box-flex:1;-ms-flex:1;flex:1;overflow:auto\n}\n.container-wrap .table-wrap[data-v-1f0b70fc]{width:100%;height:430px;background:#fff;padding:10px;border-radius:8px\n}\n.container-wrap .modefooter-wrap[data-v-1f0b70fc]{width:100%;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:end;-ms-flex-pack:end;justify-content:flex-end\n}", ""]);

// exports


/***/ }),

/***/ 3772:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", { staticClass: "container-wrap" }, [_c("div", { staticClass: "form-wrap" }, [_c("Tabs", { attrs: { type: "card" } }, [_c("TabPane", { attrs: { label: "选择资源" } }, [_c("div", { staticClass: "table-wrap" }, [_c("Table", {
    ref: "table",
    attrs: {
      border: "",
      columns: _vm.columns,
      data: _vm.tableList,
      "row-class-name": _vm.tishi,
      height: _vm.tableHeight,
      loading: _vm.loading
    },
    on: { "on-row-click": _vm.danJiRow }
  })], 1)]), _vm._v(" "), _c("TabPane", { attrs: { label: "选择平台" } }, [_c("Form", {
    ref: "formValidate",
    attrs: {
      "label-width": 80,
      model: _vm.formData,
      rules: _vm.rulesList
    }
  }, [_c("FormItem", { attrs: { label: "平台类型" } }, [_c("Select", {
    attrs: { placeholder: "请选择平台" },
    model: {
      value: _vm.formData.restype,
      callback: function callback($$v) {
        _vm.$set(_vm.formData, "restype", $$v);
      },
      expression: "formData.restype"
    }
  }, _vm._l(_vm.platformList, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.id } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "客户端", prop: "client" } }, [_c("Select", {
    attrs: { placeholder: "请选择客户端" },
    model: {
      value: _vm.formData.client,
      callback: function callback($$v) {
        _vm.$set(_vm.formData, "client", $$v);
      },
      expression: "formData.client"
    }
  }, _vm._l(_vm.clientSelect, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.id } }, [_vm._v(_vm._s(item.machine))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "协议", prop: "agre" } }, [_c("Select", {
    attrs: { placeholder: "请选择协议" },
    model: {
      value: _vm.formData.agre,
      callback: function callback($$v) {
        _vm.$set(_vm.formData, "agre", $$v);
      },
      expression: "formData.agre"
    }
  }, _vm._l(_vm.agreArr, function (item) {
    return _c("Option", { key: item, attrs: { value: item } }, [_vm._v(_vm._s(item))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "路径", prop: "mountpath" } }, [_c("Input", {
    attrs: { placeholder: "请输入路径" },
    model: {
      value: _vm.formData.mountpath,
      callback: function callback($$v) {
        _vm.$set(_vm.formData, "mountpath", $$v);
      },
      expression: "formData.mountpath"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "备注" } }, [_c("Input", {
    attrs: { placeholder: "请输入备注" },
    model: {
      value: _vm.formData.desc,
      callback: function callback($$v) {
        _vm.$set(_vm.formData, "desc", $$v);
      },
      expression: "formData.desc"
    }
  })], 1)], 1)], 1), _vm._v(" "), _c("TabPane", { attrs: { label: "选项" } }, [_vm._v(" 222 ")])], 1)], 1), _vm._v(" "), _c("div", { staticClass: "modefooter-wrap" }, [_c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { size: "large", icon: "md-close-circle" },
    on: {
      click: function click($event) {
        return _vm.closeAddPolicy();
      }
    }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      size: "large",
      icon: "md-checkmark-circle"
    },
    on: {
      click: function click($event) {
        return _vm.submitForm("formValidate");
      }
    }
  }, [_vm._v("确定")])], 1)]);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-1f0b70fc", esExports);
  }
}

/***/ }),

/***/ 3773:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_HanGao_vue__ = __webpack_require__(2828);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_HanGao_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_HanGao_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_HanGao_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_HanGao_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_13575625_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_HanGao_vue__ = __webpack_require__(3776);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_13575625_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_HanGao_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_13575625_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_HanGao_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3774)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = "data-v-13575625"
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_HanGao_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_13575625_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_HanGao_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/dupMag/newReplicaMag/HanGao.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-13575625", Component.options)
  } else {
    hotAPI.reload("data-v-13575625", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ }),

/***/ 3774:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(3775);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(5)("0507feda", content, false, {});
// Hot Module Replacement
if(false) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-13575625\",\"scoped\":true,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./HanGao.vue", function() {
     var newContent = require("!!../../../../node_modules/css-loader/index.js!../../../../node_modules/vue-loader/lib/style-compiler/index.js?{\"vue\":true,\"id\":\"data-v-13575625\",\"scoped\":true,\"hasInlineConfig\":false}!../../../../node_modules/less-loader/dist/cjs.js!../../../../node_modules/vue-loader/lib/selector.js?type=styles&index=0!./HanGao.vue");
     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 3775:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(4)(false);
// imports


// module
exports.push([module.i, "\n.container-wrap[data-v-13575625]{width:100%;height:100%;overflow:auto;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column\n}\n.container-wrap .form-wrap[data-v-13575625]{-webkit-box-flex:1;-ms-flex:1;flex:1;overflow:auto\n}\n.container-wrap .modefooter-wrap[data-v-13575625]{width:100%;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:end;-ms-flex-pack:end;justify-content:flex-end\n}", ""]);

// exports


/***/ }),

/***/ 3776:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", { staticClass: "container-wrap" }, [_c("div", { staticClass: "form-wrap" }, [_c("Form", {
    ref: "formValidate",
    attrs: {
      "label-width": 100,
      model: _vm.formData,
      rules: _vm.rulesList
    }
  }, [_c("FormItem", { attrs: { label: "客户端", prop: "client" } }, [_c("Select", {
    attrs: {
      placeholder: "请选择客户端",
      disabled: _vm.typedis
    },
    on: { "on-change": _vm.optionId },
    model: {
      value: _vm.formData.client,
      callback: function callback($$v) {
        _vm.$set(_vm.formData, "client", $$v);
      },
      expression: "formData.client"
    }
  }, _vm._l(_vm.clientSelect, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.id } }, [_vm._v(_vm._s(item.machine))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "协议", prop: "agre" } }, [_c("Select", {
    attrs: { placeholder: "请选择协议" },
    model: {
      value: _vm.formData.agre,
      callback: function callback($$v) {
        _vm.$set(_vm.formData, "agre", $$v);
      },
      expression: "formData.agre"
    }
  }, _vm._l(_vm.agreArr, function (item) {
    return _c("Option", { key: item, attrs: { value: item } }, [_vm._v(_vm._s(item))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "路径", prop: "mountpath" } }, [_c("Input", {
    attrs: { placeholder: "请输入路径" },
    model: {
      value: _vm.formData.mountpath,
      callback: function callback($$v) {
        _vm.$set(_vm.formData, "mountpath", $$v);
      },
      expression: "formData.mountpath"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "任务类型" } }, [_c("RadioGroup", {
    model: {
      value: _vm.formData.optionsObj[_vm.OPTION_LIST_TYPE["oracle只挂载不恢复"]],
      callback: function callback($$v) {
        _vm.$set(_vm.formData.optionsObj, _vm.OPTION_LIST_TYPE["oracle只挂载不恢复"], $$v);
      },
      expression: "formData.optionsObj[OPTION_LIST_TYPE['oracle只挂载不恢复']]"
    }
  }, [_c("Radio", { attrs: { label: 1 } }, [_vm._v("挂载数据库")]), _vm._v(" "), _c("Radio", { attrs: { label: 2 } }, [_vm._v("挂载文件")])], 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "备注" } }, [_c("Input", {
    attrs: { placeholder: "请输入备注" },
    model: {
      value: _vm.formData.desc,
      callback: function callback($$v) {
        _vm.$set(_vm.formData, "desc", $$v);
      },
      expression: "formData.desc"
    }
  })], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "服务名", prop: "optionsObj[22]" } }, [_c("Select", {
    attrs: { placeholder: "请选择服务名" },
    model: {
      value: _vm.formData.optionsObj[_vm.OPTION_LIST_TYPE["实例名"]],
      callback: function callback($$v) {
        _vm.$set(_vm.formData.optionsObj, _vm.OPTION_LIST_TYPE["实例名"], $$v);
      },
      expression: "formData.optionsObj[OPTION_LIST_TYPE['实例名']]"
    }
  }, _vm._l(_vm.dmsldata, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.name } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("FormItem", { attrs: { label: "恢复时间点" } }, [_c("DatePicker", {
    staticStyle: { width: "100%" },
    attrs: {
      type: "datetime",
      placeholder: "请选择恢复时间节点"
    },
    model: {
      value: _vm.formData.optionsObj[_vm.OPTION_LIST_TYPE["恢复到指定的时间点"]],
      callback: function callback($$v) {
        _vm.$set(_vm.formData.optionsObj, _vm.OPTION_LIST_TYPE["恢复到指定的时间点"], $$v);
      },
      expression: "formData.optionsObj[OPTION_LIST_TYPE['恢复到指定的时间点']]"
    }
  })], 1), _vm._v(" "), _vm.selectType.type == _vm.OPERATION_TYPE.恢复.type ? _c("FormItem", { attrs: { label: "选择文件" } }, [_c("Tree", {
    attrs: { setRowData: _vm.selectRow },
    on: { getResPost: _vm.getResPost }
  })], 1) : _vm._e()], 1)], 1), _vm._v(" "), _c("div", { staticClass: "modefooter-wrap" }, [_c("Button", {
    staticClass: "footerbnt bntclose",
    attrs: { size: "large", icon: "md-close-circle" },
    on: {
      click: function click($event) {
        return _vm.closeAddPolicy();
      }
    }
  }, [_vm._v("取消")]), _vm._v(" "), _c("Button", {
    attrs: {
      type: "primary",
      size: "large",
      icon: "md-checkmark-circle"
    },
    on: {
      click: function click($event) {
        return _vm.submitForm("formValidate");
      }
    }
  }, [_vm._v("确定")])], 1)]);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-13575625", esExports);
  }
}

/***/ }),

/***/ 3777:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", { staticClass: "mount-and-recover-wrap" }, [_c("Modal", {
    attrs: {
      closable: false,
      "mask-closable": false,
      "footer-hide": true,
      width: "50%"
    },
    model: {
      value: _vm.value,
      callback: function callback($$v) {
        _vm.value = $$v;
      },
      expression: "value"
    }
  }, [_c("div", { staticClass: "modal-wrap" }, [_c("div", { staticClass: "add-policy-wrap" }, [_c("h3", [_vm._v(_vm._s(_vm.selectType.title))]), _vm._v(" "), _c("span", {
    staticClass: "iconfont close-icon-wrap",
    on: { click: _vm.closeButs }
  }, [_vm._v("")])]), _vm._v(" "), _c("div", { staticClass: "components-wap" }, [_c(_vm.componentIdName, {
    ref: "refComponent",
    tag: "component",
    attrs: {
      selectType: _vm.selectType,
      selectRow: _vm.selectRow
    },
    on: { closeButs: _vm.closeButs }
  })], 1)])])], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-55b07331", esExports);
  }
}

/***/ }),

/***/ 3778:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


Object.defineProperty(exports, "__esModule", {
  value: true
});
var render = function render() {
  var _vm = this;
  var _h = _vm.$createElement;
  var _c = _vm._self._c || _h;
  return _c("div", { staticClass: "new-replicamag-wrap" }, [_c("Breadcrumb", { staticClass: "breadstyle" }, [_c("BreadcrumbItem", {
    staticClass: "breadlist",
    staticStyle: { display: "flex", "align-items": "center" }
  }, [_c("img", {
    staticStyle: {
      width: "1.2rem",
      height: "1.2rem",
      "margin-right": "0.3rem"
    },
    attrs: { src: __webpack_require__(318) }
  }), _vm._v(" "), _c("span", [_vm._v("数据副本 / 恢复管理")])])], 1), _vm._v(" "), _c("el-card", { staticClass: "searchBox" }, [_c("div", { staticClass: "query-wrap" }, [_c("div", { staticClass: "input-wrap" }, [_c("div", { staticClass: "searchbar-wrap" }, [_c("span", { staticStyle: { width: "60px" } }, [_vm._v("策略名称")]), _vm._v(" "), _c("Input", {
    staticStyle: { width: "160px" },
    attrs: { clearable: "", placeholder: "请输入策略名称..." },
    on: { input: _vm.getTableDataFun },
    model: {
      value: _vm.query.policyname,
      callback: function callback($$v) {
        _vm.$set(_vm.query, "policyname", $$v);
      },
      expression: "query.policyname"
    }
  })], 1), _vm._v(" "), _c("div", { staticClass: "searchbar-wrap" }, [_c("span", { staticStyle: { width: "60px" } }, [_vm._v("客户端")]), _vm._v(" "), _c("Select", {
    staticStyle: { width: "160px" },
    attrs: { clearable: "" },
    on: { "on-change": _vm.getTableDataFun },
    model: {
      value: _vm.query.client,
      callback: function callback($$v) {
        _vm.$set(_vm.query, "client", $$v);
      },
      expression: "query.client"
    }
  }, _vm._l(_vm.clientSelect, function (item) {
    return _c("Option", { key: item.id, attrs: { value: item.id } }, [_vm._v(_vm._s(item.machine))]);
  }), 1)], 1), _vm._v(" "), _c("div", { staticClass: "searchbar-wrap" }, [_c("span", { staticStyle: { width: "60px" } }, [_vm._v("IP地址")]), _vm._v(" "), _c("Input", {
    staticStyle: { width: "160px" },
    attrs: { clearable: "", placeholder: "请输入ip地址..." },
    on: { input: _vm.getTableDataFun },
    model: {
      value: _vm.query.ip,
      callback: function callback($$v) {
        _vm.$set(_vm.query, "ip", $$v);
      },
      expression: "query.ip"
    }
  })], 1), _vm._v(" "), _c("div", { staticClass: "searchbar-wrap" }, [_c("span", { staticStyle: { width: "60px" } }, [_vm._v("资源类型")]), _vm._v(" "), _c("Select", {
    staticStyle: { width: "150px" },
    attrs: { clearable: "" },
    on: { "on-change": _vm.getTableDataFun },
    model: {
      value: _vm.query.restype,
      callback: function callback($$v) {
        _vm.$set(_vm.query, "restype", $$v);
      },
      expression: "query.restype"
    }
  }, _vm._l(_vm.typeSelect, function (item) {
    return _c("Option", { key: item.type, attrs: { value: item.type } }, [_vm._v(_vm._s(item.name))]);
  }), 1)], 1), _vm._v(" "), _c("div", { staticClass: "searchbar-wrap" }, [_c("span", { staticStyle: { width: "60px" } }, [_vm._v("时间范围")]), _vm._v(" "), _c("Date-picker", {
    attrs: {
      type: "datetimerange",
      placeholder: "选择开始和结束时间"
    },
    on: { "on-change": _vm.setTime }
  })], 1)])])]), _vm._v(" "), _c("el-card", {
    staticClass: "policy",
    staticStyle: {
      margin: "0px 15px 15px 15px",
      height: "calc(100vh - 250px)",
      "overflow-y": "auto"
    }
  }, [[_c("Table", {
    attrs: {
      border: "",
      columns: _vm.columns,
      data: _vm.tableList,
      "row-class-name": _vm.tishi,
      height: _vm.tableHeight,
      loading: _vm.loading
    },
    on: { "on-row-click": _vm.danJiRow },
    scopedSlots: _vm._u([{
      key: "restype",
      fn: function fn(ref) {
        var row = ref.row;
        return [_c("svg", {
          staticClass: "icon",
          attrs: { "aria-hidden": "true" },
          domProps: {
            innerHTML: _vm._s(_vm.findIcons(row.resource_type))
          }
        }), _vm._v(" "), _c("span", [_vm._v(_vm._s(row.resource_type))])];
      }
    }, {
      key: "status",
      fn: function fn(ref) {
        var row = ref.row;
        return [_c("span", {
          style: {
            color: row.status == "正常" ? "#42bd21" : "#ff0000"
          }
        }, [_vm._v("\n            " + _vm._s(row.status) + "\n          ")])];
      }
    }, {
      key: "operation",
      fn: function fn(ref) {
        var row = ref.row;
        return [_c("div", [_c("span", {
          staticClass: "iconfont",
          on: {
            click: function click($event) {
              $event.stopPropagation();
              return _vm.staleDated(row);
            }
          }
        }, [_vm._v("")]), _vm._v(" "), row.mount == 1 ? _c("span", {
          staticClass: "iconfont icon-color-wrap2",
          on: {
            click: function click($event) {
              $event.stopPropagation();
              return _vm.setOperation(row, _vm.OPERATION_TYPE.挂载);
            }
          }
        }, [_vm._v("")]) : _c("span", { staticClass: "iconfont icon-color-wrap1" }, [_vm._v("")]), _vm._v(" "), _c("span", {
          staticClass: "iconfont",
          on: {
            click: function click($event) {
              $event.stopPropagation();
              return _vm.setOperation(row, _vm.OPERATION_TYPE.恢复);
            }
          }
        }, [_vm._v("")])])];
      }
    }])
  })], _vm._v(" "), _c("el-pagination", {
    staticClass: "page-wrap",
    attrs: {
      background: "",
      "current-page": _vm.query.pageNumber,
      "page-sizes": [10, 20, 30, 40],
      "page-size": _vm.query.pageSize,
      layout: "total, sizes, prev, pager, next, jumper",
      total: _vm.tableList.length
    },
    on: {
      "size-change": _vm.sizeChange,
      "current-change": _vm.currentChange
    }
  })], 2), _vm._v(" "), _vm.showStaleDated ? _c("ModalBox", {
    attrs: { modelProps: _vm.modelProps },
    on: { postStatus: _vm.postStatus },
    model: {
      value: _vm.showStaleDated,
      callback: function callback($$v) {
        _vm.showStaleDated = $$v;
      },
      expression: "showStaleDated"
    }
  }) : _vm._e(), _vm._v(" "), _vm.showMountAndRecover ? _c("MountAndRecover", {
    attrs: { selectRow: _vm.selectRow, selectType: _vm.selectType },
    model: {
      value: _vm.showMountAndRecover,
      callback: function callback($$v) {
        _vm.showMountAndRecover = $$v;
      },
      expression: "showMountAndRecover"
    }
  }) : _vm._e()], 1);
};
var staticRenderFns = [];
render._withStripped = true;
var esExports = { render: render, staticRenderFns: staticRenderFns };
exports.default = esExports;

if (false) {
  module.hot.accept();
  if (module.hot.data) {
    require("vue-hot-reload-api").rerender("data-v-0eb7434c", esExports);
  }
}

/***/ }),

/***/ 585:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_newReplicaMag_vue__ = __webpack_require__(2822);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_newReplicaMag_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_newReplicaMag_vue__);
/* harmony namespace reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_newReplicaMag_vue__) if(__WEBPACK_IMPORT_KEY__ !== 'default') (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_newReplicaMag_vue__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_0eb7434c_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_newReplicaMag_vue__ = __webpack_require__(3778);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_0eb7434c_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_newReplicaMag_vue___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_0eb7434c_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_newReplicaMag_vue__);
var disposed = false
function injectStyle (ssrContext) {
  if (disposed) return
  __webpack_require__(3753)
}
var normalizeComponent = __webpack_require__(10)
/* script */


/* template */

/* template functional */
var __vue_template_functional__ = false
/* styles */
var __vue_styles__ = injectStyle
/* scopeId */
var __vue_scopeId__ = "data-v-0eb7434c"
/* moduleIdentifier (server only) */
var __vue_module_identifier__ = null
var Component = normalizeComponent(
  __WEBPACK_IMPORTED_MODULE_0__babel_loader_node_modules_vue_loader_lib_selector_type_script_index_0_newReplicaMag_vue___default.a,
  __WEBPACK_IMPORTED_MODULE_1__babel_loader_node_modules_vue_loader_lib_template_compiler_index_id_data_v_0eb7434c_hasScoped_true_buble_transforms_node_modules_vue_loader_lib_selector_type_template_index_0_newReplicaMag_vue___default.a,
  __vue_template_functional__,
  __vue_styles__,
  __vue_scopeId__,
  __vue_module_identifier__
)
Component.options.__file = "src/views/dupMag/newReplicaMag.vue"

/* hot reload */
if (false) {(function () {
  var hotAPI = require("vue-hot-reload-api")
  hotAPI.install(require("vue"), false)
  if (!hotAPI.compatible) return
  module.hot.accept()
  if (!module.hot.data) {
    hotAPI.createRecord("data-v-0eb7434c", Component.options)
  } else {
    hotAPI.reload("data-v-0eb7434c", Component.options)
  }
  module.hot.dispose(function (data) {
    disposed = true
  })
})()}

/* harmony default export */ __webpack_exports__["default"] = (Component.exports);


/***/ })

});