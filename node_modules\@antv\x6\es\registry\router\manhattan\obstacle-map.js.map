{"version": 3, "file": "obstacle-map.js", "sourceRoot": "", "sources": ["../../../../src/registry/router/manhattan/obstacle-map.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAY,MAAM,iBAAiB,CAAA;AACpD,OAAO,EAAa,KAAK,EAAE,MAAM,mBAAmB,CAAA;AAIpD;;GAEG;AACH,MAAM,OAAO,WAAW;IAUtB,YAAY,OAAwB;QAClC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,WAAW,GAAG,GAAG,CAAA;QACtB,IAAI,CAAC,GAAG,GAAG,EAAE,CAAA;IACf,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,KAAY,EAAE,IAAU;QAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC5B,gEAAgE;QAChE,MAAM,iBAAiB,GAAG,OAAO,CAAC,gBAAgB,CAAC,MAAM,CACvD,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;YACb,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAA;YAC3B,IAAI,QAAQ,EAAE;gBACZ,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAE,QAAkC,CAAC,IAAI,CAAC,CAAA;gBACpE,IAAI,IAAI,EAAE;oBACR,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;iBAChB;aACF;YAED,OAAO,IAAI,CAAA;QACb,CAAC,EACD,EAAE,CACH,CAAA;QAED,IAAI,iBAAiB,GAAa,EAAE,CAAA;QAEpC,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAA;QACpD,IAAI,MAAM,EAAE;YACV,iBAAiB,GAAG,QAAQ,CAAC,KAAK,CAChC,iBAAiB,EACjB,MAAM,CAAC,YAAY,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAC7C,CAAA;SACF;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAA;QACpD,IAAI,MAAM,EAAE;YACV,iBAAiB,GAAG,QAAQ,CAAC,KAAK,CAChC,iBAAiB,EACjB,MAAM,CAAC,YAAY,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAC7C,CAAA;SACF;QAED,wEAAwE;QACxE,oEAAoE;QACpE,yEAAyE;QACzE,mCAAmC;QACnC,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAA;QAEpC,KAAK,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACpC,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,IAAI,CAC7C,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,CAC9B,CAAA;YACD,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK;gBAC9B,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;gBAC5C,CAAC,CAAC,KAAK,CAAA;YACT,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;gBACtD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;oBAC5B,OAAO,IAAI,CAAC,EAAE,KAAK,IAAI,CAAA;iBACxB;gBACD,OAAO,IAAI,KAAK,IAAI,CAAA;YACtB,CAAC,CAAC,CAAA;YACF,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YAC5D,MAAM,QAAQ,GACZ,aAAa,IAAI,gBAAgB,IAAI,YAAY,IAAI,gBAAgB,CAAA;YAEvE,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE;gBACjC,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;gBAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAA;gBACvD,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAA;gBAEvD,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI,WAAW,EAAE;oBACtD,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI,WAAW,EAAE;wBACtD,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAA;wBACtC,IAAI,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE;4BACpB,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAA;yBACd;wBACD,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;qBACpB;iBACF;aACF;YACD,OAAO,GAAG,CAAA;QACZ,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;QAEZ,OAAO,IAAI,CAAA;IACb,CAAC;IAED,YAAY,CAAC,KAAY;QACvB,MAAM,GAAG,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,CAAA;QAEjE,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAC3B,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IACzE,CAAC;CACF"}