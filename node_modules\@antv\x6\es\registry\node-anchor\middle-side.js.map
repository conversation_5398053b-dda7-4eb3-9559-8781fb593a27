{"version": 3, "file": "middle-side.js", "sourceRoot": "", "sources": ["../../../src/registry/node-anchor/middle-side.ts"], "names": [], "mappings": "AACA,OAAO,EAAkB,OAAO,EAAE,MAAM,QAAQ,CAAA;AAShD,MAAM,UAAU,GACd,UAAU,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO;IACvC,IAAI,IAAI,CAAA;IACR,IAAI,KAAK,GAAG,CAAC,CAAA;IACb,IAAI,MAAM,CAAA;IAEV,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;IACtB,IAAI,OAAO,CAAC,MAAM,EAAE;QAClB,IAAI,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAA;QAC7C,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,SAAS,EAAE,CAAA;QACnC,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAA;KACxB;SAAM;QACL,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA;KACrC;IAED,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;IAC/B,IAAI,OAAO,IAAI,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;QAC/C,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;KACtB;IAED,IAAI,OAAO,CAAC,MAAM,EAAE;QAClB,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;KAC/B;IAED,MAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAA;IACjD,IAAI,MAAa,CAAA;IACjB,QAAQ,IAAI,EAAE;QACZ,KAAK,MAAM;YACT,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;YAC7B,MAAK;QACP,KAAK,OAAO;YACV,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAA;YAC9B,MAAK;QACP,KAAK,KAAK;YACR,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAA;YAC5B,MAAK;QACP,KAAK,QAAQ;YACX,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,CAAA;YAC/B,MAAK;QACP;YACE,MAAK;KACR;IAED,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;IACnC,IAAI,SAAS,KAAK,GAAG,EAAE;QACrB,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,QAAQ,EAAE;YACvC,IAAI,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;gBACrC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;aAC9B;iBAAM;gBACL,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAA;aAC/B;SACF;KACF;SAAM,IAAI,SAAS,KAAK,GAAG,EAAE;QAC5B,IAAI,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE;YACtC,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAA;SAC7B;aAAM;YACL,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,CAAA;SAChC;KACF;IAED,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,MAAO,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,MAAO,CAAA;AAClE,CAAC,CAAA;AAEH;;;GAGG;AACH,MAAM,CAAC,MAAM,OAAO,GAAG,OAAO,CAG5B,UAAU,CAAC,CAAA"}